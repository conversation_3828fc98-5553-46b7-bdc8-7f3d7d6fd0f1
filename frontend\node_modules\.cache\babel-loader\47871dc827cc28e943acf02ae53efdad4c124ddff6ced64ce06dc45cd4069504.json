{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nvar SeriesDimensionDefine = /** @class */function () {\n  /**\r\n   * @param opt All of the fields will be shallow copied.\r\n   */\n  function SeriesDimensionDefine(opt) {\n    /**\r\n     * The format of `otherDims` is:\r\n     * ```js\r\n     * {\r\n     *     tooltip?: number\r\n     *     label?: number\r\n     *     itemName?: number\r\n     *     seriesName?: number\r\n     * }\r\n     * ```\r\n     *\r\n     * A `series.encode` can specified these fields:\r\n     * ```js\r\n     * encode: {\r\n     *     // \"3, 1, 5\" is the index of data dimension.\r\n     *     tooltip: [3, 1, 5],\r\n     *     label: [0, 3],\r\n     *     ...\r\n     * }\r\n     * ```\r\n     * `otherDims` is the parse result of the `series.encode` above, like:\r\n     * ```js\r\n     * // Suppose the index of this data dimension is `3`.\r\n     * this.otherDims = {\r\n     *     // `3` is at the index `0` of the `encode.tooltip`\r\n     *     tooltip: 0,\r\n     *     // `3` is at the index `1` of the `encode.label`\r\n     *     label: 1\r\n     * };\r\n     * ```\r\n     *\r\n     * This prop should never be `null`/`undefined` after initialized.\r\n     */\n    this.otherDims = {};\n    if (opt != null) {\n      zrUtil.extend(this, opt);\n    }\n  }\n  return SeriesDimensionDefine;\n}();\n;\nexport default SeriesDimensionDefine;", "map": {"version": 3, "names": ["zrUtil", "SeriesDimensionDefine", "opt", "otherDims", "extend"], "sources": ["E:/AI/SmartCV/node_modules/echarts/lib/data/SeriesDimensionDefine.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nvar SeriesDimensionDefine = /** @class */function () {\n  /**\r\n   * @param opt All of the fields will be shallow copied.\r\n   */\n  function SeriesDimensionDefine(opt) {\n    /**\r\n     * The format of `otherDims` is:\r\n     * ```js\r\n     * {\r\n     *     tooltip?: number\r\n     *     label?: number\r\n     *     itemName?: number\r\n     *     seriesName?: number\r\n     * }\r\n     * ```\r\n     *\r\n     * A `series.encode` can specified these fields:\r\n     * ```js\r\n     * encode: {\r\n     *     // \"3, 1, 5\" is the index of data dimension.\r\n     *     tooltip: [3, 1, 5],\r\n     *     label: [0, 3],\r\n     *     ...\r\n     * }\r\n     * ```\r\n     * `otherDims` is the parse result of the `series.encode` above, like:\r\n     * ```js\r\n     * // Suppose the index of this data dimension is `3`.\r\n     * this.otherDims = {\r\n     *     // `3` is at the index `0` of the `encode.tooltip`\r\n     *     tooltip: 0,\r\n     *     // `3` is at the index `1` of the `encode.label`\r\n     *     label: 1\r\n     * };\r\n     * ```\r\n     *\r\n     * This prop should never be `null`/`undefined` after initialized.\r\n     */\n    this.otherDims = {};\n    if (opt != null) {\n      zrUtil.extend(this, opt);\n    }\n  }\n  return SeriesDimensionDefine;\n}();\n;\nexport default SeriesDimensionDefine;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,IAAIC,qBAAqB,GAAG,aAAa,YAAY;EACnD;AACF;AACA;EACE,SAASA,qBAAqBA,CAACC,GAAG,EAAE;IAClC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC;IACnB,IAAID,GAAG,IAAI,IAAI,EAAE;MACfF,MAAM,CAACI,MAAM,CAAC,IAAI,EAAEF,GAAG,CAAC;IAC1B;EACF;EACA,OAAOD,qBAAqB;AAC9B,CAAC,CAAC,CAAC;AACH;AACA,eAAeA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}