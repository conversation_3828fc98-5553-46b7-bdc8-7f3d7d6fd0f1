{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport VisualMapModel from './VisualMapModel.js';\nimport VisualMapping from '../../visual/VisualMapping.js';\nimport visualDefault from '../../visual/visualDefault.js';\nimport { reformIntervals } from '../../util/number.js';\nimport { inheritDefaultOption } from '../../util/component.js';\nvar PiecewiseModel = /** @class */function (_super) {\n  __extends(PiecewiseModel, _super);\n  function PiecewiseModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = PiecewiseModel.type;\n    /**\r\n     * The order is always [low, ..., high].\r\n     * [{text: string, interval: Array.<number>}, ...]\r\n     */\n    _this._pieceList = [];\n    return _this;\n  }\n  PiecewiseModel.prototype.optionUpdated = function (newOption, isInit) {\n    _super.prototype.optionUpdated.apply(this, arguments);\n    this.resetExtent();\n    var mode = this._mode = this._determineMode();\n    this._pieceList = [];\n    resetMethods[this._mode].call(this, this._pieceList);\n    this._resetSelected(newOption, isInit);\n    var categories = this.option.categories;\n    this.resetVisual(function (mappingOption, state) {\n      if (mode === 'categories') {\n        mappingOption.mappingMethod = 'category';\n        mappingOption.categories = zrUtil.clone(categories);\n      } else {\n        mappingOption.dataExtent = this.getExtent();\n        mappingOption.mappingMethod = 'piecewise';\n        mappingOption.pieceList = zrUtil.map(this._pieceList, function (piece) {\n          piece = zrUtil.clone(piece);\n          if (state !== 'inRange') {\n            // FIXME\n            // outOfRange do not support special visual in pieces.\n            piece.visual = null;\n          }\n          return piece;\n        });\n      }\n    });\n  };\n  /**\r\n   * @protected\r\n   * @override\r\n   */\n  PiecewiseModel.prototype.completeVisualOption = function () {\n    // Consider this case:\n    // visualMap: {\n    //      pieces: [{symbol: 'circle', lt: 0}, {symbol: 'rect', gte: 0}]\n    // }\n    // where no inRange/outOfRange set but only pieces. So we should make\n    // default inRange/outOfRange for this case, otherwise visuals that only\n    // appear in `pieces` will not be taken into account in visual encoding.\n    var option = this.option;\n    var visualTypesInPieces = {};\n    var visualTypes = VisualMapping.listVisualTypes();\n    var isCategory = this.isCategory();\n    zrUtil.each(option.pieces, function (piece) {\n      zrUtil.each(visualTypes, function (visualType) {\n        if (piece.hasOwnProperty(visualType)) {\n          visualTypesInPieces[visualType] = 1;\n        }\n      });\n    });\n    zrUtil.each(visualTypesInPieces, function (v, visualType) {\n      var exists = false;\n      zrUtil.each(this.stateList, function (state) {\n        exists = exists || has(option, state, visualType) || has(option.target, state, visualType);\n      }, this);\n      !exists && zrUtil.each(this.stateList, function (state) {\n        (option[state] || (option[state] = {}))[visualType] = visualDefault.get(visualType, state === 'inRange' ? 'active' : 'inactive', isCategory);\n      });\n    }, this);\n    function has(obj, state, visualType) {\n      return obj && obj[state] && obj[state].hasOwnProperty(visualType);\n    }\n    _super.prototype.completeVisualOption.apply(this, arguments);\n  };\n  PiecewiseModel.prototype._resetSelected = function (newOption, isInit) {\n    var thisOption = this.option;\n    var pieceList = this._pieceList;\n    // Selected do not merge but all override.\n    var selected = (isInit ? thisOption : newOption).selected || {};\n    thisOption.selected = selected;\n    // Consider 'not specified' means true.\n    zrUtil.each(pieceList, function (piece, index) {\n      var key = this.getSelectedMapKey(piece);\n      if (!selected.hasOwnProperty(key)) {\n        selected[key] = true;\n      }\n    }, this);\n    if (thisOption.selectedMode === 'single') {\n      // Ensure there is only one selected.\n      var hasSel_1 = false;\n      zrUtil.each(pieceList, function (piece, index) {\n        var key = this.getSelectedMapKey(piece);\n        if (selected[key]) {\n          hasSel_1 ? selected[key] = false : hasSel_1 = true;\n        }\n      }, this);\n    }\n    // thisOption.selectedMode === 'multiple', default: all selected.\n  };\n  /**\r\n   * @public\r\n   */\n  PiecewiseModel.prototype.getItemSymbol = function () {\n    return this.get('itemSymbol');\n  };\n  /**\r\n   * @public\r\n   */\n  PiecewiseModel.prototype.getSelectedMapKey = function (piece) {\n    return this._mode === 'categories' ? piece.value + '' : piece.index + '';\n  };\n  /**\r\n   * @public\r\n   */\n  PiecewiseModel.prototype.getPieceList = function () {\n    return this._pieceList;\n  };\n  /**\r\n   * @return {string}\r\n   */\n  PiecewiseModel.prototype._determineMode = function () {\n    var option = this.option;\n    return option.pieces && option.pieces.length > 0 ? 'pieces' : this.option.categories ? 'categories' : 'splitNumber';\n  };\n  /**\r\n   * @override\r\n   */\n  PiecewiseModel.prototype.setSelected = function (selected) {\n    this.option.selected = zrUtil.clone(selected);\n  };\n  /**\r\n   * @override\r\n   */\n  PiecewiseModel.prototype.getValueState = function (value) {\n    var index = VisualMapping.findPieceIndex(value, this._pieceList);\n    return index != null ? this.option.selected[this.getSelectedMapKey(this._pieceList[index])] ? 'inRange' : 'outOfRange' : 'outOfRange';\n  };\n  /**\r\n   * @public\r\n   * @param pieceIndex piece index in visualMapModel.getPieceList()\r\n   */\n  PiecewiseModel.prototype.findTargetDataIndices = function (pieceIndex) {\n    var result = [];\n    var pieceList = this._pieceList;\n    this.eachTargetSeries(function (seriesModel) {\n      var dataIndices = [];\n      var data = seriesModel.getData();\n      data.each(this.getDataDimensionIndex(data), function (value, dataIndex) {\n        // Should always base on model pieceList, because it is order sensitive.\n        var pIdx = VisualMapping.findPieceIndex(value, pieceList);\n        pIdx === pieceIndex && dataIndices.push(dataIndex);\n      }, this);\n      result.push({\n        seriesId: seriesModel.id,\n        dataIndex: dataIndices\n      });\n    }, this);\n    return result;\n  };\n  /**\r\n   * @private\r\n   * @param piece piece.value or piece.interval is required.\r\n   * @return  Can be Infinity or -Infinity\r\n   */\n  PiecewiseModel.prototype.getRepresentValue = function (piece) {\n    var representValue;\n    if (this.isCategory()) {\n      representValue = piece.value;\n    } else {\n      if (piece.value != null) {\n        representValue = piece.value;\n      } else {\n        var pieceInterval = piece.interval || [];\n        representValue = pieceInterval[0] === -Infinity && pieceInterval[1] === Infinity ? 0 : (pieceInterval[0] + pieceInterval[1]) / 2;\n      }\n    }\n    return representValue;\n  };\n  PiecewiseModel.prototype.getVisualMeta = function (getColorVisual) {\n    // Do not support category. (category axis is ordinal, numerical)\n    if (this.isCategory()) {\n      return;\n    }\n    var stops = [];\n    var outerColors = ['', ''];\n    var visualMapModel = this;\n    function setStop(interval, valueState) {\n      var representValue = visualMapModel.getRepresentValue({\n        interval: interval\n      }); // Not category\n      if (!valueState) {\n        valueState = visualMapModel.getValueState(representValue);\n      }\n      var color = getColorVisual(representValue, valueState);\n      if (interval[0] === -Infinity) {\n        outerColors[0] = color;\n      } else if (interval[1] === Infinity) {\n        outerColors[1] = color;\n      } else {\n        stops.push({\n          value: interval[0],\n          color: color\n        }, {\n          value: interval[1],\n          color: color\n        });\n      }\n    }\n    // Suplement\n    var pieceList = this._pieceList.slice();\n    if (!pieceList.length) {\n      pieceList.push({\n        interval: [-Infinity, Infinity]\n      });\n    } else {\n      var edge = pieceList[0].interval[0];\n      edge !== -Infinity && pieceList.unshift({\n        interval: [-Infinity, edge]\n      });\n      edge = pieceList[pieceList.length - 1].interval[1];\n      edge !== Infinity && pieceList.push({\n        interval: [edge, Infinity]\n      });\n    }\n    var curr = -Infinity;\n    zrUtil.each(pieceList, function (piece) {\n      var interval = piece.interval;\n      if (interval) {\n        // Fulfill gap.\n        interval[0] > curr && setStop([curr, interval[0]], 'outOfRange');\n        setStop(interval.slice());\n        curr = interval[1];\n      }\n    }, this);\n    return {\n      stops: stops,\n      outerColors: outerColors\n    };\n  };\n  PiecewiseModel.type = 'visualMap.piecewise';\n  PiecewiseModel.defaultOption = inheritDefaultOption(VisualMapModel.defaultOption, {\n    selected: null,\n    minOpen: false,\n    maxOpen: false,\n    align: 'auto',\n    itemWidth: 20,\n    itemHeight: 14,\n    itemSymbol: 'roundRect',\n    pieces: null,\n    categories: null,\n    splitNumber: 5,\n    selectedMode: 'multiple',\n    itemGap: 10,\n    hoverLink: true // Enable hover highlight.\n  });\n  return PiecewiseModel;\n}(VisualMapModel);\n;\n/**\r\n * Key is this._mode\r\n * @type {Object}\r\n * @this {module:echarts/component/viusalMap/PiecewiseMode}\r\n */\nvar resetMethods = {\n  splitNumber: function (outPieceList) {\n    var thisOption = this.option;\n    var precision = Math.min(thisOption.precision, 20);\n    var dataExtent = this.getExtent();\n    var splitNumber = thisOption.splitNumber;\n    splitNumber = Math.max(parseInt(splitNumber, 10), 1);\n    thisOption.splitNumber = splitNumber;\n    var splitStep = (dataExtent[1] - dataExtent[0]) / splitNumber;\n    // Precision auto-adaption\n    while (+splitStep.toFixed(precision) !== splitStep && precision < 5) {\n      precision++;\n    }\n    thisOption.precision = precision;\n    splitStep = +splitStep.toFixed(precision);\n    if (thisOption.minOpen) {\n      outPieceList.push({\n        interval: [-Infinity, dataExtent[0]],\n        close: [0, 0]\n      });\n    }\n    for (var index = 0, curr = dataExtent[0]; index < splitNumber; curr += splitStep, index++) {\n      var max = index === splitNumber - 1 ? dataExtent[1] : curr + splitStep;\n      outPieceList.push({\n        interval: [curr, max],\n        close: [1, 1]\n      });\n    }\n    if (thisOption.maxOpen) {\n      outPieceList.push({\n        interval: [dataExtent[1], Infinity],\n        close: [0, 0]\n      });\n    }\n    reformIntervals(outPieceList);\n    zrUtil.each(outPieceList, function (piece, index) {\n      piece.index = index;\n      piece.text = this.formatValueText(piece.interval);\n    }, this);\n  },\n  categories: function (outPieceList) {\n    var thisOption = this.option;\n    zrUtil.each(thisOption.categories, function (cate) {\n      // FIXME category模式也使用pieceList，但在visualMapping中不是使用pieceList。\n      // 是否改一致。\n      outPieceList.push({\n        text: this.formatValueText(cate, true),\n        value: cate\n      });\n    }, this);\n    // See \"Order Rule\".\n    normalizeReverse(thisOption, outPieceList);\n  },\n  pieces: function (outPieceList) {\n    var thisOption = this.option;\n    zrUtil.each(thisOption.pieces, function (pieceListItem, index) {\n      if (!zrUtil.isObject(pieceListItem)) {\n        pieceListItem = {\n          value: pieceListItem\n        };\n      }\n      var item = {\n        text: '',\n        index: index\n      };\n      if (pieceListItem.label != null) {\n        item.text = pieceListItem.label;\n      }\n      if (pieceListItem.hasOwnProperty('value')) {\n        var value = item.value = pieceListItem.value;\n        item.interval = [value, value];\n        item.close = [1, 1];\n      } else {\n        // `min` `max` is legacy option.\n        // `lt` `gt` `lte` `gte` is recommended.\n        var interval = item.interval = [];\n        var close_1 = item.close = [0, 0];\n        var closeList = [1, 0, 1];\n        var infinityList = [-Infinity, Infinity];\n        var useMinMax = [];\n        for (var lg = 0; lg < 2; lg++) {\n          var names = [['gte', 'gt', 'min'], ['lte', 'lt', 'max']][lg];\n          for (var i = 0; i < 3 && interval[lg] == null; i++) {\n            interval[lg] = pieceListItem[names[i]];\n            close_1[lg] = closeList[i];\n            useMinMax[lg] = i === 2;\n          }\n          interval[lg] == null && (interval[lg] = infinityList[lg]);\n        }\n        useMinMax[0] && interval[1] === Infinity && (close_1[0] = 0);\n        useMinMax[1] && interval[0] === -Infinity && (close_1[1] = 0);\n        if (process.env.NODE_ENV !== 'production') {\n          if (interval[0] > interval[1]) {\n            console.warn('Piece ' + index + 'is illegal: ' + interval + ' lower bound should not greater then uppper bound.');\n          }\n        }\n        if (interval[0] === interval[1] && close_1[0] && close_1[1]) {\n          // Consider: [{min: 5, max: 5, visual: {...}}, {min: 0, max: 5}],\n          // we use value to lift the priority when min === max\n          item.value = interval[0];\n        }\n      }\n      item.visual = VisualMapping.retrieveVisuals(pieceListItem);\n      outPieceList.push(item);\n    }, this);\n    // See \"Order Rule\".\n    normalizeReverse(thisOption, outPieceList);\n    // Only pieces\n    reformIntervals(outPieceList);\n    zrUtil.each(outPieceList, function (piece) {\n      var close = piece.close;\n      var edgeSymbols = [['<', '≤'][close[1]], ['>', '≥'][close[0]]];\n      piece.text = piece.text || this.formatValueText(piece.value != null ? piece.value : piece.interval, false, edgeSymbols);\n    }, this);\n  }\n};\nfunction normalizeReverse(thisOption, pieceList) {\n  var inverse = thisOption.inverse;\n  if (thisOption.orient === 'vertical' ? !inverse : inverse) {\n    pieceList.reverse();\n  }\n}\nexport default PiecewiseModel;", "map": {"version": 3, "names": ["__extends", "zrUtil", "VisualMapModel", "VisualMapping", "visualDefault", "reformIntervals", "inheritDefaultOption", "PiecewiseModel", "_super", "_this", "apply", "arguments", "type", "_pieceList", "prototype", "optionUpdated", "newOption", "isInit", "resetExtent", "mode", "_mode", "_determineMode", "resetMethods", "call", "_resetSelected", "categories", "option", "resetVisual", "mappingOption", "state", "mappingMethod", "clone", "dataExtent", "getExtent", "pieceList", "map", "piece", "visual", "completeVisualOption", "visualTypesInPieces", "visualTypes", "listVisualTypes", "isCategory", "each", "pieces", "visualType", "hasOwnProperty", "v", "exists", "stateList", "has", "target", "get", "obj", "thisOption", "selected", "index", "key", "getSelectedMapKey", "selectedMode", "hasSel_1", "getItemSymbol", "value", "getPieceList", "length", "setSelected", "getValueState", "findPieceIndex", "findTargetDataIndices", "pieceIndex", "result", "eachTargetSeries", "seriesModel", "dataIndices", "data", "getData", "getDataDimensionIndex", "dataIndex", "pIdx", "push", "seriesId", "id", "getRepresentValue", "representValue", "pieceInterval", "interval", "Infinity", "getVisualMeta", "getColorVisual", "stops", "outerColors", "visualMapModel", "setStop", "valueState", "color", "slice", "edge", "unshift", "curr", "defaultOption", "minOpen", "max<PERSON><PERSON>", "align", "itemWidth", "itemHeight", "itemSymbol", "splitNumber", "itemGap", "hoverLink", "outPieceList", "precision", "Math", "min", "max", "parseInt", "splitStep", "toFixed", "close", "text", "formatValueText", "cate", "normalizeReverse", "pieceListItem", "isObject", "item", "label", "close_1", "closeList", "infinityList", "useMinMax", "lg", "names", "i", "process", "env", "NODE_ENV", "console", "warn", "retrieveVisuals", "edgeSymbols", "inverse", "orient", "reverse"], "sources": ["E:/AI/SmartCV/node_modules/echarts/lib/component/visualMap/PiecewiseModel.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport VisualMapModel from './VisualMapModel.js';\nimport VisualMapping from '../../visual/VisualMapping.js';\nimport visualDefault from '../../visual/visualDefault.js';\nimport { reformIntervals } from '../../util/number.js';\nimport { inheritDefaultOption } from '../../util/component.js';\nvar PiecewiseModel = /** @class */function (_super) {\n  __extends(PiecewiseModel, _super);\n  function PiecewiseModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = PiecewiseModel.type;\n    /**\r\n     * The order is always [low, ..., high].\r\n     * [{text: string, interval: Array.<number>}, ...]\r\n     */\n    _this._pieceList = [];\n    return _this;\n  }\n  PiecewiseModel.prototype.optionUpdated = function (newOption, isInit) {\n    _super.prototype.optionUpdated.apply(this, arguments);\n    this.resetExtent();\n    var mode = this._mode = this._determineMode();\n    this._pieceList = [];\n    resetMethods[this._mode].call(this, this._pieceList);\n    this._resetSelected(newOption, isInit);\n    var categories = this.option.categories;\n    this.resetVisual(function (mappingOption, state) {\n      if (mode === 'categories') {\n        mappingOption.mappingMethod = 'category';\n        mappingOption.categories = zrUtil.clone(categories);\n      } else {\n        mappingOption.dataExtent = this.getExtent();\n        mappingOption.mappingMethod = 'piecewise';\n        mappingOption.pieceList = zrUtil.map(this._pieceList, function (piece) {\n          piece = zrUtil.clone(piece);\n          if (state !== 'inRange') {\n            // FIXME\n            // outOfRange do not support special visual in pieces.\n            piece.visual = null;\n          }\n          return piece;\n        });\n      }\n    });\n  };\n  /**\r\n   * @protected\r\n   * @override\r\n   */\n  PiecewiseModel.prototype.completeVisualOption = function () {\n    // Consider this case:\n    // visualMap: {\n    //      pieces: [{symbol: 'circle', lt: 0}, {symbol: 'rect', gte: 0}]\n    // }\n    // where no inRange/outOfRange set but only pieces. So we should make\n    // default inRange/outOfRange for this case, otherwise visuals that only\n    // appear in `pieces` will not be taken into account in visual encoding.\n    var option = this.option;\n    var visualTypesInPieces = {};\n    var visualTypes = VisualMapping.listVisualTypes();\n    var isCategory = this.isCategory();\n    zrUtil.each(option.pieces, function (piece) {\n      zrUtil.each(visualTypes, function (visualType) {\n        if (piece.hasOwnProperty(visualType)) {\n          visualTypesInPieces[visualType] = 1;\n        }\n      });\n    });\n    zrUtil.each(visualTypesInPieces, function (v, visualType) {\n      var exists = false;\n      zrUtil.each(this.stateList, function (state) {\n        exists = exists || has(option, state, visualType) || has(option.target, state, visualType);\n      }, this);\n      !exists && zrUtil.each(this.stateList, function (state) {\n        (option[state] || (option[state] = {}))[visualType] = visualDefault.get(visualType, state === 'inRange' ? 'active' : 'inactive', isCategory);\n      });\n    }, this);\n    function has(obj, state, visualType) {\n      return obj && obj[state] && obj[state].hasOwnProperty(visualType);\n    }\n    _super.prototype.completeVisualOption.apply(this, arguments);\n  };\n  PiecewiseModel.prototype._resetSelected = function (newOption, isInit) {\n    var thisOption = this.option;\n    var pieceList = this._pieceList;\n    // Selected do not merge but all override.\n    var selected = (isInit ? thisOption : newOption).selected || {};\n    thisOption.selected = selected;\n    // Consider 'not specified' means true.\n    zrUtil.each(pieceList, function (piece, index) {\n      var key = this.getSelectedMapKey(piece);\n      if (!selected.hasOwnProperty(key)) {\n        selected[key] = true;\n      }\n    }, this);\n    if (thisOption.selectedMode === 'single') {\n      // Ensure there is only one selected.\n      var hasSel_1 = false;\n      zrUtil.each(pieceList, function (piece, index) {\n        var key = this.getSelectedMapKey(piece);\n        if (selected[key]) {\n          hasSel_1 ? selected[key] = false : hasSel_1 = true;\n        }\n      }, this);\n    }\n    // thisOption.selectedMode === 'multiple', default: all selected.\n  };\n  /**\r\n   * @public\r\n   */\n  PiecewiseModel.prototype.getItemSymbol = function () {\n    return this.get('itemSymbol');\n  };\n  /**\r\n   * @public\r\n   */\n  PiecewiseModel.prototype.getSelectedMapKey = function (piece) {\n    return this._mode === 'categories' ? piece.value + '' : piece.index + '';\n  };\n  /**\r\n   * @public\r\n   */\n  PiecewiseModel.prototype.getPieceList = function () {\n    return this._pieceList;\n  };\n  /**\r\n   * @return {string}\r\n   */\n  PiecewiseModel.prototype._determineMode = function () {\n    var option = this.option;\n    return option.pieces && option.pieces.length > 0 ? 'pieces' : this.option.categories ? 'categories' : 'splitNumber';\n  };\n  /**\r\n   * @override\r\n   */\n  PiecewiseModel.prototype.setSelected = function (selected) {\n    this.option.selected = zrUtil.clone(selected);\n  };\n  /**\r\n   * @override\r\n   */\n  PiecewiseModel.prototype.getValueState = function (value) {\n    var index = VisualMapping.findPieceIndex(value, this._pieceList);\n    return index != null ? this.option.selected[this.getSelectedMapKey(this._pieceList[index])] ? 'inRange' : 'outOfRange' : 'outOfRange';\n  };\n  /**\r\n   * @public\r\n   * @param pieceIndex piece index in visualMapModel.getPieceList()\r\n   */\n  PiecewiseModel.prototype.findTargetDataIndices = function (pieceIndex) {\n    var result = [];\n    var pieceList = this._pieceList;\n    this.eachTargetSeries(function (seriesModel) {\n      var dataIndices = [];\n      var data = seriesModel.getData();\n      data.each(this.getDataDimensionIndex(data), function (value, dataIndex) {\n        // Should always base on model pieceList, because it is order sensitive.\n        var pIdx = VisualMapping.findPieceIndex(value, pieceList);\n        pIdx === pieceIndex && dataIndices.push(dataIndex);\n      }, this);\n      result.push({\n        seriesId: seriesModel.id,\n        dataIndex: dataIndices\n      });\n    }, this);\n    return result;\n  };\n  /**\r\n   * @private\r\n   * @param piece piece.value or piece.interval is required.\r\n   * @return  Can be Infinity or -Infinity\r\n   */\n  PiecewiseModel.prototype.getRepresentValue = function (piece) {\n    var representValue;\n    if (this.isCategory()) {\n      representValue = piece.value;\n    } else {\n      if (piece.value != null) {\n        representValue = piece.value;\n      } else {\n        var pieceInterval = piece.interval || [];\n        representValue = pieceInterval[0] === -Infinity && pieceInterval[1] === Infinity ? 0 : (pieceInterval[0] + pieceInterval[1]) / 2;\n      }\n    }\n    return representValue;\n  };\n  PiecewiseModel.prototype.getVisualMeta = function (getColorVisual) {\n    // Do not support category. (category axis is ordinal, numerical)\n    if (this.isCategory()) {\n      return;\n    }\n    var stops = [];\n    var outerColors = ['', ''];\n    var visualMapModel = this;\n    function setStop(interval, valueState) {\n      var representValue = visualMapModel.getRepresentValue({\n        interval: interval\n      }); // Not category\n      if (!valueState) {\n        valueState = visualMapModel.getValueState(representValue);\n      }\n      var color = getColorVisual(representValue, valueState);\n      if (interval[0] === -Infinity) {\n        outerColors[0] = color;\n      } else if (interval[1] === Infinity) {\n        outerColors[1] = color;\n      } else {\n        stops.push({\n          value: interval[0],\n          color: color\n        }, {\n          value: interval[1],\n          color: color\n        });\n      }\n    }\n    // Suplement\n    var pieceList = this._pieceList.slice();\n    if (!pieceList.length) {\n      pieceList.push({\n        interval: [-Infinity, Infinity]\n      });\n    } else {\n      var edge = pieceList[0].interval[0];\n      edge !== -Infinity && pieceList.unshift({\n        interval: [-Infinity, edge]\n      });\n      edge = pieceList[pieceList.length - 1].interval[1];\n      edge !== Infinity && pieceList.push({\n        interval: [edge, Infinity]\n      });\n    }\n    var curr = -Infinity;\n    zrUtil.each(pieceList, function (piece) {\n      var interval = piece.interval;\n      if (interval) {\n        // Fulfill gap.\n        interval[0] > curr && setStop([curr, interval[0]], 'outOfRange');\n        setStop(interval.slice());\n        curr = interval[1];\n      }\n    }, this);\n    return {\n      stops: stops,\n      outerColors: outerColors\n    };\n  };\n  PiecewiseModel.type = 'visualMap.piecewise';\n  PiecewiseModel.defaultOption = inheritDefaultOption(VisualMapModel.defaultOption, {\n    selected: null,\n    minOpen: false,\n    maxOpen: false,\n    align: 'auto',\n    itemWidth: 20,\n    itemHeight: 14,\n    itemSymbol: 'roundRect',\n    pieces: null,\n    categories: null,\n    splitNumber: 5,\n    selectedMode: 'multiple',\n    itemGap: 10,\n    hoverLink: true // Enable hover highlight.\n  });\n  return PiecewiseModel;\n}(VisualMapModel);\n;\n/**\r\n * Key is this._mode\r\n * @type {Object}\r\n * @this {module:echarts/component/viusalMap/PiecewiseMode}\r\n */\nvar resetMethods = {\n  splitNumber: function (outPieceList) {\n    var thisOption = this.option;\n    var precision = Math.min(thisOption.precision, 20);\n    var dataExtent = this.getExtent();\n    var splitNumber = thisOption.splitNumber;\n    splitNumber = Math.max(parseInt(splitNumber, 10), 1);\n    thisOption.splitNumber = splitNumber;\n    var splitStep = (dataExtent[1] - dataExtent[0]) / splitNumber;\n    // Precision auto-adaption\n    while (+splitStep.toFixed(precision) !== splitStep && precision < 5) {\n      precision++;\n    }\n    thisOption.precision = precision;\n    splitStep = +splitStep.toFixed(precision);\n    if (thisOption.minOpen) {\n      outPieceList.push({\n        interval: [-Infinity, dataExtent[0]],\n        close: [0, 0]\n      });\n    }\n    for (var index = 0, curr = dataExtent[0]; index < splitNumber; curr += splitStep, index++) {\n      var max = index === splitNumber - 1 ? dataExtent[1] : curr + splitStep;\n      outPieceList.push({\n        interval: [curr, max],\n        close: [1, 1]\n      });\n    }\n    if (thisOption.maxOpen) {\n      outPieceList.push({\n        interval: [dataExtent[1], Infinity],\n        close: [0, 0]\n      });\n    }\n    reformIntervals(outPieceList);\n    zrUtil.each(outPieceList, function (piece, index) {\n      piece.index = index;\n      piece.text = this.formatValueText(piece.interval);\n    }, this);\n  },\n  categories: function (outPieceList) {\n    var thisOption = this.option;\n    zrUtil.each(thisOption.categories, function (cate) {\n      // FIXME category模式也使用pieceList，但在visualMapping中不是使用pieceList。\n      // 是否改一致。\n      outPieceList.push({\n        text: this.formatValueText(cate, true),\n        value: cate\n      });\n    }, this);\n    // See \"Order Rule\".\n    normalizeReverse(thisOption, outPieceList);\n  },\n  pieces: function (outPieceList) {\n    var thisOption = this.option;\n    zrUtil.each(thisOption.pieces, function (pieceListItem, index) {\n      if (!zrUtil.isObject(pieceListItem)) {\n        pieceListItem = {\n          value: pieceListItem\n        };\n      }\n      var item = {\n        text: '',\n        index: index\n      };\n      if (pieceListItem.label != null) {\n        item.text = pieceListItem.label;\n      }\n      if (pieceListItem.hasOwnProperty('value')) {\n        var value = item.value = pieceListItem.value;\n        item.interval = [value, value];\n        item.close = [1, 1];\n      } else {\n        // `min` `max` is legacy option.\n        // `lt` `gt` `lte` `gte` is recommended.\n        var interval = item.interval = [];\n        var close_1 = item.close = [0, 0];\n        var closeList = [1, 0, 1];\n        var infinityList = [-Infinity, Infinity];\n        var useMinMax = [];\n        for (var lg = 0; lg < 2; lg++) {\n          var names = [['gte', 'gt', 'min'], ['lte', 'lt', 'max']][lg];\n          for (var i = 0; i < 3 && interval[lg] == null; i++) {\n            interval[lg] = pieceListItem[names[i]];\n            close_1[lg] = closeList[i];\n            useMinMax[lg] = i === 2;\n          }\n          interval[lg] == null && (interval[lg] = infinityList[lg]);\n        }\n        useMinMax[0] && interval[1] === Infinity && (close_1[0] = 0);\n        useMinMax[1] && interval[0] === -Infinity && (close_1[1] = 0);\n        if (process.env.NODE_ENV !== 'production') {\n          if (interval[0] > interval[1]) {\n            console.warn('Piece ' + index + 'is illegal: ' + interval + ' lower bound should not greater then uppper bound.');\n          }\n        }\n        if (interval[0] === interval[1] && close_1[0] && close_1[1]) {\n          // Consider: [{min: 5, max: 5, visual: {...}}, {min: 0, max: 5}],\n          // we use value to lift the priority when min === max\n          item.value = interval[0];\n        }\n      }\n      item.visual = VisualMapping.retrieveVisuals(pieceListItem);\n      outPieceList.push(item);\n    }, this);\n    // See \"Order Rule\".\n    normalizeReverse(thisOption, outPieceList);\n    // Only pieces\n    reformIntervals(outPieceList);\n    zrUtil.each(outPieceList, function (piece) {\n      var close = piece.close;\n      var edgeSymbols = [['<', '≤'][close[1]], ['>', '≥'][close[0]]];\n      piece.text = piece.text || this.formatValueText(piece.value != null ? piece.value : piece.interval, false, edgeSymbols);\n    }, this);\n  }\n};\nfunction normalizeReverse(thisOption, pieceList) {\n  var inverse = thisOption.inverse;\n  if (thisOption.orient === 'vertical' ? !inverse : inverse) {\n    pieceList.reverse();\n  }\n}\nexport default PiecewiseModel;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,OAAOC,cAAc,MAAM,qBAAqB;AAChD,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,aAAa,MAAM,+BAA+B;AACzD,SAASC,eAAe,QAAQ,sBAAsB;AACtD,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,IAAIC,cAAc,GAAG,aAAa,UAAUC,MAAM,EAAE;EAClDR,SAAS,CAACO,cAAc,EAAEC,MAAM,CAAC;EACjC,SAASD,cAAcA,CAAA,EAAG;IACxB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,cAAc,CAACK,IAAI;IAChC;AACJ;AACA;AACA;IACIH,KAAK,CAACI,UAAU,GAAG,EAAE;IACrB,OAAOJ,KAAK;EACd;EACAF,cAAc,CAACO,SAAS,CAACC,aAAa,GAAG,UAAUC,SAAS,EAAEC,MAAM,EAAE;IACpET,MAAM,CAACM,SAAS,CAACC,aAAa,CAACL,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IACrD,IAAI,CAACO,WAAW,CAAC,CAAC;IAClB,IAAIC,IAAI,GAAG,IAAI,CAACC,KAAK,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IAC7C,IAAI,CAACR,UAAU,GAAG,EAAE;IACpBS,YAAY,CAAC,IAAI,CAACF,KAAK,CAAC,CAACG,IAAI,CAAC,IAAI,EAAE,IAAI,CAACV,UAAU,CAAC;IACpD,IAAI,CAACW,cAAc,CAACR,SAAS,EAAEC,MAAM,CAAC;IACtC,IAAIQ,UAAU,GAAG,IAAI,CAACC,MAAM,CAACD,UAAU;IACvC,IAAI,CAACE,WAAW,CAAC,UAAUC,aAAa,EAAEC,KAAK,EAAE;MAC/C,IAAIV,IAAI,KAAK,YAAY,EAAE;QACzBS,aAAa,CAACE,aAAa,GAAG,UAAU;QACxCF,aAAa,CAACH,UAAU,GAAGxB,MAAM,CAAC8B,KAAK,CAACN,UAAU,CAAC;MACrD,CAAC,MAAM;QACLG,aAAa,CAACI,UAAU,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC;QAC3CL,aAAa,CAACE,aAAa,GAAG,WAAW;QACzCF,aAAa,CAACM,SAAS,GAAGjC,MAAM,CAACkC,GAAG,CAAC,IAAI,CAACtB,UAAU,EAAE,UAAUuB,KAAK,EAAE;UACrEA,KAAK,GAAGnC,MAAM,CAAC8B,KAAK,CAACK,KAAK,CAAC;UAC3B,IAAIP,KAAK,KAAK,SAAS,EAAE;YACvB;YACA;YACAO,KAAK,CAACC,MAAM,GAAG,IAAI;UACrB;UACA,OAAOD,KAAK;QACd,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ,CAAC;EACD;AACF;AACA;AACA;EACE7B,cAAc,CAACO,SAAS,CAACwB,oBAAoB,GAAG,YAAY;IAC1D;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIZ,MAAM,GAAG,IAAI,CAACA,MAAM;IACxB,IAAIa,mBAAmB,GAAG,CAAC,CAAC;IAC5B,IAAIC,WAAW,GAAGrC,aAAa,CAACsC,eAAe,CAAC,CAAC;IACjD,IAAIC,UAAU,GAAG,IAAI,CAACA,UAAU,CAAC,CAAC;IAClCzC,MAAM,CAAC0C,IAAI,CAACjB,MAAM,CAACkB,MAAM,EAAE,UAAUR,KAAK,EAAE;MAC1CnC,MAAM,CAAC0C,IAAI,CAACH,WAAW,EAAE,UAAUK,UAAU,EAAE;QAC7C,IAAIT,KAAK,CAACU,cAAc,CAACD,UAAU,CAAC,EAAE;UACpCN,mBAAmB,CAACM,UAAU,CAAC,GAAG,CAAC;QACrC;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IACF5C,MAAM,CAAC0C,IAAI,CAACJ,mBAAmB,EAAE,UAAUQ,CAAC,EAAEF,UAAU,EAAE;MACxD,IAAIG,MAAM,GAAG,KAAK;MAClB/C,MAAM,CAAC0C,IAAI,CAAC,IAAI,CAACM,SAAS,EAAE,UAAUpB,KAAK,EAAE;QAC3CmB,MAAM,GAAGA,MAAM,IAAIE,GAAG,CAACxB,MAAM,EAAEG,KAAK,EAAEgB,UAAU,CAAC,IAAIK,GAAG,CAACxB,MAAM,CAACyB,MAAM,EAAEtB,KAAK,EAAEgB,UAAU,CAAC;MAC5F,CAAC,EAAE,IAAI,CAAC;MACR,CAACG,MAAM,IAAI/C,MAAM,CAAC0C,IAAI,CAAC,IAAI,CAACM,SAAS,EAAE,UAAUpB,KAAK,EAAE;QACtD,CAACH,MAAM,CAACG,KAAK,CAAC,KAAKH,MAAM,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAEgB,UAAU,CAAC,GAAGzC,aAAa,CAACgD,GAAG,CAACP,UAAU,EAAEhB,KAAK,KAAK,SAAS,GAAG,QAAQ,GAAG,UAAU,EAAEa,UAAU,CAAC;MAC9I,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;IACR,SAASQ,GAAGA,CAACG,GAAG,EAAExB,KAAK,EAAEgB,UAAU,EAAE;MACnC,OAAOQ,GAAG,IAAIA,GAAG,CAACxB,KAAK,CAAC,IAAIwB,GAAG,CAACxB,KAAK,CAAC,CAACiB,cAAc,CAACD,UAAU,CAAC;IACnE;IACArC,MAAM,CAACM,SAAS,CAACwB,oBAAoB,CAAC5B,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EAC9D,CAAC;EACDJ,cAAc,CAACO,SAAS,CAACU,cAAc,GAAG,UAAUR,SAAS,EAAEC,MAAM,EAAE;IACrE,IAAIqC,UAAU,GAAG,IAAI,CAAC5B,MAAM;IAC5B,IAAIQ,SAAS,GAAG,IAAI,CAACrB,UAAU;IAC/B;IACA,IAAI0C,QAAQ,GAAG,CAACtC,MAAM,GAAGqC,UAAU,GAAGtC,SAAS,EAAEuC,QAAQ,IAAI,CAAC,CAAC;IAC/DD,UAAU,CAACC,QAAQ,GAAGA,QAAQ;IAC9B;IACAtD,MAAM,CAAC0C,IAAI,CAACT,SAAS,EAAE,UAAUE,KAAK,EAAEoB,KAAK,EAAE;MAC7C,IAAIC,GAAG,GAAG,IAAI,CAACC,iBAAiB,CAACtB,KAAK,CAAC;MACvC,IAAI,CAACmB,QAAQ,CAACT,cAAc,CAACW,GAAG,CAAC,EAAE;QACjCF,QAAQ,CAACE,GAAG,CAAC,GAAG,IAAI;MACtB;IACF,CAAC,EAAE,IAAI,CAAC;IACR,IAAIH,UAAU,CAACK,YAAY,KAAK,QAAQ,EAAE;MACxC;MACA,IAAIC,QAAQ,GAAG,KAAK;MACpB3D,MAAM,CAAC0C,IAAI,CAACT,SAAS,EAAE,UAAUE,KAAK,EAAEoB,KAAK,EAAE;QAC7C,IAAIC,GAAG,GAAG,IAAI,CAACC,iBAAiB,CAACtB,KAAK,CAAC;QACvC,IAAImB,QAAQ,CAACE,GAAG,CAAC,EAAE;UACjBG,QAAQ,GAAGL,QAAQ,CAACE,GAAG,CAAC,GAAG,KAAK,GAAGG,QAAQ,GAAG,IAAI;QACpD;MACF,CAAC,EAAE,IAAI,CAAC;IACV;IACA;EACF,CAAC;EACD;AACF;AACA;EACErD,cAAc,CAACO,SAAS,CAAC+C,aAAa,GAAG,YAAY;IACnD,OAAO,IAAI,CAACT,GAAG,CAAC,YAAY,CAAC;EAC/B,CAAC;EACD;AACF;AACA;EACE7C,cAAc,CAACO,SAAS,CAAC4C,iBAAiB,GAAG,UAAUtB,KAAK,EAAE;IAC5D,OAAO,IAAI,CAAChB,KAAK,KAAK,YAAY,GAAGgB,KAAK,CAAC0B,KAAK,GAAG,EAAE,GAAG1B,KAAK,CAACoB,KAAK,GAAG,EAAE;EAC1E,CAAC;EACD;AACF;AACA;EACEjD,cAAc,CAACO,SAAS,CAACiD,YAAY,GAAG,YAAY;IAClD,OAAO,IAAI,CAAClD,UAAU;EACxB,CAAC;EACD;AACF;AACA;EACEN,cAAc,CAACO,SAAS,CAACO,cAAc,GAAG,YAAY;IACpD,IAAIK,MAAM,GAAG,IAAI,CAACA,MAAM;IACxB,OAAOA,MAAM,CAACkB,MAAM,IAAIlB,MAAM,CAACkB,MAAM,CAACoB,MAAM,GAAG,CAAC,GAAG,QAAQ,GAAG,IAAI,CAACtC,MAAM,CAACD,UAAU,GAAG,YAAY,GAAG,aAAa;EACrH,CAAC;EACD;AACF;AACA;EACElB,cAAc,CAACO,SAAS,CAACmD,WAAW,GAAG,UAAUV,QAAQ,EAAE;IACzD,IAAI,CAAC7B,MAAM,CAAC6B,QAAQ,GAAGtD,MAAM,CAAC8B,KAAK,CAACwB,QAAQ,CAAC;EAC/C,CAAC;EACD;AACF;AACA;EACEhD,cAAc,CAACO,SAAS,CAACoD,aAAa,GAAG,UAAUJ,KAAK,EAAE;IACxD,IAAIN,KAAK,GAAGrD,aAAa,CAACgE,cAAc,CAACL,KAAK,EAAE,IAAI,CAACjD,UAAU,CAAC;IAChE,OAAO2C,KAAK,IAAI,IAAI,GAAG,IAAI,CAAC9B,MAAM,CAAC6B,QAAQ,CAAC,IAAI,CAACG,iBAAiB,CAAC,IAAI,CAAC7C,UAAU,CAAC2C,KAAK,CAAC,CAAC,CAAC,GAAG,SAAS,GAAG,YAAY,GAAG,YAAY;EACvI,CAAC;EACD;AACF;AACA;AACA;EACEjD,cAAc,CAACO,SAAS,CAACsD,qBAAqB,GAAG,UAAUC,UAAU,EAAE;IACrE,IAAIC,MAAM,GAAG,EAAE;IACf,IAAIpC,SAAS,GAAG,IAAI,CAACrB,UAAU;IAC/B,IAAI,CAAC0D,gBAAgB,CAAC,UAAUC,WAAW,EAAE;MAC3C,IAAIC,WAAW,GAAG,EAAE;MACpB,IAAIC,IAAI,GAAGF,WAAW,CAACG,OAAO,CAAC,CAAC;MAChCD,IAAI,CAAC/B,IAAI,CAAC,IAAI,CAACiC,qBAAqB,CAACF,IAAI,CAAC,EAAE,UAAUZ,KAAK,EAAEe,SAAS,EAAE;QACtE;QACA,IAAIC,IAAI,GAAG3E,aAAa,CAACgE,cAAc,CAACL,KAAK,EAAE5B,SAAS,CAAC;QACzD4C,IAAI,KAAKT,UAAU,IAAII,WAAW,CAACM,IAAI,CAACF,SAAS,CAAC;MACpD,CAAC,EAAE,IAAI,CAAC;MACRP,MAAM,CAACS,IAAI,CAAC;QACVC,QAAQ,EAAER,WAAW,CAACS,EAAE;QACxBJ,SAAS,EAAEJ;MACb,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;IACR,OAAOH,MAAM;EACf,CAAC;EACD;AACF;AACA;AACA;AACA;EACE/D,cAAc,CAACO,SAAS,CAACoE,iBAAiB,GAAG,UAAU9C,KAAK,EAAE;IAC5D,IAAI+C,cAAc;IAClB,IAAI,IAAI,CAACzC,UAAU,CAAC,CAAC,EAAE;MACrByC,cAAc,GAAG/C,KAAK,CAAC0B,KAAK;IAC9B,CAAC,MAAM;MACL,IAAI1B,KAAK,CAAC0B,KAAK,IAAI,IAAI,EAAE;QACvBqB,cAAc,GAAG/C,KAAK,CAAC0B,KAAK;MAC9B,CAAC,MAAM;QACL,IAAIsB,aAAa,GAAGhD,KAAK,CAACiD,QAAQ,IAAI,EAAE;QACxCF,cAAc,GAAGC,aAAa,CAAC,CAAC,CAAC,KAAK,CAACE,QAAQ,IAAIF,aAAa,CAAC,CAAC,CAAC,KAAKE,QAAQ,GAAG,CAAC,GAAG,CAACF,aAAa,CAAC,CAAC,CAAC,GAAGA,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC;MAClI;IACF;IACA,OAAOD,cAAc;EACvB,CAAC;EACD5E,cAAc,CAACO,SAAS,CAACyE,aAAa,GAAG,UAAUC,cAAc,EAAE;IACjE;IACA,IAAI,IAAI,CAAC9C,UAAU,CAAC,CAAC,EAAE;MACrB;IACF;IACA,IAAI+C,KAAK,GAAG,EAAE;IACd,IAAIC,WAAW,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC;IAC1B,IAAIC,cAAc,GAAG,IAAI;IACzB,SAASC,OAAOA,CAACP,QAAQ,EAAEQ,UAAU,EAAE;MACrC,IAAIV,cAAc,GAAGQ,cAAc,CAACT,iBAAiB,CAAC;QACpDG,QAAQ,EAAEA;MACZ,CAAC,CAAC,CAAC,CAAC;MACJ,IAAI,CAACQ,UAAU,EAAE;QACfA,UAAU,GAAGF,cAAc,CAACzB,aAAa,CAACiB,cAAc,CAAC;MAC3D;MACA,IAAIW,KAAK,GAAGN,cAAc,CAACL,cAAc,EAAEU,UAAU,CAAC;MACtD,IAAIR,QAAQ,CAAC,CAAC,CAAC,KAAK,CAACC,QAAQ,EAAE;QAC7BI,WAAW,CAAC,CAAC,CAAC,GAAGI,KAAK;MACxB,CAAC,MAAM,IAAIT,QAAQ,CAAC,CAAC,CAAC,KAAKC,QAAQ,EAAE;QACnCI,WAAW,CAAC,CAAC,CAAC,GAAGI,KAAK;MACxB,CAAC,MAAM;QACLL,KAAK,CAACV,IAAI,CAAC;UACTjB,KAAK,EAAEuB,QAAQ,CAAC,CAAC,CAAC;UAClBS,KAAK,EAAEA;QACT,CAAC,EAAE;UACDhC,KAAK,EAAEuB,QAAQ,CAAC,CAAC,CAAC;UAClBS,KAAK,EAAEA;QACT,CAAC,CAAC;MACJ;IACF;IACA;IACA,IAAI5D,SAAS,GAAG,IAAI,CAACrB,UAAU,CAACkF,KAAK,CAAC,CAAC;IACvC,IAAI,CAAC7D,SAAS,CAAC8B,MAAM,EAAE;MACrB9B,SAAS,CAAC6C,IAAI,CAAC;QACbM,QAAQ,EAAE,CAAC,CAACC,QAAQ,EAAEA,QAAQ;MAChC,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAIU,IAAI,GAAG9D,SAAS,CAAC,CAAC,CAAC,CAACmD,QAAQ,CAAC,CAAC,CAAC;MACnCW,IAAI,KAAK,CAACV,QAAQ,IAAIpD,SAAS,CAAC+D,OAAO,CAAC;QACtCZ,QAAQ,EAAE,CAAC,CAACC,QAAQ,EAAEU,IAAI;MAC5B,CAAC,CAAC;MACFA,IAAI,GAAG9D,SAAS,CAACA,SAAS,CAAC8B,MAAM,GAAG,CAAC,CAAC,CAACqB,QAAQ,CAAC,CAAC,CAAC;MAClDW,IAAI,KAAKV,QAAQ,IAAIpD,SAAS,CAAC6C,IAAI,CAAC;QAClCM,QAAQ,EAAE,CAACW,IAAI,EAAEV,QAAQ;MAC3B,CAAC,CAAC;IACJ;IACA,IAAIY,IAAI,GAAG,CAACZ,QAAQ;IACpBrF,MAAM,CAAC0C,IAAI,CAACT,SAAS,EAAE,UAAUE,KAAK,EAAE;MACtC,IAAIiD,QAAQ,GAAGjD,KAAK,CAACiD,QAAQ;MAC7B,IAAIA,QAAQ,EAAE;QACZ;QACAA,QAAQ,CAAC,CAAC,CAAC,GAAGa,IAAI,IAAIN,OAAO,CAAC,CAACM,IAAI,EAAEb,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC;QAChEO,OAAO,CAACP,QAAQ,CAACU,KAAK,CAAC,CAAC,CAAC;QACzBG,IAAI,GAAGb,QAAQ,CAAC,CAAC,CAAC;MACpB;IACF,CAAC,EAAE,IAAI,CAAC;IACR,OAAO;MACLI,KAAK,EAAEA,KAAK;MACZC,WAAW,EAAEA;IACf,CAAC;EACH,CAAC;EACDnF,cAAc,CAACK,IAAI,GAAG,qBAAqB;EAC3CL,cAAc,CAAC4F,aAAa,GAAG7F,oBAAoB,CAACJ,cAAc,CAACiG,aAAa,EAAE;IAChF5C,QAAQ,EAAE,IAAI;IACd6C,OAAO,EAAE,KAAK;IACdC,OAAO,EAAE,KAAK;IACdC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,EAAE;IACbC,UAAU,EAAE,EAAE;IACdC,UAAU,EAAE,WAAW;IACvB7D,MAAM,EAAE,IAAI;IACZnB,UAAU,EAAE,IAAI;IAChBiF,WAAW,EAAE,CAAC;IACd/C,YAAY,EAAE,UAAU;IACxBgD,OAAO,EAAE,EAAE;IACXC,SAAS,EAAE,IAAI,CAAC;EAClB,CAAC,CAAC;EACF,OAAOrG,cAAc;AACvB,CAAC,CAACL,cAAc,CAAC;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,IAAIoB,YAAY,GAAG;EACjBoF,WAAW,EAAE,SAAAA,CAAUG,YAAY,EAAE;IACnC,IAAIvD,UAAU,GAAG,IAAI,CAAC5B,MAAM;IAC5B,IAAIoF,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC1D,UAAU,CAACwD,SAAS,EAAE,EAAE,CAAC;IAClD,IAAI9E,UAAU,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC;IACjC,IAAIyE,WAAW,GAAGpD,UAAU,CAACoD,WAAW;IACxCA,WAAW,GAAGK,IAAI,CAACE,GAAG,CAACC,QAAQ,CAACR,WAAW,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;IACpDpD,UAAU,CAACoD,WAAW,GAAGA,WAAW;IACpC,IAAIS,SAAS,GAAG,CAACnF,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,IAAI0E,WAAW;IAC7D;IACA,OAAO,CAACS,SAAS,CAACC,OAAO,CAACN,SAAS,CAAC,KAAKK,SAAS,IAAIL,SAAS,GAAG,CAAC,EAAE;MACnEA,SAAS,EAAE;IACb;IACAxD,UAAU,CAACwD,SAAS,GAAGA,SAAS;IAChCK,SAAS,GAAG,CAACA,SAAS,CAACC,OAAO,CAACN,SAAS,CAAC;IACzC,IAAIxD,UAAU,CAAC8C,OAAO,EAAE;MACtBS,YAAY,CAAC9B,IAAI,CAAC;QAChBM,QAAQ,EAAE,CAAC,CAACC,QAAQ,EAAEtD,UAAU,CAAC,CAAC,CAAC,CAAC;QACpCqF,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC;MACd,CAAC,CAAC;IACJ;IACA,KAAK,IAAI7D,KAAK,GAAG,CAAC,EAAE0C,IAAI,GAAGlE,UAAU,CAAC,CAAC,CAAC,EAAEwB,KAAK,GAAGkD,WAAW,EAAER,IAAI,IAAIiB,SAAS,EAAE3D,KAAK,EAAE,EAAE;MACzF,IAAIyD,GAAG,GAAGzD,KAAK,KAAKkD,WAAW,GAAG,CAAC,GAAG1E,UAAU,CAAC,CAAC,CAAC,GAAGkE,IAAI,GAAGiB,SAAS;MACtEN,YAAY,CAAC9B,IAAI,CAAC;QAChBM,QAAQ,EAAE,CAACa,IAAI,EAAEe,GAAG,CAAC;QACrBI,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC;MACd,CAAC,CAAC;IACJ;IACA,IAAI/D,UAAU,CAAC+C,OAAO,EAAE;MACtBQ,YAAY,CAAC9B,IAAI,CAAC;QAChBM,QAAQ,EAAE,CAACrD,UAAU,CAAC,CAAC,CAAC,EAAEsD,QAAQ,CAAC;QACnC+B,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC;MACd,CAAC,CAAC;IACJ;IACAhH,eAAe,CAACwG,YAAY,CAAC;IAC7B5G,MAAM,CAAC0C,IAAI,CAACkE,YAAY,EAAE,UAAUzE,KAAK,EAAEoB,KAAK,EAAE;MAChDpB,KAAK,CAACoB,KAAK,GAAGA,KAAK;MACnBpB,KAAK,CAACkF,IAAI,GAAG,IAAI,CAACC,eAAe,CAACnF,KAAK,CAACiD,QAAQ,CAAC;IACnD,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EACD5D,UAAU,EAAE,SAAAA,CAAUoF,YAAY,EAAE;IAClC,IAAIvD,UAAU,GAAG,IAAI,CAAC5B,MAAM;IAC5BzB,MAAM,CAAC0C,IAAI,CAACW,UAAU,CAAC7B,UAAU,EAAE,UAAU+F,IAAI,EAAE;MACjD;MACA;MACAX,YAAY,CAAC9B,IAAI,CAAC;QAChBuC,IAAI,EAAE,IAAI,CAACC,eAAe,CAACC,IAAI,EAAE,IAAI,CAAC;QACtC1D,KAAK,EAAE0D;MACT,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;IACR;IACAC,gBAAgB,CAACnE,UAAU,EAAEuD,YAAY,CAAC;EAC5C,CAAC;EACDjE,MAAM,EAAE,SAAAA,CAAUiE,YAAY,EAAE;IAC9B,IAAIvD,UAAU,GAAG,IAAI,CAAC5B,MAAM;IAC5BzB,MAAM,CAAC0C,IAAI,CAACW,UAAU,CAACV,MAAM,EAAE,UAAU8E,aAAa,EAAElE,KAAK,EAAE;MAC7D,IAAI,CAACvD,MAAM,CAAC0H,QAAQ,CAACD,aAAa,CAAC,EAAE;QACnCA,aAAa,GAAG;UACd5D,KAAK,EAAE4D;QACT,CAAC;MACH;MACA,IAAIE,IAAI,GAAG;QACTN,IAAI,EAAE,EAAE;QACR9D,KAAK,EAAEA;MACT,CAAC;MACD,IAAIkE,aAAa,CAACG,KAAK,IAAI,IAAI,EAAE;QAC/BD,IAAI,CAACN,IAAI,GAAGI,aAAa,CAACG,KAAK;MACjC;MACA,IAAIH,aAAa,CAAC5E,cAAc,CAAC,OAAO,CAAC,EAAE;QACzC,IAAIgB,KAAK,GAAG8D,IAAI,CAAC9D,KAAK,GAAG4D,aAAa,CAAC5D,KAAK;QAC5C8D,IAAI,CAACvC,QAAQ,GAAG,CAACvB,KAAK,EAAEA,KAAK,CAAC;QAC9B8D,IAAI,CAACP,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;MACrB,CAAC,MAAM;QACL;QACA;QACA,IAAIhC,QAAQ,GAAGuC,IAAI,CAACvC,QAAQ,GAAG,EAAE;QACjC,IAAIyC,OAAO,GAAGF,IAAI,CAACP,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;QACjC,IAAIU,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACzB,IAAIC,YAAY,GAAG,CAAC,CAAC1C,QAAQ,EAAEA,QAAQ,CAAC;QACxC,IAAI2C,SAAS,GAAG,EAAE;QAClB,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG,CAAC,EAAEA,EAAE,EAAE,EAAE;UAC7B,IAAIC,KAAK,GAAG,CAAC,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,CAACD,EAAE,CAAC;UAC5D,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,IAAI/C,QAAQ,CAAC6C,EAAE,CAAC,IAAI,IAAI,EAAEE,CAAC,EAAE,EAAE;YAClD/C,QAAQ,CAAC6C,EAAE,CAAC,GAAGR,aAAa,CAACS,KAAK,CAACC,CAAC,CAAC,CAAC;YACtCN,OAAO,CAACI,EAAE,CAAC,GAAGH,SAAS,CAACK,CAAC,CAAC;YAC1BH,SAAS,CAACC,EAAE,CAAC,GAAGE,CAAC,KAAK,CAAC;UACzB;UACA/C,QAAQ,CAAC6C,EAAE,CAAC,IAAI,IAAI,KAAK7C,QAAQ,CAAC6C,EAAE,CAAC,GAAGF,YAAY,CAACE,EAAE,CAAC,CAAC;QAC3D;QACAD,SAAS,CAAC,CAAC,CAAC,IAAI5C,QAAQ,CAAC,CAAC,CAAC,KAAKC,QAAQ,KAAKwC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC5DG,SAAS,CAAC,CAAC,CAAC,IAAI5C,QAAQ,CAAC,CAAC,CAAC,KAAK,CAACC,QAAQ,KAAKwC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC7D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzC,IAAIlD,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC,EAAE;YAC7BmD,OAAO,CAACC,IAAI,CAAC,QAAQ,GAAGjF,KAAK,GAAG,cAAc,GAAG6B,QAAQ,GAAG,oDAAoD,CAAC;UACnH;QACF;QACA,IAAIA,QAAQ,CAAC,CAAC,CAAC,KAAKA,QAAQ,CAAC,CAAC,CAAC,IAAIyC,OAAO,CAAC,CAAC,CAAC,IAAIA,OAAO,CAAC,CAAC,CAAC,EAAE;UAC3D;UACA;UACAF,IAAI,CAAC9D,KAAK,GAAGuB,QAAQ,CAAC,CAAC,CAAC;QAC1B;MACF;MACAuC,IAAI,CAACvF,MAAM,GAAGlC,aAAa,CAACuI,eAAe,CAAChB,aAAa,CAAC;MAC1Db,YAAY,CAAC9B,IAAI,CAAC6C,IAAI,CAAC;IACzB,CAAC,EAAE,IAAI,CAAC;IACR;IACAH,gBAAgB,CAACnE,UAAU,EAAEuD,YAAY,CAAC;IAC1C;IACAxG,eAAe,CAACwG,YAAY,CAAC;IAC7B5G,MAAM,CAAC0C,IAAI,CAACkE,YAAY,EAAE,UAAUzE,KAAK,EAAE;MACzC,IAAIiF,KAAK,GAAGjF,KAAK,CAACiF,KAAK;MACvB,IAAIsB,WAAW,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAACtB,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAACA,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DjF,KAAK,CAACkF,IAAI,GAAGlF,KAAK,CAACkF,IAAI,IAAI,IAAI,CAACC,eAAe,CAACnF,KAAK,CAAC0B,KAAK,IAAI,IAAI,GAAG1B,KAAK,CAAC0B,KAAK,GAAG1B,KAAK,CAACiD,QAAQ,EAAE,KAAK,EAAEsD,WAAW,CAAC;IACzH,CAAC,EAAE,IAAI,CAAC;EACV;AACF,CAAC;AACD,SAASlB,gBAAgBA,CAACnE,UAAU,EAAEpB,SAAS,EAAE;EAC/C,IAAI0G,OAAO,GAAGtF,UAAU,CAACsF,OAAO;EAChC,IAAItF,UAAU,CAACuF,MAAM,KAAK,UAAU,GAAG,CAACD,OAAO,GAAGA,OAAO,EAAE;IACzD1G,SAAS,CAAC4G,OAAO,CAAC,CAAC;EACrB;AACF;AACA,eAAevI,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}