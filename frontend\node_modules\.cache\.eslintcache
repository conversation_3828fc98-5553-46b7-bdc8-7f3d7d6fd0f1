[{"E:\\AI\\SmartCV\\frontend\\src\\index.js": "1", "E:\\AI\\SmartCV\\frontend\\src\\App.js": "2", "E:\\AI\\SmartCV\\frontend\\src\\components\\JobDescriptionInput.js": "3", "E:\\AI\\SmartCV\\frontend\\src\\components\\LoadingSpinner.js": "4", "E:\\AI\\SmartCV\\frontend\\src\\components\\ResultDisplay.js": "5", "E:\\AI\\SmartCV\\frontend\\src\\components\\FileUpload.js": "6", "E:\\AI\\SmartCV\\frontend\\src\\services\\api.js": "7", "E:\\AI\\SmartCV\\frontend\\src\\utils\\exportUtils.js": "8", "E:\\AI\\SmartCV\\frontend\\src\\components\\DocumentPreview.js": "9", "E:\\AI\\SmartCV\\frontend\\src\\components\\PDFViewer.js": "10", "E:\\AI\\SmartCV\\frontend\\src\\services\\ocrService.js": "11"}, {"size": 264, "mtime": 1748859416987, "results": "12", "hashOfConfig": "13"}, {"size": 23751, "mtime": 1748959823620, "results": "14", "hashOfConfig": "13"}, {"size": 3585, "mtime": 1748864007360, "results": "15", "hashOfConfig": "13"}, {"size": 4593, "mtime": 1748861711717, "results": "16", "hashOfConfig": "13"}, {"size": 65373, "mtime": 1748990353231, "results": "17", "hashOfConfig": "13"}, {"size": 17948, "mtime": 1748894515308, "results": "18", "hashOfConfig": "13"}, {"size": 6304, "mtime": 1748942669041, "results": "19", "hashOfConfig": "13"}, {"size": 5014, "mtime": 1748871922819, "results": "20", "hashOfConfig": "13"}, {"size": 14380, "mtime": 1748898377610, "results": "21", "hashOfConfig": "13"}, {"size": 4312, "mtime": 1748872859544, "results": "22", "hashOfConfig": "13"}, {"size": 2056, "mtime": 1748874639715, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "vnfw6u", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 46, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\AI\\SmartCV\\frontend\\src\\index.js", [], [], "E:\\AI\\SmartCV\\frontend\\src\\App.js", ["57", "58", "59", "60"], [], "E:\\AI\\SmartCV\\frontend\\src\\components\\JobDescriptionInput.js", [], [], "E:\\AI\\SmartCV\\frontend\\src\\components\\LoadingSpinner.js", [], [], "E:\\AI\\SmartCV\\frontend\\src\\components\\ResultDisplay.js", ["61", "62", "63", "64", "65", "66", "67", "68", "69", "70", "71", "72", "73", "74", "75", "76", "77", "78", "79", "80", "81", "82", "83", "84", "85", "86", "87", "88", "89", "90", "91", "92", "93", "94", "95", "96", "97", "98", "99", "100", "101", "102", "103", "104", "105", "106"], [], "E:\\AI\\SmartCV\\frontend\\src\\components\\FileUpload.js", [], [], "E:\\AI\\SmartCV\\frontend\\src\\services\\api.js", [], [], "E:\\AI\\SmartCV\\frontend\\src\\utils\\exportUtils.js", ["107"], [], "E:\\AI\\SmartCV\\frontend\\src\\components\\DocumentPreview.js", ["108"], [], "E:\\AI\\SmartCV\\frontend\\src\\components\\PDFViewer.js", [], [], "E:\\AI\\SmartCV\\frontend\\src\\services\\ocrService.js", [], [], {"ruleId": "109", "severity": 1, "message": "110", "line": 7, "column": 26, "nodeType": "111", "messageId": "112", "endLine": 7, "endColumn": 54}, {"ruleId": "109", "severity": 1, "message": "113", "line": 7, "column": 56, "nodeType": "111", "messageId": "112", "endLine": 7, "endColumn": 77}, {"ruleId": "114", "severity": 1, "message": "115", "line": 42, "column": 6, "nodeType": "116", "endLine": 42, "endColumn": 29, "suggestions": "117"}, {"ruleId": "109", "severity": 1, "message": "118", "line": 154, "column": 9, "nodeType": "111", "messageId": "112", "endLine": 154, "endColumn": 19}, {"ruleId": "109", "severity": 1, "message": "119", "line": 2, "column": 37, "nodeType": "111", "messageId": "112", "endLine": 2, "endColumn": 49}, {"ruleId": "109", "severity": 1, "message": "113", "line": 3, "column": 10, "nodeType": "111", "messageId": "112", "endLine": 3, "endColumn": 31}, {"ruleId": "109", "severity": 1, "message": "120", "line": 4, "column": 8, "nodeType": "111", "messageId": "112", "endLine": 4, "endColumn": 23}, {"ruleId": "109", "severity": 1, "message": "121", "line": 8, "column": 10, "nodeType": "111", "messageId": "112", "endLine": 8, "endColumn": 24}, {"ruleId": "109", "severity": 1, "message": "122", "line": 8, "column": 26, "nodeType": "111", "messageId": "112", "endLine": 8, "endColumn": 43}, {"ruleId": "109", "severity": 1, "message": "123", "line": 10, "column": 10, "nodeType": "111", "messageId": "112", "endLine": 10, "endColumn": 32}, {"ruleId": "109", "severity": 1, "message": "124", "line": 10, "column": 34, "nodeType": "111", "messageId": "112", "endLine": 10, "endColumn": 59}, {"ruleId": "109", "severity": 1, "message": "125", "line": 11, "column": 27, "nodeType": "111", "messageId": "112", "endLine": 11, "endColumn": 45}, {"ruleId": "109", "severity": 1, "message": "126", "line": 56, "column": 11, "nodeType": "111", "messageId": "112", "endLine": 56, "endColumn": 27}, {"ruleId": "127", "severity": 1, "message": "128", "line": 196, "column": 161, "nodeType": "129", "messageId": "130", "endLine": 196, "endColumn": 162, "suggestions": "131"}, {"ruleId": "127", "severity": 1, "message": "128", "line": 196, "column": 176, "nodeType": "129", "messageId": "130", "endLine": 196, "endColumn": 177, "suggestions": "132"}, {"ruleId": "127", "severity": 1, "message": "128", "line": 196, "column": 180, "nodeType": "129", "messageId": "130", "endLine": 196, "endColumn": 181, "suggestions": "133"}, {"ruleId": "127", "severity": 1, "message": "128", "line": 196, "column": 200, "nodeType": "129", "messageId": "130", "endLine": 196, "endColumn": 201, "suggestions": "134"}, {"ruleId": "127", "severity": 1, "message": "128", "line": 196, "column": 204, "nodeType": "129", "messageId": "130", "endLine": 196, "endColumn": 205, "suggestions": "135"}, {"ruleId": "127", "severity": 1, "message": "128", "line": 196, "column": 214, "nodeType": "129", "messageId": "130", "endLine": 196, "endColumn": 215, "suggestions": "136"}, {"ruleId": "127", "severity": 1, "message": "128", "line": 196, "column": 526, "nodeType": "129", "messageId": "130", "endLine": 196, "endColumn": 527, "suggestions": "137"}, {"ruleId": "127", "severity": 1, "message": "128", "line": 196, "column": 552, "nodeType": "129", "messageId": "130", "endLine": 196, "endColumn": 553, "suggestions": "138"}, {"ruleId": "127", "severity": 1, "message": "128", "line": 196, "column": 632, "nodeType": "129", "messageId": "130", "endLine": 196, "endColumn": 633, "suggestions": "139"}, {"ruleId": "127", "severity": 1, "message": "128", "line": 196, "column": 640, "nodeType": "129", "messageId": "130", "endLine": 196, "endColumn": 641, "suggestions": "140"}, {"ruleId": "127", "severity": 1, "message": "128", "line": 198, "column": 26, "nodeType": "129", "messageId": "130", "endLine": 198, "endColumn": 27, "suggestions": "141"}, {"ruleId": "127", "severity": 1, "message": "128", "line": 198, "column": 131, "nodeType": "129", "messageId": "130", "endLine": 198, "endColumn": 132, "suggestions": "142"}, {"ruleId": "127", "severity": 1, "message": "128", "line": 198, "column": 140, "nodeType": "129", "messageId": "130", "endLine": 198, "endColumn": 141, "suggestions": "143"}, {"ruleId": "127", "severity": 1, "message": "128", "line": 198, "column": 160, "nodeType": "129", "messageId": "130", "endLine": 198, "endColumn": 161, "suggestions": "144"}, {"ruleId": "127", "severity": 1, "message": "128", "line": 198, "column": 175, "nodeType": "129", "messageId": "130", "endLine": 198, "endColumn": 176, "suggestions": "145"}, {"ruleId": "127", "severity": 1, "message": "128", "line": 198, "column": 205, "nodeType": "129", "messageId": "130", "endLine": 198, "endColumn": 206, "suggestions": "146"}, {"ruleId": "127", "severity": 1, "message": "128", "line": 202, "column": 26, "nodeType": "129", "messageId": "130", "endLine": 202, "endColumn": 27, "suggestions": "147"}, {"ruleId": "127", "severity": 1, "message": "128", "line": 202, "column": 48, "nodeType": "129", "messageId": "130", "endLine": 202, "endColumn": 49, "suggestions": "148"}, {"ruleId": "127", "severity": 1, "message": "128", "line": 202, "column": 63, "nodeType": "129", "messageId": "130", "endLine": 202, "endColumn": 64, "suggestions": "149"}, {"ruleId": "127", "severity": 1, "message": "128", "line": 202, "column": 163, "nodeType": "129", "messageId": "130", "endLine": 202, "endColumn": 164, "suggestions": "150"}, {"ruleId": "127", "severity": 1, "message": "128", "line": 202, "column": 172, "nodeType": "129", "messageId": "130", "endLine": 202, "endColumn": 173, "suggestions": "151"}, {"ruleId": "127", "severity": 1, "message": "128", "line": 202, "column": 178, "nodeType": "129", "messageId": "130", "endLine": 202, "endColumn": 179, "suggestions": "152"}, {"ruleId": "127", "severity": 1, "message": "128", "line": 207, "column": 26, "nodeType": "129", "messageId": "130", "endLine": 207, "endColumn": 27, "suggestions": "153"}, {"ruleId": "127", "severity": 1, "message": "128", "line": 207, "column": 48, "nodeType": "129", "messageId": "130", "endLine": 207, "endColumn": 49, "suggestions": "154"}, {"ruleId": "127", "severity": 1, "message": "128", "line": 207, "column": 63, "nodeType": "129", "messageId": "130", "endLine": 207, "endColumn": 64, "suggestions": "155"}, {"ruleId": "127", "severity": 1, "message": "128", "line": 207, "column": 166, "nodeType": "129", "messageId": "130", "endLine": 207, "endColumn": 167, "suggestions": "156"}, {"ruleId": "127", "severity": 1, "message": "128", "line": 207, "column": 175, "nodeType": "129", "messageId": "130", "endLine": 207, "endColumn": 176, "suggestions": "157"}, {"ruleId": "127", "severity": 1, "message": "128", "line": 207, "column": 181, "nodeType": "129", "messageId": "130", "endLine": 207, "endColumn": 182, "suggestions": "158"}, {"ruleId": "127", "severity": 1, "message": "128", "line": 212, "column": 26, "nodeType": "129", "messageId": "130", "endLine": 212, "endColumn": 27, "suggestions": "159"}, {"ruleId": "127", "severity": 1, "message": "128", "line": 212, "column": 48, "nodeType": "129", "messageId": "130", "endLine": 212, "endColumn": 49, "suggestions": "160"}, {"ruleId": "127", "severity": 1, "message": "128", "line": 212, "column": 63, "nodeType": "129", "messageId": "130", "endLine": 212, "endColumn": 64, "suggestions": "161"}, {"ruleId": "127", "severity": 1, "message": "128", "line": 212, "column": 166, "nodeType": "129", "messageId": "130", "endLine": 212, "endColumn": 167, "suggestions": "162"}, {"ruleId": "127", "severity": 1, "message": "128", "line": 212, "column": 175, "nodeType": "129", "messageId": "130", "endLine": 212, "endColumn": 176, "suggestions": "163"}, {"ruleId": "127", "severity": 1, "message": "128", "line": 212, "column": 181, "nodeType": "129", "messageId": "130", "endLine": 212, "endColumn": 182, "suggestions": "164"}, {"ruleId": "127", "severity": 1, "message": "128", "line": 215, "column": 24, "nodeType": "129", "messageId": "130", "endLine": 215, "endColumn": 25, "suggestions": "165"}, {"ruleId": "127", "severity": 1, "message": "128", "line": 215, "column": 60, "nodeType": "129", "messageId": "130", "endLine": 215, "endColumn": 61, "suggestions": "166"}, {"ruleId": "109", "severity": 1, "message": "167", "line": 229, "column": 9, "nodeType": "111", "messageId": "112", "endLine": 229, "endColumn": 19}, {"ruleId": "109", "severity": 1, "message": "168", "line": 2, "column": 10, "nodeType": "111", "messageId": "112", "endLine": 2, "endColumn": 16}, {"ruleId": "114", "severity": 1, "message": "169", "line": 17, "column": 6, "nodeType": "116", "endLine": 17, "endColumn": 12, "suggestions": "170"}, "no-unused-vars", "'optimizeResumeDocxWithFormat' is defined but never used.", "Identifier", "unusedVar", "'downloadOptimizedDocx' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'handleOptimize'. Either include it or remove the dependency array.", "ArrayExpression", ["171"], "'isDocxFile' is assigned a value but never used.", "'exportToText' is defined but never used.", "'DocumentPreview' is defined but never used.", "'showComparison' is assigned a value but never used.", "'setShowComparison' is assigned a value but never used.", "'showFormattedOptimized' is assigned a value but never used.", "'setShowFormattedOptimized' is assigned a value but never used.", "'setDownloadingDocx' is assigned a value but never used.", "'safeOriginalText' is assigned a value but never used.", "no-useless-escape", "Unnecessary escape character: \\\".", "TemplateElement", "unnecessaryEscape", ["172", "173"], ["174", "175"], ["176", "177"], ["178", "179"], ["180", "181"], ["182", "183"], ["184", "185"], ["186", "187"], ["188", "189"], ["190", "191"], ["192", "193"], ["194", "195"], ["196", "197"], ["198", "199"], ["200", "201"], ["202", "203"], ["204", "205"], ["206", "207"], ["208", "209"], ["210", "211"], ["212", "213"], ["214", "215"], ["216", "217"], ["218", "219"], ["220", "221"], ["222", "223"], ["224", "225"], ["226", "227"], ["228", "229"], ["230", "231"], ["232", "233"], ["234", "235"], ["236", "237"], ["238", "239"], ["240", "241"], ["242", "243"], "'getScoreBg' is assigned a value but never used.", "'saveAs' is defined but never used.", "React Hook useEffect has a missing dependency: 'generatePreview'. Either include it or remove the dependency array.", ["244"], {"desc": "245", "fix": "246"}, {"messageId": "247", "fix": "248", "desc": "249"}, {"messageId": "250", "fix": "251", "desc": "252"}, {"messageId": "247", "fix": "253", "desc": "249"}, {"messageId": "250", "fix": "254", "desc": "252"}, {"messageId": "247", "fix": "255", "desc": "249"}, {"messageId": "250", "fix": "256", "desc": "252"}, {"messageId": "247", "fix": "257", "desc": "249"}, {"messageId": "250", "fix": "258", "desc": "252"}, {"messageId": "247", "fix": "259", "desc": "249"}, {"messageId": "250", "fix": "260", "desc": "252"}, {"messageId": "247", "fix": "261", "desc": "249"}, {"messageId": "250", "fix": "262", "desc": "252"}, {"messageId": "247", "fix": "263", "desc": "249"}, {"messageId": "250", "fix": "264", "desc": "252"}, {"messageId": "247", "fix": "265", "desc": "249"}, {"messageId": "250", "fix": "266", "desc": "252"}, {"messageId": "247", "fix": "267", "desc": "249"}, {"messageId": "250", "fix": "268", "desc": "252"}, {"messageId": "247", "fix": "269", "desc": "249"}, {"messageId": "250", "fix": "270", "desc": "252"}, {"messageId": "247", "fix": "271", "desc": "249"}, {"messageId": "250", "fix": "272", "desc": "252"}, {"messageId": "247", "fix": "273", "desc": "249"}, {"messageId": "250", "fix": "274", "desc": "252"}, {"messageId": "247", "fix": "275", "desc": "249"}, {"messageId": "250", "fix": "276", "desc": "252"}, {"messageId": "247", "fix": "277", "desc": "249"}, {"messageId": "250", "fix": "278", "desc": "252"}, {"messageId": "247", "fix": "279", "desc": "249"}, {"messageId": "250", "fix": "280", "desc": "252"}, {"messageId": "247", "fix": "281", "desc": "249"}, {"messageId": "250", "fix": "282", "desc": "252"}, {"messageId": "247", "fix": "283", "desc": "249"}, {"messageId": "250", "fix": "284", "desc": "252"}, {"messageId": "247", "fix": "285", "desc": "249"}, {"messageId": "250", "fix": "286", "desc": "252"}, {"messageId": "247", "fix": "287", "desc": "249"}, {"messageId": "250", "fix": "288", "desc": "252"}, {"messageId": "247", "fix": "289", "desc": "249"}, {"messageId": "250", "fix": "290", "desc": "252"}, {"messageId": "247", "fix": "291", "desc": "249"}, {"messageId": "250", "fix": "292", "desc": "252"}, {"messageId": "247", "fix": "293", "desc": "249"}, {"messageId": "250", "fix": "294", "desc": "252"}, {"messageId": "247", "fix": "295", "desc": "249"}, {"messageId": "250", "fix": "296", "desc": "252"}, {"messageId": "247", "fix": "297", "desc": "249"}, {"messageId": "250", "fix": "298", "desc": "252"}, {"messageId": "247", "fix": "299", "desc": "249"}, {"messageId": "250", "fix": "300", "desc": "252"}, {"messageId": "247", "fix": "301", "desc": "249"}, {"messageId": "250", "fix": "302", "desc": "252"}, {"messageId": "247", "fix": "303", "desc": "249"}, {"messageId": "250", "fix": "304", "desc": "252"}, {"messageId": "247", "fix": "305", "desc": "249"}, {"messageId": "250", "fix": "306", "desc": "252"}, {"messageId": "247", "fix": "307", "desc": "249"}, {"messageId": "250", "fix": "308", "desc": "252"}, {"messageId": "247", "fix": "309", "desc": "249"}, {"messageId": "250", "fix": "310", "desc": "252"}, {"messageId": "247", "fix": "311", "desc": "249"}, {"messageId": "250", "fix": "312", "desc": "252"}, {"messageId": "247", "fix": "313", "desc": "249"}, {"messageId": "250", "fix": "314", "desc": "252"}, {"messageId": "247", "fix": "315", "desc": "249"}, {"messageId": "250", "fix": "316", "desc": "252"}, {"messageId": "247", "fix": "317", "desc": "249"}, {"messageId": "250", "fix": "318", "desc": "252"}, {"messageId": "247", "fix": "319", "desc": "249"}, {"messageId": "250", "fix": "320", "desc": "252"}, {"messageId": "247", "fix": "321", "desc": "249"}, {"messageId": "250", "fix": "322", "desc": "252"}, {"desc": "323", "fix": "324"}, "Update the dependencies array to be: [handleOptimize, isLoading, resumeFile]", {"range": "325", "text": "326"}, "removeEscape", {"range": "327", "text": "328"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "329", "text": "330"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "331", "text": "328"}, {"range": "332", "text": "330"}, {"range": "333", "text": "328"}, {"range": "334", "text": "330"}, {"range": "335", "text": "328"}, {"range": "336", "text": "330"}, {"range": "337", "text": "328"}, {"range": "338", "text": "330"}, {"range": "339", "text": "328"}, {"range": "340", "text": "330"}, {"range": "341", "text": "328"}, {"range": "342", "text": "330"}, {"range": "343", "text": "328"}, {"range": "344", "text": "330"}, {"range": "345", "text": "328"}, {"range": "346", "text": "330"}, {"range": "347", "text": "328"}, {"range": "348", "text": "330"}, {"range": "349", "text": "328"}, {"range": "350", "text": "330"}, {"range": "351", "text": "328"}, {"range": "352", "text": "330"}, {"range": "353", "text": "328"}, {"range": "354", "text": "330"}, {"range": "355", "text": "328"}, {"range": "356", "text": "330"}, {"range": "357", "text": "328"}, {"range": "358", "text": "330"}, {"range": "359", "text": "328"}, {"range": "360", "text": "330"}, {"range": "361", "text": "328"}, {"range": "362", "text": "330"}, {"range": "363", "text": "328"}, {"range": "364", "text": "330"}, {"range": "365", "text": "328"}, {"range": "366", "text": "330"}, {"range": "367", "text": "328"}, {"range": "368", "text": "330"}, {"range": "369", "text": "328"}, {"range": "370", "text": "330"}, {"range": "371", "text": "328"}, {"range": "372", "text": "330"}, {"range": "373", "text": "328"}, {"range": "374", "text": "330"}, {"range": "375", "text": "328"}, {"range": "376", "text": "330"}, {"range": "377", "text": "328"}, {"range": "378", "text": "330"}, {"range": "379", "text": "328"}, {"range": "380", "text": "330"}, {"range": "381", "text": "328"}, {"range": "382", "text": "330"}, {"range": "383", "text": "328"}, {"range": "384", "text": "330"}, {"range": "385", "text": "328"}, {"range": "386", "text": "330"}, {"range": "387", "text": "328"}, {"range": "388", "text": "330"}, {"range": "389", "text": "328"}, {"range": "390", "text": "330"}, {"range": "391", "text": "328"}, {"range": "392", "text": "330"}, {"range": "393", "text": "328"}, {"range": "394", "text": "330"}, {"range": "395", "text": "328"}, {"range": "396", "text": "330"}, {"range": "397", "text": "328"}, {"range": "398", "text": "330"}, {"range": "399", "text": "328"}, {"range": "400", "text": "330"}, "Update the dependencies array to be: [file, generatePreview]", {"range": "401", "text": "402"}, [1695, 1718], "[handleOptimize, isLoading, resumeFile]", [8308, 8309], "", [8308, 8308], "\\", [8323, 8324], [8323, 8323], [8327, 8328], [8327, 8327], [8347, 8348], [8347, 8347], [8351, 8352], [8351, 8351], [8361, 8362], [8361, 8361], [8673, 8674], [8673, 8673], [8699, 8700], [8699, 8699], [8779, 8780], [8779, 8779], [8787, 8788], [8787, 8787], [8839, 8840], [8839, 8839], [8944, 8945], [8944, 8944], [8953, 8954], [8953, 8953], [8973, 8974], [8973, 8973], [8988, 8989], [8988, 8988], [9018, 9019], [9018, 9018], [9210, 9211], [9210, 9210], [9232, 9233], [9232, 9232], [9247, 9248], [9247, 9247], [9347, 9348], [9347, 9347], [9356, 9357], [9356, 9356], [9362, 9363], [9362, 9362], [9583, 9584], [9583, 9583], [9605, 9606], [9605, 9605], [9620, 9621], [9620, 9620], [9723, 9724], [9723, 9723], [9732, 9733], [9732, 9732], [9738, 9739], [9738, 9738], [9945, 9946], [9945, 9945], [9967, 9968], [9967, 9967], [9982, 9983], [9982, 9982], [10085, 10086], [10085, 10085], [10094, 10095], [10094, 10094], [10100, 10101], [10100, 10100], [10179, 10180], [10179, 10179], [10215, 10216], [10215, 10215], [645, 651], "[file, generatePreview]"]