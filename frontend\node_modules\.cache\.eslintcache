[{"E:\\AI\\SmartCV\\frontend\\src\\index.js": "1", "E:\\AI\\SmartCV\\frontend\\src\\App.js": "2", "E:\\AI\\SmartCV\\frontend\\src\\components\\JobDescriptionInput.js": "3", "E:\\AI\\SmartCV\\frontend\\src\\components\\LoadingSpinner.js": "4", "E:\\AI\\SmartCV\\frontend\\src\\components\\ResultDisplay.js": "5", "E:\\AI\\SmartCV\\frontend\\src\\components\\FileUpload.js": "6", "E:\\AI\\SmartCV\\frontend\\src\\services\\api.js": "7", "E:\\AI\\SmartCV\\frontend\\src\\utils\\exportUtils.js": "8", "E:\\AI\\SmartCV\\frontend\\src\\components\\DocumentPreview.js": "9", "E:\\AI\\SmartCV\\frontend\\src\\components\\PDFViewer.js": "10", "E:\\AI\\SmartCV\\frontend\\src\\services\\ocrService.js": "11"}, {"size": 264, "mtime": 1748859416987, "results": "12", "hashOfConfig": "13"}, {"size": 23849, "mtime": 1748991744739, "results": "14", "hashOfConfig": "13"}, {"size": 3585, "mtime": 1748864007360, "results": "15", "hashOfConfig": "13"}, {"size": 4593, "mtime": 1748861711717, "results": "16", "hashOfConfig": "13"}, {"size": 64651, "mtime": 1749031554158, "results": "17", "hashOfConfig": "13"}, {"size": 17948, "mtime": 1748894515308, "results": "18", "hashOfConfig": "13"}, {"size": 6304, "mtime": 1748942669041, "results": "19", "hashOfConfig": "13"}, {"size": 12698, "mtime": 1748992031824, "results": "20", "hashOfConfig": "13"}, {"size": 14380, "mtime": 1748898377610, "results": "21", "hashOfConfig": "13"}, {"size": 4312, "mtime": 1748872859544, "results": "22", "hashOfConfig": "13"}, {"size": 2056, "mtime": 1748874639715, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "vnfw6u", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 47, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\AI\\SmartCV\\frontend\\src\\index.js", [], [], "E:\\AI\\SmartCV\\frontend\\src\\App.js", ["57", "58", "59", "60"], [], "E:\\AI\\SmartCV\\frontend\\src\\components\\JobDescriptionInput.js", [], [], "E:\\AI\\SmartCV\\frontend\\src\\components\\LoadingSpinner.js", [], [], "E:\\AI\\SmartCV\\frontend\\src\\components\\ResultDisplay.js", ["61", "62", "63", "64", "65", "66", "67", "68", "69", "70", "71", "72", "73", "74", "75", "76", "77", "78", "79", "80", "81", "82", "83", "84", "85", "86", "87", "88", "89", "90", "91", "92", "93", "94", "95", "96", "97", "98", "99", "100", "101", "102", "103", "104", "105", "106", "107"], [], "E:\\AI\\SmartCV\\frontend\\src\\components\\FileUpload.js", [], [], "E:\\AI\\SmartCV\\frontend\\src\\services\\api.js", [], [], "E:\\AI\\SmartCV\\frontend\\src\\utils\\exportUtils.js", ["108"], [], "E:\\AI\\SmartCV\\frontend\\src\\components\\DocumentPreview.js", ["109"], [], "E:\\AI\\SmartCV\\frontend\\src\\components\\PDFViewer.js", [], [], "E:\\AI\\SmartCV\\frontend\\src\\services\\ocrService.js", [], [], {"ruleId": "110", "severity": 1, "message": "111", "line": 7, "column": 26, "nodeType": "112", "messageId": "113", "endLine": 7, "endColumn": 54}, {"ruleId": "110", "severity": 1, "message": "114", "line": 7, "column": 56, "nodeType": "112", "messageId": "113", "endLine": 7, "endColumn": 77}, {"ruleId": "115", "severity": 1, "message": "116", "line": 42, "column": 6, "nodeType": "117", "endLine": 42, "endColumn": 29, "suggestions": "118"}, {"ruleId": "110", "severity": 1, "message": "119", "line": 154, "column": 9, "nodeType": "112", "messageId": "113", "endLine": 154, "endColumn": 19}, {"ruleId": "110", "severity": 1, "message": "120", "line": 2, "column": 37, "nodeType": "112", "messageId": "113", "endLine": 2, "endColumn": 49}, {"ruleId": "110", "severity": 1, "message": "114", "line": 3, "column": 10, "nodeType": "112", "messageId": "113", "endLine": 3, "endColumn": 31}, {"ruleId": "110", "severity": 1, "message": "121", "line": 4, "column": 8, "nodeType": "112", "messageId": "113", "endLine": 4, "endColumn": 23}, {"ruleId": "110", "severity": 1, "message": "122", "line": 8, "column": 10, "nodeType": "112", "messageId": "113", "endLine": 8, "endColumn": 24}, {"ruleId": "110", "severity": 1, "message": "123", "line": 8, "column": 26, "nodeType": "112", "messageId": "113", "endLine": 8, "endColumn": 43}, {"ruleId": "110", "severity": 1, "message": "124", "line": 10, "column": 10, "nodeType": "112", "messageId": "113", "endLine": 10, "endColumn": 32}, {"ruleId": "110", "severity": 1, "message": "125", "line": 10, "column": 34, "nodeType": "112", "messageId": "113", "endLine": 10, "endColumn": 59}, {"ruleId": "110", "severity": 1, "message": "126", "line": 11, "column": 27, "nodeType": "112", "messageId": "113", "endLine": 11, "endColumn": 45}, {"ruleId": "110", "severity": 1, "message": "127", "line": 56, "column": 11, "nodeType": "112", "messageId": "113", "endLine": 56, "endColumn": 27}, {"ruleId": "128", "severity": 1, "message": "129", "line": 231, "column": 161, "nodeType": "130", "messageId": "131", "endLine": 231, "endColumn": 162, "suggestions": "132"}, {"ruleId": "128", "severity": 1, "message": "129", "line": 231, "column": 176, "nodeType": "130", "messageId": "131", "endLine": 231, "endColumn": 177, "suggestions": "133"}, {"ruleId": "128", "severity": 1, "message": "129", "line": 231, "column": 180, "nodeType": "130", "messageId": "131", "endLine": 231, "endColumn": 181, "suggestions": "134"}, {"ruleId": "128", "severity": 1, "message": "129", "line": 231, "column": 200, "nodeType": "130", "messageId": "131", "endLine": 231, "endColumn": 201, "suggestions": "135"}, {"ruleId": "128", "severity": 1, "message": "129", "line": 231, "column": 204, "nodeType": "130", "messageId": "131", "endLine": 231, "endColumn": 205, "suggestions": "136"}, {"ruleId": "128", "severity": 1, "message": "129", "line": 231, "column": 214, "nodeType": "130", "messageId": "131", "endLine": 231, "endColumn": 215, "suggestions": "137"}, {"ruleId": "128", "severity": 1, "message": "129", "line": 231, "column": 526, "nodeType": "130", "messageId": "131", "endLine": 231, "endColumn": 527, "suggestions": "138"}, {"ruleId": "128", "severity": 1, "message": "129", "line": 231, "column": 552, "nodeType": "130", "messageId": "131", "endLine": 231, "endColumn": 553, "suggestions": "139"}, {"ruleId": "128", "severity": 1, "message": "129", "line": 231, "column": 632, "nodeType": "130", "messageId": "131", "endLine": 231, "endColumn": 633, "suggestions": "140"}, {"ruleId": "128", "severity": 1, "message": "129", "line": 231, "column": 640, "nodeType": "130", "messageId": "131", "endLine": 231, "endColumn": 641, "suggestions": "141"}, {"ruleId": "128", "severity": 1, "message": "129", "line": 233, "column": 26, "nodeType": "130", "messageId": "131", "endLine": 233, "endColumn": 27, "suggestions": "142"}, {"ruleId": "128", "severity": 1, "message": "129", "line": 233, "column": 131, "nodeType": "130", "messageId": "131", "endLine": 233, "endColumn": 132, "suggestions": "143"}, {"ruleId": "128", "severity": 1, "message": "129", "line": 233, "column": 140, "nodeType": "130", "messageId": "131", "endLine": 233, "endColumn": 141, "suggestions": "144"}, {"ruleId": "128", "severity": 1, "message": "129", "line": 233, "column": 160, "nodeType": "130", "messageId": "131", "endLine": 233, "endColumn": 161, "suggestions": "145"}, {"ruleId": "128", "severity": 1, "message": "129", "line": 233, "column": 175, "nodeType": "130", "messageId": "131", "endLine": 233, "endColumn": 176, "suggestions": "146"}, {"ruleId": "128", "severity": 1, "message": "129", "line": 233, "column": 205, "nodeType": "130", "messageId": "131", "endLine": 233, "endColumn": 206, "suggestions": "147"}, {"ruleId": "128", "severity": 1, "message": "129", "line": 237, "column": 26, "nodeType": "130", "messageId": "131", "endLine": 237, "endColumn": 27, "suggestions": "148"}, {"ruleId": "128", "severity": 1, "message": "129", "line": 237, "column": 48, "nodeType": "130", "messageId": "131", "endLine": 237, "endColumn": 49, "suggestions": "149"}, {"ruleId": "128", "severity": 1, "message": "129", "line": 237, "column": 63, "nodeType": "130", "messageId": "131", "endLine": 237, "endColumn": 64, "suggestions": "150"}, {"ruleId": "128", "severity": 1, "message": "129", "line": 237, "column": 163, "nodeType": "130", "messageId": "131", "endLine": 237, "endColumn": 164, "suggestions": "151"}, {"ruleId": "128", "severity": 1, "message": "129", "line": 237, "column": 172, "nodeType": "130", "messageId": "131", "endLine": 237, "endColumn": 173, "suggestions": "152"}, {"ruleId": "128", "severity": 1, "message": "129", "line": 237, "column": 178, "nodeType": "130", "messageId": "131", "endLine": 237, "endColumn": 179, "suggestions": "153"}, {"ruleId": "128", "severity": 1, "message": "129", "line": 242, "column": 26, "nodeType": "130", "messageId": "131", "endLine": 242, "endColumn": 27, "suggestions": "154"}, {"ruleId": "128", "severity": 1, "message": "129", "line": 242, "column": 48, "nodeType": "130", "messageId": "131", "endLine": 242, "endColumn": 49, "suggestions": "155"}, {"ruleId": "128", "severity": 1, "message": "129", "line": 242, "column": 63, "nodeType": "130", "messageId": "131", "endLine": 242, "endColumn": 64, "suggestions": "156"}, {"ruleId": "128", "severity": 1, "message": "129", "line": 242, "column": 166, "nodeType": "130", "messageId": "131", "endLine": 242, "endColumn": 167, "suggestions": "157"}, {"ruleId": "128", "severity": 1, "message": "129", "line": 242, "column": 175, "nodeType": "130", "messageId": "131", "endLine": 242, "endColumn": 176, "suggestions": "158"}, {"ruleId": "128", "severity": 1, "message": "129", "line": 242, "column": 181, "nodeType": "130", "messageId": "131", "endLine": 242, "endColumn": 182, "suggestions": "159"}, {"ruleId": "128", "severity": 1, "message": "129", "line": 247, "column": 26, "nodeType": "130", "messageId": "131", "endLine": 247, "endColumn": 27, "suggestions": "160"}, {"ruleId": "128", "severity": 1, "message": "129", "line": 247, "column": 48, "nodeType": "130", "messageId": "131", "endLine": 247, "endColumn": 49, "suggestions": "161"}, {"ruleId": "128", "severity": 1, "message": "129", "line": 247, "column": 63, "nodeType": "130", "messageId": "131", "endLine": 247, "endColumn": 64, "suggestions": "162"}, {"ruleId": "128", "severity": 1, "message": "129", "line": 247, "column": 166, "nodeType": "130", "messageId": "131", "endLine": 247, "endColumn": 167, "suggestions": "163"}, {"ruleId": "128", "severity": 1, "message": "129", "line": 247, "column": 175, "nodeType": "130", "messageId": "131", "endLine": 247, "endColumn": 176, "suggestions": "164"}, {"ruleId": "128", "severity": 1, "message": "129", "line": 247, "column": 181, "nodeType": "130", "messageId": "131", "endLine": 247, "endColumn": 182, "suggestions": "165"}, {"ruleId": "128", "severity": 1, "message": "129", "line": 250, "column": 24, "nodeType": "130", "messageId": "131", "endLine": 250, "endColumn": 25, "suggestions": "166"}, {"ruleId": "128", "severity": 1, "message": "129", "line": 250, "column": 60, "nodeType": "130", "messageId": "131", "endLine": 250, "endColumn": 61, "suggestions": "167"}, {"ruleId": "110", "severity": 1, "message": "168", "line": 264, "column": 9, "nodeType": "112", "messageId": "113", "endLine": 264, "endColumn": 19}, {"ruleId": "110", "severity": 1, "message": "169", "line": 1109, "column": 29, "nodeType": "112", "messageId": "113", "endLine": 1109, "endColumn": 35}, {"ruleId": "110", "severity": 1, "message": "170", "line": 2, "column": 10, "nodeType": "112", "messageId": "113", "endLine": 2, "endColumn": 16}, {"ruleId": "115", "severity": 1, "message": "171", "line": 17, "column": 6, "nodeType": "117", "endLine": 17, "endColumn": 12, "suggestions": "172"}, "no-unused-vars", "'optimizeResumeDocxWithFormat' is defined but never used.", "Identifier", "unusedVar", "'downloadOptimizedDocx' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'handleOptimize'. Either include it or remove the dependency array.", "ArrayExpression", ["173"], "'isDocxFile' is assigned a value but never used.", "'exportToText' is defined but never used.", "'DocumentPreview' is defined but never used.", "'showComparison' is assigned a value but never used.", "'setShowComparison' is assigned a value but never used.", "'showFormattedOptimized' is assigned a value but never used.", "'setShowFormattedOptimized' is assigned a value but never used.", "'setDownloadingDocx' is assigned a value but never used.", "'safeOriginalText' is assigned a value but never used.", "no-useless-escape", "Unnecessary escape character: \\\".", "TemplateElement", "unnecessaryEscape", ["174", "175"], ["176", "177"], ["178", "179"], ["180", "181"], ["182", "183"], ["184", "185"], ["186", "187"], ["188", "189"], ["190", "191"], ["192", "193"], ["194", "195"], ["196", "197"], ["198", "199"], ["200", "201"], ["202", "203"], ["204", "205"], ["206", "207"], ["208", "209"], ["210", "211"], ["212", "213"], ["214", "215"], ["216", "217"], ["218", "219"], ["220", "221"], ["222", "223"], ["224", "225"], ["226", "227"], ["228", "229"], ["230", "231"], ["232", "233"], ["234", "235"], ["236", "237"], ["238", "239"], ["240", "241"], ["242", "243"], ["244", "245"], "'getScoreBg' is assigned a value but never used.", "'option' is assigned a value but never used.", "'saveAs' is defined but never used.", "React Hook useEffect has a missing dependency: 'generatePreview'. Either include it or remove the dependency array.", ["246"], {"desc": "247", "fix": "248"}, {"messageId": "249", "fix": "250", "desc": "251"}, {"messageId": "252", "fix": "253", "desc": "254"}, {"messageId": "249", "fix": "255", "desc": "251"}, {"messageId": "252", "fix": "256", "desc": "254"}, {"messageId": "249", "fix": "257", "desc": "251"}, {"messageId": "252", "fix": "258", "desc": "254"}, {"messageId": "249", "fix": "259", "desc": "251"}, {"messageId": "252", "fix": "260", "desc": "254"}, {"messageId": "249", "fix": "261", "desc": "251"}, {"messageId": "252", "fix": "262", "desc": "254"}, {"messageId": "249", "fix": "263", "desc": "251"}, {"messageId": "252", "fix": "264", "desc": "254"}, {"messageId": "249", "fix": "265", "desc": "251"}, {"messageId": "252", "fix": "266", "desc": "254"}, {"messageId": "249", "fix": "267", "desc": "251"}, {"messageId": "252", "fix": "268", "desc": "254"}, {"messageId": "249", "fix": "269", "desc": "251"}, {"messageId": "252", "fix": "270", "desc": "254"}, {"messageId": "249", "fix": "271", "desc": "251"}, {"messageId": "252", "fix": "272", "desc": "254"}, {"messageId": "249", "fix": "273", "desc": "251"}, {"messageId": "252", "fix": "274", "desc": "254"}, {"messageId": "249", "fix": "275", "desc": "251"}, {"messageId": "252", "fix": "276", "desc": "254"}, {"messageId": "249", "fix": "277", "desc": "251"}, {"messageId": "252", "fix": "278", "desc": "254"}, {"messageId": "249", "fix": "279", "desc": "251"}, {"messageId": "252", "fix": "280", "desc": "254"}, {"messageId": "249", "fix": "281", "desc": "251"}, {"messageId": "252", "fix": "282", "desc": "254"}, {"messageId": "249", "fix": "283", "desc": "251"}, {"messageId": "252", "fix": "284", "desc": "254"}, {"messageId": "249", "fix": "285", "desc": "251"}, {"messageId": "252", "fix": "286", "desc": "254"}, {"messageId": "249", "fix": "287", "desc": "251"}, {"messageId": "252", "fix": "288", "desc": "254"}, {"messageId": "249", "fix": "289", "desc": "251"}, {"messageId": "252", "fix": "290", "desc": "254"}, {"messageId": "249", "fix": "291", "desc": "251"}, {"messageId": "252", "fix": "292", "desc": "254"}, {"messageId": "249", "fix": "293", "desc": "251"}, {"messageId": "252", "fix": "294", "desc": "254"}, {"messageId": "249", "fix": "295", "desc": "251"}, {"messageId": "252", "fix": "296", "desc": "254"}, {"messageId": "249", "fix": "297", "desc": "251"}, {"messageId": "252", "fix": "298", "desc": "254"}, {"messageId": "249", "fix": "299", "desc": "251"}, {"messageId": "252", "fix": "300", "desc": "254"}, {"messageId": "249", "fix": "301", "desc": "251"}, {"messageId": "252", "fix": "302", "desc": "254"}, {"messageId": "249", "fix": "303", "desc": "251"}, {"messageId": "252", "fix": "304", "desc": "254"}, {"messageId": "249", "fix": "305", "desc": "251"}, {"messageId": "252", "fix": "306", "desc": "254"}, {"messageId": "249", "fix": "307", "desc": "251"}, {"messageId": "252", "fix": "308", "desc": "254"}, {"messageId": "249", "fix": "309", "desc": "251"}, {"messageId": "252", "fix": "310", "desc": "254"}, {"messageId": "249", "fix": "311", "desc": "251"}, {"messageId": "252", "fix": "312", "desc": "254"}, {"messageId": "249", "fix": "313", "desc": "251"}, {"messageId": "252", "fix": "314", "desc": "254"}, {"messageId": "249", "fix": "315", "desc": "251"}, {"messageId": "252", "fix": "316", "desc": "254"}, {"messageId": "249", "fix": "317", "desc": "251"}, {"messageId": "252", "fix": "318", "desc": "254"}, {"messageId": "249", "fix": "319", "desc": "251"}, {"messageId": "252", "fix": "320", "desc": "254"}, {"messageId": "249", "fix": "321", "desc": "251"}, {"messageId": "252", "fix": "322", "desc": "254"}, {"messageId": "249", "fix": "323", "desc": "251"}, {"messageId": "252", "fix": "324", "desc": "254"}, {"desc": "325", "fix": "326"}, "Update the dependencies array to be: [handleOptimize, isLoading, resumeFile]", {"range": "327", "text": "328"}, "removeEscape", {"range": "329", "text": "330"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "331", "text": "332"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "333", "text": "330"}, {"range": "334", "text": "332"}, {"range": "335", "text": "330"}, {"range": "336", "text": "332"}, {"range": "337", "text": "330"}, {"range": "338", "text": "332"}, {"range": "339", "text": "330"}, {"range": "340", "text": "332"}, {"range": "341", "text": "330"}, {"range": "342", "text": "332"}, {"range": "343", "text": "330"}, {"range": "344", "text": "332"}, {"range": "345", "text": "330"}, {"range": "346", "text": "332"}, {"range": "347", "text": "330"}, {"range": "348", "text": "332"}, {"range": "349", "text": "330"}, {"range": "350", "text": "332"}, {"range": "351", "text": "330"}, {"range": "352", "text": "332"}, {"range": "353", "text": "330"}, {"range": "354", "text": "332"}, {"range": "355", "text": "330"}, {"range": "356", "text": "332"}, {"range": "357", "text": "330"}, {"range": "358", "text": "332"}, {"range": "359", "text": "330"}, {"range": "360", "text": "332"}, {"range": "361", "text": "330"}, {"range": "362", "text": "332"}, {"range": "363", "text": "330"}, {"range": "364", "text": "332"}, {"range": "365", "text": "330"}, {"range": "366", "text": "332"}, {"range": "367", "text": "330"}, {"range": "368", "text": "332"}, {"range": "369", "text": "330"}, {"range": "370", "text": "332"}, {"range": "371", "text": "330"}, {"range": "372", "text": "332"}, {"range": "373", "text": "330"}, {"range": "374", "text": "332"}, {"range": "375", "text": "330"}, {"range": "376", "text": "332"}, {"range": "377", "text": "330"}, {"range": "378", "text": "332"}, {"range": "379", "text": "330"}, {"range": "380", "text": "332"}, {"range": "381", "text": "330"}, {"range": "382", "text": "332"}, {"range": "383", "text": "330"}, {"range": "384", "text": "332"}, {"range": "385", "text": "330"}, {"range": "386", "text": "332"}, {"range": "387", "text": "330"}, {"range": "388", "text": "332"}, {"range": "389", "text": "330"}, {"range": "390", "text": "332"}, {"range": "391", "text": "330"}, {"range": "392", "text": "332"}, {"range": "393", "text": "330"}, {"range": "394", "text": "332"}, {"range": "395", "text": "330"}, {"range": "396", "text": "332"}, {"range": "397", "text": "330"}, {"range": "398", "text": "332"}, {"range": "399", "text": "330"}, {"range": "400", "text": "332"}, {"range": "401", "text": "330"}, {"range": "402", "text": "332"}, "Update the dependencies array to be: [file, generatePreview]", {"range": "403", "text": "404"}, [1695, 1718], "[handleOptimize, isLoading, resumeFile]", [9587, 9588], "", [9587, 9587], "\\", [9602, 9603], [9602, 9602], [9606, 9607], [9606, 9606], [9626, 9627], [9626, 9626], [9630, 9631], [9630, 9630], [9640, 9641], [9640, 9640], [9952, 9953], [9952, 9952], [9978, 9979], [9978, 9978], [10058, 10059], [10058, 10058], [10066, 10067], [10066, 10066], [10116, 10117], [10116, 10116], [10221, 10222], [10221, 10221], [10230, 10231], [10230, 10230], [10250, 10251], [10250, 10250], [10265, 10266], [10265, 10265], [10295, 10296], [10295, 10295], [10483, 10484], [10483, 10483], [10505, 10506], [10505, 10505], [10520, 10521], [10520, 10520], [10620, 10621], [10620, 10620], [10629, 10630], [10629, 10629], [10635, 10636], [10635, 10635], [10851, 10852], [10851, 10851], [10873, 10874], [10873, 10873], [10888, 10889], [10888, 10888], [10991, 10992], [10991, 10991], [11000, 11001], [11000, 11000], [11006, 11007], [11006, 11006], [11208, 11209], [11208, 11208], [11230, 11231], [11230, 11230], [11245, 11246], [11245, 11245], [11348, 11349], [11348, 11348], [11357, 11358], [11357, 11357], [11363, 11364], [11363, 11363], [11439, 11440], [11439, 11439], [11475, 11476], [11475, 11475], [645, 651], "[file, generatePreview]"]