{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport TimelineModel from './TimelineModel.js';\nimport { DataFormatMixin } from '../../model/mixin/dataFormat.js';\nimport { mixin } from 'zrender/lib/core/util.js';\nimport { inheritDefaultOption } from '../../util/component.js';\nvar SliderTimelineModel = /** @class */function (_super) {\n  __extends(SliderTimelineModel, _super);\n  function SliderTimelineModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = SliderTimelineModel.type;\n    return _this;\n  }\n  SliderTimelineModel.type = 'timeline.slider';\n  /**\r\n   * @protected\r\n   */\n  SliderTimelineModel.defaultOption = inheritDefaultOption(TimelineModel.defaultOption, {\n    backgroundColor: 'rgba(0,0,0,0)',\n    borderColor: '#ccc',\n    borderWidth: 0,\n    orient: 'horizontal',\n    inverse: false,\n    tooltip: {\n      trigger: 'item' // data item may also have tootip attr.\n    },\n    symbol: 'circle',\n    symbolSize: 12,\n    lineStyle: {\n      show: true,\n      width: 2,\n      color: '#DAE1F5'\n    },\n    label: {\n      position: 'auto',\n      // When using number, label position is not\n      // restricted by viewRect.\n      // positive: right/bottom, negative: left/top\n      show: true,\n      interval: 'auto',\n      rotate: 0,\n      // formatter: null,\n      // 其余属性默认使用全局文本样式，详见TEXTSTYLE\n      color: '#A4B1D7'\n    },\n    itemStyle: {\n      color: '#A4B1D7',\n      borderWidth: 1\n    },\n    checkpointStyle: {\n      symbol: 'circle',\n      symbolSize: 15,\n      color: '#316bf3',\n      borderColor: '#fff',\n      borderWidth: 2,\n      shadowBlur: 2,\n      shadowOffsetX: 1,\n      shadowOffsetY: 1,\n      shadowColor: 'rgba(0, 0, 0, 0.3)',\n      // borderColor: 'rgba(194,53,49, 0.5)',\n      animation: true,\n      animationDuration: 300,\n      animationEasing: 'quinticInOut'\n    },\n    controlStyle: {\n      show: true,\n      showPlayBtn: true,\n      showPrevBtn: true,\n      showNextBtn: true,\n      itemSize: 24,\n      itemGap: 12,\n      position: 'left',\n      playIcon: 'path://M31.6,53C17.5,53,6,41.5,6,27.4S17.5,1.8,31.6,1.8C45.7,1.8,57.2,13.3,57.2,27.4S45.7,53,31.6,53z M31.6,3.3 C18.4,3.3,7.5,14.1,7.5,27.4c0,13.3,10.8,24.1,24.1,24.1C44.9,51.5,55.7,40.7,55.7,27.4C55.7,14.1,44.9,3.3,31.6,3.3z M24.9,21.3 c0-2.2,1.6-3.1,3.5-2l10.5,6.1c1.899,1.1,1.899,2.9,0,4l-10.5,6.1c-1.9,1.1-3.5,0.2-3.5-2V21.3z',\n      stopIcon: 'path://M30.9,53.2C16.8,53.2,5.3,41.7,5.3,27.6S16.8,2,30.9,2C45,2,56.4,13.5,56.4,27.6S45,53.2,30.9,53.2z M30.9,3.5C17.6,3.5,6.8,14.4,6.8,27.6c0,13.3,10.8,24.1,24.101,24.1C44.2,51.7,55,40.9,55,27.6C54.9,14.4,44.1,3.5,30.9,3.5z M36.9,35.8c0,0.601-0.4,1-0.9,1h-1.3c-0.5,0-0.9-0.399-0.9-1V19.5c0-0.6,0.4-1,0.9-1H36c0.5,0,0.9,0.4,0.9,1V35.8z M27.8,35.8 c0,0.601-0.4,1-0.9,1h-1.3c-0.5,0-0.9-0.399-0.9-1V19.5c0-0.6,0.4-1,0.9-1H27c0.5,0,0.9,0.4,0.9,1L27.8,35.8L27.8,35.8z',\n      // eslint-disable-next-line max-len\n      nextIcon: 'M2,18.5A1.52,1.52,0,0,1,.92,18a1.49,1.49,0,0,1,0-2.12L7.81,9.36,1,3.11A1.5,1.5,0,1,1,3,.89l8,7.34a1.48,1.48,0,0,1,.49,1.09,1.51,1.51,0,0,1-.46,1.1L3,18.08A1.5,1.5,0,0,1,2,18.5Z',\n      // eslint-disable-next-line max-len\n      prevIcon: 'M10,.5A1.52,1.52,0,0,1,11.08,1a1.49,1.49,0,0,1,0,2.12L4.19,9.64,11,15.89a1.5,1.5,0,1,1-2,2.22L1,10.77A1.48,1.48,0,0,1,.5,9.68,1.51,1.51,0,0,1,1,8.58L9,.92A1.5,1.5,0,0,1,10,.5Z',\n      prevBtnSize: 18,\n      nextBtnSize: 18,\n      color: '#A4B1D7',\n      borderColor: '#A4B1D7',\n      borderWidth: 1\n    },\n    emphasis: {\n      label: {\n        show: true,\n        // 其余属性默认使用全局文本样式，详见TEXTSTYLE\n        color: '#6f778d'\n      },\n      itemStyle: {\n        color: '#316BF3'\n      },\n      controlStyle: {\n        color: '#316BF3',\n        borderColor: '#316BF3',\n        borderWidth: 2\n      }\n    },\n    progress: {\n      lineStyle: {\n        color: '#316BF3'\n      },\n      itemStyle: {\n        color: '#316BF3'\n      },\n      label: {\n        color: '#6f778d'\n      }\n    },\n    data: []\n  });\n  return SliderTimelineModel;\n}(TimelineModel);\nmixin(SliderTimelineModel, DataFormatMixin.prototype);\nexport default SliderTimelineModel;", "map": {"version": 3, "names": ["__extends", "TimelineModel", "DataFormatMixin", "mixin", "inheritDefaultOption", "SliderTimelineModel", "_super", "_this", "apply", "arguments", "type", "defaultOption", "backgroundColor", "borderColor", "borderWidth", "orient", "inverse", "tooltip", "trigger", "symbol", "symbolSize", "lineStyle", "show", "width", "color", "label", "position", "interval", "rotate", "itemStyle", "checkpointStyle", "<PERSON><PERSON><PERSON><PERSON>", "shadowOffsetX", "shadowOffsetY", "shadowColor", "animation", "animationDuration", "animationEasing", "controlStyle", "showPlayBtn", "showPrevBtn", "showNextBtn", "itemSize", "itemGap", "playIcon", "stopIcon", "nextIcon", "prevIcon", "prevBtnSize", "nextBtnSize", "emphasis", "progress", "data", "prototype"], "sources": ["E:/AI/SmartCV/node_modules/echarts/lib/component/timeline/SliderTimelineModel.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport TimelineModel from './TimelineModel.js';\nimport { DataFormatMixin } from '../../model/mixin/dataFormat.js';\nimport { mixin } from 'zrender/lib/core/util.js';\nimport { inheritDefaultOption } from '../../util/component.js';\nvar SliderTimelineModel = /** @class */function (_super) {\n  __extends(SliderTimelineModel, _super);\n  function SliderTimelineModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = SliderTimelineModel.type;\n    return _this;\n  }\n  SliderTimelineModel.type = 'timeline.slider';\n  /**\r\n   * @protected\r\n   */\n  SliderTimelineModel.defaultOption = inheritDefaultOption(TimelineModel.defaultOption, {\n    backgroundColor: 'rgba(0,0,0,0)',\n    borderColor: '#ccc',\n    borderWidth: 0,\n    orient: 'horizontal',\n    inverse: false,\n    tooltip: {\n      trigger: 'item' // data item may also have tootip attr.\n    },\n    symbol: 'circle',\n    symbolSize: 12,\n    lineStyle: {\n      show: true,\n      width: 2,\n      color: '#DAE1F5'\n    },\n    label: {\n      position: 'auto',\n      // When using number, label position is not\n      // restricted by viewRect.\n      // positive: right/bottom, negative: left/top\n      show: true,\n      interval: 'auto',\n      rotate: 0,\n      // formatter: null,\n      // 其余属性默认使用全局文本样式，详见TEXTSTYLE\n      color: '#A4B1D7'\n    },\n    itemStyle: {\n      color: '#A4B1D7',\n      borderWidth: 1\n    },\n    checkpointStyle: {\n      symbol: 'circle',\n      symbolSize: 15,\n      color: '#316bf3',\n      borderColor: '#fff',\n      borderWidth: 2,\n      shadowBlur: 2,\n      shadowOffsetX: 1,\n      shadowOffsetY: 1,\n      shadowColor: 'rgba(0, 0, 0, 0.3)',\n      // borderColor: 'rgba(194,53,49, 0.5)',\n      animation: true,\n      animationDuration: 300,\n      animationEasing: 'quinticInOut'\n    },\n    controlStyle: {\n      show: true,\n      showPlayBtn: true,\n      showPrevBtn: true,\n      showNextBtn: true,\n      itemSize: 24,\n      itemGap: 12,\n      position: 'left',\n      playIcon: 'path://M31.6,53C17.5,53,6,41.5,6,27.4S17.5,1.8,31.6,1.8C45.7,1.8,57.2,13.3,57.2,27.4S45.7,53,31.6,53z M31.6,3.3 C18.4,3.3,7.5,14.1,7.5,27.4c0,13.3,10.8,24.1,24.1,24.1C44.9,51.5,55.7,40.7,55.7,27.4C55.7,14.1,44.9,3.3,31.6,3.3z M24.9,21.3 c0-2.2,1.6-3.1,3.5-2l10.5,6.1c1.899,1.1,1.899,2.9,0,4l-10.5,6.1c-1.9,1.1-3.5,0.2-3.5-2V21.3z',\n      stopIcon: 'path://M30.9,53.2C16.8,53.2,5.3,41.7,5.3,27.6S16.8,2,30.9,2C45,2,56.4,13.5,56.4,27.6S45,53.2,30.9,53.2z M30.9,3.5C17.6,3.5,6.8,14.4,6.8,27.6c0,13.3,10.8,24.1,24.101,24.1C44.2,51.7,55,40.9,55,27.6C54.9,14.4,44.1,3.5,30.9,3.5z M36.9,35.8c0,0.601-0.4,1-0.9,1h-1.3c-0.5,0-0.9-0.399-0.9-1V19.5c0-0.6,0.4-1,0.9-1H36c0.5,0,0.9,0.4,0.9,1V35.8z M27.8,35.8 c0,0.601-0.4,1-0.9,1h-1.3c-0.5,0-0.9-0.399-0.9-1V19.5c0-0.6,0.4-1,0.9-1H27c0.5,0,0.9,0.4,0.9,1L27.8,35.8L27.8,35.8z',\n      // eslint-disable-next-line max-len\n      nextIcon: 'M2,18.5A1.52,1.52,0,0,1,.92,18a1.49,1.49,0,0,1,0-2.12L7.81,9.36,1,3.11A1.5,1.5,0,1,1,3,.89l8,7.34a1.48,1.48,0,0,1,.49,1.09,1.51,1.51,0,0,1-.46,1.1L3,18.08A1.5,1.5,0,0,1,2,18.5Z',\n      // eslint-disable-next-line max-len\n      prevIcon: 'M10,.5A1.52,1.52,0,0,1,11.08,1a1.49,1.49,0,0,1,0,2.12L4.19,9.64,11,15.89a1.5,1.5,0,1,1-2,2.22L1,10.77A1.48,1.48,0,0,1,.5,9.68,1.51,1.51,0,0,1,1,8.58L9,.92A1.5,1.5,0,0,1,10,.5Z',\n      prevBtnSize: 18,\n      nextBtnSize: 18,\n      color: '#A4B1D7',\n      borderColor: '#A4B1D7',\n      borderWidth: 1\n    },\n    emphasis: {\n      label: {\n        show: true,\n        // 其余属性默认使用全局文本样式，详见TEXTSTYLE\n        color: '#6f778d'\n      },\n      itemStyle: {\n        color: '#316BF3'\n      },\n      controlStyle: {\n        color: '#316BF3',\n        borderColor: '#316BF3',\n        borderWidth: 2\n      }\n    },\n    progress: {\n      lineStyle: {\n        color: '#316BF3'\n      },\n      itemStyle: {\n        color: '#316BF3'\n      },\n      label: {\n        color: '#6f778d'\n      }\n    },\n    data: []\n  });\n  return SliderTimelineModel;\n}(TimelineModel);\nmixin(SliderTimelineModel, DataFormatMixin.prototype);\nexport default SliderTimelineModel;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,SAASC,eAAe,QAAQ,iCAAiC;AACjE,SAASC,KAAK,QAAQ,0BAA0B;AAChD,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,IAAIC,mBAAmB,GAAG,aAAa,UAAUC,MAAM,EAAE;EACvDN,SAAS,CAACK,mBAAmB,EAAEC,MAAM,CAAC;EACtC,SAASD,mBAAmBA,CAAA,EAAG;IAC7B,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,mBAAmB,CAACK,IAAI;IACrC,OAAOH,KAAK;EACd;EACAF,mBAAmB,CAACK,IAAI,GAAG,iBAAiB;EAC5C;AACF;AACA;EACEL,mBAAmB,CAACM,aAAa,GAAGP,oBAAoB,CAACH,aAAa,CAACU,aAAa,EAAE;IACpFC,eAAe,EAAE,eAAe;IAChCC,WAAW,EAAE,MAAM;IACnBC,WAAW,EAAE,CAAC;IACdC,MAAM,EAAE,YAAY;IACpBC,OAAO,EAAE,KAAK;IACdC,OAAO,EAAE;MACPC,OAAO,EAAE,MAAM,CAAC;IAClB,CAAC;IACDC,MAAM,EAAE,QAAQ;IAChBC,UAAU,EAAE,EAAE;IACdC,SAAS,EAAE;MACTC,IAAI,EAAE,IAAI;MACVC,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE;IACT,CAAC;IACDC,KAAK,EAAE;MACLC,QAAQ,EAAE,MAAM;MAChB;MACA;MACA;MACAJ,IAAI,EAAE,IAAI;MACVK,QAAQ,EAAE,MAAM;MAChBC,MAAM,EAAE,CAAC;MACT;MACA;MACAJ,KAAK,EAAE;IACT,CAAC;IACDK,SAAS,EAAE;MACTL,KAAK,EAAE,SAAS;MAChBV,WAAW,EAAE;IACf,CAAC;IACDgB,eAAe,EAAE;MACfX,MAAM,EAAE,QAAQ;MAChBC,UAAU,EAAE,EAAE;MACdI,KAAK,EAAE,SAAS;MAChBX,WAAW,EAAE,MAAM;MACnBC,WAAW,EAAE,CAAC;MACdiB,UAAU,EAAE,CAAC;MACbC,aAAa,EAAE,CAAC;MAChBC,aAAa,EAAE,CAAC;MAChBC,WAAW,EAAE,oBAAoB;MACjC;MACAC,SAAS,EAAE,IAAI;MACfC,iBAAiB,EAAE,GAAG;MACtBC,eAAe,EAAE;IACnB,CAAC;IACDC,YAAY,EAAE;MACZhB,IAAI,EAAE,IAAI;MACViB,WAAW,EAAE,IAAI;MACjBC,WAAW,EAAE,IAAI;MACjBC,WAAW,EAAE,IAAI;MACjBC,QAAQ,EAAE,EAAE;MACZC,OAAO,EAAE,EAAE;MACXjB,QAAQ,EAAE,MAAM;MAChBkB,QAAQ,EAAE,2UAA2U;MACrVC,QAAQ,EAAE,gdAAgd;MAC1d;MACAC,QAAQ,EAAE,kLAAkL;MAC5L;MACAC,QAAQ,EAAE,iLAAiL;MAC3LC,WAAW,EAAE,EAAE;MACfC,WAAW,EAAE,EAAE;MACfzB,KAAK,EAAE,SAAS;MAChBX,WAAW,EAAE,SAAS;MACtBC,WAAW,EAAE;IACf,CAAC;IACDoC,QAAQ,EAAE;MACRzB,KAAK,EAAE;QACLH,IAAI,EAAE,IAAI;QACV;QACAE,KAAK,EAAE;MACT,CAAC;MACDK,SAAS,EAAE;QACTL,KAAK,EAAE;MACT,CAAC;MACDc,YAAY,EAAE;QACZd,KAAK,EAAE,SAAS;QAChBX,WAAW,EAAE,SAAS;QACtBC,WAAW,EAAE;MACf;IACF,CAAC;IACDqC,QAAQ,EAAE;MACR9B,SAAS,EAAE;QACTG,KAAK,EAAE;MACT,CAAC;MACDK,SAAS,EAAE;QACTL,KAAK,EAAE;MACT,CAAC;MACDC,KAAK,EAAE;QACLD,KAAK,EAAE;MACT;IACF,CAAC;IACD4B,IAAI,EAAE;EACR,CAAC,CAAC;EACF,OAAO/C,mBAAmB;AAC5B,CAAC,CAACJ,aAAa,CAAC;AAChBE,KAAK,CAACE,mBAAmB,EAAEH,eAAe,CAACmD,SAAS,CAAC;AACrD,eAAehD,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}