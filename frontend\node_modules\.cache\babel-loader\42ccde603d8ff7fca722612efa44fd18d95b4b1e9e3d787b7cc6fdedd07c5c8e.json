{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { isString, indexOf, each, bind, isFunction, isArray, isDom, retrieve2 } from 'zrender/lib/core/util.js';\nimport { normalizeEvent } from 'zrender/lib/core/event.js';\nimport { transformLocalCoord } from 'zrender/lib/core/dom.js';\nimport env from 'zrender/lib/core/env.js';\nimport { convertToColorString, toCamelCase, normalizeCssArray } from '../../util/format.js';\nimport { shouldTooltipConfine, toCSSVendorPrefix, getComputedStyle, TRANSFORM_VENDOR, TRANSITION_VENDOR } from './helper.js';\nimport { getPaddingFromTooltipModel } from './tooltipMarkup.js';\n/* global document, window */\nvar CSS_TRANSITION_VENDOR = toCSSVendorPrefix(TRANSITION_VENDOR, 'transition');\nvar CSS_TRANSFORM_VENDOR = toCSSVendorPrefix(TRANSFORM_VENDOR, 'transform');\n// eslint-disable-next-line\nvar gCssText = \"position:absolute;display:block;border-style:solid;white-space:nowrap;z-index:9999999;\" + (env.transform3dSupported ? 'will-change:transform;' : '');\nfunction mirrorPos(pos) {\n  pos = pos === 'left' ? 'right' : pos === 'right' ? 'left' : pos === 'top' ? 'bottom' : 'top';\n  return pos;\n}\nfunction assembleArrow(tooltipModel, borderColor, arrowPosition) {\n  if (!isString(arrowPosition) || arrowPosition === 'inside') {\n    return '';\n  }\n  var backgroundColor = tooltipModel.get('backgroundColor');\n  var borderWidth = tooltipModel.get('borderWidth');\n  borderColor = convertToColorString(borderColor);\n  var arrowPos = mirrorPos(arrowPosition);\n  var arrowSize = Math.max(Math.round(borderWidth) * 1.5, 6);\n  var positionStyle = '';\n  var transformStyle = CSS_TRANSFORM_VENDOR + ':';\n  var rotateDeg;\n  if (indexOf(['left', 'right'], arrowPos) > -1) {\n    positionStyle += 'top:50%';\n    transformStyle += \"translateY(-50%) rotate(\" + (rotateDeg = arrowPos === 'left' ? -225 : -45) + \"deg)\";\n  } else {\n    positionStyle += 'left:50%';\n    transformStyle += \"translateX(-50%) rotate(\" + (rotateDeg = arrowPos === 'top' ? 225 : 45) + \"deg)\";\n  }\n  var rotateRadian = rotateDeg * Math.PI / 180;\n  var arrowWH = arrowSize + borderWidth;\n  var rotatedWH = arrowWH * Math.abs(Math.cos(rotateRadian)) + arrowWH * Math.abs(Math.sin(rotateRadian));\n  var arrowOffset = Math.round(((rotatedWH - Math.SQRT2 * borderWidth) / 2 + Math.SQRT2 * borderWidth - (rotatedWH - arrowWH) / 2) * 100) / 100;\n  positionStyle += \";\" + arrowPos + \":-\" + arrowOffset + \"px\";\n  var borderStyle = borderColor + \" solid \" + borderWidth + \"px;\";\n  var styleCss = [\"position:absolute;width:\" + arrowSize + \"px;height:\" + arrowSize + \"px;z-index:-1;\", positionStyle + \";\" + transformStyle + \";\", \"border-bottom:\" + borderStyle, \"border-right:\" + borderStyle, \"background-color:\" + backgroundColor + \";\"];\n  return \"<div style=\\\"\" + styleCss.join('') + \"\\\"></div>\";\n}\nfunction assembleTransition(duration, onlyFade) {\n  var transitionCurve = 'cubic-bezier(0.23,1,0.32,1)';\n  var transitionOption = \" \" + duration / 2 + \"s \" + transitionCurve;\n  var transitionText = \"opacity\" + transitionOption + \",visibility\" + transitionOption;\n  if (!onlyFade) {\n    transitionOption = \" \" + duration + \"s \" + transitionCurve;\n    transitionText += env.transformSupported ? \",\" + CSS_TRANSFORM_VENDOR + transitionOption : \",left\" + transitionOption + \",top\" + transitionOption;\n  }\n  return CSS_TRANSITION_VENDOR + ':' + transitionText;\n}\nfunction assembleTransform(x, y, toString) {\n  // If using float on style, the final width of the dom might\n  // keep changing slightly while mouse move. So `toFixed(0)` them.\n  var x0 = x.toFixed(0) + 'px';\n  var y0 = y.toFixed(0) + 'px';\n  // not support transform, use `left` and `top` instead.\n  if (!env.transformSupported) {\n    return toString ? \"top:\" + y0 + \";left:\" + x0 + \";\" : [['top', y0], ['left', x0]];\n  }\n  // support transform\n  var is3d = env.transform3dSupported;\n  var translate = \"translate\" + (is3d ? '3d' : '') + \"(\" + x0 + \",\" + y0 + (is3d ? ',0' : '') + \")\";\n  return toString ? 'top:0;left:0;' + CSS_TRANSFORM_VENDOR + ':' + translate + ';' : [['top', 0], ['left', 0], [TRANSFORM_VENDOR, translate]];\n}\n/**\r\n * @param {Object} textStyle\r\n * @return {string}\r\n * @inner\r\n */\nfunction assembleFont(textStyleModel) {\n  var cssText = [];\n  var fontSize = textStyleModel.get('fontSize');\n  var color = textStyleModel.getTextColor();\n  color && cssText.push('color:' + color);\n  cssText.push('font:' + textStyleModel.getFont());\n  // @ts-ignore, leave it to the tooltip refactor.\n  var lineHeight = retrieve2(textStyleModel.get('lineHeight'), Math.round(fontSize * 3 / 2));\n  fontSize && cssText.push('line-height:' + lineHeight + 'px');\n  var shadowColor = textStyleModel.get('textShadowColor');\n  var shadowBlur = textStyleModel.get('textShadowBlur') || 0;\n  var shadowOffsetX = textStyleModel.get('textShadowOffsetX') || 0;\n  var shadowOffsetY = textStyleModel.get('textShadowOffsetY') || 0;\n  shadowColor && shadowBlur && cssText.push('text-shadow:' + shadowOffsetX + 'px ' + shadowOffsetY + 'px ' + shadowBlur + 'px ' + shadowColor);\n  each(['decoration', 'align'], function (name) {\n    var val = textStyleModel.get(name);\n    val && cssText.push('text-' + name + ':' + val);\n  });\n  return cssText.join(';');\n}\nfunction assembleCssText(tooltipModel, enableTransition, onlyFade) {\n  var cssText = [];\n  var transitionDuration = tooltipModel.get('transitionDuration');\n  var backgroundColor = tooltipModel.get('backgroundColor');\n  var shadowBlur = tooltipModel.get('shadowBlur');\n  var shadowColor = tooltipModel.get('shadowColor');\n  var shadowOffsetX = tooltipModel.get('shadowOffsetX');\n  var shadowOffsetY = tooltipModel.get('shadowOffsetY');\n  var textStyleModel = tooltipModel.getModel('textStyle');\n  var padding = getPaddingFromTooltipModel(tooltipModel, 'html');\n  var boxShadow = shadowOffsetX + \"px \" + shadowOffsetY + \"px \" + shadowBlur + \"px \" + shadowColor;\n  cssText.push('box-shadow:' + boxShadow);\n  // Animation transition. Do not animate when transitionDuration is 0.\n  enableTransition && transitionDuration && cssText.push(assembleTransition(transitionDuration, onlyFade));\n  if (backgroundColor) {\n    cssText.push('background-color:' + backgroundColor);\n  }\n  // Border style\n  each(['width', 'color', 'radius'], function (name) {\n    var borderName = 'border-' + name;\n    var camelCase = toCamelCase(borderName);\n    var val = tooltipModel.get(camelCase);\n    val != null && cssText.push(borderName + ':' + val + (name === 'color' ? '' : 'px'));\n  });\n  // Text style\n  cssText.push(assembleFont(textStyleModel));\n  // Padding\n  if (padding != null) {\n    cssText.push('padding:' + normalizeCssArray(padding).join('px ') + 'px');\n  }\n  return cssText.join(';') + ';';\n}\n// If not able to make, do not modify the input `out`.\nfunction makeStyleCoord(out, zr, container, zrX, zrY) {\n  var zrPainter = zr && zr.painter;\n  if (container) {\n    var zrViewportRoot = zrPainter && zrPainter.getViewportRoot();\n    if (zrViewportRoot) {\n      // Some APPs might use scale on body, so we support CSS transform here.\n      transformLocalCoord(out, zrViewportRoot, container, zrX, zrY);\n    }\n  } else {\n    out[0] = zrX;\n    out[1] = zrY;\n    // xy should be based on canvas root. But tooltipContent is\n    // the sibling of canvas root. So padding of ec container\n    // should be considered here.\n    var viewportRootOffset = zrPainter && zrPainter.getViewportRootOffset();\n    if (viewportRootOffset) {\n      out[0] += viewportRootOffset.offsetLeft;\n      out[1] += viewportRootOffset.offsetTop;\n    }\n  }\n  out[2] = out[0] / zr.getWidth();\n  out[3] = out[1] / zr.getHeight();\n}\nvar TooltipHTMLContent = /** @class */function () {\n  function TooltipHTMLContent(api, opt) {\n    this._show = false;\n    this._styleCoord = [0, 0, 0, 0];\n    this._enterable = true;\n    this._alwaysShowContent = false;\n    this._firstShow = true;\n    this._longHide = true;\n    if (env.wxa) {\n      return null;\n    }\n    var el = document.createElement('div');\n    // TODO: TYPE\n    el.domBelongToZr = true;\n    this.el = el;\n    var zr = this._zr = api.getZr();\n    var appendTo = opt.appendTo;\n    var container = appendTo && (isString(appendTo) ? document.querySelector(appendTo) : isDom(appendTo) ? appendTo : isFunction(appendTo) && appendTo(api.getDom()));\n    makeStyleCoord(this._styleCoord, zr, container, api.getWidth() / 2, api.getHeight() / 2);\n    (container || api.getDom()).appendChild(el);\n    this._api = api;\n    this._container = container;\n    // FIXME\n    // Is it needed to trigger zr event manually if\n    // the browser do not support `pointer-events: none`.\n    var self = this;\n    el.onmouseenter = function () {\n      // clear the timeout in hideLater and keep showing tooltip\n      if (self._enterable) {\n        clearTimeout(self._hideTimeout);\n        self._show = true;\n      }\n      self._inContent = true;\n    };\n    el.onmousemove = function (e) {\n      e = e || window.event;\n      if (!self._enterable) {\n        // `pointer-events: none` is set to tooltip content div\n        // if `enterable` is set as `false`, and `el.onmousemove`\n        // can not be triggered. But in browser that do not\n        // support `pointer-events`, we need to do this:\n        // Try trigger zrender event to avoid mouse\n        // in and out shape too frequently\n        var handler = zr.handler;\n        var zrViewportRoot = zr.painter.getViewportRoot();\n        normalizeEvent(zrViewportRoot, e, true);\n        handler.dispatch('mousemove', e);\n      }\n    };\n    el.onmouseleave = function () {\n      // set `_inContent` to `false` before `hideLater`\n      self._inContent = false;\n      if (self._enterable) {\n        if (self._show) {\n          self.hideLater(self._hideDelay);\n        }\n      }\n    };\n  }\n  /**\r\n   * Update when tooltip is rendered\r\n   */\n  TooltipHTMLContent.prototype.update = function (tooltipModel) {\n    // FIXME\n    // Move this logic to ec main?\n    if (!this._container) {\n      var container = this._api.getDom();\n      var position = getComputedStyle(container, 'position');\n      var domStyle = container.style;\n      if (domStyle.position !== 'absolute' && position !== 'absolute') {\n        domStyle.position = 'relative';\n      }\n    }\n    // move tooltip if chart resized\n    var alwaysShowContent = tooltipModel.get('alwaysShowContent');\n    alwaysShowContent && this._moveIfResized();\n    // update alwaysShowContent\n    this._alwaysShowContent = alwaysShowContent;\n    // update className\n    this.el.className = tooltipModel.get('className') || '';\n    // Hide the tooltip\n    // PENDING\n    // this.hide();\n  };\n  TooltipHTMLContent.prototype.show = function (tooltipModel, nearPointColor) {\n    clearTimeout(this._hideTimeout);\n    clearTimeout(this._longHideTimeout);\n    var el = this.el;\n    var style = el.style;\n    var styleCoord = this._styleCoord;\n    if (!el.innerHTML) {\n      style.display = 'none';\n    } else {\n      style.cssText = gCssText + assembleCssText(tooltipModel, !this._firstShow, this._longHide)\n      // initial transform\n      + assembleTransform(styleCoord[0], styleCoord[1], true) + (\"border-color:\" + convertToColorString(nearPointColor) + \";\") + (tooltipModel.get('extraCssText') || '')\n      // If mouse occasionally move over the tooltip, a mouseout event will be\n      // triggered by canvas, and cause some unexpectable result like dragging\n      // stop, \"unfocusAdjacency\". Here `pointer-events: none` is used to solve\n      // it. Although it is not supported by IE8~IE10, fortunately it is a rare\n      // scenario.\n      + (\";pointer-events:\" + (this._enterable ? 'auto' : 'none'));\n    }\n    this._show = true;\n    this._firstShow = false;\n    this._longHide = false;\n  };\n  TooltipHTMLContent.prototype.setContent = function (content, markers, tooltipModel, borderColor, arrowPosition) {\n    var el = this.el;\n    if (content == null) {\n      el.innerHTML = '';\n      return;\n    }\n    var arrow = '';\n    if (isString(arrowPosition) && tooltipModel.get('trigger') === 'item' && !shouldTooltipConfine(tooltipModel)) {\n      arrow = assembleArrow(tooltipModel, borderColor, arrowPosition);\n    }\n    if (isString(content)) {\n      el.innerHTML = content + arrow;\n    } else if (content) {\n      // Clear previous\n      el.innerHTML = '';\n      if (!isArray(content)) {\n        content = [content];\n      }\n      for (var i = 0; i < content.length; i++) {\n        if (isDom(content[i]) && content[i].parentNode !== el) {\n          el.appendChild(content[i]);\n        }\n      }\n      // no arrow if empty\n      if (arrow && el.childNodes.length) {\n        // no need to create a new parent element, but it's not supported by IE 10 and older.\n        // const arrowEl = document.createRange().createContextualFragment(arrow);\n        var arrowEl = document.createElement('div');\n        arrowEl.innerHTML = arrow;\n        el.appendChild(arrowEl);\n      }\n    }\n  };\n  TooltipHTMLContent.prototype.setEnterable = function (enterable) {\n    this._enterable = enterable;\n  };\n  TooltipHTMLContent.prototype.getSize = function () {\n    var el = this.el;\n    return el ? [el.offsetWidth, el.offsetHeight] : [0, 0];\n  };\n  TooltipHTMLContent.prototype.moveTo = function (zrX, zrY) {\n    if (!this.el) {\n      return;\n    }\n    var styleCoord = this._styleCoord;\n    makeStyleCoord(styleCoord, this._zr, this._container, zrX, zrY);\n    if (styleCoord[0] != null && styleCoord[1] != null) {\n      var style_1 = this.el.style;\n      var transforms = assembleTransform(styleCoord[0], styleCoord[1]);\n      each(transforms, function (transform) {\n        style_1[transform[0]] = transform[1];\n      });\n    }\n  };\n  /**\r\n   * when `alwaysShowContent` is true,\r\n   * move the tooltip after chart resized\r\n   */\n  TooltipHTMLContent.prototype._moveIfResized = function () {\n    // The ratio of left to width\n    var ratioX = this._styleCoord[2];\n    // The ratio of top to height\n    var ratioY = this._styleCoord[3];\n    this.moveTo(ratioX * this._zr.getWidth(), ratioY * this._zr.getHeight());\n  };\n  TooltipHTMLContent.prototype.hide = function () {\n    var _this = this;\n    var style = this.el.style;\n    style.visibility = 'hidden';\n    style.opacity = '0';\n    env.transform3dSupported && (style.willChange = '');\n    this._show = false;\n    this._longHideTimeout = setTimeout(function () {\n      return _this._longHide = true;\n    }, 500);\n  };\n  TooltipHTMLContent.prototype.hideLater = function (time) {\n    if (this._show && !(this._inContent && this._enterable) && !this._alwaysShowContent) {\n      if (time) {\n        this._hideDelay = time;\n        // Set show false to avoid invoke hideLater multiple times\n        this._show = false;\n        this._hideTimeout = setTimeout(bind(this.hide, this), time);\n      } else {\n        this.hide();\n      }\n    }\n  };\n  TooltipHTMLContent.prototype.isShow = function () {\n    return this._show;\n  };\n  TooltipHTMLContent.prototype.dispose = function () {\n    clearTimeout(this._hideTimeout);\n    clearTimeout(this._longHideTimeout);\n    var parentNode = this.el.parentNode;\n    parentNode && parentNode.removeChild(this.el);\n    this.el = this._container = null;\n  };\n  return TooltipHTMLContent;\n}();\nexport default TooltipHTMLContent;", "map": {"version": 3, "names": ["isString", "indexOf", "each", "bind", "isFunction", "isArray", "isDom", "retrieve2", "normalizeEvent", "transformLocalCoord", "env", "convertToColorString", "toCamelCase", "normalizeCssArray", "shouldTooltipConfine", "toCSSVendorPrefix", "getComputedStyle", "TRANSFORM_VENDOR", "TRANSITION_VENDOR", "getPaddingFromTooltipModel", "CSS_TRANSITION_VENDOR", "CSS_TRANSFORM_VENDOR", "gCssText", "transform3dSupported", "mirrorPos", "pos", "assembleArrow", "tooltipModel", "borderColor", "arrowPosition", "backgroundColor", "get", "borderWidth", "arrowPos", "arrowSize", "Math", "max", "round", "positionStyle", "transformStyle", "rotateDeg", "rotateRadian", "PI", "arrowWH", "rotatedWH", "abs", "cos", "sin", "arrowOffset", "SQRT2", "borderStyle", "styleCss", "join", "assembleTransition", "duration", "onlyFade", "transitionCurve", "transitionOption", "transitionText", "transformSupported", "assembleTransform", "x", "y", "toString", "x0", "toFixed", "y0", "is3d", "translate", "assembleFont", "textStyleModel", "cssText", "fontSize", "color", "getTextColor", "push", "getFont", "lineHeight", "shadowColor", "<PERSON><PERSON><PERSON><PERSON>", "shadowOffsetX", "shadowOffsetY", "name", "val", "assembleCssText", "enableTransition", "transitionDuration", "getModel", "padding", "boxShadow", "borderName", "camelCase", "makeStyleCoord", "out", "zr", "container", "zrX", "zrY", "zrPainter", "painter", "zrViewportRoot", "getViewportRoot", "viewportRootOffset", "getViewportRootOffset", "offsetLeft", "offsetTop", "getWidth", "getHeight", "TooltipHTMLContent", "api", "opt", "_show", "_styleCoord", "_enterable", "_alwaysShowContent", "_firstShow", "_longHide", "wxa", "el", "document", "createElement", "domBelongToZr", "_zr", "getZr", "appendTo", "querySelector", "getDom", "append<PERSON><PERSON><PERSON>", "_api", "_container", "self", "onmouseenter", "clearTimeout", "_hideTimeout", "_inContent", "<PERSON><PERSON><PERSON><PERSON>", "e", "window", "event", "handler", "dispatch", "onmouseleave", "hideLater", "_hideDelay", "prototype", "update", "position", "domStyle", "style", "alwaysS<PERSON><PERSON><PERSON>nt", "_moveIfResized", "className", "show", "nearPointColor", "_longHideTimeout", "styleCoord", "innerHTML", "display", "<PERSON><PERSON><PERSON><PERSON>", "content", "markers", "arrow", "i", "length", "parentNode", "childNodes", "arrowEl", "setEnterable", "enterable", "getSize", "offsetWidth", "offsetHeight", "moveTo", "style_1", "transforms", "transform", "ratioX", "ratioY", "hide", "_this", "visibility", "opacity", "<PERSON><PERSON><PERSON><PERSON>", "setTimeout", "time", "isShow", "dispose", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["E:/AI/SmartCV/node_modules/echarts/lib/component/tooltip/TooltipHTMLContent.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { isString, indexOf, each, bind, isFunction, isArray, isDom, retrieve2 } from 'zrender/lib/core/util.js';\nimport { normalizeEvent } from 'zrender/lib/core/event.js';\nimport { transformLocalCoord } from 'zrender/lib/core/dom.js';\nimport env from 'zrender/lib/core/env.js';\nimport { convertToColorString, toCamelCase, normalizeCssArray } from '../../util/format.js';\nimport { shouldTooltipConfine, toCSSVendorPrefix, getComputedStyle, TRANSFORM_VENDOR, TRANSITION_VENDOR } from './helper.js';\nimport { getPaddingFromTooltipModel } from './tooltipMarkup.js';\n/* global document, window */\nvar CSS_TRANSITION_VENDOR = toCSSVendorPrefix(TRANSITION_VENDOR, 'transition');\nvar CSS_TRANSFORM_VENDOR = toCSSVendorPrefix(TRANSFORM_VENDOR, 'transform');\n// eslint-disable-next-line\nvar gCssText = \"position:absolute;display:block;border-style:solid;white-space:nowrap;z-index:9999999;\" + (env.transform3dSupported ? 'will-change:transform;' : '');\nfunction mirrorPos(pos) {\n  pos = pos === 'left' ? 'right' : pos === 'right' ? 'left' : pos === 'top' ? 'bottom' : 'top';\n  return pos;\n}\nfunction assembleArrow(tooltipModel, borderColor, arrowPosition) {\n  if (!isString(arrowPosition) || arrowPosition === 'inside') {\n    return '';\n  }\n  var backgroundColor = tooltipModel.get('backgroundColor');\n  var borderWidth = tooltipModel.get('borderWidth');\n  borderColor = convertToColorString(borderColor);\n  var arrowPos = mirrorPos(arrowPosition);\n  var arrowSize = Math.max(Math.round(borderWidth) * 1.5, 6);\n  var positionStyle = '';\n  var transformStyle = CSS_TRANSFORM_VENDOR + ':';\n  var rotateDeg;\n  if (indexOf(['left', 'right'], arrowPos) > -1) {\n    positionStyle += 'top:50%';\n    transformStyle += \"translateY(-50%) rotate(\" + (rotateDeg = arrowPos === 'left' ? -225 : -45) + \"deg)\";\n  } else {\n    positionStyle += 'left:50%';\n    transformStyle += \"translateX(-50%) rotate(\" + (rotateDeg = arrowPos === 'top' ? 225 : 45) + \"deg)\";\n  }\n  var rotateRadian = rotateDeg * Math.PI / 180;\n  var arrowWH = arrowSize + borderWidth;\n  var rotatedWH = arrowWH * Math.abs(Math.cos(rotateRadian)) + arrowWH * Math.abs(Math.sin(rotateRadian));\n  var arrowOffset = Math.round(((rotatedWH - Math.SQRT2 * borderWidth) / 2 + Math.SQRT2 * borderWidth - (rotatedWH - arrowWH) / 2) * 100) / 100;\n  positionStyle += \";\" + arrowPos + \":-\" + arrowOffset + \"px\";\n  var borderStyle = borderColor + \" solid \" + borderWidth + \"px;\";\n  var styleCss = [\"position:absolute;width:\" + arrowSize + \"px;height:\" + arrowSize + \"px;z-index:-1;\", positionStyle + \";\" + transformStyle + \";\", \"border-bottom:\" + borderStyle, \"border-right:\" + borderStyle, \"background-color:\" + backgroundColor + \";\"];\n  return \"<div style=\\\"\" + styleCss.join('') + \"\\\"></div>\";\n}\nfunction assembleTransition(duration, onlyFade) {\n  var transitionCurve = 'cubic-bezier(0.23,1,0.32,1)';\n  var transitionOption = \" \" + duration / 2 + \"s \" + transitionCurve;\n  var transitionText = \"opacity\" + transitionOption + \",visibility\" + transitionOption;\n  if (!onlyFade) {\n    transitionOption = \" \" + duration + \"s \" + transitionCurve;\n    transitionText += env.transformSupported ? \",\" + CSS_TRANSFORM_VENDOR + transitionOption : \",left\" + transitionOption + \",top\" + transitionOption;\n  }\n  return CSS_TRANSITION_VENDOR + ':' + transitionText;\n}\nfunction assembleTransform(x, y, toString) {\n  // If using float on style, the final width of the dom might\n  // keep changing slightly while mouse move. So `toFixed(0)` them.\n  var x0 = x.toFixed(0) + 'px';\n  var y0 = y.toFixed(0) + 'px';\n  // not support transform, use `left` and `top` instead.\n  if (!env.transformSupported) {\n    return toString ? \"top:\" + y0 + \";left:\" + x0 + \";\" : [['top', y0], ['left', x0]];\n  }\n  // support transform\n  var is3d = env.transform3dSupported;\n  var translate = \"translate\" + (is3d ? '3d' : '') + \"(\" + x0 + \",\" + y0 + (is3d ? ',0' : '') + \")\";\n  return toString ? 'top:0;left:0;' + CSS_TRANSFORM_VENDOR + ':' + translate + ';' : [['top', 0], ['left', 0], [TRANSFORM_VENDOR, translate]];\n}\n/**\r\n * @param {Object} textStyle\r\n * @return {string}\r\n * @inner\r\n */\nfunction assembleFont(textStyleModel) {\n  var cssText = [];\n  var fontSize = textStyleModel.get('fontSize');\n  var color = textStyleModel.getTextColor();\n  color && cssText.push('color:' + color);\n  cssText.push('font:' + textStyleModel.getFont());\n  // @ts-ignore, leave it to the tooltip refactor.\n  var lineHeight = retrieve2(textStyleModel.get('lineHeight'), Math.round(fontSize * 3 / 2));\n  fontSize && cssText.push('line-height:' + lineHeight + 'px');\n  var shadowColor = textStyleModel.get('textShadowColor');\n  var shadowBlur = textStyleModel.get('textShadowBlur') || 0;\n  var shadowOffsetX = textStyleModel.get('textShadowOffsetX') || 0;\n  var shadowOffsetY = textStyleModel.get('textShadowOffsetY') || 0;\n  shadowColor && shadowBlur && cssText.push('text-shadow:' + shadowOffsetX + 'px ' + shadowOffsetY + 'px ' + shadowBlur + 'px ' + shadowColor);\n  each(['decoration', 'align'], function (name) {\n    var val = textStyleModel.get(name);\n    val && cssText.push('text-' + name + ':' + val);\n  });\n  return cssText.join(';');\n}\nfunction assembleCssText(tooltipModel, enableTransition, onlyFade) {\n  var cssText = [];\n  var transitionDuration = tooltipModel.get('transitionDuration');\n  var backgroundColor = tooltipModel.get('backgroundColor');\n  var shadowBlur = tooltipModel.get('shadowBlur');\n  var shadowColor = tooltipModel.get('shadowColor');\n  var shadowOffsetX = tooltipModel.get('shadowOffsetX');\n  var shadowOffsetY = tooltipModel.get('shadowOffsetY');\n  var textStyleModel = tooltipModel.getModel('textStyle');\n  var padding = getPaddingFromTooltipModel(tooltipModel, 'html');\n  var boxShadow = shadowOffsetX + \"px \" + shadowOffsetY + \"px \" + shadowBlur + \"px \" + shadowColor;\n  cssText.push('box-shadow:' + boxShadow);\n  // Animation transition. Do not animate when transitionDuration is 0.\n  enableTransition && transitionDuration && cssText.push(assembleTransition(transitionDuration, onlyFade));\n  if (backgroundColor) {\n    cssText.push('background-color:' + backgroundColor);\n  }\n  // Border style\n  each(['width', 'color', 'radius'], function (name) {\n    var borderName = 'border-' + name;\n    var camelCase = toCamelCase(borderName);\n    var val = tooltipModel.get(camelCase);\n    val != null && cssText.push(borderName + ':' + val + (name === 'color' ? '' : 'px'));\n  });\n  // Text style\n  cssText.push(assembleFont(textStyleModel));\n  // Padding\n  if (padding != null) {\n    cssText.push('padding:' + normalizeCssArray(padding).join('px ') + 'px');\n  }\n  return cssText.join(';') + ';';\n}\n// If not able to make, do not modify the input `out`.\nfunction makeStyleCoord(out, zr, container, zrX, zrY) {\n  var zrPainter = zr && zr.painter;\n  if (container) {\n    var zrViewportRoot = zrPainter && zrPainter.getViewportRoot();\n    if (zrViewportRoot) {\n      // Some APPs might use scale on body, so we support CSS transform here.\n      transformLocalCoord(out, zrViewportRoot, container, zrX, zrY);\n    }\n  } else {\n    out[0] = zrX;\n    out[1] = zrY;\n    // xy should be based on canvas root. But tooltipContent is\n    // the sibling of canvas root. So padding of ec container\n    // should be considered here.\n    var viewportRootOffset = zrPainter && zrPainter.getViewportRootOffset();\n    if (viewportRootOffset) {\n      out[0] += viewportRootOffset.offsetLeft;\n      out[1] += viewportRootOffset.offsetTop;\n    }\n  }\n  out[2] = out[0] / zr.getWidth();\n  out[3] = out[1] / zr.getHeight();\n}\nvar TooltipHTMLContent = /** @class */function () {\n  function TooltipHTMLContent(api, opt) {\n    this._show = false;\n    this._styleCoord = [0, 0, 0, 0];\n    this._enterable = true;\n    this._alwaysShowContent = false;\n    this._firstShow = true;\n    this._longHide = true;\n    if (env.wxa) {\n      return null;\n    }\n    var el = document.createElement('div');\n    // TODO: TYPE\n    el.domBelongToZr = true;\n    this.el = el;\n    var zr = this._zr = api.getZr();\n    var appendTo = opt.appendTo;\n    var container = appendTo && (isString(appendTo) ? document.querySelector(appendTo) : isDom(appendTo) ? appendTo : isFunction(appendTo) && appendTo(api.getDom()));\n    makeStyleCoord(this._styleCoord, zr, container, api.getWidth() / 2, api.getHeight() / 2);\n    (container || api.getDom()).appendChild(el);\n    this._api = api;\n    this._container = container;\n    // FIXME\n    // Is it needed to trigger zr event manually if\n    // the browser do not support `pointer-events: none`.\n    var self = this;\n    el.onmouseenter = function () {\n      // clear the timeout in hideLater and keep showing tooltip\n      if (self._enterable) {\n        clearTimeout(self._hideTimeout);\n        self._show = true;\n      }\n      self._inContent = true;\n    };\n    el.onmousemove = function (e) {\n      e = e || window.event;\n      if (!self._enterable) {\n        // `pointer-events: none` is set to tooltip content div\n        // if `enterable` is set as `false`, and `el.onmousemove`\n        // can not be triggered. But in browser that do not\n        // support `pointer-events`, we need to do this:\n        // Try trigger zrender event to avoid mouse\n        // in and out shape too frequently\n        var handler = zr.handler;\n        var zrViewportRoot = zr.painter.getViewportRoot();\n        normalizeEvent(zrViewportRoot, e, true);\n        handler.dispatch('mousemove', e);\n      }\n    };\n    el.onmouseleave = function () {\n      // set `_inContent` to `false` before `hideLater`\n      self._inContent = false;\n      if (self._enterable) {\n        if (self._show) {\n          self.hideLater(self._hideDelay);\n        }\n      }\n    };\n  }\n  /**\r\n   * Update when tooltip is rendered\r\n   */\n  TooltipHTMLContent.prototype.update = function (tooltipModel) {\n    // FIXME\n    // Move this logic to ec main?\n    if (!this._container) {\n      var container = this._api.getDom();\n      var position = getComputedStyle(container, 'position');\n      var domStyle = container.style;\n      if (domStyle.position !== 'absolute' && position !== 'absolute') {\n        domStyle.position = 'relative';\n      }\n    }\n    // move tooltip if chart resized\n    var alwaysShowContent = tooltipModel.get('alwaysShowContent');\n    alwaysShowContent && this._moveIfResized();\n    // update alwaysShowContent\n    this._alwaysShowContent = alwaysShowContent;\n    // update className\n    this.el.className = tooltipModel.get('className') || '';\n    // Hide the tooltip\n    // PENDING\n    // this.hide();\n  };\n  TooltipHTMLContent.prototype.show = function (tooltipModel, nearPointColor) {\n    clearTimeout(this._hideTimeout);\n    clearTimeout(this._longHideTimeout);\n    var el = this.el;\n    var style = el.style;\n    var styleCoord = this._styleCoord;\n    if (!el.innerHTML) {\n      style.display = 'none';\n    } else {\n      style.cssText = gCssText + assembleCssText(tooltipModel, !this._firstShow, this._longHide)\n      // initial transform\n      + assembleTransform(styleCoord[0], styleCoord[1], true) + (\"border-color:\" + convertToColorString(nearPointColor) + \";\") + (tooltipModel.get('extraCssText') || '')\n      // If mouse occasionally move over the tooltip, a mouseout event will be\n      // triggered by canvas, and cause some unexpectable result like dragging\n      // stop, \"unfocusAdjacency\". Here `pointer-events: none` is used to solve\n      // it. Although it is not supported by IE8~IE10, fortunately it is a rare\n      // scenario.\n      + (\";pointer-events:\" + (this._enterable ? 'auto' : 'none'));\n    }\n    this._show = true;\n    this._firstShow = false;\n    this._longHide = false;\n  };\n  TooltipHTMLContent.prototype.setContent = function (content, markers, tooltipModel, borderColor, arrowPosition) {\n    var el = this.el;\n    if (content == null) {\n      el.innerHTML = '';\n      return;\n    }\n    var arrow = '';\n    if (isString(arrowPosition) && tooltipModel.get('trigger') === 'item' && !shouldTooltipConfine(tooltipModel)) {\n      arrow = assembleArrow(tooltipModel, borderColor, arrowPosition);\n    }\n    if (isString(content)) {\n      el.innerHTML = content + arrow;\n    } else if (content) {\n      // Clear previous\n      el.innerHTML = '';\n      if (!isArray(content)) {\n        content = [content];\n      }\n      for (var i = 0; i < content.length; i++) {\n        if (isDom(content[i]) && content[i].parentNode !== el) {\n          el.appendChild(content[i]);\n        }\n      }\n      // no arrow if empty\n      if (arrow && el.childNodes.length) {\n        // no need to create a new parent element, but it's not supported by IE 10 and older.\n        // const arrowEl = document.createRange().createContextualFragment(arrow);\n        var arrowEl = document.createElement('div');\n        arrowEl.innerHTML = arrow;\n        el.appendChild(arrowEl);\n      }\n    }\n  };\n  TooltipHTMLContent.prototype.setEnterable = function (enterable) {\n    this._enterable = enterable;\n  };\n  TooltipHTMLContent.prototype.getSize = function () {\n    var el = this.el;\n    return el ? [el.offsetWidth, el.offsetHeight] : [0, 0];\n  };\n  TooltipHTMLContent.prototype.moveTo = function (zrX, zrY) {\n    if (!this.el) {\n      return;\n    }\n    var styleCoord = this._styleCoord;\n    makeStyleCoord(styleCoord, this._zr, this._container, zrX, zrY);\n    if (styleCoord[0] != null && styleCoord[1] != null) {\n      var style_1 = this.el.style;\n      var transforms = assembleTransform(styleCoord[0], styleCoord[1]);\n      each(transforms, function (transform) {\n        style_1[transform[0]] = transform[1];\n      });\n    }\n  };\n  /**\r\n   * when `alwaysShowContent` is true,\r\n   * move the tooltip after chart resized\r\n   */\n  TooltipHTMLContent.prototype._moveIfResized = function () {\n    // The ratio of left to width\n    var ratioX = this._styleCoord[2];\n    // The ratio of top to height\n    var ratioY = this._styleCoord[3];\n    this.moveTo(ratioX * this._zr.getWidth(), ratioY * this._zr.getHeight());\n  };\n  TooltipHTMLContent.prototype.hide = function () {\n    var _this = this;\n    var style = this.el.style;\n    style.visibility = 'hidden';\n    style.opacity = '0';\n    env.transform3dSupported && (style.willChange = '');\n    this._show = false;\n    this._longHideTimeout = setTimeout(function () {\n      return _this._longHide = true;\n    }, 500);\n  };\n  TooltipHTMLContent.prototype.hideLater = function (time) {\n    if (this._show && !(this._inContent && this._enterable) && !this._alwaysShowContent) {\n      if (time) {\n        this._hideDelay = time;\n        // Set show false to avoid invoke hideLater multiple times\n        this._show = false;\n        this._hideTimeout = setTimeout(bind(this.hide, this), time);\n      } else {\n        this.hide();\n      }\n    }\n  };\n  TooltipHTMLContent.prototype.isShow = function () {\n    return this._show;\n  };\n  TooltipHTMLContent.prototype.dispose = function () {\n    clearTimeout(this._hideTimeout);\n    clearTimeout(this._longHideTimeout);\n    var parentNode = this.el.parentNode;\n    parentNode && parentNode.removeChild(this.el);\n    this.el = this._container = null;\n  };\n  return TooltipHTMLContent;\n}();\nexport default TooltipHTMLContent;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,QAAQ,EAAEC,OAAO,EAAEC,IAAI,EAAEC,IAAI,EAAEC,UAAU,EAAEC,OAAO,EAAEC,KAAK,EAAEC,SAAS,QAAQ,0BAA0B;AAC/G,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,OAAOC,GAAG,MAAM,yBAAyB;AACzC,SAASC,oBAAoB,EAAEC,WAAW,EAAEC,iBAAiB,QAAQ,sBAAsB;AAC3F,SAASC,oBAAoB,EAAEC,iBAAiB,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,iBAAiB,QAAQ,aAAa;AAC5H,SAASC,0BAA0B,QAAQ,oBAAoB;AAC/D;AACA,IAAIC,qBAAqB,GAAGL,iBAAiB,CAACG,iBAAiB,EAAE,YAAY,CAAC;AAC9E,IAAIG,oBAAoB,GAAGN,iBAAiB,CAACE,gBAAgB,EAAE,WAAW,CAAC;AAC3E;AACA,IAAIK,QAAQ,GAAG,wFAAwF,IAAIZ,GAAG,CAACa,oBAAoB,GAAG,wBAAwB,GAAG,EAAE,CAAC;AACpK,SAASC,SAASA,CAACC,GAAG,EAAE;EACtBA,GAAG,GAAGA,GAAG,KAAK,MAAM,GAAG,OAAO,GAAGA,GAAG,KAAK,OAAO,GAAG,MAAM,GAAGA,GAAG,KAAK,KAAK,GAAG,QAAQ,GAAG,KAAK;EAC5F,OAAOA,GAAG;AACZ;AACA,SAASC,aAAaA,CAACC,YAAY,EAAEC,WAAW,EAAEC,aAAa,EAAE;EAC/D,IAAI,CAAC7B,QAAQ,CAAC6B,aAAa,CAAC,IAAIA,aAAa,KAAK,QAAQ,EAAE;IAC1D,OAAO,EAAE;EACX;EACA,IAAIC,eAAe,GAAGH,YAAY,CAACI,GAAG,CAAC,iBAAiB,CAAC;EACzD,IAAIC,WAAW,GAAGL,YAAY,CAACI,GAAG,CAAC,aAAa,CAAC;EACjDH,WAAW,GAAGjB,oBAAoB,CAACiB,WAAW,CAAC;EAC/C,IAAIK,QAAQ,GAAGT,SAAS,CAACK,aAAa,CAAC;EACvC,IAAIK,SAAS,GAAGC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,KAAK,CAACL,WAAW,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC;EAC1D,IAAIM,aAAa,GAAG,EAAE;EACtB,IAAIC,cAAc,GAAGlB,oBAAoB,GAAG,GAAG;EAC/C,IAAImB,SAAS;EACb,IAAIvC,OAAO,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC,EAAEgC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE;IAC7CK,aAAa,IAAI,SAAS;IAC1BC,cAAc,IAAI,0BAA0B,IAAIC,SAAS,GAAGP,QAAQ,KAAK,MAAM,GAAG,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM;EACxG,CAAC,MAAM;IACLK,aAAa,IAAI,UAAU;IAC3BC,cAAc,IAAI,0BAA0B,IAAIC,SAAS,GAAGP,QAAQ,KAAK,KAAK,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,MAAM;EACrG;EACA,IAAIQ,YAAY,GAAGD,SAAS,GAAGL,IAAI,CAACO,EAAE,GAAG,GAAG;EAC5C,IAAIC,OAAO,GAAGT,SAAS,GAAGF,WAAW;EACrC,IAAIY,SAAS,GAAGD,OAAO,GAAGR,IAAI,CAACU,GAAG,CAACV,IAAI,CAACW,GAAG,CAACL,YAAY,CAAC,CAAC,GAAGE,OAAO,GAAGR,IAAI,CAACU,GAAG,CAACV,IAAI,CAACY,GAAG,CAACN,YAAY,CAAC,CAAC;EACvG,IAAIO,WAAW,GAAGb,IAAI,CAACE,KAAK,CAAC,CAAC,CAACO,SAAS,GAAGT,IAAI,CAACc,KAAK,GAAGjB,WAAW,IAAI,CAAC,GAAGG,IAAI,CAACc,KAAK,GAAGjB,WAAW,GAAG,CAACY,SAAS,GAAGD,OAAO,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,GAAG;EAC7IL,aAAa,IAAI,GAAG,GAAGL,QAAQ,GAAG,IAAI,GAAGe,WAAW,GAAG,IAAI;EAC3D,IAAIE,WAAW,GAAGtB,WAAW,GAAG,SAAS,GAAGI,WAAW,GAAG,KAAK;EAC/D,IAAImB,QAAQ,GAAG,CAAC,0BAA0B,GAAGjB,SAAS,GAAG,YAAY,GAAGA,SAAS,GAAG,gBAAgB,EAAEI,aAAa,GAAG,GAAG,GAAGC,cAAc,GAAG,GAAG,EAAE,gBAAgB,GAAGW,WAAW,EAAE,eAAe,GAAGA,WAAW,EAAE,mBAAmB,GAAGpB,eAAe,GAAG,GAAG,CAAC;EAC7P,OAAO,eAAe,GAAGqB,QAAQ,CAACC,IAAI,CAAC,EAAE,CAAC,GAAG,WAAW;AAC1D;AACA,SAASC,kBAAkBA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;EAC9C,IAAIC,eAAe,GAAG,6BAA6B;EACnD,IAAIC,gBAAgB,GAAG,GAAG,GAAGH,QAAQ,GAAG,CAAC,GAAG,IAAI,GAAGE,eAAe;EAClE,IAAIE,cAAc,GAAG,SAAS,GAAGD,gBAAgB,GAAG,aAAa,GAAGA,gBAAgB;EACpF,IAAI,CAACF,QAAQ,EAAE;IACbE,gBAAgB,GAAG,GAAG,GAAGH,QAAQ,GAAG,IAAI,GAAGE,eAAe;IAC1DE,cAAc,IAAIhD,GAAG,CAACiD,kBAAkB,GAAG,GAAG,GAAGtC,oBAAoB,GAAGoC,gBAAgB,GAAG,OAAO,GAAGA,gBAAgB,GAAG,MAAM,GAAGA,gBAAgB;EACnJ;EACA,OAAOrC,qBAAqB,GAAG,GAAG,GAAGsC,cAAc;AACrD;AACA,SAASE,iBAAiBA,CAACC,CAAC,EAAEC,CAAC,EAAEC,QAAQ,EAAE;EACzC;EACA;EACA,IAAIC,EAAE,GAAGH,CAAC,CAACI,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI;EAC5B,IAAIC,EAAE,GAAGJ,CAAC,CAACG,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI;EAC5B;EACA,IAAI,CAACvD,GAAG,CAACiD,kBAAkB,EAAE;IAC3B,OAAOI,QAAQ,GAAG,MAAM,GAAGG,EAAE,GAAG,QAAQ,GAAGF,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,EAAEE,EAAE,CAAC,EAAE,CAAC,MAAM,EAAEF,EAAE,CAAC,CAAC;EACnF;EACA;EACA,IAAIG,IAAI,GAAGzD,GAAG,CAACa,oBAAoB;EACnC,IAAI6C,SAAS,GAAG,WAAW,IAAID,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC,GAAG,GAAG,GAAGH,EAAE,GAAG,GAAG,GAAGE,EAAE,IAAIC,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC,GAAG,GAAG;EACjG,OAAOJ,QAAQ,GAAG,eAAe,GAAG1C,oBAAoB,GAAG,GAAG,GAAG+C,SAAS,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,CAACnD,gBAAgB,EAAEmD,SAAS,CAAC,CAAC;AAC7I;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,cAAc,EAAE;EACpC,IAAIC,OAAO,GAAG,EAAE;EAChB,IAAIC,QAAQ,GAAGF,cAAc,CAACvC,GAAG,CAAC,UAAU,CAAC;EAC7C,IAAI0C,KAAK,GAAGH,cAAc,CAACI,YAAY,CAAC,CAAC;EACzCD,KAAK,IAAIF,OAAO,CAACI,IAAI,CAAC,QAAQ,GAAGF,KAAK,CAAC;EACvCF,OAAO,CAACI,IAAI,CAAC,OAAO,GAAGL,cAAc,CAACM,OAAO,CAAC,CAAC,CAAC;EAChD;EACA,IAAIC,UAAU,GAAGtE,SAAS,CAAC+D,cAAc,CAACvC,GAAG,CAAC,YAAY,CAAC,EAAEI,IAAI,CAACE,KAAK,CAACmC,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;EAC1FA,QAAQ,IAAID,OAAO,CAACI,IAAI,CAAC,cAAc,GAAGE,UAAU,GAAG,IAAI,CAAC;EAC5D,IAAIC,WAAW,GAAGR,cAAc,CAACvC,GAAG,CAAC,iBAAiB,CAAC;EACvD,IAAIgD,UAAU,GAAGT,cAAc,CAACvC,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC;EAC1D,IAAIiD,aAAa,GAAGV,cAAc,CAACvC,GAAG,CAAC,mBAAmB,CAAC,IAAI,CAAC;EAChE,IAAIkD,aAAa,GAAGX,cAAc,CAACvC,GAAG,CAAC,mBAAmB,CAAC,IAAI,CAAC;EAChE+C,WAAW,IAAIC,UAAU,IAAIR,OAAO,CAACI,IAAI,CAAC,cAAc,GAAGK,aAAa,GAAG,KAAK,GAAGC,aAAa,GAAG,KAAK,GAAGF,UAAU,GAAG,KAAK,GAAGD,WAAW,CAAC;EAC5I5E,IAAI,CAAC,CAAC,YAAY,EAAE,OAAO,CAAC,EAAE,UAAUgF,IAAI,EAAE;IAC5C,IAAIC,GAAG,GAAGb,cAAc,CAACvC,GAAG,CAACmD,IAAI,CAAC;IAClCC,GAAG,IAAIZ,OAAO,CAACI,IAAI,CAAC,OAAO,GAAGO,IAAI,GAAG,GAAG,GAAGC,GAAG,CAAC;EACjD,CAAC,CAAC;EACF,OAAOZ,OAAO,CAACnB,IAAI,CAAC,GAAG,CAAC;AAC1B;AACA,SAASgC,eAAeA,CAACzD,YAAY,EAAE0D,gBAAgB,EAAE9B,QAAQ,EAAE;EACjE,IAAIgB,OAAO,GAAG,EAAE;EAChB,IAAIe,kBAAkB,GAAG3D,YAAY,CAACI,GAAG,CAAC,oBAAoB,CAAC;EAC/D,IAAID,eAAe,GAAGH,YAAY,CAACI,GAAG,CAAC,iBAAiB,CAAC;EACzD,IAAIgD,UAAU,GAAGpD,YAAY,CAACI,GAAG,CAAC,YAAY,CAAC;EAC/C,IAAI+C,WAAW,GAAGnD,YAAY,CAACI,GAAG,CAAC,aAAa,CAAC;EACjD,IAAIiD,aAAa,GAAGrD,YAAY,CAACI,GAAG,CAAC,eAAe,CAAC;EACrD,IAAIkD,aAAa,GAAGtD,YAAY,CAACI,GAAG,CAAC,eAAe,CAAC;EACrD,IAAIuC,cAAc,GAAG3C,YAAY,CAAC4D,QAAQ,CAAC,WAAW,CAAC;EACvD,IAAIC,OAAO,GAAGrE,0BAA0B,CAACQ,YAAY,EAAE,MAAM,CAAC;EAC9D,IAAI8D,SAAS,GAAGT,aAAa,GAAG,KAAK,GAAGC,aAAa,GAAG,KAAK,GAAGF,UAAU,GAAG,KAAK,GAAGD,WAAW;EAChGP,OAAO,CAACI,IAAI,CAAC,aAAa,GAAGc,SAAS,CAAC;EACvC;EACAJ,gBAAgB,IAAIC,kBAAkB,IAAIf,OAAO,CAACI,IAAI,CAACtB,kBAAkB,CAACiC,kBAAkB,EAAE/B,QAAQ,CAAC,CAAC;EACxG,IAAIzB,eAAe,EAAE;IACnByC,OAAO,CAACI,IAAI,CAAC,mBAAmB,GAAG7C,eAAe,CAAC;EACrD;EACA;EACA5B,IAAI,CAAC,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,EAAE,UAAUgF,IAAI,EAAE;IACjD,IAAIQ,UAAU,GAAG,SAAS,GAAGR,IAAI;IACjC,IAAIS,SAAS,GAAG/E,WAAW,CAAC8E,UAAU,CAAC;IACvC,IAAIP,GAAG,GAAGxD,YAAY,CAACI,GAAG,CAAC4D,SAAS,CAAC;IACrCR,GAAG,IAAI,IAAI,IAAIZ,OAAO,CAACI,IAAI,CAACe,UAAU,GAAG,GAAG,GAAGP,GAAG,IAAID,IAAI,KAAK,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;EACtF,CAAC,CAAC;EACF;EACAX,OAAO,CAACI,IAAI,CAACN,YAAY,CAACC,cAAc,CAAC,CAAC;EAC1C;EACA,IAAIkB,OAAO,IAAI,IAAI,EAAE;IACnBjB,OAAO,CAACI,IAAI,CAAC,UAAU,GAAG9D,iBAAiB,CAAC2E,OAAO,CAAC,CAACpC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;EAC1E;EACA,OAAOmB,OAAO,CAACnB,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG;AAChC;AACA;AACA,SAASwC,cAAcA,CAACC,GAAG,EAAEC,EAAE,EAAEC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAE;EACpD,IAAIC,SAAS,GAAGJ,EAAE,IAAIA,EAAE,CAACK,OAAO;EAChC,IAAIJ,SAAS,EAAE;IACb,IAAIK,cAAc,GAAGF,SAAS,IAAIA,SAAS,CAACG,eAAe,CAAC,CAAC;IAC7D,IAAID,cAAc,EAAE;MAClB;MACA3F,mBAAmB,CAACoF,GAAG,EAAEO,cAAc,EAAEL,SAAS,EAAEC,GAAG,EAAEC,GAAG,CAAC;IAC/D;EACF,CAAC,MAAM;IACLJ,GAAG,CAAC,CAAC,CAAC,GAAGG,GAAG;IACZH,GAAG,CAAC,CAAC,CAAC,GAAGI,GAAG;IACZ;IACA;IACA;IACA,IAAIK,kBAAkB,GAAGJ,SAAS,IAAIA,SAAS,CAACK,qBAAqB,CAAC,CAAC;IACvE,IAAID,kBAAkB,EAAE;MACtBT,GAAG,CAAC,CAAC,CAAC,IAAIS,kBAAkB,CAACE,UAAU;MACvCX,GAAG,CAAC,CAAC,CAAC,IAAIS,kBAAkB,CAACG,SAAS;IACxC;EACF;EACAZ,GAAG,CAAC,CAAC,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,GAAGC,EAAE,CAACY,QAAQ,CAAC,CAAC;EAC/Bb,GAAG,CAAC,CAAC,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,GAAGC,EAAE,CAACa,SAAS,CAAC,CAAC;AAClC;AACA,IAAIC,kBAAkB,GAAG,aAAa,YAAY;EAChD,SAASA,kBAAkBA,CAACC,GAAG,EAAEC,GAAG,EAAE;IACpC,IAAI,CAACC,KAAK,GAAG,KAAK;IAClB,IAAI,CAACC,WAAW,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC/B,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI1G,GAAG,CAAC2G,GAAG,EAAE;MACX,OAAO,IAAI;IACb;IACA,IAAIC,EAAE,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACtC;IACAF,EAAE,CAACG,aAAa,GAAG,IAAI;IACvB,IAAI,CAACH,EAAE,GAAGA,EAAE;IACZ,IAAIxB,EAAE,GAAG,IAAI,CAAC4B,GAAG,GAAGb,GAAG,CAACc,KAAK,CAAC,CAAC;IAC/B,IAAIC,QAAQ,GAAGd,GAAG,CAACc,QAAQ;IAC3B,IAAI7B,SAAS,GAAG6B,QAAQ,KAAK5H,QAAQ,CAAC4H,QAAQ,CAAC,GAAGL,QAAQ,CAACM,aAAa,CAACD,QAAQ,CAAC,GAAGtH,KAAK,CAACsH,QAAQ,CAAC,GAAGA,QAAQ,GAAGxH,UAAU,CAACwH,QAAQ,CAAC,IAAIA,QAAQ,CAACf,GAAG,CAACiB,MAAM,CAAC,CAAC,CAAC,CAAC;IACjKlC,cAAc,CAAC,IAAI,CAACoB,WAAW,EAAElB,EAAE,EAAEC,SAAS,EAAEc,GAAG,CAACH,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAEG,GAAG,CAACF,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC;IACxF,CAACZ,SAAS,IAAIc,GAAG,CAACiB,MAAM,CAAC,CAAC,EAAEC,WAAW,CAACT,EAAE,CAAC;IAC3C,IAAI,CAACU,IAAI,GAAGnB,GAAG;IACf,IAAI,CAACoB,UAAU,GAAGlC,SAAS;IAC3B;IACA;IACA;IACA,IAAImC,IAAI,GAAG,IAAI;IACfZ,EAAE,CAACa,YAAY,GAAG,YAAY;MAC5B;MACA,IAAID,IAAI,CAACjB,UAAU,EAAE;QACnBmB,YAAY,CAACF,IAAI,CAACG,YAAY,CAAC;QAC/BH,IAAI,CAACnB,KAAK,GAAG,IAAI;MACnB;MACAmB,IAAI,CAACI,UAAU,GAAG,IAAI;IACxB,CAAC;IACDhB,EAAE,CAACiB,WAAW,GAAG,UAAUC,CAAC,EAAE;MAC5BA,CAAC,GAAGA,CAAC,IAAIC,MAAM,CAACC,KAAK;MACrB,IAAI,CAACR,IAAI,CAACjB,UAAU,EAAE;QACpB;QACA;QACA;QACA;QACA;QACA;QACA,IAAI0B,OAAO,GAAG7C,EAAE,CAAC6C,OAAO;QACxB,IAAIvC,cAAc,GAAGN,EAAE,CAACK,OAAO,CAACE,eAAe,CAAC,CAAC;QACjD7F,cAAc,CAAC4F,cAAc,EAAEoC,CAAC,EAAE,IAAI,CAAC;QACvCG,OAAO,CAACC,QAAQ,CAAC,WAAW,EAAEJ,CAAC,CAAC;MAClC;IACF,CAAC;IACDlB,EAAE,CAACuB,YAAY,GAAG,YAAY;MAC5B;MACAX,IAAI,CAACI,UAAU,GAAG,KAAK;MACvB,IAAIJ,IAAI,CAACjB,UAAU,EAAE;QACnB,IAAIiB,IAAI,CAACnB,KAAK,EAAE;UACdmB,IAAI,CAACY,SAAS,CAACZ,IAAI,CAACa,UAAU,CAAC;QACjC;MACF;IACF,CAAC;EACH;EACA;AACF;AACA;EACEnC,kBAAkB,CAACoC,SAAS,CAACC,MAAM,GAAG,UAAUtH,YAAY,EAAE;IAC5D;IACA;IACA,IAAI,CAAC,IAAI,CAACsG,UAAU,EAAE;MACpB,IAAIlC,SAAS,GAAG,IAAI,CAACiC,IAAI,CAACF,MAAM,CAAC,CAAC;MAClC,IAAIoB,QAAQ,GAAGlI,gBAAgB,CAAC+E,SAAS,EAAE,UAAU,CAAC;MACtD,IAAIoD,QAAQ,GAAGpD,SAAS,CAACqD,KAAK;MAC9B,IAAID,QAAQ,CAACD,QAAQ,KAAK,UAAU,IAAIA,QAAQ,KAAK,UAAU,EAAE;QAC/DC,QAAQ,CAACD,QAAQ,GAAG,UAAU;MAChC;IACF;IACA;IACA,IAAIG,iBAAiB,GAAG1H,YAAY,CAACI,GAAG,CAAC,mBAAmB,CAAC;IAC7DsH,iBAAiB,IAAI,IAAI,CAACC,cAAc,CAAC,CAAC;IAC1C;IACA,IAAI,CAACpC,kBAAkB,GAAGmC,iBAAiB;IAC3C;IACA,IAAI,CAAC/B,EAAE,CAACiC,SAAS,GAAG5H,YAAY,CAACI,GAAG,CAAC,WAAW,CAAC,IAAI,EAAE;IACvD;IACA;IACA;EACF,CAAC;EACD6E,kBAAkB,CAACoC,SAAS,CAACQ,IAAI,GAAG,UAAU7H,YAAY,EAAE8H,cAAc,EAAE;IAC1ErB,YAAY,CAAC,IAAI,CAACC,YAAY,CAAC;IAC/BD,YAAY,CAAC,IAAI,CAACsB,gBAAgB,CAAC;IACnC,IAAIpC,EAAE,GAAG,IAAI,CAACA,EAAE;IAChB,IAAI8B,KAAK,GAAG9B,EAAE,CAAC8B,KAAK;IACpB,IAAIO,UAAU,GAAG,IAAI,CAAC3C,WAAW;IACjC,IAAI,CAACM,EAAE,CAACsC,SAAS,EAAE;MACjBR,KAAK,CAACS,OAAO,GAAG,MAAM;IACxB,CAAC,MAAM;MACLT,KAAK,CAAC7E,OAAO,GAAGjD,QAAQ,GAAG8D,eAAe,CAACzD,YAAY,EAAE,CAAC,IAAI,CAACwF,UAAU,EAAE,IAAI,CAACC,SAAS;MACzF;MAAA,EACExD,iBAAiB,CAAC+F,UAAU,CAAC,CAAC,CAAC,EAAEA,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,eAAe,GAAGhJ,oBAAoB,CAAC8I,cAAc,CAAC,GAAG,GAAG,CAAC,IAAI9H,YAAY,CAACI,GAAG,CAAC,cAAc,CAAC,IAAI,EAAE;MAClK;MACA;MACA;MACA;MACA;MAAA,GACG,kBAAkB,IAAI,IAAI,CAACkF,UAAU,GAAG,MAAM,GAAG,MAAM,CAAC,CAAC;IAC9D;IACA,IAAI,CAACF,KAAK,GAAG,IAAI;IACjB,IAAI,CAACI,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,SAAS,GAAG,KAAK;EACxB,CAAC;EACDR,kBAAkB,CAACoC,SAAS,CAACc,UAAU,GAAG,UAAUC,OAAO,EAAEC,OAAO,EAAErI,YAAY,EAAEC,WAAW,EAAEC,aAAa,EAAE;IAC9G,IAAIyF,EAAE,GAAG,IAAI,CAACA,EAAE;IAChB,IAAIyC,OAAO,IAAI,IAAI,EAAE;MACnBzC,EAAE,CAACsC,SAAS,GAAG,EAAE;MACjB;IACF;IACA,IAAIK,KAAK,GAAG,EAAE;IACd,IAAIjK,QAAQ,CAAC6B,aAAa,CAAC,IAAIF,YAAY,CAACI,GAAG,CAAC,SAAS,CAAC,KAAK,MAAM,IAAI,CAACjB,oBAAoB,CAACa,YAAY,CAAC,EAAE;MAC5GsI,KAAK,GAAGvI,aAAa,CAACC,YAAY,EAAEC,WAAW,EAAEC,aAAa,CAAC;IACjE;IACA,IAAI7B,QAAQ,CAAC+J,OAAO,CAAC,EAAE;MACrBzC,EAAE,CAACsC,SAAS,GAAGG,OAAO,GAAGE,KAAK;IAChC,CAAC,MAAM,IAAIF,OAAO,EAAE;MAClB;MACAzC,EAAE,CAACsC,SAAS,GAAG,EAAE;MACjB,IAAI,CAACvJ,OAAO,CAAC0J,OAAO,CAAC,EAAE;QACrBA,OAAO,GAAG,CAACA,OAAO,CAAC;MACrB;MACA,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,OAAO,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;QACvC,IAAI5J,KAAK,CAACyJ,OAAO,CAACG,CAAC,CAAC,CAAC,IAAIH,OAAO,CAACG,CAAC,CAAC,CAACE,UAAU,KAAK9C,EAAE,EAAE;UACrDA,EAAE,CAACS,WAAW,CAACgC,OAAO,CAACG,CAAC,CAAC,CAAC;QAC5B;MACF;MACA;MACA,IAAID,KAAK,IAAI3C,EAAE,CAAC+C,UAAU,CAACF,MAAM,EAAE;QACjC;QACA;QACA,IAAIG,OAAO,GAAG/C,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QAC3C8C,OAAO,CAACV,SAAS,GAAGK,KAAK;QACzB3C,EAAE,CAACS,WAAW,CAACuC,OAAO,CAAC;MACzB;IACF;EACF,CAAC;EACD1D,kBAAkB,CAACoC,SAAS,CAACuB,YAAY,GAAG,UAAUC,SAAS,EAAE;IAC/D,IAAI,CAACvD,UAAU,GAAGuD,SAAS;EAC7B,CAAC;EACD5D,kBAAkB,CAACoC,SAAS,CAACyB,OAAO,GAAG,YAAY;IACjD,IAAInD,EAAE,GAAG,IAAI,CAACA,EAAE;IAChB,OAAOA,EAAE,GAAG,CAACA,EAAE,CAACoD,WAAW,EAAEpD,EAAE,CAACqD,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;EACxD,CAAC;EACD/D,kBAAkB,CAACoC,SAAS,CAAC4B,MAAM,GAAG,UAAU5E,GAAG,EAAEC,GAAG,EAAE;IACxD,IAAI,CAAC,IAAI,CAACqB,EAAE,EAAE;MACZ;IACF;IACA,IAAIqC,UAAU,GAAG,IAAI,CAAC3C,WAAW;IACjCpB,cAAc,CAAC+D,UAAU,EAAE,IAAI,CAACjC,GAAG,EAAE,IAAI,CAACO,UAAU,EAAEjC,GAAG,EAAEC,GAAG,CAAC;IAC/D,IAAI0D,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,IAAIA,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;MAClD,IAAIkB,OAAO,GAAG,IAAI,CAACvD,EAAE,CAAC8B,KAAK;MAC3B,IAAI0B,UAAU,GAAGlH,iBAAiB,CAAC+F,UAAU,CAAC,CAAC,CAAC,EAAEA,UAAU,CAAC,CAAC,CAAC,CAAC;MAChEzJ,IAAI,CAAC4K,UAAU,EAAE,UAAUC,SAAS,EAAE;QACpCF,OAAO,CAACE,SAAS,CAAC,CAAC,CAAC,CAAC,GAAGA,SAAS,CAAC,CAAC,CAAC;MACtC,CAAC,CAAC;IACJ;EACF,CAAC;EACD;AACF;AACA;AACA;EACEnE,kBAAkB,CAACoC,SAAS,CAACM,cAAc,GAAG,YAAY;IACxD;IACA,IAAI0B,MAAM,GAAG,IAAI,CAAChE,WAAW,CAAC,CAAC,CAAC;IAChC;IACA,IAAIiE,MAAM,GAAG,IAAI,CAACjE,WAAW,CAAC,CAAC,CAAC;IAChC,IAAI,CAAC4D,MAAM,CAACI,MAAM,GAAG,IAAI,CAACtD,GAAG,CAAChB,QAAQ,CAAC,CAAC,EAAEuE,MAAM,GAAG,IAAI,CAACvD,GAAG,CAACf,SAAS,CAAC,CAAC,CAAC;EAC1E,CAAC;EACDC,kBAAkB,CAACoC,SAAS,CAACkC,IAAI,GAAG,YAAY;IAC9C,IAAIC,KAAK,GAAG,IAAI;IAChB,IAAI/B,KAAK,GAAG,IAAI,CAAC9B,EAAE,CAAC8B,KAAK;IACzBA,KAAK,CAACgC,UAAU,GAAG,QAAQ;IAC3BhC,KAAK,CAACiC,OAAO,GAAG,GAAG;IACnB3K,GAAG,CAACa,oBAAoB,KAAK6H,KAAK,CAACkC,UAAU,GAAG,EAAE,CAAC;IACnD,IAAI,CAACvE,KAAK,GAAG,KAAK;IAClB,IAAI,CAAC2C,gBAAgB,GAAG6B,UAAU,CAAC,YAAY;MAC7C,OAAOJ,KAAK,CAAC/D,SAAS,GAAG,IAAI;IAC/B,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;EACDR,kBAAkB,CAACoC,SAAS,CAACF,SAAS,GAAG,UAAU0C,IAAI,EAAE;IACvD,IAAI,IAAI,CAACzE,KAAK,IAAI,EAAE,IAAI,CAACuB,UAAU,IAAI,IAAI,CAACrB,UAAU,CAAC,IAAI,CAAC,IAAI,CAACC,kBAAkB,EAAE;MACnF,IAAIsE,IAAI,EAAE;QACR,IAAI,CAACzC,UAAU,GAAGyC,IAAI;QACtB;QACA,IAAI,CAACzE,KAAK,GAAG,KAAK;QAClB,IAAI,CAACsB,YAAY,GAAGkD,UAAU,CAACpL,IAAI,CAAC,IAAI,CAAC+K,IAAI,EAAE,IAAI,CAAC,EAAEM,IAAI,CAAC;MAC7D,CAAC,MAAM;QACL,IAAI,CAACN,IAAI,CAAC,CAAC;MACb;IACF;EACF,CAAC;EACDtE,kBAAkB,CAACoC,SAAS,CAACyC,MAAM,GAAG,YAAY;IAChD,OAAO,IAAI,CAAC1E,KAAK;EACnB,CAAC;EACDH,kBAAkB,CAACoC,SAAS,CAAC0C,OAAO,GAAG,YAAY;IACjDtD,YAAY,CAAC,IAAI,CAACC,YAAY,CAAC;IAC/BD,YAAY,CAAC,IAAI,CAACsB,gBAAgB,CAAC;IACnC,IAAIU,UAAU,GAAG,IAAI,CAAC9C,EAAE,CAAC8C,UAAU;IACnCA,UAAU,IAAIA,UAAU,CAACuB,WAAW,CAAC,IAAI,CAACrE,EAAE,CAAC;IAC7C,IAAI,CAACA,EAAE,GAAG,IAAI,CAACW,UAAU,GAAG,IAAI;EAClC,CAAC;EACD,OAAOrB,kBAAkB;AAC3B,CAAC,CAAC,CAAC;AACH,eAAeA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}