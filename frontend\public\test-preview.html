<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Preview Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .preview-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        .tab {
            padding: 8px 16px;
            background: #f0f0f0;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background 0.3s;
        }
        .tab.active {
            background: #007bff;
            color: white;
        }
        .tab:hover {
            background: #e0e0e0;
        }
        .tab.active:hover {
            background: #0056b3;
        }
        .preview-container {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 4px;
            overflow: hidden;
        }
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        .info {
            margin-bottom: 20px;
            padding: 15px;
            background: #e7f3ff;
            border-left: 4px solid #007bff;
            border-radius: 4px;
        }
        .warning {
            margin-bottom: 20px;
            padding: 15px;
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📄 Word文档预览测试</h1>
        
        <div class="info">
            <strong>说明：</strong>此页面用于测试Word文档的在线预览功能。请确保您有一个可公开访问的Word文档URL。
        </div>
        
        <div class="warning">
            <strong>注意：</strong>Office Online和Google Docs Viewer只能预览公开可访问的文档URL。本地文件无法直接预览。
        </div>
        
        <div>
            <label for="docUrl">文档URL：</label>
            <input type="url" id="docUrl" placeholder="请输入Word文档的公开URL" style="width: 70%; padding: 8px; margin: 10px 0;">
            <button onclick="updatePreview()" style="padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">更新预览</button>
        </div>
        
        <div class="preview-tabs">
            <button class="tab active" onclick="switchTab('office')">Office Online</button>
            <button class="tab" onclick="switchTab('google')">Google Docs</button>
        </div>
        
        <div class="preview-container">
            <iframe id="previewFrame" src="about:blank"></iframe>
        </div>
    </div>

    <script>
        let currentMode = 'office';
        let currentUrl = '';

        function switchTab(mode) {
            currentMode = mode;
            
            // 更新标签样式
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            event.target.classList.add('active');
            
            // 更新预览
            updatePreview();
        }

        function updatePreview() {
            const urlInput = document.getElementById('docUrl');
            const frame = document.getElementById('previewFrame');
            
            currentUrl = urlInput.value.trim();
            
            if (!currentUrl) {
                frame.src = 'about:blank';
                return;
            }
            
            let previewUrl = '';
            
            if (currentMode === 'office') {
                previewUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(currentUrl)}`;
            } else if (currentMode === 'google') {
                previewUrl = `https://docs.google.com/gview?url=${encodeURIComponent(currentUrl)}&embedded=true`;
            }
            
            frame.src = previewUrl;
        }

        // 测试用的示例URL（如果需要的话）
        function loadSampleDoc() {
            document.getElementById('docUrl').value = 'https://example.com/sample.docx';
            updatePreview();
        }
    </script>
</body>
</html> 