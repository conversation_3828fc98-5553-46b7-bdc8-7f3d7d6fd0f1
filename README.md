# ChatGPT简历优化平台 MVP

一个基于AI的简历优化平台，用户可以上传简历文件和职位描述，通过ChatGPT生成优化版简历。

## 项目结构

```
SmartCV/
├── frontend/          # React前端应用
│   ├── src/
│   ├── package.json
│   └── ...
├── backend/           # Flask后端API
│   ├── app.py
│   ├── requirements.txt
│   └── ...
└── README.md
```

## 功能特点

- 📄 支持PDF和DOCX简历文件上传
- 💼 支持职位描述文本输入或文件上传
- 🤖 集成OpenAI GPT-4o进行简历优化
- 📥 支持优化简历导出（PDF/DOCX）
- 🎨 现代化UI设计（React + Tailwind CSS）

## 快速开始

### 后端设置

1. 进入后端目录：
```bash
cd backend
```

2. 安装依赖：
```bash
pip install -r requirements.txt
```

3. 配置环境变量：
```bash
# 创建 .env 文件
OPENAI_API_KEY=your_openai_api_key_here
```

4. 启动后端服务：
```bash
python app.py
```

### 前端设置

1. 进入前端目录：
```bash
cd frontend
```

2. 安装依赖：
```bash
npm install
```

3. 启动开发服务器：
```bash
npm start
```

## 部署

- 前端：推荐使用Vercel或Netlify
- 后端：推荐使用Render或Heroku

## API文档

### POST /api/optimize-resume

优化简历接口

**请求参数：**
- `resume_file`: 简历文件（PDF/DOCX）
- `job_description`: 职位描述文本或文件

**响应格式：**
```json
{
  "success": true,
  "optimized_resume": "优化后的简历内容...",
  "message": "简历优化完成"
}
``` 