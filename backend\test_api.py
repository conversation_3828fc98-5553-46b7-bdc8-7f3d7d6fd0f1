#!/usr/bin/env python3
import requests

# 测试API端点
url = "http://localhost:5000/api/optimize-resume"

# 准备测试数据
with open('test.txt', 'rb') as f:
    files = {'resume_file': f}
    data = {'job_description_text': 'Software Engineer position requiring Python and Flask experience'}
    
    response = requests.post(url, files=files, data=data)
    
    print("Status Code:", response.status_code)
    print("Response:", response.json()) 