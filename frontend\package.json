{"name": "smartcv-frontend", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.5.0", "file-saver": "^2.0.5", "html-docx-js": "^0.3.1", "jspdf": "^2.5.1", "pdfjs-dist": "^5.3.31", "react": "^18.2.0", "react-dom": "^18.2.0", "react-pdf": "^9.2.1", "react-scripts": "5.0.1", "tesseract.js": "^6.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.14", "postcss": "^8.4.24", "tailwindcss": "^3.3.0"}, "proxy": "http://localhost:5000"}