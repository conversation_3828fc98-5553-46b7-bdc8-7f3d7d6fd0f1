#!/usr/bin/env python3
import os
import sys
import json
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

print("=== 环境变量检查 ===")
print(f"API Key: {'✅ 已设置' if os.getenv('AZURE_OPENAI_API_KEY') else '❌ 未设置'}")
print(f"Endpoint: {os.getenv('AZURE_OPENAI_ENDPOINT')}")
print(f"Deployment: {os.getenv('AZURE_OPENAI_GPT4_DEPLOYMENT')}")

try:
    from openai import AzureOpenAI
    print("✅ OpenAI库导入成功")
    
    # 初始化客户端
    client = AzureOpenAI(
        api_key=os.getenv("AZURE_OPENAI_API_KEY"),
        api_version="2024-02-15-preview",
        azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT")
    )
    print("✅ Azure OpenAI客户端初始化成功")
    
    # 测试简单调用
    print("🔄 测试API调用...")
    response = client.chat.completions.create(
        model=os.getenv("AZURE_OPENAI_GPT4_DEPLOYMENT"),
        messages=[
            {"role": "user", "content": "请回复'测试成功'"}
        ],
        max_tokens=50
    )
    
    result = response.choices[0].message.content.strip()
    print(f"✅ API调用成功，响应: {result}")
    
    # 测试JD解析
    print("\n🔄 测试JD解析...")
    jd_prompt = """
请分析以下职位描述，提取：
1. 职位名称（title）
2. 核心硬技能（core_skills）
3. 所需软技能（soft_skills）
4. 关键词及其重要性（keywords，格式为{"关键词": 权重}，权重为1-5）

请严格以如下JSON格式输出：
{
  "title": "",
  "core_skills": [],
  "soft_skills": [],
  "keywords": {}
}

职位描述：
我们正在寻找一名高级Python开发工程师，要求有5年以上Python开发经验，熟悉Django框架，具备React前端开发能力。
"""
    
    jd_response = client.chat.completions.create(
        model=os.getenv("AZURE_OPENAI_GPT4_DEPLOYMENT"),
        messages=[
            {"role": "system", "content": "你是一个专业的JD解析专家，擅长结构化提取职位描述关键信息。"},
            {"role": "user", "content": jd_prompt}
        ],
        temperature=0.2,
        max_tokens=800
    )
    
    jd_result = jd_response.choices[0].message.content.strip()
    print(f"📋 JD解析响应: {jd_result}")
    
    try:
        jd_json = json.loads(jd_result)
        print(f"✅ JD解析JSON成功: {json.dumps(jd_json, ensure_ascii=False, indent=2)}")
    except:
        print(f"❌ JD解析JSON失败")
    
    # 测试简历优化
    print("\n🔄 测试简历优化...")
    resume_text = "张三，软件工程师，3年Python开发经验，熟悉Web开发。"
    
    optimization_prompt = f"""
你是一个专为HR系统优化简历的语言专家。请根据以下简历和职位描述，优化简历内容，并以严格JSON数组格式返回：

简历原文：
{resume_text}

职位描述：
高级Python开发工程师，要求5年以上Python经验，熟悉Django和React。

请输出JSON数组格式：
[
  {{"section": "Experience", "original": "...", "optimized": "...", "jd_keywords": ["Python", "Django"]}}
]
不要输出任何多余内容。
"""
    
    opt_response = client.chat.completions.create(
        model=os.getenv("AZURE_OPENAI_GPT4_DEPLOYMENT"),
        messages=[
            {"role": "system", "content": "你是一个专为HR系统优化简历的语言专家，所有内容必须基于原文，不得虚构。输出JSON。"},
            {"role": "user", "content": optimization_prompt}
        ],
        max_tokens=2000,
        temperature=0.3
    )
    
    opt_result = opt_response.choices[0].message.content.strip()
    print(f"🤖 优化响应: {opt_result}")
    
    # 尝试解析优化结果
    import re
    match = re.search(r'\[.*\]', opt_result, re.DOTALL)
    if match:
        json_text = match.group()
        try:
            opt_json = json.loads(json_text)
            print(f"✅ 优化JSON解析成功: {json.dumps(opt_json, ensure_ascii=False, indent=2)}")
        except Exception as e:
            print(f"❌ 优化JSON解析失败: {e}")
    else:
        print("❌ 未找到JSON数组格式")
    
    print("\n🎉 所有测试完成！")
    
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()
