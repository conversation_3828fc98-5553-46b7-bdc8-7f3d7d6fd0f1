<PERSON>
Software Engineer

Contact Information:
Email: <EMAIL>
Phone: (*************
LinkedIn: linkedin.com/in/johnsmith

Professional Summary:
Experienced software engineer with 3 years of Python development experience. Skilled in web development, database management, and collaborative software development. Passionate about creating efficient and scalable solutions.

Work Experience:

Software Developer | Tech Solutions Inc. | 2021 - Present
- Developed web applications using Python and JavaScript
- Collaborated with cross-functional teams on various projects
- Implemented database solutions using MySQL
- Participated in code reviews and agile development processes

Junior Developer | StartupCorp | 2020 - 2021
- Assisted in developing web-based applications
- Learned and applied best practices in software development
- Worked with senior developers on feature implementation

Education:
Bachelor of Science in Computer Science
University of Technology | 2016 - 2020
- Relevant coursework: Data Structures, Algorithms, Database Systems, Web Development

Technical Skills:
- Programming Languages: Python, JavaScript, HTML, CSS
- Frameworks: Django, Flask, React (basic)
- Databases: MySQL, PostgreSQL (basic)
- Tools: Git, VS Code, Linux
- Soft Skills: Problem-solving, Communication, Teamwork

Projects:
Personal Portfolio Website
- Built a responsive website using HTML, CSS, and JavaScript
- Implemented contact form with backend processing

Task Management App
- Developed a simple task management application using Python and Flask
- Integrated with SQLite database for data persistence

Certifications:
- Python Programming Certificate (2020)
- Web Development Fundamentals (2019)
