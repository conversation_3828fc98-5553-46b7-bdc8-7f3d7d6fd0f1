#!/usr/bin/env python3
"""
测试JSON到文本转换功能
"""

from app import convert_json_to_resume_text

# 测试数据
test_json = [
    {
        "section": "Professional Summary",
        "original": "Experienced software developer with 5 years of experience.",
        "optimized": "Results-driven Software Developer with 5+ years of experience in full-stack development, specializing in Python, JavaScript, and cloud technologies. Proven track record of delivering scalable solutions and improving system performance.",
        "jd_keywords": ["Python", "JavaScript", "full-stack", "cloud technologies"]
    },
    {
        "section": "Experience",
        "original": "Software Developer at ABC Company (2019-2024)\n- Developed web applications\n- Worked with databases",
        "optimized": "Senior Software Developer | ABC Company | 2019-2024\n• Developed and maintained 15+ responsive web applications using React, Node.js, and Python\n• Optimized database queries resulting in 40% performance improvement\n• Collaborated with cross-functional teams to deliver projects on time",
        "jd_keywords": ["React", "Node.js", "Python", "database optimization", "cross-functional teams"]
    },
    {
        "section": "Skills",
        "original": "Programming: Python, JavaScript\nDatabases: MySQL",
        "optimized": "**Technical Skills:**\n• Programming Languages: Python, JavaScript, TypeScript, Java\n• Frontend: React, Vue.js, HTML5, CSS3\n• Backend: Node.js, Django, Flask, Express.js\n• Databases: MySQL, PostgreSQL, MongoDB\n• Cloud: AWS, Azure, Docker, Kubernetes",
        "jd_keywords": ["Python", "JavaScript", "React", "Node.js", "AWS", "Docker"]
    }
]

def test_conversion():
    print("Testing JSON to resume text conversion...")
    print("=" * 50)
    
    # 测试正常情况
    result = convert_json_to_resume_text(test_json)
    print("Converted Resume:")
    print(result)
    print("\n" + "=" * 50)
    
    # 测试空数组
    empty_result = convert_json_to_resume_text([])
    print("Empty JSON result:")
    print(empty_result)
    print("\n" + "=" * 50)
    
    # 测试无效输入
    invalid_result = convert_json_to_resume_text("invalid")
    print("Invalid input result:")
    print(invalid_result)

if __name__ == "__main__":
    test_conversion()
