{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { assert, clone, createHashMap, isFunction, keys, map, reduce } from 'zrender/lib/core/util.js';\nimport { parseDataValue } from './helper/dataValueHelper.js';\nimport { shouldRetrieveDataByName } from './Source.js';\nvar UNDEFINED = 'undefined';\n/* global Float64Array, Int32Array, Uint32Array, Uint16Array */\n// Caution: MUST not use `new CtorUint32Array(arr, 0, len)`, because the Ctor of array is\n// different from the Ctor of typed array.\nexport var CtorUint32Array = typeof Uint32Array === UNDEFINED ? Array : Uint32Array;\nexport var CtorUint16Array = typeof Uint16Array === UNDEFINED ? Array : Uint16Array;\nexport var CtorInt32Array = typeof Int32Array === UNDEFINED ? Array : Int32Array;\nexport var CtorFloat64Array = typeof Float64Array === UNDEFINED ? Array : Float64Array;\n/**\r\n * Multi dimensional data store\r\n */\nvar dataCtors = {\n  'float': CtorFloat64Array,\n  'int': CtorInt32Array,\n  // Ordinal data type can be string or int\n  'ordinal': Array,\n  'number': Array,\n  'time': CtorFloat64Array\n};\nvar defaultDimValueGetters;\nfunction getIndicesCtor(rawCount) {\n  // The possible max value in this._indicies is always this._rawCount despite of filtering.\n  return rawCount > 65535 ? CtorUint32Array : CtorUint16Array;\n}\n;\nfunction getInitialExtent() {\n  return [Infinity, -Infinity];\n}\n;\nfunction cloneChunk(originalChunk) {\n  var Ctor = originalChunk.constructor;\n  // Only shallow clone is enough when Array.\n  return Ctor === Array ? originalChunk.slice() : new Ctor(originalChunk);\n}\nfunction prepareStore(store, dimIdx, dimType, end, append) {\n  var DataCtor = dataCtors[dimType || 'float'];\n  if (append) {\n    var oldStore = store[dimIdx];\n    var oldLen = oldStore && oldStore.length;\n    if (!(oldLen === end)) {\n      var newStore = new DataCtor(end);\n      // The cost of the copy is probably inconsiderable\n      // within the initial chunkSize.\n      for (var j = 0; j < oldLen; j++) {\n        newStore[j] = oldStore[j];\n      }\n      store[dimIdx] = newStore;\n    }\n  } else {\n    store[dimIdx] = new DataCtor(end);\n  }\n}\n;\n/**\r\n * Basically, DataStore API keep immutable.\r\n */\nvar DataStore = /** @class */function () {\n  function DataStore() {\n    this._chunks = [];\n    // It will not be calculated until needed.\n    this._rawExtent = [];\n    this._extent = [];\n    this._count = 0;\n    this._rawCount = 0;\n    this._calcDimNameToIdx = createHashMap();\n  }\n  /**\r\n   * Initialize from data\r\n   */\n  DataStore.prototype.initData = function (provider, inputDimensions, dimValueGetter) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(isFunction(provider.getItem) && isFunction(provider.count), 'Invalid data provider.');\n    }\n    this._provider = provider;\n    // Clear\n    this._chunks = [];\n    this._indices = null;\n    this.getRawIndex = this._getRawIdxIdentity;\n    var source = provider.getSource();\n    var defaultGetter = this.defaultDimValueGetter = defaultDimValueGetters[source.sourceFormat];\n    // Default dim value getter\n    this._dimValueGetter = dimValueGetter || defaultGetter;\n    // Reset raw extent.\n    this._rawExtent = [];\n    var willRetrieveDataByName = shouldRetrieveDataByName(source);\n    this._dimensions = map(inputDimensions, function (dim) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (willRetrieveDataByName) {\n          assert(dim.property != null);\n        }\n      }\n      return {\n        // Only pick these two props. Not leak other properties like orderMeta.\n        type: dim.type,\n        property: dim.property\n      };\n    });\n    this._initDataFromProvider(0, provider.count());\n  };\n  DataStore.prototype.getProvider = function () {\n    return this._provider;\n  };\n  /**\r\n   * Caution: even when a `source` instance owned by a series, the created data store\r\n   * may still be shared by different sereis (the source hash does not use all `source`\r\n   * props, see `sourceManager`). In this case, the `source` props that are not used in\r\n   * hash (like `source.dimensionDefine`) probably only belongs to a certain series and\r\n   * thus should not be fetch here.\r\n   */\n  DataStore.prototype.getSource = function () {\n    return this._provider.getSource();\n  };\n  /**\r\n   * @caution Only used in dataStack.\r\n   */\n  DataStore.prototype.ensureCalculationDimension = function (dimName, type) {\n    var calcDimNameToIdx = this._calcDimNameToIdx;\n    var dimensions = this._dimensions;\n    var calcDimIdx = calcDimNameToIdx.get(dimName);\n    if (calcDimIdx != null) {\n      if (dimensions[calcDimIdx].type === type) {\n        return calcDimIdx;\n      }\n    } else {\n      calcDimIdx = dimensions.length;\n    }\n    dimensions[calcDimIdx] = {\n      type: type\n    };\n    calcDimNameToIdx.set(dimName, calcDimIdx);\n    this._chunks[calcDimIdx] = new dataCtors[type || 'float'](this._rawCount);\n    this._rawExtent[calcDimIdx] = getInitialExtent();\n    return calcDimIdx;\n  };\n  DataStore.prototype.collectOrdinalMeta = function (dimIdx, ordinalMeta) {\n    var chunk = this._chunks[dimIdx];\n    var dim = this._dimensions[dimIdx];\n    var rawExtents = this._rawExtent;\n    var offset = dim.ordinalOffset || 0;\n    var len = chunk.length;\n    if (offset === 0) {\n      // We need to reset the rawExtent if collect is from start.\n      // Because this dimension may be guessed as number and calcuating a wrong extent.\n      rawExtents[dimIdx] = getInitialExtent();\n    }\n    var dimRawExtent = rawExtents[dimIdx];\n    // Parse from previous data offset. len may be changed after appendData\n    for (var i = offset; i < len; i++) {\n      var val = chunk[i] = ordinalMeta.parseAndCollect(chunk[i]);\n      if (!isNaN(val)) {\n        dimRawExtent[0] = Math.min(val, dimRawExtent[0]);\n        dimRawExtent[1] = Math.max(val, dimRawExtent[1]);\n      }\n    }\n    dim.ordinalMeta = ordinalMeta;\n    dim.ordinalOffset = len;\n    dim.type = 'ordinal'; // Force to be ordinal\n  };\n  DataStore.prototype.getOrdinalMeta = function (dimIdx) {\n    var dimInfo = this._dimensions[dimIdx];\n    var ordinalMeta = dimInfo.ordinalMeta;\n    return ordinalMeta;\n  };\n  DataStore.prototype.getDimensionProperty = function (dimIndex) {\n    var item = this._dimensions[dimIndex];\n    return item && item.property;\n  };\n  /**\r\n   * Caution: Can be only called on raw data (before `this._indices` created).\r\n   */\n  DataStore.prototype.appendData = function (data) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(!this._indices, 'appendData can only be called on raw data.');\n    }\n    var provider = this._provider;\n    var start = this.count();\n    provider.appendData(data);\n    var end = provider.count();\n    if (!provider.persistent) {\n      end += start;\n    }\n    if (start < end) {\n      this._initDataFromProvider(start, end, true);\n    }\n    return [start, end];\n  };\n  DataStore.prototype.appendValues = function (values, minFillLen) {\n    var chunks = this._chunks;\n    var dimensions = this._dimensions;\n    var dimLen = dimensions.length;\n    var rawExtent = this._rawExtent;\n    var start = this.count();\n    var end = start + Math.max(values.length, minFillLen || 0);\n    for (var i = 0; i < dimLen; i++) {\n      var dim = dimensions[i];\n      prepareStore(chunks, i, dim.type, end, true);\n    }\n    var emptyDataItem = [];\n    for (var idx = start; idx < end; idx++) {\n      var sourceIdx = idx - start;\n      // Store the data by dimensions\n      for (var dimIdx = 0; dimIdx < dimLen; dimIdx++) {\n        var dim = dimensions[dimIdx];\n        var val = defaultDimValueGetters.arrayRows.call(this, values[sourceIdx] || emptyDataItem, dim.property, sourceIdx, dimIdx);\n        chunks[dimIdx][idx] = val;\n        var dimRawExtent = rawExtent[dimIdx];\n        val < dimRawExtent[0] && (dimRawExtent[0] = val);\n        val > dimRawExtent[1] && (dimRawExtent[1] = val);\n      }\n    }\n    this._rawCount = this._count = end;\n    return {\n      start: start,\n      end: end\n    };\n  };\n  DataStore.prototype._initDataFromProvider = function (start, end, append) {\n    var provider = this._provider;\n    var chunks = this._chunks;\n    var dimensions = this._dimensions;\n    var dimLen = dimensions.length;\n    var rawExtent = this._rawExtent;\n    var dimNames = map(dimensions, function (dim) {\n      return dim.property;\n    });\n    for (var i = 0; i < dimLen; i++) {\n      var dim = dimensions[i];\n      if (!rawExtent[i]) {\n        rawExtent[i] = getInitialExtent();\n      }\n      prepareStore(chunks, i, dim.type, end, append);\n    }\n    if (provider.fillStorage) {\n      provider.fillStorage(start, end, chunks, rawExtent);\n    } else {\n      var dataItem = [];\n      for (var idx = start; idx < end; idx++) {\n        // NOTICE: Try not to write things into dataItem\n        dataItem = provider.getItem(idx, dataItem);\n        // Each data item is value\n        // [1, 2]\n        // 2\n        // Bar chart, line chart which uses category axis\n        // only gives the 'y' value. 'x' value is the indices of category\n        // Use a tempValue to normalize the value to be a (x, y) value\n        // Store the data by dimensions\n        for (var dimIdx = 0; dimIdx < dimLen; dimIdx++) {\n          var dimStorage = chunks[dimIdx];\n          // PENDING NULL is empty or zero\n          var val = this._dimValueGetter(dataItem, dimNames[dimIdx], idx, dimIdx);\n          dimStorage[idx] = val;\n          var dimRawExtent = rawExtent[dimIdx];\n          val < dimRawExtent[0] && (dimRawExtent[0] = val);\n          val > dimRawExtent[1] && (dimRawExtent[1] = val);\n        }\n      }\n    }\n    if (!provider.persistent && provider.clean) {\n      // Clean unused data if data source is typed array.\n      provider.clean();\n    }\n    this._rawCount = this._count = end;\n    // Reset data extent\n    this._extent = [];\n  };\n  DataStore.prototype.count = function () {\n    return this._count;\n  };\n  /**\r\n   * Get value. Return NaN if idx is out of range.\r\n   */\n  DataStore.prototype.get = function (dim, idx) {\n    if (!(idx >= 0 && idx < this._count)) {\n      return NaN;\n    }\n    var dimStore = this._chunks[dim];\n    return dimStore ? dimStore[this.getRawIndex(idx)] : NaN;\n  };\n  DataStore.prototype.getValues = function (dimensions, idx) {\n    var values = [];\n    var dimArr = [];\n    if (idx == null) {\n      idx = dimensions;\n      // TODO get all from store?\n      dimensions = [];\n      // All dimensions\n      for (var i = 0; i < this._dimensions.length; i++) {\n        dimArr.push(i);\n      }\n    } else {\n      dimArr = dimensions;\n    }\n    for (var i = 0, len = dimArr.length; i < len; i++) {\n      values.push(this.get(dimArr[i], idx));\n    }\n    return values;\n  };\n  /**\r\n   * @param dim concrete dim\r\n   */\n  DataStore.prototype.getByRawIndex = function (dim, rawIdx) {\n    if (!(rawIdx >= 0 && rawIdx < this._rawCount)) {\n      return NaN;\n    }\n    var dimStore = this._chunks[dim];\n    return dimStore ? dimStore[rawIdx] : NaN;\n  };\n  /**\r\n   * Get sum of data in one dimension\r\n   */\n  DataStore.prototype.getSum = function (dim) {\n    var dimData = this._chunks[dim];\n    var sum = 0;\n    if (dimData) {\n      for (var i = 0, len = this.count(); i < len; i++) {\n        var value = this.get(dim, i);\n        if (!isNaN(value)) {\n          sum += value;\n        }\n      }\n    }\n    return sum;\n  };\n  /**\r\n   * Get median of data in one dimension\r\n   */\n  DataStore.prototype.getMedian = function (dim) {\n    var dimDataArray = [];\n    // map all data of one dimension\n    this.each([dim], function (val) {\n      if (!isNaN(val)) {\n        dimDataArray.push(val);\n      }\n    });\n    // TODO\n    // Use quick select?\n    var sortedDimDataArray = dimDataArray.sort(function (a, b) {\n      return a - b;\n    });\n    var len = this.count();\n    // calculate median\n    return len === 0 ? 0 : len % 2 === 1 ? sortedDimDataArray[(len - 1) / 2] : (sortedDimDataArray[len / 2] + sortedDimDataArray[len / 2 - 1]) / 2;\n  };\n  /**\r\n   * Retrieve the index with given raw data index.\r\n   */\n  DataStore.prototype.indexOfRawIndex = function (rawIndex) {\n    if (rawIndex >= this._rawCount || rawIndex < 0) {\n      return -1;\n    }\n    if (!this._indices) {\n      return rawIndex;\n    }\n    // Indices are ascending\n    var indices = this._indices;\n    // If rawIndex === dataIndex\n    var rawDataIndex = indices[rawIndex];\n    if (rawDataIndex != null && rawDataIndex < this._count && rawDataIndex === rawIndex) {\n      return rawIndex;\n    }\n    var left = 0;\n    var right = this._count - 1;\n    while (left <= right) {\n      var mid = (left + right) / 2 | 0;\n      if (indices[mid] < rawIndex) {\n        left = mid + 1;\n      } else if (indices[mid] > rawIndex) {\n        right = mid - 1;\n      } else {\n        return mid;\n      }\n    }\n    return -1;\n  };\n  /**\r\n   * Retrieve the index of nearest value.\r\n   * @param dim\r\n   * @param value\r\n   * @param [maxDistance=Infinity]\r\n   * @return If and only if multiple indices have\r\n   *         the same value, they are put to the result.\r\n   */\n  DataStore.prototype.indicesOfNearest = function (dim, value, maxDistance) {\n    var chunks = this._chunks;\n    var dimData = chunks[dim];\n    var nearestIndices = [];\n    if (!dimData) {\n      return nearestIndices;\n    }\n    if (maxDistance == null) {\n      maxDistance = Infinity;\n    }\n    var minDist = Infinity;\n    var minDiff = -1;\n    var nearestIndicesLen = 0;\n    // Check the test case of `test/ut/spec/data/SeriesData.js`.\n    for (var i = 0, len = this.count(); i < len; i++) {\n      var dataIndex = this.getRawIndex(i);\n      var diff = value - dimData[dataIndex];\n      var dist = Math.abs(diff);\n      if (dist <= maxDistance) {\n        // When the `value` is at the middle of `this.get(dim, i)` and `this.get(dim, i+1)`,\n        // we'd better not push both of them to `nearestIndices`, otherwise it is easy to\n        // get more than one item in `nearestIndices` (more specifically, in `tooltip`).\n        // So we choose the one that `diff >= 0` in this case.\n        // But if `this.get(dim, i)` and `this.get(dim, j)` get the same value, both of them\n        // should be push to `nearestIndices`.\n        if (dist < minDist || dist === minDist && diff >= 0 && minDiff < 0) {\n          minDist = dist;\n          minDiff = diff;\n          nearestIndicesLen = 0;\n        }\n        if (diff === minDiff) {\n          nearestIndices[nearestIndicesLen++] = i;\n        }\n      }\n    }\n    nearestIndices.length = nearestIndicesLen;\n    return nearestIndices;\n  };\n  DataStore.prototype.getIndices = function () {\n    var newIndices;\n    var indices = this._indices;\n    if (indices) {\n      var Ctor = indices.constructor;\n      var thisCount = this._count;\n      // `new Array(a, b, c)` is different from `new Uint32Array(a, b, c)`.\n      if (Ctor === Array) {\n        newIndices = new Ctor(thisCount);\n        for (var i = 0; i < thisCount; i++) {\n          newIndices[i] = indices[i];\n        }\n      } else {\n        newIndices = new Ctor(indices.buffer, 0, thisCount);\n      }\n    } else {\n      var Ctor = getIndicesCtor(this._rawCount);\n      newIndices = new Ctor(this.count());\n      for (var i = 0; i < newIndices.length; i++) {\n        newIndices[i] = i;\n      }\n    }\n    return newIndices;\n  };\n  /**\r\n   * Data filter.\r\n   */\n  DataStore.prototype.filter = function (dims, cb) {\n    if (!this._count) {\n      return this;\n    }\n    var newStore = this.clone();\n    var count = newStore.count();\n    var Ctor = getIndicesCtor(newStore._rawCount);\n    var newIndices = new Ctor(count);\n    var value = [];\n    var dimSize = dims.length;\n    var offset = 0;\n    var dim0 = dims[0];\n    var chunks = newStore._chunks;\n    for (var i = 0; i < count; i++) {\n      var keep = void 0;\n      var rawIdx = newStore.getRawIndex(i);\n      // Simple optimization\n      if (dimSize === 0) {\n        keep = cb(i);\n      } else if (dimSize === 1) {\n        var val = chunks[dim0][rawIdx];\n        keep = cb(val, i);\n      } else {\n        var k = 0;\n        for (; k < dimSize; k++) {\n          value[k] = chunks[dims[k]][rawIdx];\n        }\n        value[k] = i;\n        keep = cb.apply(null, value);\n      }\n      if (keep) {\n        newIndices[offset++] = rawIdx;\n      }\n    }\n    // Set indices after filtered.\n    if (offset < count) {\n      newStore._indices = newIndices;\n    }\n    newStore._count = offset;\n    // Reset data extent\n    newStore._extent = [];\n    newStore._updateGetRawIdx();\n    return newStore;\n  };\n  /**\r\n   * Select data in range. (For optimization of filter)\r\n   * (Manually inline code, support 5 million data filtering in data zoom.)\r\n   */\n  DataStore.prototype.selectRange = function (range) {\n    var newStore = this.clone();\n    var len = newStore._count;\n    if (!len) {\n      return this;\n    }\n    var dims = keys(range);\n    var dimSize = dims.length;\n    if (!dimSize) {\n      return this;\n    }\n    var originalCount = newStore.count();\n    var Ctor = getIndicesCtor(newStore._rawCount);\n    var newIndices = new Ctor(originalCount);\n    var offset = 0;\n    var dim0 = dims[0];\n    var min = range[dim0][0];\n    var max = range[dim0][1];\n    var storeArr = newStore._chunks;\n    var quickFinished = false;\n    if (!newStore._indices) {\n      // Extreme optimization for common case. About 2x faster in chrome.\n      var idx = 0;\n      if (dimSize === 1) {\n        var dimStorage = storeArr[dims[0]];\n        for (var i = 0; i < len; i++) {\n          var val = dimStorage[i];\n          // NaN will not be filtered. Consider the case, in line chart, empty\n          // value indicates the line should be broken. But for the case like\n          // scatter plot, a data item with empty value will not be rendered,\n          // but the axis extent may be effected if some other dim of the data\n          // item has value. Fortunately it is not a significant negative effect.\n          if (val >= min && val <= max || isNaN(val)) {\n            newIndices[offset++] = idx;\n          }\n          idx++;\n        }\n        quickFinished = true;\n      } else if (dimSize === 2) {\n        var dimStorage = storeArr[dims[0]];\n        var dimStorage2 = storeArr[dims[1]];\n        var min2 = range[dims[1]][0];\n        var max2 = range[dims[1]][1];\n        for (var i = 0; i < len; i++) {\n          var val = dimStorage[i];\n          var val2 = dimStorage2[i];\n          // Do not filter NaN, see comment above.\n          if ((val >= min && val <= max || isNaN(val)) && (val2 >= min2 && val2 <= max2 || isNaN(val2))) {\n            newIndices[offset++] = idx;\n          }\n          idx++;\n        }\n        quickFinished = true;\n      }\n    }\n    if (!quickFinished) {\n      if (dimSize === 1) {\n        for (var i = 0; i < originalCount; i++) {\n          var rawIndex = newStore.getRawIndex(i);\n          var val = storeArr[dims[0]][rawIndex];\n          // Do not filter NaN, see comment above.\n          if (val >= min && val <= max || isNaN(val)) {\n            newIndices[offset++] = rawIndex;\n          }\n        }\n      } else {\n        for (var i = 0; i < originalCount; i++) {\n          var keep = true;\n          var rawIndex = newStore.getRawIndex(i);\n          for (var k = 0; k < dimSize; k++) {\n            var dimk = dims[k];\n            var val = storeArr[dimk][rawIndex];\n            // Do not filter NaN, see comment above.\n            if (val < range[dimk][0] || val > range[dimk][1]) {\n              keep = false;\n            }\n          }\n          if (keep) {\n            newIndices[offset++] = newStore.getRawIndex(i);\n          }\n        }\n      }\n    }\n    // Set indices after filtered.\n    if (offset < originalCount) {\n      newStore._indices = newIndices;\n    }\n    newStore._count = offset;\n    // Reset data extent\n    newStore._extent = [];\n    newStore._updateGetRawIdx();\n    return newStore;\n  };\n  // /**\n  //  * Data mapping to a plain array\n  //  */\n  // mapArray(dims: DimensionIndex[], cb: MapArrayCb): any[] {\n  //     const result: any[] = [];\n  //     this.each(dims, function () {\n  //         result.push(cb && (cb as MapArrayCb).apply(null, arguments));\n  //     });\n  //     return result;\n  // }\n  /**\r\n   * Data mapping to a new List with given dimensions\r\n   */\n  DataStore.prototype.map = function (dims, cb) {\n    // TODO only clone picked chunks.\n    var target = this.clone(dims);\n    this._updateDims(target, dims, cb);\n    return target;\n  };\n  /**\r\n   * @caution Danger!! Only used in dataStack.\r\n   */\n  DataStore.prototype.modify = function (dims, cb) {\n    this._updateDims(this, dims, cb);\n  };\n  DataStore.prototype._updateDims = function (target, dims, cb) {\n    var targetChunks = target._chunks;\n    var tmpRetValue = [];\n    var dimSize = dims.length;\n    var dataCount = target.count();\n    var values = [];\n    var rawExtent = target._rawExtent;\n    for (var i = 0; i < dims.length; i++) {\n      rawExtent[dims[i]] = getInitialExtent();\n    }\n    for (var dataIndex = 0; dataIndex < dataCount; dataIndex++) {\n      var rawIndex = target.getRawIndex(dataIndex);\n      for (var k = 0; k < dimSize; k++) {\n        values[k] = targetChunks[dims[k]][rawIndex];\n      }\n      values[dimSize] = dataIndex;\n      var retValue = cb && cb.apply(null, values);\n      if (retValue != null) {\n        // a number or string (in oridinal dimension)?\n        if (typeof retValue !== 'object') {\n          tmpRetValue[0] = retValue;\n          retValue = tmpRetValue;\n        }\n        for (var i = 0; i < retValue.length; i++) {\n          var dim = dims[i];\n          var val = retValue[i];\n          var rawExtentOnDim = rawExtent[dim];\n          var dimStore = targetChunks[dim];\n          if (dimStore) {\n            dimStore[rawIndex] = val;\n          }\n          if (val < rawExtentOnDim[0]) {\n            rawExtentOnDim[0] = val;\n          }\n          if (val > rawExtentOnDim[1]) {\n            rawExtentOnDim[1] = val;\n          }\n        }\n      }\n    }\n  };\n  /**\r\n   * Large data down sampling using largest-triangle-three-buckets\r\n   * @param {string} valueDimension\r\n   * @param {number} targetCount\r\n   */\n  DataStore.prototype.lttbDownSample = function (valueDimension, rate) {\n    var target = this.clone([valueDimension], true);\n    var targetStorage = target._chunks;\n    var dimStore = targetStorage[valueDimension];\n    var len = this.count();\n    var sampledIndex = 0;\n    var frameSize = Math.floor(1 / rate);\n    var currentRawIndex = this.getRawIndex(0);\n    var maxArea;\n    var area;\n    var nextRawIndex;\n    var newIndices = new (getIndicesCtor(this._rawCount))(Math.min((Math.ceil(len / frameSize) + 2) * 2, len));\n    // First frame use the first data.\n    newIndices[sampledIndex++] = currentRawIndex;\n    for (var i = 1; i < len - 1; i += frameSize) {\n      var nextFrameStart = Math.min(i + frameSize, len - 1);\n      var nextFrameEnd = Math.min(i + frameSize * 2, len);\n      var avgX = (nextFrameEnd + nextFrameStart) / 2;\n      var avgY = 0;\n      for (var idx = nextFrameStart; idx < nextFrameEnd; idx++) {\n        var rawIndex = this.getRawIndex(idx);\n        var y = dimStore[rawIndex];\n        if (isNaN(y)) {\n          continue;\n        }\n        avgY += y;\n      }\n      avgY /= nextFrameEnd - nextFrameStart;\n      var frameStart = i;\n      var frameEnd = Math.min(i + frameSize, len);\n      var pointAX = i - 1;\n      var pointAY = dimStore[currentRawIndex];\n      maxArea = -1;\n      nextRawIndex = frameStart;\n      var firstNaNIndex = -1;\n      var countNaN = 0;\n      // Find a point from current frame that construct a triangle with largest area with previous selected point\n      // And the average of next frame.\n      for (var idx = frameStart; idx < frameEnd; idx++) {\n        var rawIndex = this.getRawIndex(idx);\n        var y = dimStore[rawIndex];\n        if (isNaN(y)) {\n          countNaN++;\n          if (firstNaNIndex < 0) {\n            firstNaNIndex = rawIndex;\n          }\n          continue;\n        }\n        // Calculate triangle area over three buckets\n        area = Math.abs((pointAX - avgX) * (y - pointAY) - (pointAX - idx) * (avgY - pointAY));\n        if (area > maxArea) {\n          maxArea = area;\n          nextRawIndex = rawIndex; // Next a is this b\n        }\n      }\n      if (countNaN > 0 && countNaN < frameEnd - frameStart) {\n        // Append first NaN point in every bucket.\n        // It is necessary to ensure the correct order of indices.\n        newIndices[sampledIndex++] = Math.min(firstNaNIndex, nextRawIndex);\n        nextRawIndex = Math.max(firstNaNIndex, nextRawIndex);\n      }\n      newIndices[sampledIndex++] = nextRawIndex;\n      currentRawIndex = nextRawIndex; // This a is the next a (chosen b)\n    }\n    // First frame use the last data.\n    newIndices[sampledIndex++] = this.getRawIndex(len - 1);\n    target._count = sampledIndex;\n    target._indices = newIndices;\n    target.getRawIndex = this._getRawIdx;\n    return target;\n  };\n  /**\r\n   * Large data down sampling using min-max\r\n   * @param {string} valueDimension\r\n   * @param {number} rate\r\n   */\n  DataStore.prototype.minmaxDownSample = function (valueDimension, rate) {\n    var target = this.clone([valueDimension], true);\n    var targetStorage = target._chunks;\n    var frameSize = Math.floor(1 / rate);\n    var dimStore = targetStorage[valueDimension];\n    var len = this.count();\n    // Each frame results in 2 data points, one for min and one for max\n    var newIndices = new (getIndicesCtor(this._rawCount))(Math.ceil(len / frameSize) * 2);\n    var offset = 0;\n    for (var i = 0; i < len; i += frameSize) {\n      var minIndex = i;\n      var minValue = dimStore[this.getRawIndex(minIndex)];\n      var maxIndex = i;\n      var maxValue = dimStore[this.getRawIndex(maxIndex)];\n      var thisFrameSize = frameSize;\n      // Handle final smaller frame\n      if (i + frameSize > len) {\n        thisFrameSize = len - i;\n      }\n      // Determine min and max within the current frame\n      for (var k = 0; k < thisFrameSize; k++) {\n        var rawIndex = this.getRawIndex(i + k);\n        var value = dimStore[rawIndex];\n        if (value < minValue) {\n          minValue = value;\n          minIndex = i + k;\n        }\n        if (value > maxValue) {\n          maxValue = value;\n          maxIndex = i + k;\n        }\n      }\n      var rawMinIndex = this.getRawIndex(minIndex);\n      var rawMaxIndex = this.getRawIndex(maxIndex);\n      // Set the order of the min and max values, based on their ordering in the frame\n      if (minIndex < maxIndex) {\n        newIndices[offset++] = rawMinIndex;\n        newIndices[offset++] = rawMaxIndex;\n      } else {\n        newIndices[offset++] = rawMaxIndex;\n        newIndices[offset++] = rawMinIndex;\n      }\n    }\n    target._count = offset;\n    target._indices = newIndices;\n    target._updateGetRawIdx();\n    return target;\n  };\n  /**\r\n   * Large data down sampling on given dimension\r\n   * @param sampleIndex Sample index for name and id\r\n   */\n  DataStore.prototype.downSample = function (dimension, rate, sampleValue, sampleIndex) {\n    var target = this.clone([dimension], true);\n    var targetStorage = target._chunks;\n    var frameValues = [];\n    var frameSize = Math.floor(1 / rate);\n    var dimStore = targetStorage[dimension];\n    var len = this.count();\n    var rawExtentOnDim = target._rawExtent[dimension] = getInitialExtent();\n    var newIndices = new (getIndicesCtor(this._rawCount))(Math.ceil(len / frameSize));\n    var offset = 0;\n    for (var i = 0; i < len; i += frameSize) {\n      // Last frame\n      if (frameSize > len - i) {\n        frameSize = len - i;\n        frameValues.length = frameSize;\n      }\n      for (var k = 0; k < frameSize; k++) {\n        var dataIdx = this.getRawIndex(i + k);\n        frameValues[k] = dimStore[dataIdx];\n      }\n      var value = sampleValue(frameValues);\n      var sampleFrameIdx = this.getRawIndex(Math.min(i + sampleIndex(frameValues, value) || 0, len - 1));\n      // Only write value on the filtered data\n      dimStore[sampleFrameIdx] = value;\n      if (value < rawExtentOnDim[0]) {\n        rawExtentOnDim[0] = value;\n      }\n      if (value > rawExtentOnDim[1]) {\n        rawExtentOnDim[1] = value;\n      }\n      newIndices[offset++] = sampleFrameIdx;\n    }\n    target._count = offset;\n    target._indices = newIndices;\n    target._updateGetRawIdx();\n    return target;\n  };\n  /**\r\n   * Data iteration\r\n   * @param ctx default this\r\n   * @example\r\n   *  list.each('x', function (x, idx) {});\r\n   *  list.each(['x', 'y'], function (x, y, idx) {});\r\n   *  list.each(function (idx) {})\r\n   */\n  DataStore.prototype.each = function (dims, cb) {\n    if (!this._count) {\n      return;\n    }\n    var dimSize = dims.length;\n    var chunks = this._chunks;\n    for (var i = 0, len = this.count(); i < len; i++) {\n      var rawIdx = this.getRawIndex(i);\n      // Simple optimization\n      switch (dimSize) {\n        case 0:\n          cb(i);\n          break;\n        case 1:\n          cb(chunks[dims[0]][rawIdx], i);\n          break;\n        case 2:\n          cb(chunks[dims[0]][rawIdx], chunks[dims[1]][rawIdx], i);\n          break;\n        default:\n          var k = 0;\n          var value = [];\n          for (; k < dimSize; k++) {\n            value[k] = chunks[dims[k]][rawIdx];\n          }\n          // Index\n          value[k] = i;\n          cb.apply(null, value);\n      }\n    }\n  };\n  /**\r\n   * Get extent of data in one dimension\r\n   */\n  DataStore.prototype.getDataExtent = function (dim) {\n    // Make sure use concrete dim as cache name.\n    var dimData = this._chunks[dim];\n    var initialExtent = getInitialExtent();\n    if (!dimData) {\n      return initialExtent;\n    }\n    // Make more strict checkings to ensure hitting cache.\n    var currEnd = this.count();\n    // Consider the most cases when using data zoom, `getDataExtent`\n    // happened before filtering. We cache raw extent, which is not\n    // necessary to be cleared and recalculated when restore data.\n    var useRaw = !this._indices;\n    var dimExtent;\n    if (useRaw) {\n      return this._rawExtent[dim].slice();\n    }\n    dimExtent = this._extent[dim];\n    if (dimExtent) {\n      return dimExtent.slice();\n    }\n    dimExtent = initialExtent;\n    var min = dimExtent[0];\n    var max = dimExtent[1];\n    for (var i = 0; i < currEnd; i++) {\n      var rawIdx = this.getRawIndex(i);\n      var value = dimData[rawIdx];\n      value < min && (min = value);\n      value > max && (max = value);\n    }\n    dimExtent = [min, max];\n    this._extent[dim] = dimExtent;\n    return dimExtent;\n  };\n  /**\r\n   * Get raw data item\r\n   */\n  DataStore.prototype.getRawDataItem = function (idx) {\n    var rawIdx = this.getRawIndex(idx);\n    if (!this._provider.persistent) {\n      var val = [];\n      var chunks = this._chunks;\n      for (var i = 0; i < chunks.length; i++) {\n        val.push(chunks[i][rawIdx]);\n      }\n      return val;\n    } else {\n      return this._provider.getItem(rawIdx);\n    }\n  };\n  /**\r\n   * Clone shallow.\r\n   *\r\n   * @param clonedDims Determine which dims to clone. Will share the data if not specified.\r\n   */\n  DataStore.prototype.clone = function (clonedDims, ignoreIndices) {\n    var target = new DataStore();\n    var chunks = this._chunks;\n    var clonedDimsMap = clonedDims && reduce(clonedDims, function (obj, dimIdx) {\n      obj[dimIdx] = true;\n      return obj;\n    }, {});\n    if (clonedDimsMap) {\n      for (var i = 0; i < chunks.length; i++) {\n        // Not clone if dim is not picked.\n        target._chunks[i] = !clonedDimsMap[i] ? chunks[i] : cloneChunk(chunks[i]);\n      }\n    } else {\n      target._chunks = chunks;\n    }\n    this._copyCommonProps(target);\n    if (!ignoreIndices) {\n      target._indices = this._cloneIndices();\n    }\n    target._updateGetRawIdx();\n    return target;\n  };\n  DataStore.prototype._copyCommonProps = function (target) {\n    target._count = this._count;\n    target._rawCount = this._rawCount;\n    target._provider = this._provider;\n    target._dimensions = this._dimensions;\n    target._extent = clone(this._extent);\n    target._rawExtent = clone(this._rawExtent);\n  };\n  DataStore.prototype._cloneIndices = function () {\n    if (this._indices) {\n      var Ctor = this._indices.constructor;\n      var indices = void 0;\n      if (Ctor === Array) {\n        var thisCount = this._indices.length;\n        indices = new Ctor(thisCount);\n        for (var i = 0; i < thisCount; i++) {\n          indices[i] = this._indices[i];\n        }\n      } else {\n        indices = new Ctor(this._indices);\n      }\n      return indices;\n    }\n    return null;\n  };\n  DataStore.prototype._getRawIdxIdentity = function (idx) {\n    return idx;\n  };\n  DataStore.prototype._getRawIdx = function (idx) {\n    if (idx < this._count && idx >= 0) {\n      return this._indices[idx];\n    }\n    return -1;\n  };\n  DataStore.prototype._updateGetRawIdx = function () {\n    this.getRawIndex = this._indices ? this._getRawIdx : this._getRawIdxIdentity;\n  };\n  DataStore.internalField = function () {\n    function getDimValueSimply(dataItem, property, dataIndex, dimIndex) {\n      return parseDataValue(dataItem[dimIndex], this._dimensions[dimIndex]);\n    }\n    defaultDimValueGetters = {\n      arrayRows: getDimValueSimply,\n      objectRows: function (dataItem, property, dataIndex, dimIndex) {\n        return parseDataValue(dataItem[property], this._dimensions[dimIndex]);\n      },\n      keyedColumns: getDimValueSimply,\n      original: function (dataItem, property, dataIndex, dimIndex) {\n        // Performance sensitive, do not use modelUtil.getDataItemValue.\n        // If dataItem is an plain object with no value field, the let `value`\n        // will be assigned with the object, but it will be tread correctly\n        // in the `convertValue`.\n        var value = dataItem && (dataItem.value == null ? dataItem : dataItem.value);\n        return parseDataValue(value instanceof Array ? value[dimIndex]\n        // If value is a single number or something else not array.\n        : value, this._dimensions[dimIndex]);\n      },\n      typedArray: function (dataItem, property, dataIndex, dimIndex) {\n        return dataItem[dimIndex];\n      }\n    };\n  }();\n  return DataStore;\n}();\nexport default DataStore;", "map": {"version": 3, "names": ["assert", "clone", "createHashMap", "isFunction", "keys", "map", "reduce", "parseDataValue", "shouldRetrieveDataByName", "UNDEFINED", "CtorUint32Array", "Uint32Array", "Array", "CtorUint16Array", "Uint16Array", "CtorInt32Array", "Int32Array", "CtorFloat64Array", "Float64Array", "dataCtors", "defaultDimValueGetters", "getIndicesCtor", "rawCount", "getInitialExtent", "Infinity", "cloneChunk", "originalChunk", "Ctor", "constructor", "slice", "prepareStore", "store", "dimIdx", "dimType", "end", "append", "DataCtor", "oldStore", "old<PERSON>en", "length", "newStore", "j", "DataStore", "_chunks", "_rawExtent", "_extent", "_count", "_rawCount", "_calcDimNameToIdx", "prototype", "initData", "provider", "inputDimensions", "dimValueGetter", "process", "env", "NODE_ENV", "getItem", "count", "_provider", "_indices", "getRawIndex", "_getRawIdxIdentity", "source", "getSource", "defaultGetter", "defaultDimValueGetter", "sourceFormat", "_dimValueGetter", "willRetrieveDataByName", "_dimensions", "dim", "property", "type", "_initDataFromProvider", "get<PERSON><PERSON><PERSON>", "ensureCalculationDimension", "dimName", "calcDimNameToIdx", "dimensions", "calcDimIdx", "get", "set", "collectOrdinalMeta", "ordinalMeta", "chunk", "rawExtents", "offset", "ordinalOffset", "len", "dimRawExtent", "i", "val", "parseAndCollect", "isNaN", "Math", "min", "max", "getOrdinalMeta", "dimInfo", "getDimensionProperty", "dimIndex", "item", "appendData", "data", "start", "persistent", "append<PERSON>al<PERSON>", "values", "minFillLen", "chunks", "dim<PERSON>en", "rawExtent", "emptyDataItem", "idx", "sourceIdx", "arrayRows", "call", "dimNames", "fillStorage", "dataItem", "dimStorage", "clean", "NaN", "dimStore", "getV<PERSON>ues", "dimArr", "push", "getByRawIndex", "rawIdx", "getSum", "dimData", "sum", "value", "getMedian", "dimDataArray", "each", "sortedDimDataArray", "sort", "a", "b", "indexOfRawIndex", "rawIndex", "indices", "rawDataIndex", "left", "right", "mid", "indicesOfNearest", "maxDistance", "nearestIndices", "minDist", "minDiff", "nearestIndicesLen", "dataIndex", "diff", "dist", "abs", "getIndices", "newIndices", "thisCount", "buffer", "filter", "dims", "cb", "dimSize", "dim0", "keep", "k", "apply", "_updateGetRawIdx", "selectRange", "range", "originalCount", "storeArr", "quickFinished", "dimStorage2", "min2", "max2", "val2", "dimk", "target", "_updateDims", "modify", "targetChunks", "tmpRetValue", "dataCount", "retValue", "rawExtentOnDim", "lttbDownSample", "valueDimension", "rate", "targetStorage", "sampledIndex", "frameSize", "floor", "currentRawIndex", "maxArea", "area", "nextRawIndex", "ceil", "nextFrameStart", "nextFrameEnd", "avgX", "avgY", "y", "frameStart", "frameEnd", "pointAX", "pointAY", "firstNaNIndex", "countNaN", "_getRawIdx", "minmaxDownSample", "minIndex", "minValue", "maxIndex", "maxValue", "thisFrameSize", "rawMinIndex", "rawMaxIndex", "downSample", "dimension", "sampleValue", "sampleIndex", "frameValues", "dataIdx", "sampleFrameIdx", "getDataExtent", "initialExtent", "currEnd", "useRaw", "dimExtent", "getRawDataItem", "clonedDims", "ignoreIndices", "clonedDimsMap", "obj", "_copyCommonProps", "_cloneIndices", "internalField", "getDimValueSimply", "objectRows", "keyedColumns", "original", "typedArray"], "sources": ["E:/AI/SmartCV/node_modules/echarts/lib/data/DataStore.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { assert, clone, createHashMap, isFunction, keys, map, reduce } from 'zrender/lib/core/util.js';\nimport { parseDataValue } from './helper/dataValueHelper.js';\nimport { shouldRetrieveDataByName } from './Source.js';\nvar UNDEFINED = 'undefined';\n/* global Float64Array, Int32Array, Uint32Array, Uint16Array */\n// Caution: MUST not use `new CtorUint32Array(arr, 0, len)`, because the Ctor of array is\n// different from the Ctor of typed array.\nexport var CtorUint32Array = typeof Uint32Array === UNDEFINED ? Array : Uint32Array;\nexport var CtorUint16Array = typeof Uint16Array === UNDEFINED ? Array : Uint16Array;\nexport var CtorInt32Array = typeof Int32Array === UNDEFINED ? Array : Int32Array;\nexport var CtorFloat64Array = typeof Float64Array === UNDEFINED ? Array : Float64Array;\n/**\r\n * Multi dimensional data store\r\n */\nvar dataCtors = {\n  'float': CtorFloat64Array,\n  'int': CtorInt32Array,\n  // Ordinal data type can be string or int\n  'ordinal': Array,\n  'number': Array,\n  'time': CtorFloat64Array\n};\nvar defaultDimValueGetters;\nfunction getIndicesCtor(rawCount) {\n  // The possible max value in this._indicies is always this._rawCount despite of filtering.\n  return rawCount > 65535 ? CtorUint32Array : CtorUint16Array;\n}\n;\nfunction getInitialExtent() {\n  return [Infinity, -Infinity];\n}\n;\nfunction cloneChunk(originalChunk) {\n  var Ctor = originalChunk.constructor;\n  // Only shallow clone is enough when Array.\n  return Ctor === Array ? originalChunk.slice() : new Ctor(originalChunk);\n}\nfunction prepareStore(store, dimIdx, dimType, end, append) {\n  var DataCtor = dataCtors[dimType || 'float'];\n  if (append) {\n    var oldStore = store[dimIdx];\n    var oldLen = oldStore && oldStore.length;\n    if (!(oldLen === end)) {\n      var newStore = new DataCtor(end);\n      // The cost of the copy is probably inconsiderable\n      // within the initial chunkSize.\n      for (var j = 0; j < oldLen; j++) {\n        newStore[j] = oldStore[j];\n      }\n      store[dimIdx] = newStore;\n    }\n  } else {\n    store[dimIdx] = new DataCtor(end);\n  }\n}\n;\n/**\r\n * Basically, DataStore API keep immutable.\r\n */\nvar DataStore = /** @class */function () {\n  function DataStore() {\n    this._chunks = [];\n    // It will not be calculated until needed.\n    this._rawExtent = [];\n    this._extent = [];\n    this._count = 0;\n    this._rawCount = 0;\n    this._calcDimNameToIdx = createHashMap();\n  }\n  /**\r\n   * Initialize from data\r\n   */\n  DataStore.prototype.initData = function (provider, inputDimensions, dimValueGetter) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(isFunction(provider.getItem) && isFunction(provider.count), 'Invalid data provider.');\n    }\n    this._provider = provider;\n    // Clear\n    this._chunks = [];\n    this._indices = null;\n    this.getRawIndex = this._getRawIdxIdentity;\n    var source = provider.getSource();\n    var defaultGetter = this.defaultDimValueGetter = defaultDimValueGetters[source.sourceFormat];\n    // Default dim value getter\n    this._dimValueGetter = dimValueGetter || defaultGetter;\n    // Reset raw extent.\n    this._rawExtent = [];\n    var willRetrieveDataByName = shouldRetrieveDataByName(source);\n    this._dimensions = map(inputDimensions, function (dim) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (willRetrieveDataByName) {\n          assert(dim.property != null);\n        }\n      }\n      return {\n        // Only pick these two props. Not leak other properties like orderMeta.\n        type: dim.type,\n        property: dim.property\n      };\n    });\n    this._initDataFromProvider(0, provider.count());\n  };\n  DataStore.prototype.getProvider = function () {\n    return this._provider;\n  };\n  /**\r\n   * Caution: even when a `source` instance owned by a series, the created data store\r\n   * may still be shared by different sereis (the source hash does not use all `source`\r\n   * props, see `sourceManager`). In this case, the `source` props that are not used in\r\n   * hash (like `source.dimensionDefine`) probably only belongs to a certain series and\r\n   * thus should not be fetch here.\r\n   */\n  DataStore.prototype.getSource = function () {\n    return this._provider.getSource();\n  };\n  /**\r\n   * @caution Only used in dataStack.\r\n   */\n  DataStore.prototype.ensureCalculationDimension = function (dimName, type) {\n    var calcDimNameToIdx = this._calcDimNameToIdx;\n    var dimensions = this._dimensions;\n    var calcDimIdx = calcDimNameToIdx.get(dimName);\n    if (calcDimIdx != null) {\n      if (dimensions[calcDimIdx].type === type) {\n        return calcDimIdx;\n      }\n    } else {\n      calcDimIdx = dimensions.length;\n    }\n    dimensions[calcDimIdx] = {\n      type: type\n    };\n    calcDimNameToIdx.set(dimName, calcDimIdx);\n    this._chunks[calcDimIdx] = new dataCtors[type || 'float'](this._rawCount);\n    this._rawExtent[calcDimIdx] = getInitialExtent();\n    return calcDimIdx;\n  };\n  DataStore.prototype.collectOrdinalMeta = function (dimIdx, ordinalMeta) {\n    var chunk = this._chunks[dimIdx];\n    var dim = this._dimensions[dimIdx];\n    var rawExtents = this._rawExtent;\n    var offset = dim.ordinalOffset || 0;\n    var len = chunk.length;\n    if (offset === 0) {\n      // We need to reset the rawExtent if collect is from start.\n      // Because this dimension may be guessed as number and calcuating a wrong extent.\n      rawExtents[dimIdx] = getInitialExtent();\n    }\n    var dimRawExtent = rawExtents[dimIdx];\n    // Parse from previous data offset. len may be changed after appendData\n    for (var i = offset; i < len; i++) {\n      var val = chunk[i] = ordinalMeta.parseAndCollect(chunk[i]);\n      if (!isNaN(val)) {\n        dimRawExtent[0] = Math.min(val, dimRawExtent[0]);\n        dimRawExtent[1] = Math.max(val, dimRawExtent[1]);\n      }\n    }\n    dim.ordinalMeta = ordinalMeta;\n    dim.ordinalOffset = len;\n    dim.type = 'ordinal'; // Force to be ordinal\n  };\n  DataStore.prototype.getOrdinalMeta = function (dimIdx) {\n    var dimInfo = this._dimensions[dimIdx];\n    var ordinalMeta = dimInfo.ordinalMeta;\n    return ordinalMeta;\n  };\n  DataStore.prototype.getDimensionProperty = function (dimIndex) {\n    var item = this._dimensions[dimIndex];\n    return item && item.property;\n  };\n  /**\r\n   * Caution: Can be only called on raw data (before `this._indices` created).\r\n   */\n  DataStore.prototype.appendData = function (data) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(!this._indices, 'appendData can only be called on raw data.');\n    }\n    var provider = this._provider;\n    var start = this.count();\n    provider.appendData(data);\n    var end = provider.count();\n    if (!provider.persistent) {\n      end += start;\n    }\n    if (start < end) {\n      this._initDataFromProvider(start, end, true);\n    }\n    return [start, end];\n  };\n  DataStore.prototype.appendValues = function (values, minFillLen) {\n    var chunks = this._chunks;\n    var dimensions = this._dimensions;\n    var dimLen = dimensions.length;\n    var rawExtent = this._rawExtent;\n    var start = this.count();\n    var end = start + Math.max(values.length, minFillLen || 0);\n    for (var i = 0; i < dimLen; i++) {\n      var dim = dimensions[i];\n      prepareStore(chunks, i, dim.type, end, true);\n    }\n    var emptyDataItem = [];\n    for (var idx = start; idx < end; idx++) {\n      var sourceIdx = idx - start;\n      // Store the data by dimensions\n      for (var dimIdx = 0; dimIdx < dimLen; dimIdx++) {\n        var dim = dimensions[dimIdx];\n        var val = defaultDimValueGetters.arrayRows.call(this, values[sourceIdx] || emptyDataItem, dim.property, sourceIdx, dimIdx);\n        chunks[dimIdx][idx] = val;\n        var dimRawExtent = rawExtent[dimIdx];\n        val < dimRawExtent[0] && (dimRawExtent[0] = val);\n        val > dimRawExtent[1] && (dimRawExtent[1] = val);\n      }\n    }\n    this._rawCount = this._count = end;\n    return {\n      start: start,\n      end: end\n    };\n  };\n  DataStore.prototype._initDataFromProvider = function (start, end, append) {\n    var provider = this._provider;\n    var chunks = this._chunks;\n    var dimensions = this._dimensions;\n    var dimLen = dimensions.length;\n    var rawExtent = this._rawExtent;\n    var dimNames = map(dimensions, function (dim) {\n      return dim.property;\n    });\n    for (var i = 0; i < dimLen; i++) {\n      var dim = dimensions[i];\n      if (!rawExtent[i]) {\n        rawExtent[i] = getInitialExtent();\n      }\n      prepareStore(chunks, i, dim.type, end, append);\n    }\n    if (provider.fillStorage) {\n      provider.fillStorage(start, end, chunks, rawExtent);\n    } else {\n      var dataItem = [];\n      for (var idx = start; idx < end; idx++) {\n        // NOTICE: Try not to write things into dataItem\n        dataItem = provider.getItem(idx, dataItem);\n        // Each data item is value\n        // [1, 2]\n        // 2\n        // Bar chart, line chart which uses category axis\n        // only gives the 'y' value. 'x' value is the indices of category\n        // Use a tempValue to normalize the value to be a (x, y) value\n        // Store the data by dimensions\n        for (var dimIdx = 0; dimIdx < dimLen; dimIdx++) {\n          var dimStorage = chunks[dimIdx];\n          // PENDING NULL is empty or zero\n          var val = this._dimValueGetter(dataItem, dimNames[dimIdx], idx, dimIdx);\n          dimStorage[idx] = val;\n          var dimRawExtent = rawExtent[dimIdx];\n          val < dimRawExtent[0] && (dimRawExtent[0] = val);\n          val > dimRawExtent[1] && (dimRawExtent[1] = val);\n        }\n      }\n    }\n    if (!provider.persistent && provider.clean) {\n      // Clean unused data if data source is typed array.\n      provider.clean();\n    }\n    this._rawCount = this._count = end;\n    // Reset data extent\n    this._extent = [];\n  };\n  DataStore.prototype.count = function () {\n    return this._count;\n  };\n  /**\r\n   * Get value. Return NaN if idx is out of range.\r\n   */\n  DataStore.prototype.get = function (dim, idx) {\n    if (!(idx >= 0 && idx < this._count)) {\n      return NaN;\n    }\n    var dimStore = this._chunks[dim];\n    return dimStore ? dimStore[this.getRawIndex(idx)] : NaN;\n  };\n  DataStore.prototype.getValues = function (dimensions, idx) {\n    var values = [];\n    var dimArr = [];\n    if (idx == null) {\n      idx = dimensions;\n      // TODO get all from store?\n      dimensions = [];\n      // All dimensions\n      for (var i = 0; i < this._dimensions.length; i++) {\n        dimArr.push(i);\n      }\n    } else {\n      dimArr = dimensions;\n    }\n    for (var i = 0, len = dimArr.length; i < len; i++) {\n      values.push(this.get(dimArr[i], idx));\n    }\n    return values;\n  };\n  /**\r\n   * @param dim concrete dim\r\n   */\n  DataStore.prototype.getByRawIndex = function (dim, rawIdx) {\n    if (!(rawIdx >= 0 && rawIdx < this._rawCount)) {\n      return NaN;\n    }\n    var dimStore = this._chunks[dim];\n    return dimStore ? dimStore[rawIdx] : NaN;\n  };\n  /**\r\n   * Get sum of data in one dimension\r\n   */\n  DataStore.prototype.getSum = function (dim) {\n    var dimData = this._chunks[dim];\n    var sum = 0;\n    if (dimData) {\n      for (var i = 0, len = this.count(); i < len; i++) {\n        var value = this.get(dim, i);\n        if (!isNaN(value)) {\n          sum += value;\n        }\n      }\n    }\n    return sum;\n  };\n  /**\r\n   * Get median of data in one dimension\r\n   */\n  DataStore.prototype.getMedian = function (dim) {\n    var dimDataArray = [];\n    // map all data of one dimension\n    this.each([dim], function (val) {\n      if (!isNaN(val)) {\n        dimDataArray.push(val);\n      }\n    });\n    // TODO\n    // Use quick select?\n    var sortedDimDataArray = dimDataArray.sort(function (a, b) {\n      return a - b;\n    });\n    var len = this.count();\n    // calculate median\n    return len === 0 ? 0 : len % 2 === 1 ? sortedDimDataArray[(len - 1) / 2] : (sortedDimDataArray[len / 2] + sortedDimDataArray[len / 2 - 1]) / 2;\n  };\n  /**\r\n   * Retrieve the index with given raw data index.\r\n   */\n  DataStore.prototype.indexOfRawIndex = function (rawIndex) {\n    if (rawIndex >= this._rawCount || rawIndex < 0) {\n      return -1;\n    }\n    if (!this._indices) {\n      return rawIndex;\n    }\n    // Indices are ascending\n    var indices = this._indices;\n    // If rawIndex === dataIndex\n    var rawDataIndex = indices[rawIndex];\n    if (rawDataIndex != null && rawDataIndex < this._count && rawDataIndex === rawIndex) {\n      return rawIndex;\n    }\n    var left = 0;\n    var right = this._count - 1;\n    while (left <= right) {\n      var mid = (left + right) / 2 | 0;\n      if (indices[mid] < rawIndex) {\n        left = mid + 1;\n      } else if (indices[mid] > rawIndex) {\n        right = mid - 1;\n      } else {\n        return mid;\n      }\n    }\n    return -1;\n  };\n  /**\r\n   * Retrieve the index of nearest value.\r\n   * @param dim\r\n   * @param value\r\n   * @param [maxDistance=Infinity]\r\n   * @return If and only if multiple indices have\r\n   *         the same value, they are put to the result.\r\n   */\n  DataStore.prototype.indicesOfNearest = function (dim, value, maxDistance) {\n    var chunks = this._chunks;\n    var dimData = chunks[dim];\n    var nearestIndices = [];\n    if (!dimData) {\n      return nearestIndices;\n    }\n    if (maxDistance == null) {\n      maxDistance = Infinity;\n    }\n    var minDist = Infinity;\n    var minDiff = -1;\n    var nearestIndicesLen = 0;\n    // Check the test case of `test/ut/spec/data/SeriesData.js`.\n    for (var i = 0, len = this.count(); i < len; i++) {\n      var dataIndex = this.getRawIndex(i);\n      var diff = value - dimData[dataIndex];\n      var dist = Math.abs(diff);\n      if (dist <= maxDistance) {\n        // When the `value` is at the middle of `this.get(dim, i)` and `this.get(dim, i+1)`,\n        // we'd better not push both of them to `nearestIndices`, otherwise it is easy to\n        // get more than one item in `nearestIndices` (more specifically, in `tooltip`).\n        // So we choose the one that `diff >= 0` in this case.\n        // But if `this.get(dim, i)` and `this.get(dim, j)` get the same value, both of them\n        // should be push to `nearestIndices`.\n        if (dist < minDist || dist === minDist && diff >= 0 && minDiff < 0) {\n          minDist = dist;\n          minDiff = diff;\n          nearestIndicesLen = 0;\n        }\n        if (diff === minDiff) {\n          nearestIndices[nearestIndicesLen++] = i;\n        }\n      }\n    }\n    nearestIndices.length = nearestIndicesLen;\n    return nearestIndices;\n  };\n  DataStore.prototype.getIndices = function () {\n    var newIndices;\n    var indices = this._indices;\n    if (indices) {\n      var Ctor = indices.constructor;\n      var thisCount = this._count;\n      // `new Array(a, b, c)` is different from `new Uint32Array(a, b, c)`.\n      if (Ctor === Array) {\n        newIndices = new Ctor(thisCount);\n        for (var i = 0; i < thisCount; i++) {\n          newIndices[i] = indices[i];\n        }\n      } else {\n        newIndices = new Ctor(indices.buffer, 0, thisCount);\n      }\n    } else {\n      var Ctor = getIndicesCtor(this._rawCount);\n      newIndices = new Ctor(this.count());\n      for (var i = 0; i < newIndices.length; i++) {\n        newIndices[i] = i;\n      }\n    }\n    return newIndices;\n  };\n  /**\r\n   * Data filter.\r\n   */\n  DataStore.prototype.filter = function (dims, cb) {\n    if (!this._count) {\n      return this;\n    }\n    var newStore = this.clone();\n    var count = newStore.count();\n    var Ctor = getIndicesCtor(newStore._rawCount);\n    var newIndices = new Ctor(count);\n    var value = [];\n    var dimSize = dims.length;\n    var offset = 0;\n    var dim0 = dims[0];\n    var chunks = newStore._chunks;\n    for (var i = 0; i < count; i++) {\n      var keep = void 0;\n      var rawIdx = newStore.getRawIndex(i);\n      // Simple optimization\n      if (dimSize === 0) {\n        keep = cb(i);\n      } else if (dimSize === 1) {\n        var val = chunks[dim0][rawIdx];\n        keep = cb(val, i);\n      } else {\n        var k = 0;\n        for (; k < dimSize; k++) {\n          value[k] = chunks[dims[k]][rawIdx];\n        }\n        value[k] = i;\n        keep = cb.apply(null, value);\n      }\n      if (keep) {\n        newIndices[offset++] = rawIdx;\n      }\n    }\n    // Set indices after filtered.\n    if (offset < count) {\n      newStore._indices = newIndices;\n    }\n    newStore._count = offset;\n    // Reset data extent\n    newStore._extent = [];\n    newStore._updateGetRawIdx();\n    return newStore;\n  };\n  /**\r\n   * Select data in range. (For optimization of filter)\r\n   * (Manually inline code, support 5 million data filtering in data zoom.)\r\n   */\n  DataStore.prototype.selectRange = function (range) {\n    var newStore = this.clone();\n    var len = newStore._count;\n    if (!len) {\n      return this;\n    }\n    var dims = keys(range);\n    var dimSize = dims.length;\n    if (!dimSize) {\n      return this;\n    }\n    var originalCount = newStore.count();\n    var Ctor = getIndicesCtor(newStore._rawCount);\n    var newIndices = new Ctor(originalCount);\n    var offset = 0;\n    var dim0 = dims[0];\n    var min = range[dim0][0];\n    var max = range[dim0][1];\n    var storeArr = newStore._chunks;\n    var quickFinished = false;\n    if (!newStore._indices) {\n      // Extreme optimization for common case. About 2x faster in chrome.\n      var idx = 0;\n      if (dimSize === 1) {\n        var dimStorage = storeArr[dims[0]];\n        for (var i = 0; i < len; i++) {\n          var val = dimStorage[i];\n          // NaN will not be filtered. Consider the case, in line chart, empty\n          // value indicates the line should be broken. But for the case like\n          // scatter plot, a data item with empty value will not be rendered,\n          // but the axis extent may be effected if some other dim of the data\n          // item has value. Fortunately it is not a significant negative effect.\n          if (val >= min && val <= max || isNaN(val)) {\n            newIndices[offset++] = idx;\n          }\n          idx++;\n        }\n        quickFinished = true;\n      } else if (dimSize === 2) {\n        var dimStorage = storeArr[dims[0]];\n        var dimStorage2 = storeArr[dims[1]];\n        var min2 = range[dims[1]][0];\n        var max2 = range[dims[1]][1];\n        for (var i = 0; i < len; i++) {\n          var val = dimStorage[i];\n          var val2 = dimStorage2[i];\n          // Do not filter NaN, see comment above.\n          if ((val >= min && val <= max || isNaN(val)) && (val2 >= min2 && val2 <= max2 || isNaN(val2))) {\n            newIndices[offset++] = idx;\n          }\n          idx++;\n        }\n        quickFinished = true;\n      }\n    }\n    if (!quickFinished) {\n      if (dimSize === 1) {\n        for (var i = 0; i < originalCount; i++) {\n          var rawIndex = newStore.getRawIndex(i);\n          var val = storeArr[dims[0]][rawIndex];\n          // Do not filter NaN, see comment above.\n          if (val >= min && val <= max || isNaN(val)) {\n            newIndices[offset++] = rawIndex;\n          }\n        }\n      } else {\n        for (var i = 0; i < originalCount; i++) {\n          var keep = true;\n          var rawIndex = newStore.getRawIndex(i);\n          for (var k = 0; k < dimSize; k++) {\n            var dimk = dims[k];\n            var val = storeArr[dimk][rawIndex];\n            // Do not filter NaN, see comment above.\n            if (val < range[dimk][0] || val > range[dimk][1]) {\n              keep = false;\n            }\n          }\n          if (keep) {\n            newIndices[offset++] = newStore.getRawIndex(i);\n          }\n        }\n      }\n    }\n    // Set indices after filtered.\n    if (offset < originalCount) {\n      newStore._indices = newIndices;\n    }\n    newStore._count = offset;\n    // Reset data extent\n    newStore._extent = [];\n    newStore._updateGetRawIdx();\n    return newStore;\n  };\n  // /**\n  //  * Data mapping to a plain array\n  //  */\n  // mapArray(dims: DimensionIndex[], cb: MapArrayCb): any[] {\n  //     const result: any[] = [];\n  //     this.each(dims, function () {\n  //         result.push(cb && (cb as MapArrayCb).apply(null, arguments));\n  //     });\n  //     return result;\n  // }\n  /**\r\n   * Data mapping to a new List with given dimensions\r\n   */\n  DataStore.prototype.map = function (dims, cb) {\n    // TODO only clone picked chunks.\n    var target = this.clone(dims);\n    this._updateDims(target, dims, cb);\n    return target;\n  };\n  /**\r\n   * @caution Danger!! Only used in dataStack.\r\n   */\n  DataStore.prototype.modify = function (dims, cb) {\n    this._updateDims(this, dims, cb);\n  };\n  DataStore.prototype._updateDims = function (target, dims, cb) {\n    var targetChunks = target._chunks;\n    var tmpRetValue = [];\n    var dimSize = dims.length;\n    var dataCount = target.count();\n    var values = [];\n    var rawExtent = target._rawExtent;\n    for (var i = 0; i < dims.length; i++) {\n      rawExtent[dims[i]] = getInitialExtent();\n    }\n    for (var dataIndex = 0; dataIndex < dataCount; dataIndex++) {\n      var rawIndex = target.getRawIndex(dataIndex);\n      for (var k = 0; k < dimSize; k++) {\n        values[k] = targetChunks[dims[k]][rawIndex];\n      }\n      values[dimSize] = dataIndex;\n      var retValue = cb && cb.apply(null, values);\n      if (retValue != null) {\n        // a number or string (in oridinal dimension)?\n        if (typeof retValue !== 'object') {\n          tmpRetValue[0] = retValue;\n          retValue = tmpRetValue;\n        }\n        for (var i = 0; i < retValue.length; i++) {\n          var dim = dims[i];\n          var val = retValue[i];\n          var rawExtentOnDim = rawExtent[dim];\n          var dimStore = targetChunks[dim];\n          if (dimStore) {\n            dimStore[rawIndex] = val;\n          }\n          if (val < rawExtentOnDim[0]) {\n            rawExtentOnDim[0] = val;\n          }\n          if (val > rawExtentOnDim[1]) {\n            rawExtentOnDim[1] = val;\n          }\n        }\n      }\n    }\n  };\n  /**\r\n   * Large data down sampling using largest-triangle-three-buckets\r\n   * @param {string} valueDimension\r\n   * @param {number} targetCount\r\n   */\n  DataStore.prototype.lttbDownSample = function (valueDimension, rate) {\n    var target = this.clone([valueDimension], true);\n    var targetStorage = target._chunks;\n    var dimStore = targetStorage[valueDimension];\n    var len = this.count();\n    var sampledIndex = 0;\n    var frameSize = Math.floor(1 / rate);\n    var currentRawIndex = this.getRawIndex(0);\n    var maxArea;\n    var area;\n    var nextRawIndex;\n    var newIndices = new (getIndicesCtor(this._rawCount))(Math.min((Math.ceil(len / frameSize) + 2) * 2, len));\n    // First frame use the first data.\n    newIndices[sampledIndex++] = currentRawIndex;\n    for (var i = 1; i < len - 1; i += frameSize) {\n      var nextFrameStart = Math.min(i + frameSize, len - 1);\n      var nextFrameEnd = Math.min(i + frameSize * 2, len);\n      var avgX = (nextFrameEnd + nextFrameStart) / 2;\n      var avgY = 0;\n      for (var idx = nextFrameStart; idx < nextFrameEnd; idx++) {\n        var rawIndex = this.getRawIndex(idx);\n        var y = dimStore[rawIndex];\n        if (isNaN(y)) {\n          continue;\n        }\n        avgY += y;\n      }\n      avgY /= nextFrameEnd - nextFrameStart;\n      var frameStart = i;\n      var frameEnd = Math.min(i + frameSize, len);\n      var pointAX = i - 1;\n      var pointAY = dimStore[currentRawIndex];\n      maxArea = -1;\n      nextRawIndex = frameStart;\n      var firstNaNIndex = -1;\n      var countNaN = 0;\n      // Find a point from current frame that construct a triangle with largest area with previous selected point\n      // And the average of next frame.\n      for (var idx = frameStart; idx < frameEnd; idx++) {\n        var rawIndex = this.getRawIndex(idx);\n        var y = dimStore[rawIndex];\n        if (isNaN(y)) {\n          countNaN++;\n          if (firstNaNIndex < 0) {\n            firstNaNIndex = rawIndex;\n          }\n          continue;\n        }\n        // Calculate triangle area over three buckets\n        area = Math.abs((pointAX - avgX) * (y - pointAY) - (pointAX - idx) * (avgY - pointAY));\n        if (area > maxArea) {\n          maxArea = area;\n          nextRawIndex = rawIndex; // Next a is this b\n        }\n      }\n      if (countNaN > 0 && countNaN < frameEnd - frameStart) {\n        // Append first NaN point in every bucket.\n        // It is necessary to ensure the correct order of indices.\n        newIndices[sampledIndex++] = Math.min(firstNaNIndex, nextRawIndex);\n        nextRawIndex = Math.max(firstNaNIndex, nextRawIndex);\n      }\n      newIndices[sampledIndex++] = nextRawIndex;\n      currentRawIndex = nextRawIndex; // This a is the next a (chosen b)\n    }\n    // First frame use the last data.\n    newIndices[sampledIndex++] = this.getRawIndex(len - 1);\n    target._count = sampledIndex;\n    target._indices = newIndices;\n    target.getRawIndex = this._getRawIdx;\n    return target;\n  };\n  /**\r\n   * Large data down sampling using min-max\r\n   * @param {string} valueDimension\r\n   * @param {number} rate\r\n   */\n  DataStore.prototype.minmaxDownSample = function (valueDimension, rate) {\n    var target = this.clone([valueDimension], true);\n    var targetStorage = target._chunks;\n    var frameSize = Math.floor(1 / rate);\n    var dimStore = targetStorage[valueDimension];\n    var len = this.count();\n    // Each frame results in 2 data points, one for min and one for max\n    var newIndices = new (getIndicesCtor(this._rawCount))(Math.ceil(len / frameSize) * 2);\n    var offset = 0;\n    for (var i = 0; i < len; i += frameSize) {\n      var minIndex = i;\n      var minValue = dimStore[this.getRawIndex(minIndex)];\n      var maxIndex = i;\n      var maxValue = dimStore[this.getRawIndex(maxIndex)];\n      var thisFrameSize = frameSize;\n      // Handle final smaller frame\n      if (i + frameSize > len) {\n        thisFrameSize = len - i;\n      }\n      // Determine min and max within the current frame\n      for (var k = 0; k < thisFrameSize; k++) {\n        var rawIndex = this.getRawIndex(i + k);\n        var value = dimStore[rawIndex];\n        if (value < minValue) {\n          minValue = value;\n          minIndex = i + k;\n        }\n        if (value > maxValue) {\n          maxValue = value;\n          maxIndex = i + k;\n        }\n      }\n      var rawMinIndex = this.getRawIndex(minIndex);\n      var rawMaxIndex = this.getRawIndex(maxIndex);\n      // Set the order of the min and max values, based on their ordering in the frame\n      if (minIndex < maxIndex) {\n        newIndices[offset++] = rawMinIndex;\n        newIndices[offset++] = rawMaxIndex;\n      } else {\n        newIndices[offset++] = rawMaxIndex;\n        newIndices[offset++] = rawMinIndex;\n      }\n    }\n    target._count = offset;\n    target._indices = newIndices;\n    target._updateGetRawIdx();\n    return target;\n  };\n  /**\r\n   * Large data down sampling on given dimension\r\n   * @param sampleIndex Sample index for name and id\r\n   */\n  DataStore.prototype.downSample = function (dimension, rate, sampleValue, sampleIndex) {\n    var target = this.clone([dimension], true);\n    var targetStorage = target._chunks;\n    var frameValues = [];\n    var frameSize = Math.floor(1 / rate);\n    var dimStore = targetStorage[dimension];\n    var len = this.count();\n    var rawExtentOnDim = target._rawExtent[dimension] = getInitialExtent();\n    var newIndices = new (getIndicesCtor(this._rawCount))(Math.ceil(len / frameSize));\n    var offset = 0;\n    for (var i = 0; i < len; i += frameSize) {\n      // Last frame\n      if (frameSize > len - i) {\n        frameSize = len - i;\n        frameValues.length = frameSize;\n      }\n      for (var k = 0; k < frameSize; k++) {\n        var dataIdx = this.getRawIndex(i + k);\n        frameValues[k] = dimStore[dataIdx];\n      }\n      var value = sampleValue(frameValues);\n      var sampleFrameIdx = this.getRawIndex(Math.min(i + sampleIndex(frameValues, value) || 0, len - 1));\n      // Only write value on the filtered data\n      dimStore[sampleFrameIdx] = value;\n      if (value < rawExtentOnDim[0]) {\n        rawExtentOnDim[0] = value;\n      }\n      if (value > rawExtentOnDim[1]) {\n        rawExtentOnDim[1] = value;\n      }\n      newIndices[offset++] = sampleFrameIdx;\n    }\n    target._count = offset;\n    target._indices = newIndices;\n    target._updateGetRawIdx();\n    return target;\n  };\n  /**\r\n   * Data iteration\r\n   * @param ctx default this\r\n   * @example\r\n   *  list.each('x', function (x, idx) {});\r\n   *  list.each(['x', 'y'], function (x, y, idx) {});\r\n   *  list.each(function (idx) {})\r\n   */\n  DataStore.prototype.each = function (dims, cb) {\n    if (!this._count) {\n      return;\n    }\n    var dimSize = dims.length;\n    var chunks = this._chunks;\n    for (var i = 0, len = this.count(); i < len; i++) {\n      var rawIdx = this.getRawIndex(i);\n      // Simple optimization\n      switch (dimSize) {\n        case 0:\n          cb(i);\n          break;\n        case 1:\n          cb(chunks[dims[0]][rawIdx], i);\n          break;\n        case 2:\n          cb(chunks[dims[0]][rawIdx], chunks[dims[1]][rawIdx], i);\n          break;\n        default:\n          var k = 0;\n          var value = [];\n          for (; k < dimSize; k++) {\n            value[k] = chunks[dims[k]][rawIdx];\n          }\n          // Index\n          value[k] = i;\n          cb.apply(null, value);\n      }\n    }\n  };\n  /**\r\n   * Get extent of data in one dimension\r\n   */\n  DataStore.prototype.getDataExtent = function (dim) {\n    // Make sure use concrete dim as cache name.\n    var dimData = this._chunks[dim];\n    var initialExtent = getInitialExtent();\n    if (!dimData) {\n      return initialExtent;\n    }\n    // Make more strict checkings to ensure hitting cache.\n    var currEnd = this.count();\n    // Consider the most cases when using data zoom, `getDataExtent`\n    // happened before filtering. We cache raw extent, which is not\n    // necessary to be cleared and recalculated when restore data.\n    var useRaw = !this._indices;\n    var dimExtent;\n    if (useRaw) {\n      return this._rawExtent[dim].slice();\n    }\n    dimExtent = this._extent[dim];\n    if (dimExtent) {\n      return dimExtent.slice();\n    }\n    dimExtent = initialExtent;\n    var min = dimExtent[0];\n    var max = dimExtent[1];\n    for (var i = 0; i < currEnd; i++) {\n      var rawIdx = this.getRawIndex(i);\n      var value = dimData[rawIdx];\n      value < min && (min = value);\n      value > max && (max = value);\n    }\n    dimExtent = [min, max];\n    this._extent[dim] = dimExtent;\n    return dimExtent;\n  };\n  /**\r\n   * Get raw data item\r\n   */\n  DataStore.prototype.getRawDataItem = function (idx) {\n    var rawIdx = this.getRawIndex(idx);\n    if (!this._provider.persistent) {\n      var val = [];\n      var chunks = this._chunks;\n      for (var i = 0; i < chunks.length; i++) {\n        val.push(chunks[i][rawIdx]);\n      }\n      return val;\n    } else {\n      return this._provider.getItem(rawIdx);\n    }\n  };\n  /**\r\n   * Clone shallow.\r\n   *\r\n   * @param clonedDims Determine which dims to clone. Will share the data if not specified.\r\n   */\n  DataStore.prototype.clone = function (clonedDims, ignoreIndices) {\n    var target = new DataStore();\n    var chunks = this._chunks;\n    var clonedDimsMap = clonedDims && reduce(clonedDims, function (obj, dimIdx) {\n      obj[dimIdx] = true;\n      return obj;\n    }, {});\n    if (clonedDimsMap) {\n      for (var i = 0; i < chunks.length; i++) {\n        // Not clone if dim is not picked.\n        target._chunks[i] = !clonedDimsMap[i] ? chunks[i] : cloneChunk(chunks[i]);\n      }\n    } else {\n      target._chunks = chunks;\n    }\n    this._copyCommonProps(target);\n    if (!ignoreIndices) {\n      target._indices = this._cloneIndices();\n    }\n    target._updateGetRawIdx();\n    return target;\n  };\n  DataStore.prototype._copyCommonProps = function (target) {\n    target._count = this._count;\n    target._rawCount = this._rawCount;\n    target._provider = this._provider;\n    target._dimensions = this._dimensions;\n    target._extent = clone(this._extent);\n    target._rawExtent = clone(this._rawExtent);\n  };\n  DataStore.prototype._cloneIndices = function () {\n    if (this._indices) {\n      var Ctor = this._indices.constructor;\n      var indices = void 0;\n      if (Ctor === Array) {\n        var thisCount = this._indices.length;\n        indices = new Ctor(thisCount);\n        for (var i = 0; i < thisCount; i++) {\n          indices[i] = this._indices[i];\n        }\n      } else {\n        indices = new Ctor(this._indices);\n      }\n      return indices;\n    }\n    return null;\n  };\n  DataStore.prototype._getRawIdxIdentity = function (idx) {\n    return idx;\n  };\n  DataStore.prototype._getRawIdx = function (idx) {\n    if (idx < this._count && idx >= 0) {\n      return this._indices[idx];\n    }\n    return -1;\n  };\n  DataStore.prototype._updateGetRawIdx = function () {\n    this.getRawIndex = this._indices ? this._getRawIdx : this._getRawIdxIdentity;\n  };\n  DataStore.internalField = function () {\n    function getDimValueSimply(dataItem, property, dataIndex, dimIndex) {\n      return parseDataValue(dataItem[dimIndex], this._dimensions[dimIndex]);\n    }\n    defaultDimValueGetters = {\n      arrayRows: getDimValueSimply,\n      objectRows: function (dataItem, property, dataIndex, dimIndex) {\n        return parseDataValue(dataItem[property], this._dimensions[dimIndex]);\n      },\n      keyedColumns: getDimValueSimply,\n      original: function (dataItem, property, dataIndex, dimIndex) {\n        // Performance sensitive, do not use modelUtil.getDataItemValue.\n        // If dataItem is an plain object with no value field, the let `value`\n        // will be assigned with the object, but it will be tread correctly\n        // in the `convertValue`.\n        var value = dataItem && (dataItem.value == null ? dataItem : dataItem.value);\n        return parseDataValue(value instanceof Array ? value[dimIndex]\n        // If value is a single number or something else not array.\n        : value, this._dimensions[dimIndex]);\n      },\n      typedArray: function (dataItem, property, dataIndex, dimIndex) {\n        return dataItem[dimIndex];\n      }\n    };\n  }();\n  return DataStore;\n}();\nexport default DataStore;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,MAAM,EAAEC,KAAK,EAAEC,aAAa,EAAEC,UAAU,EAAEC,IAAI,EAAEC,GAAG,EAAEC,MAAM,QAAQ,0BAA0B;AACtG,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,wBAAwB,QAAQ,aAAa;AACtD,IAAIC,SAAS,GAAG,WAAW;AAC3B;AACA;AACA;AACA,OAAO,IAAIC,eAAe,GAAG,OAAOC,WAAW,KAAKF,SAAS,GAAGG,KAAK,GAAGD,WAAW;AACnF,OAAO,IAAIE,eAAe,GAAG,OAAOC,WAAW,KAAKL,SAAS,GAAGG,KAAK,GAAGE,WAAW;AACnF,OAAO,IAAIC,cAAc,GAAG,OAAOC,UAAU,KAAKP,SAAS,GAAGG,KAAK,GAAGI,UAAU;AAChF,OAAO,IAAIC,gBAAgB,GAAG,OAAOC,YAAY,KAAKT,SAAS,GAAGG,KAAK,GAAGM,YAAY;AACtF;AACA;AACA;AACA,IAAIC,SAAS,GAAG;EACd,OAAO,EAAEF,gBAAgB;EACzB,KAAK,EAAEF,cAAc;EACrB;EACA,SAAS,EAAEH,KAAK;EAChB,QAAQ,EAAEA,KAAK;EACf,MAAM,EAAEK;AACV,CAAC;AACD,IAAIG,sBAAsB;AAC1B,SAASC,cAAcA,CAACC,QAAQ,EAAE;EAChC;EACA,OAAOA,QAAQ,GAAG,KAAK,GAAGZ,eAAe,GAAGG,eAAe;AAC7D;AACA;AACA,SAASU,gBAAgBA,CAAA,EAAG;EAC1B,OAAO,CAACC,QAAQ,EAAE,CAACA,QAAQ,CAAC;AAC9B;AACA;AACA,SAASC,UAAUA,CAACC,aAAa,EAAE;EACjC,IAAIC,IAAI,GAAGD,aAAa,CAACE,WAAW;EACpC;EACA,OAAOD,IAAI,KAAKf,KAAK,GAAGc,aAAa,CAACG,KAAK,CAAC,CAAC,GAAG,IAAIF,IAAI,CAACD,aAAa,CAAC;AACzE;AACA,SAASI,YAAYA,CAACC,KAAK,EAAEC,MAAM,EAAEC,OAAO,EAAEC,GAAG,EAAEC,MAAM,EAAE;EACzD,IAAIC,QAAQ,GAAGjB,SAAS,CAACc,OAAO,IAAI,OAAO,CAAC;EAC5C,IAAIE,MAAM,EAAE;IACV,IAAIE,QAAQ,GAAGN,KAAK,CAACC,MAAM,CAAC;IAC5B,IAAIM,MAAM,GAAGD,QAAQ,IAAIA,QAAQ,CAACE,MAAM;IACxC,IAAI,EAAED,MAAM,KAAKJ,GAAG,CAAC,EAAE;MACrB,IAAIM,QAAQ,GAAG,IAAIJ,QAAQ,CAACF,GAAG,CAAC;MAChC;MACA;MACA,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,EAAEG,CAAC,EAAE,EAAE;QAC/BD,QAAQ,CAACC,CAAC,CAAC,GAAGJ,QAAQ,CAACI,CAAC,CAAC;MAC3B;MACAV,KAAK,CAACC,MAAM,CAAC,GAAGQ,QAAQ;IAC1B;EACF,CAAC,MAAM;IACLT,KAAK,CAACC,MAAM,CAAC,GAAG,IAAII,QAAQ,CAACF,GAAG,CAAC;EACnC;AACF;AACA;AACA;AACA;AACA;AACA,IAAIQ,SAAS,GAAG,aAAa,YAAY;EACvC,SAASA,SAASA,CAAA,EAAG;IACnB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB;IACA,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,iBAAiB,GAAG9C,aAAa,CAAC,CAAC;EAC1C;EACA;AACF;AACA;EACEwC,SAAS,CAACO,SAAS,CAACC,QAAQ,GAAG,UAAUC,QAAQ,EAAEC,eAAe,EAAEC,cAAc,EAAE;IAClF,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCxD,MAAM,CAACG,UAAU,CAACgD,QAAQ,CAACM,OAAO,CAAC,IAAItD,UAAU,CAACgD,QAAQ,CAACO,KAAK,CAAC,EAAE,wBAAwB,CAAC;IAC9F;IACA,IAAI,CAACC,SAAS,GAAGR,QAAQ;IACzB;IACA,IAAI,CAACR,OAAO,GAAG,EAAE;IACjB,IAAI,CAACiB,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,WAAW,GAAG,IAAI,CAACC,kBAAkB;IAC1C,IAAIC,MAAM,GAAGZ,QAAQ,CAACa,SAAS,CAAC,CAAC;IACjC,IAAIC,aAAa,GAAG,IAAI,CAACC,qBAAqB,GAAG9C,sBAAsB,CAAC2C,MAAM,CAACI,YAAY,CAAC;IAC5F;IACA,IAAI,CAACC,eAAe,GAAGf,cAAc,IAAIY,aAAa;IACtD;IACA,IAAI,CAACrB,UAAU,GAAG,EAAE;IACpB,IAAIyB,sBAAsB,GAAG7D,wBAAwB,CAACuD,MAAM,CAAC;IAC7D,IAAI,CAACO,WAAW,GAAGjE,GAAG,CAAC+C,eAAe,EAAE,UAAUmB,GAAG,EAAE;MACrD,IAAIjB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,IAAIa,sBAAsB,EAAE;UAC1BrE,MAAM,CAACuE,GAAG,CAACC,QAAQ,IAAI,IAAI,CAAC;QAC9B;MACF;MACA,OAAO;QACL;QACAC,IAAI,EAAEF,GAAG,CAACE,IAAI;QACdD,QAAQ,EAAED,GAAG,CAACC;MAChB,CAAC;IACH,CAAC,CAAC;IACF,IAAI,CAACE,qBAAqB,CAAC,CAAC,EAAEvB,QAAQ,CAACO,KAAK,CAAC,CAAC,CAAC;EACjD,CAAC;EACDhB,SAAS,CAACO,SAAS,CAAC0B,WAAW,GAAG,YAAY;IAC5C,OAAO,IAAI,CAAChB,SAAS;EACvB,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;EACEjB,SAAS,CAACO,SAAS,CAACe,SAAS,GAAG,YAAY;IAC1C,OAAO,IAAI,CAACL,SAAS,CAACK,SAAS,CAAC,CAAC;EACnC,CAAC;EACD;AACF;AACA;EACEtB,SAAS,CAACO,SAAS,CAAC2B,0BAA0B,GAAG,UAAUC,OAAO,EAAEJ,IAAI,EAAE;IACxE,IAAIK,gBAAgB,GAAG,IAAI,CAAC9B,iBAAiB;IAC7C,IAAI+B,UAAU,GAAG,IAAI,CAACT,WAAW;IACjC,IAAIU,UAAU,GAAGF,gBAAgB,CAACG,GAAG,CAACJ,OAAO,CAAC;IAC9C,IAAIG,UAAU,IAAI,IAAI,EAAE;MACtB,IAAID,UAAU,CAACC,UAAU,CAAC,CAACP,IAAI,KAAKA,IAAI,EAAE;QACxC,OAAOO,UAAU;MACnB;IACF,CAAC,MAAM;MACLA,UAAU,GAAGD,UAAU,CAACxC,MAAM;IAChC;IACAwC,UAAU,CAACC,UAAU,CAAC,GAAG;MACvBP,IAAI,EAAEA;IACR,CAAC;IACDK,gBAAgB,CAACI,GAAG,CAACL,OAAO,EAAEG,UAAU,CAAC;IACzC,IAAI,CAACrC,OAAO,CAACqC,UAAU,CAAC,GAAG,IAAI7D,SAAS,CAACsD,IAAI,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC1B,SAAS,CAAC;IACzE,IAAI,CAACH,UAAU,CAACoC,UAAU,CAAC,GAAGzD,gBAAgB,CAAC,CAAC;IAChD,OAAOyD,UAAU;EACnB,CAAC;EACDtC,SAAS,CAACO,SAAS,CAACkC,kBAAkB,GAAG,UAAUnD,MAAM,EAAEoD,WAAW,EAAE;IACtE,IAAIC,KAAK,GAAG,IAAI,CAAC1C,OAAO,CAACX,MAAM,CAAC;IAChC,IAAIuC,GAAG,GAAG,IAAI,CAACD,WAAW,CAACtC,MAAM,CAAC;IAClC,IAAIsD,UAAU,GAAG,IAAI,CAAC1C,UAAU;IAChC,IAAI2C,MAAM,GAAGhB,GAAG,CAACiB,aAAa,IAAI,CAAC;IACnC,IAAIC,GAAG,GAAGJ,KAAK,CAAC9C,MAAM;IACtB,IAAIgD,MAAM,KAAK,CAAC,EAAE;MAChB;MACA;MACAD,UAAU,CAACtD,MAAM,CAAC,GAAGT,gBAAgB,CAAC,CAAC;IACzC;IACA,IAAImE,YAAY,GAAGJ,UAAU,CAACtD,MAAM,CAAC;IACrC;IACA,KAAK,IAAI2D,CAAC,GAAGJ,MAAM,EAAEI,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;MACjC,IAAIC,GAAG,GAAGP,KAAK,CAACM,CAAC,CAAC,GAAGP,WAAW,CAACS,eAAe,CAACR,KAAK,CAACM,CAAC,CAAC,CAAC;MAC1D,IAAI,CAACG,KAAK,CAACF,GAAG,CAAC,EAAE;QACfF,YAAY,CAAC,CAAC,CAAC,GAAGK,IAAI,CAACC,GAAG,CAACJ,GAAG,EAAEF,YAAY,CAAC,CAAC,CAAC,CAAC;QAChDA,YAAY,CAAC,CAAC,CAAC,GAAGK,IAAI,CAACE,GAAG,CAACL,GAAG,EAAEF,YAAY,CAAC,CAAC,CAAC,CAAC;MAClD;IACF;IACAnB,GAAG,CAACa,WAAW,GAAGA,WAAW;IAC7Bb,GAAG,CAACiB,aAAa,GAAGC,GAAG;IACvBlB,GAAG,CAACE,IAAI,GAAG,SAAS,CAAC,CAAC;EACxB,CAAC;EACD/B,SAAS,CAACO,SAAS,CAACiD,cAAc,GAAG,UAAUlE,MAAM,EAAE;IACrD,IAAImE,OAAO,GAAG,IAAI,CAAC7B,WAAW,CAACtC,MAAM,CAAC;IACtC,IAAIoD,WAAW,GAAGe,OAAO,CAACf,WAAW;IACrC,OAAOA,WAAW;EACpB,CAAC;EACD1C,SAAS,CAACO,SAAS,CAACmD,oBAAoB,GAAG,UAAUC,QAAQ,EAAE;IAC7D,IAAIC,IAAI,GAAG,IAAI,CAAChC,WAAW,CAAC+B,QAAQ,CAAC;IACrC,OAAOC,IAAI,IAAIA,IAAI,CAAC9B,QAAQ;EAC9B,CAAC;EACD;AACF;AACA;EACE9B,SAAS,CAACO,SAAS,CAACsD,UAAU,GAAG,UAAUC,IAAI,EAAE;IAC/C,IAAIlD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCxD,MAAM,CAAC,CAAC,IAAI,CAAC4D,QAAQ,EAAE,4CAA4C,CAAC;IACtE;IACA,IAAIT,QAAQ,GAAG,IAAI,CAACQ,SAAS;IAC7B,IAAI8C,KAAK,GAAG,IAAI,CAAC/C,KAAK,CAAC,CAAC;IACxBP,QAAQ,CAACoD,UAAU,CAACC,IAAI,CAAC;IACzB,IAAItE,GAAG,GAAGiB,QAAQ,CAACO,KAAK,CAAC,CAAC;IAC1B,IAAI,CAACP,QAAQ,CAACuD,UAAU,EAAE;MACxBxE,GAAG,IAAIuE,KAAK;IACd;IACA,IAAIA,KAAK,GAAGvE,GAAG,EAAE;MACf,IAAI,CAACwC,qBAAqB,CAAC+B,KAAK,EAAEvE,GAAG,EAAE,IAAI,CAAC;IAC9C;IACA,OAAO,CAACuE,KAAK,EAAEvE,GAAG,CAAC;EACrB,CAAC;EACDQ,SAAS,CAACO,SAAS,CAAC0D,YAAY,GAAG,UAAUC,MAAM,EAAEC,UAAU,EAAE;IAC/D,IAAIC,MAAM,GAAG,IAAI,CAACnE,OAAO;IACzB,IAAIoC,UAAU,GAAG,IAAI,CAACT,WAAW;IACjC,IAAIyC,MAAM,GAAGhC,UAAU,CAACxC,MAAM;IAC9B,IAAIyE,SAAS,GAAG,IAAI,CAACpE,UAAU;IAC/B,IAAI6D,KAAK,GAAG,IAAI,CAAC/C,KAAK,CAAC,CAAC;IACxB,IAAIxB,GAAG,GAAGuE,KAAK,GAAGV,IAAI,CAACE,GAAG,CAACW,MAAM,CAACrE,MAAM,EAAEsE,UAAU,IAAI,CAAC,CAAC;IAC1D,KAAK,IAAIlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoB,MAAM,EAAEpB,CAAC,EAAE,EAAE;MAC/B,IAAIpB,GAAG,GAAGQ,UAAU,CAACY,CAAC,CAAC;MACvB7D,YAAY,CAACgF,MAAM,EAAEnB,CAAC,EAAEpB,GAAG,CAACE,IAAI,EAAEvC,GAAG,EAAE,IAAI,CAAC;IAC9C;IACA,IAAI+E,aAAa,GAAG,EAAE;IACtB,KAAK,IAAIC,GAAG,GAAGT,KAAK,EAAES,GAAG,GAAGhF,GAAG,EAAEgF,GAAG,EAAE,EAAE;MACtC,IAAIC,SAAS,GAAGD,GAAG,GAAGT,KAAK;MAC3B;MACA,KAAK,IAAIzE,MAAM,GAAG,CAAC,EAAEA,MAAM,GAAG+E,MAAM,EAAE/E,MAAM,EAAE,EAAE;QAC9C,IAAIuC,GAAG,GAAGQ,UAAU,CAAC/C,MAAM,CAAC;QAC5B,IAAI4D,GAAG,GAAGxE,sBAAsB,CAACgG,SAAS,CAACC,IAAI,CAAC,IAAI,EAAET,MAAM,CAACO,SAAS,CAAC,IAAIF,aAAa,EAAE1C,GAAG,CAACC,QAAQ,EAAE2C,SAAS,EAAEnF,MAAM,CAAC;QAC1H8E,MAAM,CAAC9E,MAAM,CAAC,CAACkF,GAAG,CAAC,GAAGtB,GAAG;QACzB,IAAIF,YAAY,GAAGsB,SAAS,CAAChF,MAAM,CAAC;QACpC4D,GAAG,GAAGF,YAAY,CAAC,CAAC,CAAC,KAAKA,YAAY,CAAC,CAAC,CAAC,GAAGE,GAAG,CAAC;QAChDA,GAAG,GAAGF,YAAY,CAAC,CAAC,CAAC,KAAKA,YAAY,CAAC,CAAC,CAAC,GAAGE,GAAG,CAAC;MAClD;IACF;IACA,IAAI,CAAC7C,SAAS,GAAG,IAAI,CAACD,MAAM,GAAGZ,GAAG;IAClC,OAAO;MACLuE,KAAK,EAAEA,KAAK;MACZvE,GAAG,EAAEA;IACP,CAAC;EACH,CAAC;EACDQ,SAAS,CAACO,SAAS,CAACyB,qBAAqB,GAAG,UAAU+B,KAAK,EAAEvE,GAAG,EAAEC,MAAM,EAAE;IACxE,IAAIgB,QAAQ,GAAG,IAAI,CAACQ,SAAS;IAC7B,IAAImD,MAAM,GAAG,IAAI,CAACnE,OAAO;IACzB,IAAIoC,UAAU,GAAG,IAAI,CAACT,WAAW;IACjC,IAAIyC,MAAM,GAAGhC,UAAU,CAACxC,MAAM;IAC9B,IAAIyE,SAAS,GAAG,IAAI,CAACpE,UAAU;IAC/B,IAAI0E,QAAQ,GAAGjH,GAAG,CAAC0E,UAAU,EAAE,UAAUR,GAAG,EAAE;MAC5C,OAAOA,GAAG,CAACC,QAAQ;IACrB,CAAC,CAAC;IACF,KAAK,IAAImB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoB,MAAM,EAAEpB,CAAC,EAAE,EAAE;MAC/B,IAAIpB,GAAG,GAAGQ,UAAU,CAACY,CAAC,CAAC;MACvB,IAAI,CAACqB,SAAS,CAACrB,CAAC,CAAC,EAAE;QACjBqB,SAAS,CAACrB,CAAC,CAAC,GAAGpE,gBAAgB,CAAC,CAAC;MACnC;MACAO,YAAY,CAACgF,MAAM,EAAEnB,CAAC,EAAEpB,GAAG,CAACE,IAAI,EAAEvC,GAAG,EAAEC,MAAM,CAAC;IAChD;IACA,IAAIgB,QAAQ,CAACoE,WAAW,EAAE;MACxBpE,QAAQ,CAACoE,WAAW,CAACd,KAAK,EAAEvE,GAAG,EAAE4E,MAAM,EAAEE,SAAS,CAAC;IACrD,CAAC,MAAM;MACL,IAAIQ,QAAQ,GAAG,EAAE;MACjB,KAAK,IAAIN,GAAG,GAAGT,KAAK,EAAES,GAAG,GAAGhF,GAAG,EAAEgF,GAAG,EAAE,EAAE;QACtC;QACAM,QAAQ,GAAGrE,QAAQ,CAACM,OAAO,CAACyD,GAAG,EAAEM,QAAQ,CAAC;QAC1C;QACA;QACA;QACA;QACA;QACA;QACA;QACA,KAAK,IAAIxF,MAAM,GAAG,CAAC,EAAEA,MAAM,GAAG+E,MAAM,EAAE/E,MAAM,EAAE,EAAE;UAC9C,IAAIyF,UAAU,GAAGX,MAAM,CAAC9E,MAAM,CAAC;UAC/B;UACA,IAAI4D,GAAG,GAAG,IAAI,CAACxB,eAAe,CAACoD,QAAQ,EAAEF,QAAQ,CAACtF,MAAM,CAAC,EAAEkF,GAAG,EAAElF,MAAM,CAAC;UACvEyF,UAAU,CAACP,GAAG,CAAC,GAAGtB,GAAG;UACrB,IAAIF,YAAY,GAAGsB,SAAS,CAAChF,MAAM,CAAC;UACpC4D,GAAG,GAAGF,YAAY,CAAC,CAAC,CAAC,KAAKA,YAAY,CAAC,CAAC,CAAC,GAAGE,GAAG,CAAC;UAChDA,GAAG,GAAGF,YAAY,CAAC,CAAC,CAAC,KAAKA,YAAY,CAAC,CAAC,CAAC,GAAGE,GAAG,CAAC;QAClD;MACF;IACF;IACA,IAAI,CAACzC,QAAQ,CAACuD,UAAU,IAAIvD,QAAQ,CAACuE,KAAK,EAAE;MAC1C;MACAvE,QAAQ,CAACuE,KAAK,CAAC,CAAC;IAClB;IACA,IAAI,CAAC3E,SAAS,GAAG,IAAI,CAACD,MAAM,GAAGZ,GAAG;IAClC;IACA,IAAI,CAACW,OAAO,GAAG,EAAE;EACnB,CAAC;EACDH,SAAS,CAACO,SAAS,CAACS,KAAK,GAAG,YAAY;IACtC,OAAO,IAAI,CAACZ,MAAM;EACpB,CAAC;EACD;AACF;AACA;EACEJ,SAAS,CAACO,SAAS,CAACgC,GAAG,GAAG,UAAUV,GAAG,EAAE2C,GAAG,EAAE;IAC5C,IAAI,EAAEA,GAAG,IAAI,CAAC,IAAIA,GAAG,GAAG,IAAI,CAACpE,MAAM,CAAC,EAAE;MACpC,OAAO6E,GAAG;IACZ;IACA,IAAIC,QAAQ,GAAG,IAAI,CAACjF,OAAO,CAAC4B,GAAG,CAAC;IAChC,OAAOqD,QAAQ,GAAGA,QAAQ,CAAC,IAAI,CAAC/D,WAAW,CAACqD,GAAG,CAAC,CAAC,GAAGS,GAAG;EACzD,CAAC;EACDjF,SAAS,CAACO,SAAS,CAAC4E,SAAS,GAAG,UAAU9C,UAAU,EAAEmC,GAAG,EAAE;IACzD,IAAIN,MAAM,GAAG,EAAE;IACf,IAAIkB,MAAM,GAAG,EAAE;IACf,IAAIZ,GAAG,IAAI,IAAI,EAAE;MACfA,GAAG,GAAGnC,UAAU;MAChB;MACAA,UAAU,GAAG,EAAE;MACf;MACA,KAAK,IAAIY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACrB,WAAW,CAAC/B,MAAM,EAAEoD,CAAC,EAAE,EAAE;QAChDmC,MAAM,CAACC,IAAI,CAACpC,CAAC,CAAC;MAChB;IACF,CAAC,MAAM;MACLmC,MAAM,GAAG/C,UAAU;IACrB;IACA,KAAK,IAAIY,CAAC,GAAG,CAAC,EAAEF,GAAG,GAAGqC,MAAM,CAACvF,MAAM,EAAEoD,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;MACjDiB,MAAM,CAACmB,IAAI,CAAC,IAAI,CAAC9C,GAAG,CAAC6C,MAAM,CAACnC,CAAC,CAAC,EAAEuB,GAAG,CAAC,CAAC;IACvC;IACA,OAAON,MAAM;EACf,CAAC;EACD;AACF;AACA;EACElE,SAAS,CAACO,SAAS,CAAC+E,aAAa,GAAG,UAAUzD,GAAG,EAAE0D,MAAM,EAAE;IACzD,IAAI,EAAEA,MAAM,IAAI,CAAC,IAAIA,MAAM,GAAG,IAAI,CAAClF,SAAS,CAAC,EAAE;MAC7C,OAAO4E,GAAG;IACZ;IACA,IAAIC,QAAQ,GAAG,IAAI,CAACjF,OAAO,CAAC4B,GAAG,CAAC;IAChC,OAAOqD,QAAQ,GAAGA,QAAQ,CAACK,MAAM,CAAC,GAAGN,GAAG;EAC1C,CAAC;EACD;AACF;AACA;EACEjF,SAAS,CAACO,SAAS,CAACiF,MAAM,GAAG,UAAU3D,GAAG,EAAE;IAC1C,IAAI4D,OAAO,GAAG,IAAI,CAACxF,OAAO,CAAC4B,GAAG,CAAC;IAC/B,IAAI6D,GAAG,GAAG,CAAC;IACX,IAAID,OAAO,EAAE;MACX,KAAK,IAAIxC,CAAC,GAAG,CAAC,EAAEF,GAAG,GAAG,IAAI,CAAC/B,KAAK,CAAC,CAAC,EAAEiC,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;QAChD,IAAI0C,KAAK,GAAG,IAAI,CAACpD,GAAG,CAACV,GAAG,EAAEoB,CAAC,CAAC;QAC5B,IAAI,CAACG,KAAK,CAACuC,KAAK,CAAC,EAAE;UACjBD,GAAG,IAAIC,KAAK;QACd;MACF;IACF;IACA,OAAOD,GAAG;EACZ,CAAC;EACD;AACF;AACA;EACE1F,SAAS,CAACO,SAAS,CAACqF,SAAS,GAAG,UAAU/D,GAAG,EAAE;IAC7C,IAAIgE,YAAY,GAAG,EAAE;IACrB;IACA,IAAI,CAACC,IAAI,CAAC,CAACjE,GAAG,CAAC,EAAE,UAAUqB,GAAG,EAAE;MAC9B,IAAI,CAACE,KAAK,CAACF,GAAG,CAAC,EAAE;QACf2C,YAAY,CAACR,IAAI,CAACnC,GAAG,CAAC;MACxB;IACF,CAAC,CAAC;IACF;IACA;IACA,IAAI6C,kBAAkB,GAAGF,YAAY,CAACG,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;MACzD,OAAOD,CAAC,GAAGC,CAAC;IACd,CAAC,CAAC;IACF,IAAInD,GAAG,GAAG,IAAI,CAAC/B,KAAK,CAAC,CAAC;IACtB;IACA,OAAO+B,GAAG,KAAK,CAAC,GAAG,CAAC,GAAGA,GAAG,GAAG,CAAC,KAAK,CAAC,GAAGgD,kBAAkB,CAAC,CAAChD,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAACgD,kBAAkB,CAAChD,GAAG,GAAG,CAAC,CAAC,GAAGgD,kBAAkB,CAAChD,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;EAChJ,CAAC;EACD;AACF;AACA;EACE/C,SAAS,CAACO,SAAS,CAAC4F,eAAe,GAAG,UAAUC,QAAQ,EAAE;IACxD,IAAIA,QAAQ,IAAI,IAAI,CAAC/F,SAAS,IAAI+F,QAAQ,GAAG,CAAC,EAAE;MAC9C,OAAO,CAAC,CAAC;IACX;IACA,IAAI,CAAC,IAAI,CAAClF,QAAQ,EAAE;MAClB,OAAOkF,QAAQ;IACjB;IACA;IACA,IAAIC,OAAO,GAAG,IAAI,CAACnF,QAAQ;IAC3B;IACA,IAAIoF,YAAY,GAAGD,OAAO,CAACD,QAAQ,CAAC;IACpC,IAAIE,YAAY,IAAI,IAAI,IAAIA,YAAY,GAAG,IAAI,CAAClG,MAAM,IAAIkG,YAAY,KAAKF,QAAQ,EAAE;MACnF,OAAOA,QAAQ;IACjB;IACA,IAAIG,IAAI,GAAG,CAAC;IACZ,IAAIC,KAAK,GAAG,IAAI,CAACpG,MAAM,GAAG,CAAC;IAC3B,OAAOmG,IAAI,IAAIC,KAAK,EAAE;MACpB,IAAIC,GAAG,GAAG,CAACF,IAAI,GAAGC,KAAK,IAAI,CAAC,GAAG,CAAC;MAChC,IAAIH,OAAO,CAACI,GAAG,CAAC,GAAGL,QAAQ,EAAE;QAC3BG,IAAI,GAAGE,GAAG,GAAG,CAAC;MAChB,CAAC,MAAM,IAAIJ,OAAO,CAACI,GAAG,CAAC,GAAGL,QAAQ,EAAE;QAClCI,KAAK,GAAGC,GAAG,GAAG,CAAC;MACjB,CAAC,MAAM;QACL,OAAOA,GAAG;MACZ;IACF;IACA,OAAO,CAAC,CAAC;EACX,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEzG,SAAS,CAACO,SAAS,CAACmG,gBAAgB,GAAG,UAAU7E,GAAG,EAAE8D,KAAK,EAAEgB,WAAW,EAAE;IACxE,IAAIvC,MAAM,GAAG,IAAI,CAACnE,OAAO;IACzB,IAAIwF,OAAO,GAAGrB,MAAM,CAACvC,GAAG,CAAC;IACzB,IAAI+E,cAAc,GAAG,EAAE;IACvB,IAAI,CAACnB,OAAO,EAAE;MACZ,OAAOmB,cAAc;IACvB;IACA,IAAID,WAAW,IAAI,IAAI,EAAE;MACvBA,WAAW,GAAG7H,QAAQ;IACxB;IACA,IAAI+H,OAAO,GAAG/H,QAAQ;IACtB,IAAIgI,OAAO,GAAG,CAAC,CAAC;IAChB,IAAIC,iBAAiB,GAAG,CAAC;IACzB;IACA,KAAK,IAAI9D,CAAC,GAAG,CAAC,EAAEF,GAAG,GAAG,IAAI,CAAC/B,KAAK,CAAC,CAAC,EAAEiC,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;MAChD,IAAI+D,SAAS,GAAG,IAAI,CAAC7F,WAAW,CAAC8B,CAAC,CAAC;MACnC,IAAIgE,IAAI,GAAGtB,KAAK,GAAGF,OAAO,CAACuB,SAAS,CAAC;MACrC,IAAIE,IAAI,GAAG7D,IAAI,CAAC8D,GAAG,CAACF,IAAI,CAAC;MACzB,IAAIC,IAAI,IAAIP,WAAW,EAAE;QACvB;QACA;QACA;QACA;QACA;QACA;QACA,IAAIO,IAAI,GAAGL,OAAO,IAAIK,IAAI,KAAKL,OAAO,IAAII,IAAI,IAAI,CAAC,IAAIH,OAAO,GAAG,CAAC,EAAE;UAClED,OAAO,GAAGK,IAAI;UACdJ,OAAO,GAAGG,IAAI;UACdF,iBAAiB,GAAG,CAAC;QACvB;QACA,IAAIE,IAAI,KAAKH,OAAO,EAAE;UACpBF,cAAc,CAACG,iBAAiB,EAAE,CAAC,GAAG9D,CAAC;QACzC;MACF;IACF;IACA2D,cAAc,CAAC/G,MAAM,GAAGkH,iBAAiB;IACzC,OAAOH,cAAc;EACvB,CAAC;EACD5G,SAAS,CAACO,SAAS,CAAC6G,UAAU,GAAG,YAAY;IAC3C,IAAIC,UAAU;IACd,IAAIhB,OAAO,GAAG,IAAI,CAACnF,QAAQ;IAC3B,IAAImF,OAAO,EAAE;MACX,IAAIpH,IAAI,GAAGoH,OAAO,CAACnH,WAAW;MAC9B,IAAIoI,SAAS,GAAG,IAAI,CAAClH,MAAM;MAC3B;MACA,IAAInB,IAAI,KAAKf,KAAK,EAAE;QAClBmJ,UAAU,GAAG,IAAIpI,IAAI,CAACqI,SAAS,CAAC;QAChC,KAAK,IAAIrE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqE,SAAS,EAAErE,CAAC,EAAE,EAAE;UAClCoE,UAAU,CAACpE,CAAC,CAAC,GAAGoD,OAAO,CAACpD,CAAC,CAAC;QAC5B;MACF,CAAC,MAAM;QACLoE,UAAU,GAAG,IAAIpI,IAAI,CAACoH,OAAO,CAACkB,MAAM,EAAE,CAAC,EAAED,SAAS,CAAC;MACrD;IACF,CAAC,MAAM;MACL,IAAIrI,IAAI,GAAGN,cAAc,CAAC,IAAI,CAAC0B,SAAS,CAAC;MACzCgH,UAAU,GAAG,IAAIpI,IAAI,CAAC,IAAI,CAAC+B,KAAK,CAAC,CAAC,CAAC;MACnC,KAAK,IAAIiC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoE,UAAU,CAACxH,MAAM,EAAEoD,CAAC,EAAE,EAAE;QAC1CoE,UAAU,CAACpE,CAAC,CAAC,GAAGA,CAAC;MACnB;IACF;IACA,OAAOoE,UAAU;EACnB,CAAC;EACD;AACF;AACA;EACErH,SAAS,CAACO,SAAS,CAACiH,MAAM,GAAG,UAAUC,IAAI,EAAEC,EAAE,EAAE;IAC/C,IAAI,CAAC,IAAI,CAACtH,MAAM,EAAE;MAChB,OAAO,IAAI;IACb;IACA,IAAIN,QAAQ,GAAG,IAAI,CAACvC,KAAK,CAAC,CAAC;IAC3B,IAAIyD,KAAK,GAAGlB,QAAQ,CAACkB,KAAK,CAAC,CAAC;IAC5B,IAAI/B,IAAI,GAAGN,cAAc,CAACmB,QAAQ,CAACO,SAAS,CAAC;IAC7C,IAAIgH,UAAU,GAAG,IAAIpI,IAAI,CAAC+B,KAAK,CAAC;IAChC,IAAI2E,KAAK,GAAG,EAAE;IACd,IAAIgC,OAAO,GAAGF,IAAI,CAAC5H,MAAM;IACzB,IAAIgD,MAAM,GAAG,CAAC;IACd,IAAI+E,IAAI,GAAGH,IAAI,CAAC,CAAC,CAAC;IAClB,IAAIrD,MAAM,GAAGtE,QAAQ,CAACG,OAAO;IAC7B,KAAK,IAAIgD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjC,KAAK,EAAEiC,CAAC,EAAE,EAAE;MAC9B,IAAI4E,IAAI,GAAG,KAAK,CAAC;MACjB,IAAItC,MAAM,GAAGzF,QAAQ,CAACqB,WAAW,CAAC8B,CAAC,CAAC;MACpC;MACA,IAAI0E,OAAO,KAAK,CAAC,EAAE;QACjBE,IAAI,GAAGH,EAAE,CAACzE,CAAC,CAAC;MACd,CAAC,MAAM,IAAI0E,OAAO,KAAK,CAAC,EAAE;QACxB,IAAIzE,GAAG,GAAGkB,MAAM,CAACwD,IAAI,CAAC,CAACrC,MAAM,CAAC;QAC9BsC,IAAI,GAAGH,EAAE,CAACxE,GAAG,EAAED,CAAC,CAAC;MACnB,CAAC,MAAM;QACL,IAAI6E,CAAC,GAAG,CAAC;QACT,OAAOA,CAAC,GAAGH,OAAO,EAAEG,CAAC,EAAE,EAAE;UACvBnC,KAAK,CAACmC,CAAC,CAAC,GAAG1D,MAAM,CAACqD,IAAI,CAACK,CAAC,CAAC,CAAC,CAACvC,MAAM,CAAC;QACpC;QACAI,KAAK,CAACmC,CAAC,CAAC,GAAG7E,CAAC;QACZ4E,IAAI,GAAGH,EAAE,CAACK,KAAK,CAAC,IAAI,EAAEpC,KAAK,CAAC;MAC9B;MACA,IAAIkC,IAAI,EAAE;QACRR,UAAU,CAACxE,MAAM,EAAE,CAAC,GAAG0C,MAAM;MAC/B;IACF;IACA;IACA,IAAI1C,MAAM,GAAG7B,KAAK,EAAE;MAClBlB,QAAQ,CAACoB,QAAQ,GAAGmG,UAAU;IAChC;IACAvH,QAAQ,CAACM,MAAM,GAAGyC,MAAM;IACxB;IACA/C,QAAQ,CAACK,OAAO,GAAG,EAAE;IACrBL,QAAQ,CAACkI,gBAAgB,CAAC,CAAC;IAC3B,OAAOlI,QAAQ;EACjB,CAAC;EACD;AACF;AACA;AACA;EACEE,SAAS,CAACO,SAAS,CAAC0H,WAAW,GAAG,UAAUC,KAAK,EAAE;IACjD,IAAIpI,QAAQ,GAAG,IAAI,CAACvC,KAAK,CAAC,CAAC;IAC3B,IAAIwF,GAAG,GAAGjD,QAAQ,CAACM,MAAM;IACzB,IAAI,CAAC2C,GAAG,EAAE;MACR,OAAO,IAAI;IACb;IACA,IAAI0E,IAAI,GAAG/J,IAAI,CAACwK,KAAK,CAAC;IACtB,IAAIP,OAAO,GAAGF,IAAI,CAAC5H,MAAM;IACzB,IAAI,CAAC8H,OAAO,EAAE;MACZ,OAAO,IAAI;IACb;IACA,IAAIQ,aAAa,GAAGrI,QAAQ,CAACkB,KAAK,CAAC,CAAC;IACpC,IAAI/B,IAAI,GAAGN,cAAc,CAACmB,QAAQ,CAACO,SAAS,CAAC;IAC7C,IAAIgH,UAAU,GAAG,IAAIpI,IAAI,CAACkJ,aAAa,CAAC;IACxC,IAAItF,MAAM,GAAG,CAAC;IACd,IAAI+E,IAAI,GAAGH,IAAI,CAAC,CAAC,CAAC;IAClB,IAAInE,GAAG,GAAG4E,KAAK,CAACN,IAAI,CAAC,CAAC,CAAC,CAAC;IACxB,IAAIrE,GAAG,GAAG2E,KAAK,CAACN,IAAI,CAAC,CAAC,CAAC,CAAC;IACxB,IAAIQ,QAAQ,GAAGtI,QAAQ,CAACG,OAAO;IAC/B,IAAIoI,aAAa,GAAG,KAAK;IACzB,IAAI,CAACvI,QAAQ,CAACoB,QAAQ,EAAE;MACtB;MACA,IAAIsD,GAAG,GAAG,CAAC;MACX,IAAImD,OAAO,KAAK,CAAC,EAAE;QACjB,IAAI5C,UAAU,GAAGqD,QAAQ,CAACX,IAAI,CAAC,CAAC,CAAC,CAAC;QAClC,KAAK,IAAIxE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;UAC5B,IAAIC,GAAG,GAAG6B,UAAU,CAAC9B,CAAC,CAAC;UACvB;UACA;UACA;UACA;UACA;UACA,IAAIC,GAAG,IAAII,GAAG,IAAIJ,GAAG,IAAIK,GAAG,IAAIH,KAAK,CAACF,GAAG,CAAC,EAAE;YAC1CmE,UAAU,CAACxE,MAAM,EAAE,CAAC,GAAG2B,GAAG;UAC5B;UACAA,GAAG,EAAE;QACP;QACA6D,aAAa,GAAG,IAAI;MACtB,CAAC,MAAM,IAAIV,OAAO,KAAK,CAAC,EAAE;QACxB,IAAI5C,UAAU,GAAGqD,QAAQ,CAACX,IAAI,CAAC,CAAC,CAAC,CAAC;QAClC,IAAIa,WAAW,GAAGF,QAAQ,CAACX,IAAI,CAAC,CAAC,CAAC,CAAC;QACnC,IAAIc,IAAI,GAAGL,KAAK,CAACT,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,IAAIe,IAAI,GAAGN,KAAK,CAACT,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,KAAK,IAAIxE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;UAC5B,IAAIC,GAAG,GAAG6B,UAAU,CAAC9B,CAAC,CAAC;UACvB,IAAIwF,IAAI,GAAGH,WAAW,CAACrF,CAAC,CAAC;UACzB;UACA,IAAI,CAACC,GAAG,IAAII,GAAG,IAAIJ,GAAG,IAAIK,GAAG,IAAIH,KAAK,CAACF,GAAG,CAAC,MAAMuF,IAAI,IAAIF,IAAI,IAAIE,IAAI,IAAID,IAAI,IAAIpF,KAAK,CAACqF,IAAI,CAAC,CAAC,EAAE;YAC7FpB,UAAU,CAACxE,MAAM,EAAE,CAAC,GAAG2B,GAAG;UAC5B;UACAA,GAAG,EAAE;QACP;QACA6D,aAAa,GAAG,IAAI;MACtB;IACF;IACA,IAAI,CAACA,aAAa,EAAE;MAClB,IAAIV,OAAO,KAAK,CAAC,EAAE;QACjB,KAAK,IAAI1E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkF,aAAa,EAAElF,CAAC,EAAE,EAAE;UACtC,IAAImD,QAAQ,GAAGtG,QAAQ,CAACqB,WAAW,CAAC8B,CAAC,CAAC;UACtC,IAAIC,GAAG,GAAGkF,QAAQ,CAACX,IAAI,CAAC,CAAC,CAAC,CAAC,CAACrB,QAAQ,CAAC;UACrC;UACA,IAAIlD,GAAG,IAAII,GAAG,IAAIJ,GAAG,IAAIK,GAAG,IAAIH,KAAK,CAACF,GAAG,CAAC,EAAE;YAC1CmE,UAAU,CAACxE,MAAM,EAAE,CAAC,GAAGuD,QAAQ;UACjC;QACF;MACF,CAAC,MAAM;QACL,KAAK,IAAInD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkF,aAAa,EAAElF,CAAC,EAAE,EAAE;UACtC,IAAI4E,IAAI,GAAG,IAAI;UACf,IAAIzB,QAAQ,GAAGtG,QAAQ,CAACqB,WAAW,CAAC8B,CAAC,CAAC;UACtC,KAAK,IAAI6E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,OAAO,EAAEG,CAAC,EAAE,EAAE;YAChC,IAAIY,IAAI,GAAGjB,IAAI,CAACK,CAAC,CAAC;YAClB,IAAI5E,GAAG,GAAGkF,QAAQ,CAACM,IAAI,CAAC,CAACtC,QAAQ,CAAC;YAClC;YACA,IAAIlD,GAAG,GAAGgF,KAAK,CAACQ,IAAI,CAAC,CAAC,CAAC,CAAC,IAAIxF,GAAG,GAAGgF,KAAK,CAACQ,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;cAChDb,IAAI,GAAG,KAAK;YACd;UACF;UACA,IAAIA,IAAI,EAAE;YACRR,UAAU,CAACxE,MAAM,EAAE,CAAC,GAAG/C,QAAQ,CAACqB,WAAW,CAAC8B,CAAC,CAAC;UAChD;QACF;MACF;IACF;IACA;IACA,IAAIJ,MAAM,GAAGsF,aAAa,EAAE;MAC1BrI,QAAQ,CAACoB,QAAQ,GAAGmG,UAAU;IAChC;IACAvH,QAAQ,CAACM,MAAM,GAAGyC,MAAM;IACxB;IACA/C,QAAQ,CAACK,OAAO,GAAG,EAAE;IACrBL,QAAQ,CAACkI,gBAAgB,CAAC,CAAC;IAC3B,OAAOlI,QAAQ;EACjB,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACF;AACA;EACEE,SAAS,CAACO,SAAS,CAAC5C,GAAG,GAAG,UAAU8J,IAAI,EAAEC,EAAE,EAAE;IAC5C;IACA,IAAIiB,MAAM,GAAG,IAAI,CAACpL,KAAK,CAACkK,IAAI,CAAC;IAC7B,IAAI,CAACmB,WAAW,CAACD,MAAM,EAAElB,IAAI,EAAEC,EAAE,CAAC;IAClC,OAAOiB,MAAM;EACf,CAAC;EACD;AACF;AACA;EACE3I,SAAS,CAACO,SAAS,CAACsI,MAAM,GAAG,UAAUpB,IAAI,EAAEC,EAAE,EAAE;IAC/C,IAAI,CAACkB,WAAW,CAAC,IAAI,EAAEnB,IAAI,EAAEC,EAAE,CAAC;EAClC,CAAC;EACD1H,SAAS,CAACO,SAAS,CAACqI,WAAW,GAAG,UAAUD,MAAM,EAAElB,IAAI,EAAEC,EAAE,EAAE;IAC5D,IAAIoB,YAAY,GAAGH,MAAM,CAAC1I,OAAO;IACjC,IAAI8I,WAAW,GAAG,EAAE;IACpB,IAAIpB,OAAO,GAAGF,IAAI,CAAC5H,MAAM;IACzB,IAAImJ,SAAS,GAAGL,MAAM,CAAC3H,KAAK,CAAC,CAAC;IAC9B,IAAIkD,MAAM,GAAG,EAAE;IACf,IAAII,SAAS,GAAGqE,MAAM,CAACzI,UAAU;IACjC,KAAK,IAAI+C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwE,IAAI,CAAC5H,MAAM,EAAEoD,CAAC,EAAE,EAAE;MACpCqB,SAAS,CAACmD,IAAI,CAACxE,CAAC,CAAC,CAAC,GAAGpE,gBAAgB,CAAC,CAAC;IACzC;IACA,KAAK,IAAImI,SAAS,GAAG,CAAC,EAAEA,SAAS,GAAGgC,SAAS,EAAEhC,SAAS,EAAE,EAAE;MAC1D,IAAIZ,QAAQ,GAAGuC,MAAM,CAACxH,WAAW,CAAC6F,SAAS,CAAC;MAC5C,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,OAAO,EAAEG,CAAC,EAAE,EAAE;QAChC5D,MAAM,CAAC4D,CAAC,CAAC,GAAGgB,YAAY,CAACrB,IAAI,CAACK,CAAC,CAAC,CAAC,CAAC1B,QAAQ,CAAC;MAC7C;MACAlC,MAAM,CAACyD,OAAO,CAAC,GAAGX,SAAS;MAC3B,IAAIiC,QAAQ,GAAGvB,EAAE,IAAIA,EAAE,CAACK,KAAK,CAAC,IAAI,EAAE7D,MAAM,CAAC;MAC3C,IAAI+E,QAAQ,IAAI,IAAI,EAAE;QACpB;QACA,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;UAChCF,WAAW,CAAC,CAAC,CAAC,GAAGE,QAAQ;UACzBA,QAAQ,GAAGF,WAAW;QACxB;QACA,KAAK,IAAI9F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgG,QAAQ,CAACpJ,MAAM,EAAEoD,CAAC,EAAE,EAAE;UACxC,IAAIpB,GAAG,GAAG4F,IAAI,CAACxE,CAAC,CAAC;UACjB,IAAIC,GAAG,GAAG+F,QAAQ,CAAChG,CAAC,CAAC;UACrB,IAAIiG,cAAc,GAAG5E,SAAS,CAACzC,GAAG,CAAC;UACnC,IAAIqD,QAAQ,GAAG4D,YAAY,CAACjH,GAAG,CAAC;UAChC,IAAIqD,QAAQ,EAAE;YACZA,QAAQ,CAACkB,QAAQ,CAAC,GAAGlD,GAAG;UAC1B;UACA,IAAIA,GAAG,GAAGgG,cAAc,CAAC,CAAC,CAAC,EAAE;YAC3BA,cAAc,CAAC,CAAC,CAAC,GAAGhG,GAAG;UACzB;UACA,IAAIA,GAAG,GAAGgG,cAAc,CAAC,CAAC,CAAC,EAAE;YAC3BA,cAAc,CAAC,CAAC,CAAC,GAAGhG,GAAG;UACzB;QACF;MACF;IACF;EACF,CAAC;EACD;AACF;AACA;AACA;AACA;EACElD,SAAS,CAACO,SAAS,CAAC4I,cAAc,GAAG,UAAUC,cAAc,EAAEC,IAAI,EAAE;IACnE,IAAIV,MAAM,GAAG,IAAI,CAACpL,KAAK,CAAC,CAAC6L,cAAc,CAAC,EAAE,IAAI,CAAC;IAC/C,IAAIE,aAAa,GAAGX,MAAM,CAAC1I,OAAO;IAClC,IAAIiF,QAAQ,GAAGoE,aAAa,CAACF,cAAc,CAAC;IAC5C,IAAIrG,GAAG,GAAG,IAAI,CAAC/B,KAAK,CAAC,CAAC;IACtB,IAAIuI,YAAY,GAAG,CAAC;IACpB,IAAIC,SAAS,GAAGnG,IAAI,CAACoG,KAAK,CAAC,CAAC,GAAGJ,IAAI,CAAC;IACpC,IAAIK,eAAe,GAAG,IAAI,CAACvI,WAAW,CAAC,CAAC,CAAC;IACzC,IAAIwI,OAAO;IACX,IAAIC,IAAI;IACR,IAAIC,YAAY;IAChB,IAAIxC,UAAU,GAAG,KAAK1I,cAAc,CAAC,IAAI,CAAC0B,SAAS,CAAC,EAAEgD,IAAI,CAACC,GAAG,CAAC,CAACD,IAAI,CAACyG,IAAI,CAAC/G,GAAG,GAAGyG,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAEzG,GAAG,CAAC,CAAC;IAC1G;IACAsE,UAAU,CAACkC,YAAY,EAAE,CAAC,GAAGG,eAAe;IAC5C,KAAK,IAAIzG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,GAAG,CAAC,EAAEE,CAAC,IAAIuG,SAAS,EAAE;MAC3C,IAAIO,cAAc,GAAG1G,IAAI,CAACC,GAAG,CAACL,CAAC,GAAGuG,SAAS,EAAEzG,GAAG,GAAG,CAAC,CAAC;MACrD,IAAIiH,YAAY,GAAG3G,IAAI,CAACC,GAAG,CAACL,CAAC,GAAGuG,SAAS,GAAG,CAAC,EAAEzG,GAAG,CAAC;MACnD,IAAIkH,IAAI,GAAG,CAACD,YAAY,GAAGD,cAAc,IAAI,CAAC;MAC9C,IAAIG,IAAI,GAAG,CAAC;MACZ,KAAK,IAAI1F,GAAG,GAAGuF,cAAc,EAAEvF,GAAG,GAAGwF,YAAY,EAAExF,GAAG,EAAE,EAAE;QACxD,IAAI4B,QAAQ,GAAG,IAAI,CAACjF,WAAW,CAACqD,GAAG,CAAC;QACpC,IAAI2F,CAAC,GAAGjF,QAAQ,CAACkB,QAAQ,CAAC;QAC1B,IAAIhD,KAAK,CAAC+G,CAAC,CAAC,EAAE;UACZ;QACF;QACAD,IAAI,IAAIC,CAAC;MACX;MACAD,IAAI,IAAIF,YAAY,GAAGD,cAAc;MACrC,IAAIK,UAAU,GAAGnH,CAAC;MAClB,IAAIoH,QAAQ,GAAGhH,IAAI,CAACC,GAAG,CAACL,CAAC,GAAGuG,SAAS,EAAEzG,GAAG,CAAC;MAC3C,IAAIuH,OAAO,GAAGrH,CAAC,GAAG,CAAC;MACnB,IAAIsH,OAAO,GAAGrF,QAAQ,CAACwE,eAAe,CAAC;MACvCC,OAAO,GAAG,CAAC,CAAC;MACZE,YAAY,GAAGO,UAAU;MACzB,IAAII,aAAa,GAAG,CAAC,CAAC;MACtB,IAAIC,QAAQ,GAAG,CAAC;MAChB;MACA;MACA,KAAK,IAAIjG,GAAG,GAAG4F,UAAU,EAAE5F,GAAG,GAAG6F,QAAQ,EAAE7F,GAAG,EAAE,EAAE;QAChD,IAAI4B,QAAQ,GAAG,IAAI,CAACjF,WAAW,CAACqD,GAAG,CAAC;QACpC,IAAI2F,CAAC,GAAGjF,QAAQ,CAACkB,QAAQ,CAAC;QAC1B,IAAIhD,KAAK,CAAC+G,CAAC,CAAC,EAAE;UACZM,QAAQ,EAAE;UACV,IAAID,aAAa,GAAG,CAAC,EAAE;YACrBA,aAAa,GAAGpE,QAAQ;UAC1B;UACA;QACF;QACA;QACAwD,IAAI,GAAGvG,IAAI,CAAC8D,GAAG,CAAC,CAACmD,OAAO,GAAGL,IAAI,KAAKE,CAAC,GAAGI,OAAO,CAAC,GAAG,CAACD,OAAO,GAAG9F,GAAG,KAAK0F,IAAI,GAAGK,OAAO,CAAC,CAAC;QACtF,IAAIX,IAAI,GAAGD,OAAO,EAAE;UAClBA,OAAO,GAAGC,IAAI;UACdC,YAAY,GAAGzD,QAAQ,CAAC,CAAC;QAC3B;MACF;MACA,IAAIqE,QAAQ,GAAG,CAAC,IAAIA,QAAQ,GAAGJ,QAAQ,GAAGD,UAAU,EAAE;QACpD;QACA;QACA/C,UAAU,CAACkC,YAAY,EAAE,CAAC,GAAGlG,IAAI,CAACC,GAAG,CAACkH,aAAa,EAAEX,YAAY,CAAC;QAClEA,YAAY,GAAGxG,IAAI,CAACE,GAAG,CAACiH,aAAa,EAAEX,YAAY,CAAC;MACtD;MACAxC,UAAU,CAACkC,YAAY,EAAE,CAAC,GAAGM,YAAY;MACzCH,eAAe,GAAGG,YAAY,CAAC,CAAC;IAClC;IACA;IACAxC,UAAU,CAACkC,YAAY,EAAE,CAAC,GAAG,IAAI,CAACpI,WAAW,CAAC4B,GAAG,GAAG,CAAC,CAAC;IACtD4F,MAAM,CAACvI,MAAM,GAAGmJ,YAAY;IAC5BZ,MAAM,CAACzH,QAAQ,GAAGmG,UAAU;IAC5BsB,MAAM,CAACxH,WAAW,GAAG,IAAI,CAACuJ,UAAU;IACpC,OAAO/B,MAAM;EACf,CAAC;EACD;AACF;AACA;AACA;AACA;EACE3I,SAAS,CAACO,SAAS,CAACoK,gBAAgB,GAAG,UAAUvB,cAAc,EAAEC,IAAI,EAAE;IACrE,IAAIV,MAAM,GAAG,IAAI,CAACpL,KAAK,CAAC,CAAC6L,cAAc,CAAC,EAAE,IAAI,CAAC;IAC/C,IAAIE,aAAa,GAAGX,MAAM,CAAC1I,OAAO;IAClC,IAAIuJ,SAAS,GAAGnG,IAAI,CAACoG,KAAK,CAAC,CAAC,GAAGJ,IAAI,CAAC;IACpC,IAAInE,QAAQ,GAAGoE,aAAa,CAACF,cAAc,CAAC;IAC5C,IAAIrG,GAAG,GAAG,IAAI,CAAC/B,KAAK,CAAC,CAAC;IACtB;IACA,IAAIqG,UAAU,GAAG,KAAK1I,cAAc,CAAC,IAAI,CAAC0B,SAAS,CAAC,EAAEgD,IAAI,CAACyG,IAAI,CAAC/G,GAAG,GAAGyG,SAAS,CAAC,GAAG,CAAC,CAAC;IACrF,IAAI3G,MAAM,GAAG,CAAC;IACd,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,IAAIuG,SAAS,EAAE;MACvC,IAAIoB,QAAQ,GAAG3H,CAAC;MAChB,IAAI4H,QAAQ,GAAG3F,QAAQ,CAAC,IAAI,CAAC/D,WAAW,CAACyJ,QAAQ,CAAC,CAAC;MACnD,IAAIE,QAAQ,GAAG7H,CAAC;MAChB,IAAI8H,QAAQ,GAAG7F,QAAQ,CAAC,IAAI,CAAC/D,WAAW,CAAC2J,QAAQ,CAAC,CAAC;MACnD,IAAIE,aAAa,GAAGxB,SAAS;MAC7B;MACA,IAAIvG,CAAC,GAAGuG,SAAS,GAAGzG,GAAG,EAAE;QACvBiI,aAAa,GAAGjI,GAAG,GAAGE,CAAC;MACzB;MACA;MACA,KAAK,IAAI6E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkD,aAAa,EAAElD,CAAC,EAAE,EAAE;QACtC,IAAI1B,QAAQ,GAAG,IAAI,CAACjF,WAAW,CAAC8B,CAAC,GAAG6E,CAAC,CAAC;QACtC,IAAInC,KAAK,GAAGT,QAAQ,CAACkB,QAAQ,CAAC;QAC9B,IAAIT,KAAK,GAAGkF,QAAQ,EAAE;UACpBA,QAAQ,GAAGlF,KAAK;UAChBiF,QAAQ,GAAG3H,CAAC,GAAG6E,CAAC;QAClB;QACA,IAAInC,KAAK,GAAGoF,QAAQ,EAAE;UACpBA,QAAQ,GAAGpF,KAAK;UAChBmF,QAAQ,GAAG7H,CAAC,GAAG6E,CAAC;QAClB;MACF;MACA,IAAImD,WAAW,GAAG,IAAI,CAAC9J,WAAW,CAACyJ,QAAQ,CAAC;MAC5C,IAAIM,WAAW,GAAG,IAAI,CAAC/J,WAAW,CAAC2J,QAAQ,CAAC;MAC5C;MACA,IAAIF,QAAQ,GAAGE,QAAQ,EAAE;QACvBzD,UAAU,CAACxE,MAAM,EAAE,CAAC,GAAGoI,WAAW;QAClC5D,UAAU,CAACxE,MAAM,EAAE,CAAC,GAAGqI,WAAW;MACpC,CAAC,MAAM;QACL7D,UAAU,CAACxE,MAAM,EAAE,CAAC,GAAGqI,WAAW;QAClC7D,UAAU,CAACxE,MAAM,EAAE,CAAC,GAAGoI,WAAW;MACpC;IACF;IACAtC,MAAM,CAACvI,MAAM,GAAGyC,MAAM;IACtB8F,MAAM,CAACzH,QAAQ,GAAGmG,UAAU;IAC5BsB,MAAM,CAACX,gBAAgB,CAAC,CAAC;IACzB,OAAOW,MAAM;EACf,CAAC;EACD;AACF;AACA;AACA;EACE3I,SAAS,CAACO,SAAS,CAAC4K,UAAU,GAAG,UAAUC,SAAS,EAAE/B,IAAI,EAAEgC,WAAW,EAAEC,WAAW,EAAE;IACpF,IAAI3C,MAAM,GAAG,IAAI,CAACpL,KAAK,CAAC,CAAC6N,SAAS,CAAC,EAAE,IAAI,CAAC;IAC1C,IAAI9B,aAAa,GAAGX,MAAM,CAAC1I,OAAO;IAClC,IAAIsL,WAAW,GAAG,EAAE;IACpB,IAAI/B,SAAS,GAAGnG,IAAI,CAACoG,KAAK,CAAC,CAAC,GAAGJ,IAAI,CAAC;IACpC,IAAInE,QAAQ,GAAGoE,aAAa,CAAC8B,SAAS,CAAC;IACvC,IAAIrI,GAAG,GAAG,IAAI,CAAC/B,KAAK,CAAC,CAAC;IACtB,IAAIkI,cAAc,GAAGP,MAAM,CAACzI,UAAU,CAACkL,SAAS,CAAC,GAAGvM,gBAAgB,CAAC,CAAC;IACtE,IAAIwI,UAAU,GAAG,KAAK1I,cAAc,CAAC,IAAI,CAAC0B,SAAS,CAAC,EAAEgD,IAAI,CAACyG,IAAI,CAAC/G,GAAG,GAAGyG,SAAS,CAAC,CAAC;IACjF,IAAI3G,MAAM,GAAG,CAAC;IACd,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,IAAIuG,SAAS,EAAE;MACvC;MACA,IAAIA,SAAS,GAAGzG,GAAG,GAAGE,CAAC,EAAE;QACvBuG,SAAS,GAAGzG,GAAG,GAAGE,CAAC;QACnBsI,WAAW,CAAC1L,MAAM,GAAG2J,SAAS;MAChC;MACA,KAAK,IAAI1B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0B,SAAS,EAAE1B,CAAC,EAAE,EAAE;QAClC,IAAI0D,OAAO,GAAG,IAAI,CAACrK,WAAW,CAAC8B,CAAC,GAAG6E,CAAC,CAAC;QACrCyD,WAAW,CAACzD,CAAC,CAAC,GAAG5C,QAAQ,CAACsG,OAAO,CAAC;MACpC;MACA,IAAI7F,KAAK,GAAG0F,WAAW,CAACE,WAAW,CAAC;MACpC,IAAIE,cAAc,GAAG,IAAI,CAACtK,WAAW,CAACkC,IAAI,CAACC,GAAG,CAACL,CAAC,GAAGqI,WAAW,CAACC,WAAW,EAAE5F,KAAK,CAAC,IAAI,CAAC,EAAE5C,GAAG,GAAG,CAAC,CAAC,CAAC;MAClG;MACAmC,QAAQ,CAACuG,cAAc,CAAC,GAAG9F,KAAK;MAChC,IAAIA,KAAK,GAAGuD,cAAc,CAAC,CAAC,CAAC,EAAE;QAC7BA,cAAc,CAAC,CAAC,CAAC,GAAGvD,KAAK;MAC3B;MACA,IAAIA,KAAK,GAAGuD,cAAc,CAAC,CAAC,CAAC,EAAE;QAC7BA,cAAc,CAAC,CAAC,CAAC,GAAGvD,KAAK;MAC3B;MACA0B,UAAU,CAACxE,MAAM,EAAE,CAAC,GAAG4I,cAAc;IACvC;IACA9C,MAAM,CAACvI,MAAM,GAAGyC,MAAM;IACtB8F,MAAM,CAACzH,QAAQ,GAAGmG,UAAU;IAC5BsB,MAAM,CAACX,gBAAgB,CAAC,CAAC;IACzB,OAAOW,MAAM;EACf,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE3I,SAAS,CAACO,SAAS,CAACuF,IAAI,GAAG,UAAU2B,IAAI,EAAEC,EAAE,EAAE;IAC7C,IAAI,CAAC,IAAI,CAACtH,MAAM,EAAE;MAChB;IACF;IACA,IAAIuH,OAAO,GAAGF,IAAI,CAAC5H,MAAM;IACzB,IAAIuE,MAAM,GAAG,IAAI,CAACnE,OAAO;IACzB,KAAK,IAAIgD,CAAC,GAAG,CAAC,EAAEF,GAAG,GAAG,IAAI,CAAC/B,KAAK,CAAC,CAAC,EAAEiC,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;MAChD,IAAIsC,MAAM,GAAG,IAAI,CAACpE,WAAW,CAAC8B,CAAC,CAAC;MAChC;MACA,QAAQ0E,OAAO;QACb,KAAK,CAAC;UACJD,EAAE,CAACzE,CAAC,CAAC;UACL;QACF,KAAK,CAAC;UACJyE,EAAE,CAACtD,MAAM,CAACqD,IAAI,CAAC,CAAC,CAAC,CAAC,CAAClC,MAAM,CAAC,EAAEtC,CAAC,CAAC;UAC9B;QACF,KAAK,CAAC;UACJyE,EAAE,CAACtD,MAAM,CAACqD,IAAI,CAAC,CAAC,CAAC,CAAC,CAAClC,MAAM,CAAC,EAAEnB,MAAM,CAACqD,IAAI,CAAC,CAAC,CAAC,CAAC,CAAClC,MAAM,CAAC,EAAEtC,CAAC,CAAC;UACvD;QACF;UACE,IAAI6E,CAAC,GAAG,CAAC;UACT,IAAInC,KAAK,GAAG,EAAE;UACd,OAAOmC,CAAC,GAAGH,OAAO,EAAEG,CAAC,EAAE,EAAE;YACvBnC,KAAK,CAACmC,CAAC,CAAC,GAAG1D,MAAM,CAACqD,IAAI,CAACK,CAAC,CAAC,CAAC,CAACvC,MAAM,CAAC;UACpC;UACA;UACAI,KAAK,CAACmC,CAAC,CAAC,GAAG7E,CAAC;UACZyE,EAAE,CAACK,KAAK,CAAC,IAAI,EAAEpC,KAAK,CAAC;MACzB;IACF;EACF,CAAC;EACD;AACF;AACA;EACE3F,SAAS,CAACO,SAAS,CAACmL,aAAa,GAAG,UAAU7J,GAAG,EAAE;IACjD;IACA,IAAI4D,OAAO,GAAG,IAAI,CAACxF,OAAO,CAAC4B,GAAG,CAAC;IAC/B,IAAI8J,aAAa,GAAG9M,gBAAgB,CAAC,CAAC;IACtC,IAAI,CAAC4G,OAAO,EAAE;MACZ,OAAOkG,aAAa;IACtB;IACA;IACA,IAAIC,OAAO,GAAG,IAAI,CAAC5K,KAAK,CAAC,CAAC;IAC1B;IACA;IACA;IACA,IAAI6K,MAAM,GAAG,CAAC,IAAI,CAAC3K,QAAQ;IAC3B,IAAI4K,SAAS;IACb,IAAID,MAAM,EAAE;MACV,OAAO,IAAI,CAAC3L,UAAU,CAAC2B,GAAG,CAAC,CAAC1C,KAAK,CAAC,CAAC;IACrC;IACA2M,SAAS,GAAG,IAAI,CAAC3L,OAAO,CAAC0B,GAAG,CAAC;IAC7B,IAAIiK,SAAS,EAAE;MACb,OAAOA,SAAS,CAAC3M,KAAK,CAAC,CAAC;IAC1B;IACA2M,SAAS,GAAGH,aAAa;IACzB,IAAIrI,GAAG,GAAGwI,SAAS,CAAC,CAAC,CAAC;IACtB,IAAIvI,GAAG,GAAGuI,SAAS,CAAC,CAAC,CAAC;IACtB,KAAK,IAAI7I,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2I,OAAO,EAAE3I,CAAC,EAAE,EAAE;MAChC,IAAIsC,MAAM,GAAG,IAAI,CAACpE,WAAW,CAAC8B,CAAC,CAAC;MAChC,IAAI0C,KAAK,GAAGF,OAAO,CAACF,MAAM,CAAC;MAC3BI,KAAK,GAAGrC,GAAG,KAAKA,GAAG,GAAGqC,KAAK,CAAC;MAC5BA,KAAK,GAAGpC,GAAG,KAAKA,GAAG,GAAGoC,KAAK,CAAC;IAC9B;IACAmG,SAAS,GAAG,CAACxI,GAAG,EAAEC,GAAG,CAAC;IACtB,IAAI,CAACpD,OAAO,CAAC0B,GAAG,CAAC,GAAGiK,SAAS;IAC7B,OAAOA,SAAS;EAClB,CAAC;EACD;AACF;AACA;EACE9L,SAAS,CAACO,SAAS,CAACwL,cAAc,GAAG,UAAUvH,GAAG,EAAE;IAClD,IAAIe,MAAM,GAAG,IAAI,CAACpE,WAAW,CAACqD,GAAG,CAAC;IAClC,IAAI,CAAC,IAAI,CAACvD,SAAS,CAAC+C,UAAU,EAAE;MAC9B,IAAId,GAAG,GAAG,EAAE;MACZ,IAAIkB,MAAM,GAAG,IAAI,CAACnE,OAAO;MACzB,KAAK,IAAIgD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmB,MAAM,CAACvE,MAAM,EAAEoD,CAAC,EAAE,EAAE;QACtCC,GAAG,CAACmC,IAAI,CAACjB,MAAM,CAACnB,CAAC,CAAC,CAACsC,MAAM,CAAC,CAAC;MAC7B;MACA,OAAOrC,GAAG;IACZ,CAAC,MAAM;MACL,OAAO,IAAI,CAACjC,SAAS,CAACF,OAAO,CAACwE,MAAM,CAAC;IACvC;EACF,CAAC;EACD;AACF;AACA;AACA;AACA;EACEvF,SAAS,CAACO,SAAS,CAAChD,KAAK,GAAG,UAAUyO,UAAU,EAAEC,aAAa,EAAE;IAC/D,IAAItD,MAAM,GAAG,IAAI3I,SAAS,CAAC,CAAC;IAC5B,IAAIoE,MAAM,GAAG,IAAI,CAACnE,OAAO;IACzB,IAAIiM,aAAa,GAAGF,UAAU,IAAIpO,MAAM,CAACoO,UAAU,EAAE,UAAUG,GAAG,EAAE7M,MAAM,EAAE;MAC1E6M,GAAG,CAAC7M,MAAM,CAAC,GAAG,IAAI;MAClB,OAAO6M,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,IAAID,aAAa,EAAE;MACjB,KAAK,IAAIjJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmB,MAAM,CAACvE,MAAM,EAAEoD,CAAC,EAAE,EAAE;QACtC;QACA0F,MAAM,CAAC1I,OAAO,CAACgD,CAAC,CAAC,GAAG,CAACiJ,aAAa,CAACjJ,CAAC,CAAC,GAAGmB,MAAM,CAACnB,CAAC,CAAC,GAAGlE,UAAU,CAACqF,MAAM,CAACnB,CAAC,CAAC,CAAC;MAC3E;IACF,CAAC,MAAM;MACL0F,MAAM,CAAC1I,OAAO,GAAGmE,MAAM;IACzB;IACA,IAAI,CAACgI,gBAAgB,CAACzD,MAAM,CAAC;IAC7B,IAAI,CAACsD,aAAa,EAAE;MAClBtD,MAAM,CAACzH,QAAQ,GAAG,IAAI,CAACmL,aAAa,CAAC,CAAC;IACxC;IACA1D,MAAM,CAACX,gBAAgB,CAAC,CAAC;IACzB,OAAOW,MAAM;EACf,CAAC;EACD3I,SAAS,CAACO,SAAS,CAAC6L,gBAAgB,GAAG,UAAUzD,MAAM,EAAE;IACvDA,MAAM,CAACvI,MAAM,GAAG,IAAI,CAACA,MAAM;IAC3BuI,MAAM,CAACtI,SAAS,GAAG,IAAI,CAACA,SAAS;IACjCsI,MAAM,CAAC1H,SAAS,GAAG,IAAI,CAACA,SAAS;IACjC0H,MAAM,CAAC/G,WAAW,GAAG,IAAI,CAACA,WAAW;IACrC+G,MAAM,CAACxI,OAAO,GAAG5C,KAAK,CAAC,IAAI,CAAC4C,OAAO,CAAC;IACpCwI,MAAM,CAACzI,UAAU,GAAG3C,KAAK,CAAC,IAAI,CAAC2C,UAAU,CAAC;EAC5C,CAAC;EACDF,SAAS,CAACO,SAAS,CAAC8L,aAAa,GAAG,YAAY;IAC9C,IAAI,IAAI,CAACnL,QAAQ,EAAE;MACjB,IAAIjC,IAAI,GAAG,IAAI,CAACiC,QAAQ,CAAChC,WAAW;MACpC,IAAImH,OAAO,GAAG,KAAK,CAAC;MACpB,IAAIpH,IAAI,KAAKf,KAAK,EAAE;QAClB,IAAIoJ,SAAS,GAAG,IAAI,CAACpG,QAAQ,CAACrB,MAAM;QACpCwG,OAAO,GAAG,IAAIpH,IAAI,CAACqI,SAAS,CAAC;QAC7B,KAAK,IAAIrE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqE,SAAS,EAAErE,CAAC,EAAE,EAAE;UAClCoD,OAAO,CAACpD,CAAC,CAAC,GAAG,IAAI,CAAC/B,QAAQ,CAAC+B,CAAC,CAAC;QAC/B;MACF,CAAC,MAAM;QACLoD,OAAO,GAAG,IAAIpH,IAAI,CAAC,IAAI,CAACiC,QAAQ,CAAC;MACnC;MACA,OAAOmF,OAAO;IAChB;IACA,OAAO,IAAI;EACb,CAAC;EACDrG,SAAS,CAACO,SAAS,CAACa,kBAAkB,GAAG,UAAUoD,GAAG,EAAE;IACtD,OAAOA,GAAG;EACZ,CAAC;EACDxE,SAAS,CAACO,SAAS,CAACmK,UAAU,GAAG,UAAUlG,GAAG,EAAE;IAC9C,IAAIA,GAAG,GAAG,IAAI,CAACpE,MAAM,IAAIoE,GAAG,IAAI,CAAC,EAAE;MACjC,OAAO,IAAI,CAACtD,QAAQ,CAACsD,GAAG,CAAC;IAC3B;IACA,OAAO,CAAC,CAAC;EACX,CAAC;EACDxE,SAAS,CAACO,SAAS,CAACyH,gBAAgB,GAAG,YAAY;IACjD,IAAI,CAAC7G,WAAW,GAAG,IAAI,CAACD,QAAQ,GAAG,IAAI,CAACwJ,UAAU,GAAG,IAAI,CAACtJ,kBAAkB;EAC9E,CAAC;EACDpB,SAAS,CAACsM,aAAa,GAAG,YAAY;IACpC,SAASC,iBAAiBA,CAACzH,QAAQ,EAAEhD,QAAQ,EAAEkF,SAAS,EAAErD,QAAQ,EAAE;MAClE,OAAO9F,cAAc,CAACiH,QAAQ,CAACnB,QAAQ,CAAC,EAAE,IAAI,CAAC/B,WAAW,CAAC+B,QAAQ,CAAC,CAAC;IACvE;IACAjF,sBAAsB,GAAG;MACvBgG,SAAS,EAAE6H,iBAAiB;MAC5BC,UAAU,EAAE,SAAAA,CAAU1H,QAAQ,EAAEhD,QAAQ,EAAEkF,SAAS,EAAErD,QAAQ,EAAE;QAC7D,OAAO9F,cAAc,CAACiH,QAAQ,CAAChD,QAAQ,CAAC,EAAE,IAAI,CAACF,WAAW,CAAC+B,QAAQ,CAAC,CAAC;MACvE,CAAC;MACD8I,YAAY,EAAEF,iBAAiB;MAC/BG,QAAQ,EAAE,SAAAA,CAAU5H,QAAQ,EAAEhD,QAAQ,EAAEkF,SAAS,EAAErD,QAAQ,EAAE;QAC3D;QACA;QACA;QACA;QACA,IAAIgC,KAAK,GAAGb,QAAQ,KAAKA,QAAQ,CAACa,KAAK,IAAI,IAAI,GAAGb,QAAQ,GAAGA,QAAQ,CAACa,KAAK,CAAC;QAC5E,OAAO9H,cAAc,CAAC8H,KAAK,YAAYzH,KAAK,GAAGyH,KAAK,CAAChC,QAAQ;QAC7D;QAAA,EACEgC,KAAK,EAAE,IAAI,CAAC/D,WAAW,CAAC+B,QAAQ,CAAC,CAAC;MACtC,CAAC;MACDgJ,UAAU,EAAE,SAAAA,CAAU7H,QAAQ,EAAEhD,QAAQ,EAAEkF,SAAS,EAAErD,QAAQ,EAAE;QAC7D,OAAOmB,QAAQ,CAACnB,QAAQ,CAAC;MAC3B;IACF,CAAC;EACH,CAAC,CAAC,CAAC;EACH,OAAO3D,SAAS;AAClB,CAAC,CAAC,CAAC;AACH,eAAeA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}