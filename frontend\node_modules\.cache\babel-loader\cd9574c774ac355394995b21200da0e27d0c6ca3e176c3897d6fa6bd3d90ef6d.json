{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { each, indexOf, curry, assert, map, createHashMap } from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport * as brushHelper from './brushHelper.js';\nimport { parseFinder as modelUtilParseFinder } from '../../util/model.js';\n// FIXME\n// how to genarialize to more coordinate systems.\nvar INCLUDE_FINDER_MAIN_TYPES = ['grid', 'xAxis', 'yAxis', 'geo', 'graph', 'polar', 'radiusAxis', 'angleAxis', 'bmap'];\nvar BrushTargetManager = /** @class */function () {\n  /**\r\n   * @param finder contains Index/Id/Name of xAxis/yAxis/geo/grid\r\n   *        Each can be {number|Array.<number>}. like: {xAxisIndex: [3, 4]}\r\n   * @param opt.include include coordinate system types.\r\n   */\n  function BrushTargetManager(finder, ecModel, opt) {\n    var _this = this;\n    this._targetInfoList = [];\n    var foundCpts = parseFinder(ecModel, finder);\n    each(targetInfoBuilders, function (builder, type) {\n      if (!opt || !opt.include || indexOf(opt.include, type) >= 0) {\n        builder(foundCpts, _this._targetInfoList);\n      }\n    });\n  }\n  BrushTargetManager.prototype.setOutputRanges = function (areas, ecModel) {\n    this.matchOutputRanges(areas, ecModel, function (area, coordRange, coordSys) {\n      (area.coordRanges || (area.coordRanges = [])).push(coordRange);\n      // area.coordRange is the first of area.coordRanges\n      if (!area.coordRange) {\n        area.coordRange = coordRange;\n        // In 'category' axis, coord to pixel is not reversible, so we can not\n        // rebuild range by coordRange accrately, which may bring trouble when\n        // brushing only one item. So we use __rangeOffset to rebuilding range\n        // by coordRange. And this it only used in brush component so it is no\n        // need to be adapted to coordRanges.\n        var result = coordConvert[area.brushType](0, coordSys, coordRange);\n        area.__rangeOffset = {\n          offset: diffProcessor[area.brushType](result.values, area.range, [1, 1]),\n          xyMinMax: result.xyMinMax\n        };\n      }\n    });\n    return areas;\n  };\n  BrushTargetManager.prototype.matchOutputRanges = function (areas, ecModel, cb) {\n    each(areas, function (area) {\n      var targetInfo = this.findTargetInfo(area, ecModel);\n      if (targetInfo && targetInfo !== true) {\n        each(targetInfo.coordSyses, function (coordSys) {\n          var result = coordConvert[area.brushType](1, coordSys, area.range, true);\n          cb(area, result.values, coordSys, ecModel);\n        });\n      }\n    }, this);\n  };\n  /**\r\n   * the `areas` is `BrushModel.areas`.\r\n   * Called in layout stage.\r\n   * convert `area.coordRange` to global range and set panelId to `area.range`.\r\n   */\n  BrushTargetManager.prototype.setInputRanges = function (areas, ecModel) {\n    each(areas, function (area) {\n      var targetInfo = this.findTargetInfo(area, ecModel);\n      if (process.env.NODE_ENV !== 'production') {\n        assert(!targetInfo || targetInfo === true || area.coordRange, 'coordRange must be specified when coord index specified.');\n        assert(!targetInfo || targetInfo !== true || area.range, 'range must be specified in global brush.');\n      }\n      area.range = area.range || [];\n      // convert coordRange to global range and set panelId.\n      if (targetInfo && targetInfo !== true) {\n        area.panelId = targetInfo.panelId;\n        // (1) area.range should always be calculate from coordRange but does\n        // not keep its original value, for the sake of the dataZoom scenario,\n        // where area.coordRange remains unchanged but area.range may be changed.\n        // (2) Only support converting one coordRange to pixel range in brush\n        // component. So do not consider `coordRanges`.\n        // (3) About __rangeOffset, see comment above.\n        var result = coordConvert[area.brushType](0, targetInfo.coordSys, area.coordRange);\n        var rangeOffset = area.__rangeOffset;\n        area.range = rangeOffset ? diffProcessor[area.brushType](result.values, rangeOffset.offset, getScales(result.xyMinMax, rangeOffset.xyMinMax)) : result.values;\n      }\n    }, this);\n  };\n  BrushTargetManager.prototype.makePanelOpts = function (api, getDefaultBrushType) {\n    return map(this._targetInfoList, function (targetInfo) {\n      var rect = targetInfo.getPanelRect();\n      return {\n        panelId: targetInfo.panelId,\n        defaultBrushType: getDefaultBrushType ? getDefaultBrushType(targetInfo) : null,\n        clipPath: brushHelper.makeRectPanelClipPath(rect),\n        isTargetByCursor: brushHelper.makeRectIsTargetByCursor(rect, api, targetInfo.coordSysModel),\n        getLinearBrushOtherExtent: brushHelper.makeLinearBrushOtherExtent(rect)\n      };\n    });\n  };\n  BrushTargetManager.prototype.controlSeries = function (area, seriesModel, ecModel) {\n    // Check whether area is bound in coord, and series do not belong to that coord.\n    // If do not do this check, some brush (like lineX) will controll all axes.\n    var targetInfo = this.findTargetInfo(area, ecModel);\n    return targetInfo === true || targetInfo && indexOf(targetInfo.coordSyses, seriesModel.coordinateSystem) >= 0;\n  };\n  /**\r\n   * If return Object, a coord found.\r\n   * If return true, global found.\r\n   * Otherwise nothing found.\r\n   */\n  BrushTargetManager.prototype.findTargetInfo = function (area, ecModel) {\n    var targetInfoList = this._targetInfoList;\n    var foundCpts = parseFinder(ecModel, area);\n    for (var i = 0; i < targetInfoList.length; i++) {\n      var targetInfo = targetInfoList[i];\n      var areaPanelId = area.panelId;\n      if (areaPanelId) {\n        if (targetInfo.panelId === areaPanelId) {\n          return targetInfo;\n        }\n      } else {\n        for (var j = 0; j < targetInfoMatchers.length; j++) {\n          if (targetInfoMatchers[j](foundCpts, targetInfo)) {\n            return targetInfo;\n          }\n        }\n      }\n    }\n    return true;\n  };\n  return BrushTargetManager;\n}();\nfunction formatMinMax(minMax) {\n  minMax[0] > minMax[1] && minMax.reverse();\n  return minMax;\n}\nfunction parseFinder(ecModel, finder) {\n  return modelUtilParseFinder(ecModel, finder, {\n    includeMainTypes: INCLUDE_FINDER_MAIN_TYPES\n  });\n}\nvar targetInfoBuilders = {\n  grid: function (foundCpts, targetInfoList) {\n    var xAxisModels = foundCpts.xAxisModels;\n    var yAxisModels = foundCpts.yAxisModels;\n    var gridModels = foundCpts.gridModels;\n    // Remove duplicated.\n    var gridModelMap = createHashMap();\n    var xAxesHas = {};\n    var yAxesHas = {};\n    if (!xAxisModels && !yAxisModels && !gridModels) {\n      return;\n    }\n    each(xAxisModels, function (axisModel) {\n      var gridModel = axisModel.axis.grid.model;\n      gridModelMap.set(gridModel.id, gridModel);\n      xAxesHas[gridModel.id] = true;\n    });\n    each(yAxisModels, function (axisModel) {\n      var gridModel = axisModel.axis.grid.model;\n      gridModelMap.set(gridModel.id, gridModel);\n      yAxesHas[gridModel.id] = true;\n    });\n    each(gridModels, function (gridModel) {\n      gridModelMap.set(gridModel.id, gridModel);\n      xAxesHas[gridModel.id] = true;\n      yAxesHas[gridModel.id] = true;\n    });\n    gridModelMap.each(function (gridModel) {\n      var grid = gridModel.coordinateSystem;\n      var cartesians = [];\n      each(grid.getCartesians(), function (cartesian, index) {\n        if (indexOf(xAxisModels, cartesian.getAxis('x').model) >= 0 || indexOf(yAxisModels, cartesian.getAxis('y').model) >= 0) {\n          cartesians.push(cartesian);\n        }\n      });\n      targetInfoList.push({\n        panelId: 'grid--' + gridModel.id,\n        gridModel: gridModel,\n        coordSysModel: gridModel,\n        // Use the first one as the representitive coordSys.\n        coordSys: cartesians[0],\n        coordSyses: cartesians,\n        getPanelRect: panelRectBuilders.grid,\n        xAxisDeclared: xAxesHas[gridModel.id],\n        yAxisDeclared: yAxesHas[gridModel.id]\n      });\n    });\n  },\n  geo: function (foundCpts, targetInfoList) {\n    each(foundCpts.geoModels, function (geoModel) {\n      var coordSys = geoModel.coordinateSystem;\n      targetInfoList.push({\n        panelId: 'geo--' + geoModel.id,\n        geoModel: geoModel,\n        coordSysModel: geoModel,\n        coordSys: coordSys,\n        coordSyses: [coordSys],\n        getPanelRect: panelRectBuilders.geo\n      });\n    });\n  }\n};\nvar targetInfoMatchers = [\n// grid\nfunction (foundCpts, targetInfo) {\n  var xAxisModel = foundCpts.xAxisModel;\n  var yAxisModel = foundCpts.yAxisModel;\n  var gridModel = foundCpts.gridModel;\n  !gridModel && xAxisModel && (gridModel = xAxisModel.axis.grid.model);\n  !gridModel && yAxisModel && (gridModel = yAxisModel.axis.grid.model);\n  return gridModel && gridModel === targetInfo.gridModel;\n},\n// geo\nfunction (foundCpts, targetInfo) {\n  var geoModel = foundCpts.geoModel;\n  return geoModel && geoModel === targetInfo.geoModel;\n}];\nvar panelRectBuilders = {\n  grid: function () {\n    // grid is not Transformable.\n    return this.coordSys.master.getRect().clone();\n  },\n  geo: function () {\n    var coordSys = this.coordSys;\n    var rect = coordSys.getBoundingRect().clone();\n    // geo roam and zoom transform\n    rect.applyTransform(graphic.getTransform(coordSys));\n    return rect;\n  }\n};\nvar coordConvert = {\n  lineX: curry(axisConvert, 0),\n  lineY: curry(axisConvert, 1),\n  rect: function (to, coordSys, rangeOrCoordRange, clamp) {\n    var xminymin = to ? coordSys.pointToData([rangeOrCoordRange[0][0], rangeOrCoordRange[1][0]], clamp) : coordSys.dataToPoint([rangeOrCoordRange[0][0], rangeOrCoordRange[1][0]], clamp);\n    var xmaxymax = to ? coordSys.pointToData([rangeOrCoordRange[0][1], rangeOrCoordRange[1][1]], clamp) : coordSys.dataToPoint([rangeOrCoordRange[0][1], rangeOrCoordRange[1][1]], clamp);\n    var values = [formatMinMax([xminymin[0], xmaxymax[0]]), formatMinMax([xminymin[1], xmaxymax[1]])];\n    return {\n      values: values,\n      xyMinMax: values\n    };\n  },\n  polygon: function (to, coordSys, rangeOrCoordRange, clamp) {\n    var xyMinMax = [[Infinity, -Infinity], [Infinity, -Infinity]];\n    var values = map(rangeOrCoordRange, function (item) {\n      var p = to ? coordSys.pointToData(item, clamp) : coordSys.dataToPoint(item, clamp);\n      xyMinMax[0][0] = Math.min(xyMinMax[0][0], p[0]);\n      xyMinMax[1][0] = Math.min(xyMinMax[1][0], p[1]);\n      xyMinMax[0][1] = Math.max(xyMinMax[0][1], p[0]);\n      xyMinMax[1][1] = Math.max(xyMinMax[1][1], p[1]);\n      return p;\n    });\n    return {\n      values: values,\n      xyMinMax: xyMinMax\n    };\n  }\n};\nfunction axisConvert(axisNameIndex, to, coordSys, rangeOrCoordRange) {\n  if (process.env.NODE_ENV !== 'production') {\n    assert(coordSys.type === 'cartesian2d', 'lineX/lineY brush is available only in cartesian2d.');\n  }\n  var axis = coordSys.getAxis(['x', 'y'][axisNameIndex]);\n  var values = formatMinMax(map([0, 1], function (i) {\n    return to ? axis.coordToData(axis.toLocalCoord(rangeOrCoordRange[i]), true) : axis.toGlobalCoord(axis.dataToCoord(rangeOrCoordRange[i]));\n  }));\n  var xyMinMax = [];\n  xyMinMax[axisNameIndex] = values;\n  xyMinMax[1 - axisNameIndex] = [NaN, NaN];\n  return {\n    values: values,\n    xyMinMax: xyMinMax\n  };\n}\nvar diffProcessor = {\n  lineX: curry(axisDiffProcessor, 0),\n  lineY: curry(axisDiffProcessor, 1),\n  rect: function (values, refer, scales) {\n    return [[values[0][0] - scales[0] * refer[0][0], values[0][1] - scales[0] * refer[0][1]], [values[1][0] - scales[1] * refer[1][0], values[1][1] - scales[1] * refer[1][1]]];\n  },\n  polygon: function (values, refer, scales) {\n    return map(values, function (item, idx) {\n      return [item[0] - scales[0] * refer[idx][0], item[1] - scales[1] * refer[idx][1]];\n    });\n  }\n};\nfunction axisDiffProcessor(axisNameIndex, values, refer, scales) {\n  return [values[0] - scales[axisNameIndex] * refer[0], values[1] - scales[axisNameIndex] * refer[1]];\n}\n// We have to process scale caused by dataZoom manually,\n// although it might be not accurate.\n// Return [0~1, 0~1]\nfunction getScales(xyMinMaxCurr, xyMinMaxOrigin) {\n  var sizeCurr = getSize(xyMinMaxCurr);\n  var sizeOrigin = getSize(xyMinMaxOrigin);\n  var scales = [sizeCurr[0] / sizeOrigin[0], sizeCurr[1] / sizeOrigin[1]];\n  isNaN(scales[0]) && (scales[0] = 1);\n  isNaN(scales[1]) && (scales[1] = 1);\n  return scales;\n}\nfunction getSize(xyMinMax) {\n  return xyMinMax ? [xyMinMax[0][1] - xyMinMax[0][0], xyMinMax[1][1] - xyMinMax[1][0]] : [NaN, NaN];\n}\nexport default BrushTargetManager;", "map": {"version": 3, "names": ["each", "indexOf", "curry", "assert", "map", "createHashMap", "graphic", "brushHelper", "parseFinder", "modelUtilParseFinder", "INCLUDE_FINDER_MAIN_TYPES", "BrushTargetManager", "finder", "ecModel", "opt", "_this", "_targetInfoList", "foundCpts", "targetInfoBuilders", "builder", "type", "include", "prototype", "setOutputRanges", "areas", "matchOutputRanges", "area", "coordRange", "coordSys", "coordRang<PERSON>", "push", "result", "coordConvert", "brushType", "__rangeOffset", "offset", "diffProcessor", "values", "range", "xyMinMax", "cb", "targetInfo", "findTargetInfo", "coordSyses", "setInputRanges", "process", "env", "NODE_ENV", "panelId", "rangeOffset", "getScales", "makePanelOpts", "api", "getDefaultBrushType", "rect", "getPanelRect", "defaultBrushType", "clipPath", "makeRectPanelClipPath", "isTargetByCursor", "makeRectIsTargetByCursor", "coordSysModel", "getLinearBrushOtherExtent", "makeLinearBrushOtherExtent", "controlSeries", "seriesModel", "coordinateSystem", "targetInfoList", "i", "length", "areaPanelId", "j", "targetInfoMatchers", "formatMinMax", "minMax", "reverse", "includeMainTypes", "grid", "xAxisModels", "yAxisModels", "gridModels", "gridModelMap", "xAxesHas", "yAxesHas", "axisModel", "gridModel", "axis", "model", "set", "id", "cartesians", "getCartesians", "cartesian", "index", "getAxis", "panelRectBuilders", "xAxisDeclared", "yAxisDeclared", "geo", "geoModels", "geoModel", "xAxisModel", "yAxisModel", "master", "getRect", "clone", "getBoundingRect", "applyTransform", "getTransform", "lineX", "axisConvert", "lineY", "to", "rangeOrCoordRange", "clamp", "xminymin", "pointToData", "dataToPoint", "xmaxymax", "polygon", "Infinity", "item", "p", "Math", "min", "max", "axisNameIndex", "coordToData", "toLocalCoord", "toGlobalCoord", "dataToCoord", "NaN", "axisDiffProcessor", "refer", "scales", "idx", "xyMinMaxCurr", "xyMinMaxOrigin", "sizeCurr", "getSize", "size<PERSON>rigin", "isNaN"], "sources": ["E:/AI/SmartCV/node_modules/echarts/lib/component/helper/BrushTargetManager.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { each, indexOf, curry, assert, map, createHashMap } from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport * as brushHelper from './brushHelper.js';\nimport { parseFinder as modelUtilParseFinder } from '../../util/model.js';\n// FIXME\n// how to genarialize to more coordinate systems.\nvar INCLUDE_FINDER_MAIN_TYPES = ['grid', 'xAxis', 'yAxis', 'geo', 'graph', 'polar', 'radiusAxis', 'angleAxis', 'bmap'];\nvar BrushTargetManager = /** @class */function () {\n  /**\r\n   * @param finder contains Index/Id/Name of xAxis/yAxis/geo/grid\r\n   *        Each can be {number|Array.<number>}. like: {xAxisIndex: [3, 4]}\r\n   * @param opt.include include coordinate system types.\r\n   */\n  function BrushTargetManager(finder, ecModel, opt) {\n    var _this = this;\n    this._targetInfoList = [];\n    var foundCpts = parseFinder(ecModel, finder);\n    each(targetInfoBuilders, function (builder, type) {\n      if (!opt || !opt.include || indexOf(opt.include, type) >= 0) {\n        builder(foundCpts, _this._targetInfoList);\n      }\n    });\n  }\n  BrushTargetManager.prototype.setOutputRanges = function (areas, ecModel) {\n    this.matchOutputRanges(areas, ecModel, function (area, coordRange, coordSys) {\n      (area.coordRanges || (area.coordRanges = [])).push(coordRange);\n      // area.coordRange is the first of area.coordRanges\n      if (!area.coordRange) {\n        area.coordRange = coordRange;\n        // In 'category' axis, coord to pixel is not reversible, so we can not\n        // rebuild range by coordRange accrately, which may bring trouble when\n        // brushing only one item. So we use __rangeOffset to rebuilding range\n        // by coordRange. And this it only used in brush component so it is no\n        // need to be adapted to coordRanges.\n        var result = coordConvert[area.brushType](0, coordSys, coordRange);\n        area.__rangeOffset = {\n          offset: diffProcessor[area.brushType](result.values, area.range, [1, 1]),\n          xyMinMax: result.xyMinMax\n        };\n      }\n    });\n    return areas;\n  };\n  BrushTargetManager.prototype.matchOutputRanges = function (areas, ecModel, cb) {\n    each(areas, function (area) {\n      var targetInfo = this.findTargetInfo(area, ecModel);\n      if (targetInfo && targetInfo !== true) {\n        each(targetInfo.coordSyses, function (coordSys) {\n          var result = coordConvert[area.brushType](1, coordSys, area.range, true);\n          cb(area, result.values, coordSys, ecModel);\n        });\n      }\n    }, this);\n  };\n  /**\r\n   * the `areas` is `BrushModel.areas`.\r\n   * Called in layout stage.\r\n   * convert `area.coordRange` to global range and set panelId to `area.range`.\r\n   */\n  BrushTargetManager.prototype.setInputRanges = function (areas, ecModel) {\n    each(areas, function (area) {\n      var targetInfo = this.findTargetInfo(area, ecModel);\n      if (process.env.NODE_ENV !== 'production') {\n        assert(!targetInfo || targetInfo === true || area.coordRange, 'coordRange must be specified when coord index specified.');\n        assert(!targetInfo || targetInfo !== true || area.range, 'range must be specified in global brush.');\n      }\n      area.range = area.range || [];\n      // convert coordRange to global range and set panelId.\n      if (targetInfo && targetInfo !== true) {\n        area.panelId = targetInfo.panelId;\n        // (1) area.range should always be calculate from coordRange but does\n        // not keep its original value, for the sake of the dataZoom scenario,\n        // where area.coordRange remains unchanged but area.range may be changed.\n        // (2) Only support converting one coordRange to pixel range in brush\n        // component. So do not consider `coordRanges`.\n        // (3) About __rangeOffset, see comment above.\n        var result = coordConvert[area.brushType](0, targetInfo.coordSys, area.coordRange);\n        var rangeOffset = area.__rangeOffset;\n        area.range = rangeOffset ? diffProcessor[area.brushType](result.values, rangeOffset.offset, getScales(result.xyMinMax, rangeOffset.xyMinMax)) : result.values;\n      }\n    }, this);\n  };\n  BrushTargetManager.prototype.makePanelOpts = function (api, getDefaultBrushType) {\n    return map(this._targetInfoList, function (targetInfo) {\n      var rect = targetInfo.getPanelRect();\n      return {\n        panelId: targetInfo.panelId,\n        defaultBrushType: getDefaultBrushType ? getDefaultBrushType(targetInfo) : null,\n        clipPath: brushHelper.makeRectPanelClipPath(rect),\n        isTargetByCursor: brushHelper.makeRectIsTargetByCursor(rect, api, targetInfo.coordSysModel),\n        getLinearBrushOtherExtent: brushHelper.makeLinearBrushOtherExtent(rect)\n      };\n    });\n  };\n  BrushTargetManager.prototype.controlSeries = function (area, seriesModel, ecModel) {\n    // Check whether area is bound in coord, and series do not belong to that coord.\n    // If do not do this check, some brush (like lineX) will controll all axes.\n    var targetInfo = this.findTargetInfo(area, ecModel);\n    return targetInfo === true || targetInfo && indexOf(targetInfo.coordSyses, seriesModel.coordinateSystem) >= 0;\n  };\n  /**\r\n   * If return Object, a coord found.\r\n   * If return true, global found.\r\n   * Otherwise nothing found.\r\n   */\n  BrushTargetManager.prototype.findTargetInfo = function (area, ecModel) {\n    var targetInfoList = this._targetInfoList;\n    var foundCpts = parseFinder(ecModel, area);\n    for (var i = 0; i < targetInfoList.length; i++) {\n      var targetInfo = targetInfoList[i];\n      var areaPanelId = area.panelId;\n      if (areaPanelId) {\n        if (targetInfo.panelId === areaPanelId) {\n          return targetInfo;\n        }\n      } else {\n        for (var j = 0; j < targetInfoMatchers.length; j++) {\n          if (targetInfoMatchers[j](foundCpts, targetInfo)) {\n            return targetInfo;\n          }\n        }\n      }\n    }\n    return true;\n  };\n  return BrushTargetManager;\n}();\nfunction formatMinMax(minMax) {\n  minMax[0] > minMax[1] && minMax.reverse();\n  return minMax;\n}\nfunction parseFinder(ecModel, finder) {\n  return modelUtilParseFinder(ecModel, finder, {\n    includeMainTypes: INCLUDE_FINDER_MAIN_TYPES\n  });\n}\nvar targetInfoBuilders = {\n  grid: function (foundCpts, targetInfoList) {\n    var xAxisModels = foundCpts.xAxisModels;\n    var yAxisModels = foundCpts.yAxisModels;\n    var gridModels = foundCpts.gridModels;\n    // Remove duplicated.\n    var gridModelMap = createHashMap();\n    var xAxesHas = {};\n    var yAxesHas = {};\n    if (!xAxisModels && !yAxisModels && !gridModels) {\n      return;\n    }\n    each(xAxisModels, function (axisModel) {\n      var gridModel = axisModel.axis.grid.model;\n      gridModelMap.set(gridModel.id, gridModel);\n      xAxesHas[gridModel.id] = true;\n    });\n    each(yAxisModels, function (axisModel) {\n      var gridModel = axisModel.axis.grid.model;\n      gridModelMap.set(gridModel.id, gridModel);\n      yAxesHas[gridModel.id] = true;\n    });\n    each(gridModels, function (gridModel) {\n      gridModelMap.set(gridModel.id, gridModel);\n      xAxesHas[gridModel.id] = true;\n      yAxesHas[gridModel.id] = true;\n    });\n    gridModelMap.each(function (gridModel) {\n      var grid = gridModel.coordinateSystem;\n      var cartesians = [];\n      each(grid.getCartesians(), function (cartesian, index) {\n        if (indexOf(xAxisModels, cartesian.getAxis('x').model) >= 0 || indexOf(yAxisModels, cartesian.getAxis('y').model) >= 0) {\n          cartesians.push(cartesian);\n        }\n      });\n      targetInfoList.push({\n        panelId: 'grid--' + gridModel.id,\n        gridModel: gridModel,\n        coordSysModel: gridModel,\n        // Use the first one as the representitive coordSys.\n        coordSys: cartesians[0],\n        coordSyses: cartesians,\n        getPanelRect: panelRectBuilders.grid,\n        xAxisDeclared: xAxesHas[gridModel.id],\n        yAxisDeclared: yAxesHas[gridModel.id]\n      });\n    });\n  },\n  geo: function (foundCpts, targetInfoList) {\n    each(foundCpts.geoModels, function (geoModel) {\n      var coordSys = geoModel.coordinateSystem;\n      targetInfoList.push({\n        panelId: 'geo--' + geoModel.id,\n        geoModel: geoModel,\n        coordSysModel: geoModel,\n        coordSys: coordSys,\n        coordSyses: [coordSys],\n        getPanelRect: panelRectBuilders.geo\n      });\n    });\n  }\n};\nvar targetInfoMatchers = [\n// grid\nfunction (foundCpts, targetInfo) {\n  var xAxisModel = foundCpts.xAxisModel;\n  var yAxisModel = foundCpts.yAxisModel;\n  var gridModel = foundCpts.gridModel;\n  !gridModel && xAxisModel && (gridModel = xAxisModel.axis.grid.model);\n  !gridModel && yAxisModel && (gridModel = yAxisModel.axis.grid.model);\n  return gridModel && gridModel === targetInfo.gridModel;\n},\n// geo\nfunction (foundCpts, targetInfo) {\n  var geoModel = foundCpts.geoModel;\n  return geoModel && geoModel === targetInfo.geoModel;\n}];\nvar panelRectBuilders = {\n  grid: function () {\n    // grid is not Transformable.\n    return this.coordSys.master.getRect().clone();\n  },\n  geo: function () {\n    var coordSys = this.coordSys;\n    var rect = coordSys.getBoundingRect().clone();\n    // geo roam and zoom transform\n    rect.applyTransform(graphic.getTransform(coordSys));\n    return rect;\n  }\n};\nvar coordConvert = {\n  lineX: curry(axisConvert, 0),\n  lineY: curry(axisConvert, 1),\n  rect: function (to, coordSys, rangeOrCoordRange, clamp) {\n    var xminymin = to ? coordSys.pointToData([rangeOrCoordRange[0][0], rangeOrCoordRange[1][0]], clamp) : coordSys.dataToPoint([rangeOrCoordRange[0][0], rangeOrCoordRange[1][0]], clamp);\n    var xmaxymax = to ? coordSys.pointToData([rangeOrCoordRange[0][1], rangeOrCoordRange[1][1]], clamp) : coordSys.dataToPoint([rangeOrCoordRange[0][1], rangeOrCoordRange[1][1]], clamp);\n    var values = [formatMinMax([xminymin[0], xmaxymax[0]]), formatMinMax([xminymin[1], xmaxymax[1]])];\n    return {\n      values: values,\n      xyMinMax: values\n    };\n  },\n  polygon: function (to, coordSys, rangeOrCoordRange, clamp) {\n    var xyMinMax = [[Infinity, -Infinity], [Infinity, -Infinity]];\n    var values = map(rangeOrCoordRange, function (item) {\n      var p = to ? coordSys.pointToData(item, clamp) : coordSys.dataToPoint(item, clamp);\n      xyMinMax[0][0] = Math.min(xyMinMax[0][0], p[0]);\n      xyMinMax[1][0] = Math.min(xyMinMax[1][0], p[1]);\n      xyMinMax[0][1] = Math.max(xyMinMax[0][1], p[0]);\n      xyMinMax[1][1] = Math.max(xyMinMax[1][1], p[1]);\n      return p;\n    });\n    return {\n      values: values,\n      xyMinMax: xyMinMax\n    };\n  }\n};\nfunction axisConvert(axisNameIndex, to, coordSys, rangeOrCoordRange) {\n  if (process.env.NODE_ENV !== 'production') {\n    assert(coordSys.type === 'cartesian2d', 'lineX/lineY brush is available only in cartesian2d.');\n  }\n  var axis = coordSys.getAxis(['x', 'y'][axisNameIndex]);\n  var values = formatMinMax(map([0, 1], function (i) {\n    return to ? axis.coordToData(axis.toLocalCoord(rangeOrCoordRange[i]), true) : axis.toGlobalCoord(axis.dataToCoord(rangeOrCoordRange[i]));\n  }));\n  var xyMinMax = [];\n  xyMinMax[axisNameIndex] = values;\n  xyMinMax[1 - axisNameIndex] = [NaN, NaN];\n  return {\n    values: values,\n    xyMinMax: xyMinMax\n  };\n}\nvar diffProcessor = {\n  lineX: curry(axisDiffProcessor, 0),\n  lineY: curry(axisDiffProcessor, 1),\n  rect: function (values, refer, scales) {\n    return [[values[0][0] - scales[0] * refer[0][0], values[0][1] - scales[0] * refer[0][1]], [values[1][0] - scales[1] * refer[1][0], values[1][1] - scales[1] * refer[1][1]]];\n  },\n  polygon: function (values, refer, scales) {\n    return map(values, function (item, idx) {\n      return [item[0] - scales[0] * refer[idx][0], item[1] - scales[1] * refer[idx][1]];\n    });\n  }\n};\nfunction axisDiffProcessor(axisNameIndex, values, refer, scales) {\n  return [values[0] - scales[axisNameIndex] * refer[0], values[1] - scales[axisNameIndex] * refer[1]];\n}\n// We have to process scale caused by dataZoom manually,\n// although it might be not accurate.\n// Return [0~1, 0~1]\nfunction getScales(xyMinMaxCurr, xyMinMaxOrigin) {\n  var sizeCurr = getSize(xyMinMaxCurr);\n  var sizeOrigin = getSize(xyMinMaxOrigin);\n  var scales = [sizeCurr[0] / sizeOrigin[0], sizeCurr[1] / sizeOrigin[1]];\n  isNaN(scales[0]) && (scales[0] = 1);\n  isNaN(scales[1]) && (scales[1] = 1);\n  return scales;\n}\nfunction getSize(xyMinMax) {\n  return xyMinMax ? [xyMinMax[0][1] - xyMinMax[0][0], xyMinMax[1][1] - xyMinMax[1][0]] : [NaN, NaN];\n}\nexport default BrushTargetManager;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,IAAI,EAAEC,OAAO,EAAEC,KAAK,EAAEC,MAAM,EAAEC,GAAG,EAAEC,aAAa,QAAQ,0BAA0B;AAC3F,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,OAAO,KAAKC,WAAW,MAAM,kBAAkB;AAC/C,SAASC,WAAW,IAAIC,oBAAoB,QAAQ,qBAAqB;AACzE;AACA;AACA,IAAIC,yBAAyB,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,MAAM,CAAC;AACtH,IAAIC,kBAAkB,GAAG,aAAa,YAAY;EAChD;AACF;AACA;AACA;AACA;EACE,SAASA,kBAAkBA,CAACC,MAAM,EAAEC,OAAO,EAAEC,GAAG,EAAE;IAChD,IAAIC,KAAK,GAAG,IAAI;IAChB,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB,IAAIC,SAAS,GAAGT,WAAW,CAACK,OAAO,EAAED,MAAM,CAAC;IAC5CZ,IAAI,CAACkB,kBAAkB,EAAE,UAAUC,OAAO,EAAEC,IAAI,EAAE;MAChD,IAAI,CAACN,GAAG,IAAI,CAACA,GAAG,CAACO,OAAO,IAAIpB,OAAO,CAACa,GAAG,CAACO,OAAO,EAAED,IAAI,CAAC,IAAI,CAAC,EAAE;QAC3DD,OAAO,CAACF,SAAS,EAAEF,KAAK,CAACC,eAAe,CAAC;MAC3C;IACF,CAAC,CAAC;EACJ;EACAL,kBAAkB,CAACW,SAAS,CAACC,eAAe,GAAG,UAAUC,KAAK,EAAEX,OAAO,EAAE;IACvE,IAAI,CAACY,iBAAiB,CAACD,KAAK,EAAEX,OAAO,EAAE,UAAUa,IAAI,EAAEC,UAAU,EAAEC,QAAQ,EAAE;MAC3E,CAACF,IAAI,CAACG,WAAW,KAAKH,IAAI,CAACG,WAAW,GAAG,EAAE,CAAC,EAAEC,IAAI,CAACH,UAAU,CAAC;MAC9D;MACA,IAAI,CAACD,IAAI,CAACC,UAAU,EAAE;QACpBD,IAAI,CAACC,UAAU,GAAGA,UAAU;QAC5B;QACA;QACA;QACA;QACA;QACA,IAAII,MAAM,GAAGC,YAAY,CAACN,IAAI,CAACO,SAAS,CAAC,CAAC,CAAC,EAAEL,QAAQ,EAAED,UAAU,CAAC;QAClED,IAAI,CAACQ,aAAa,GAAG;UACnBC,MAAM,EAAEC,aAAa,CAACV,IAAI,CAACO,SAAS,CAAC,CAACF,MAAM,CAACM,MAAM,EAAEX,IAAI,CAACY,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACxEC,QAAQ,EAAER,MAAM,CAACQ;QACnB,CAAC;MACH;IACF,CAAC,CAAC;IACF,OAAOf,KAAK;EACd,CAAC;EACDb,kBAAkB,CAACW,SAAS,CAACG,iBAAiB,GAAG,UAAUD,KAAK,EAAEX,OAAO,EAAE2B,EAAE,EAAE;IAC7ExC,IAAI,CAACwB,KAAK,EAAE,UAAUE,IAAI,EAAE;MAC1B,IAAIe,UAAU,GAAG,IAAI,CAACC,cAAc,CAAChB,IAAI,EAAEb,OAAO,CAAC;MACnD,IAAI4B,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;QACrCzC,IAAI,CAACyC,UAAU,CAACE,UAAU,EAAE,UAAUf,QAAQ,EAAE;UAC9C,IAAIG,MAAM,GAAGC,YAAY,CAACN,IAAI,CAACO,SAAS,CAAC,CAAC,CAAC,EAAEL,QAAQ,EAAEF,IAAI,CAACY,KAAK,EAAE,IAAI,CAAC;UACxEE,EAAE,CAACd,IAAI,EAAEK,MAAM,CAACM,MAAM,EAAET,QAAQ,EAAEf,OAAO,CAAC;QAC5C,CAAC,CAAC;MACJ;IACF,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EACD;AACF;AACA;AACA;AACA;EACEF,kBAAkB,CAACW,SAAS,CAACsB,cAAc,GAAG,UAAUpB,KAAK,EAAEX,OAAO,EAAE;IACtEb,IAAI,CAACwB,KAAK,EAAE,UAAUE,IAAI,EAAE;MAC1B,IAAIe,UAAU,GAAG,IAAI,CAACC,cAAc,CAAChB,IAAI,EAAEb,OAAO,CAAC;MACnD,IAAIgC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC5C,MAAM,CAAC,CAACsC,UAAU,IAAIA,UAAU,KAAK,IAAI,IAAIf,IAAI,CAACC,UAAU,EAAE,0DAA0D,CAAC;QACzHxB,MAAM,CAAC,CAACsC,UAAU,IAAIA,UAAU,KAAK,IAAI,IAAIf,IAAI,CAACY,KAAK,EAAE,0CAA0C,CAAC;MACtG;MACAZ,IAAI,CAACY,KAAK,GAAGZ,IAAI,CAACY,KAAK,IAAI,EAAE;MAC7B;MACA,IAAIG,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;QACrCf,IAAI,CAACsB,OAAO,GAAGP,UAAU,CAACO,OAAO;QACjC;QACA;QACA;QACA;QACA;QACA;QACA,IAAIjB,MAAM,GAAGC,YAAY,CAACN,IAAI,CAACO,SAAS,CAAC,CAAC,CAAC,EAAEQ,UAAU,CAACb,QAAQ,EAAEF,IAAI,CAACC,UAAU,CAAC;QAClF,IAAIsB,WAAW,GAAGvB,IAAI,CAACQ,aAAa;QACpCR,IAAI,CAACY,KAAK,GAAGW,WAAW,GAAGb,aAAa,CAACV,IAAI,CAACO,SAAS,CAAC,CAACF,MAAM,CAACM,MAAM,EAAEY,WAAW,CAACd,MAAM,EAAEe,SAAS,CAACnB,MAAM,CAACQ,QAAQ,EAAEU,WAAW,CAACV,QAAQ,CAAC,CAAC,GAAGR,MAAM,CAACM,MAAM;MAC/J;IACF,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EACD1B,kBAAkB,CAACW,SAAS,CAAC6B,aAAa,GAAG,UAAUC,GAAG,EAAEC,mBAAmB,EAAE;IAC/E,OAAOjD,GAAG,CAAC,IAAI,CAACY,eAAe,EAAE,UAAUyB,UAAU,EAAE;MACrD,IAAIa,IAAI,GAAGb,UAAU,CAACc,YAAY,CAAC,CAAC;MACpC,OAAO;QACLP,OAAO,EAAEP,UAAU,CAACO,OAAO;QAC3BQ,gBAAgB,EAAEH,mBAAmB,GAAGA,mBAAmB,CAACZ,UAAU,CAAC,GAAG,IAAI;QAC9EgB,QAAQ,EAAElD,WAAW,CAACmD,qBAAqB,CAACJ,IAAI,CAAC;QACjDK,gBAAgB,EAAEpD,WAAW,CAACqD,wBAAwB,CAACN,IAAI,EAAEF,GAAG,EAAEX,UAAU,CAACoB,aAAa,CAAC;QAC3FC,yBAAyB,EAAEvD,WAAW,CAACwD,0BAA0B,CAACT,IAAI;MACxE,CAAC;IACH,CAAC,CAAC;EACJ,CAAC;EACD3C,kBAAkB,CAACW,SAAS,CAAC0C,aAAa,GAAG,UAAUtC,IAAI,EAAEuC,WAAW,EAAEpD,OAAO,EAAE;IACjF;IACA;IACA,IAAI4B,UAAU,GAAG,IAAI,CAACC,cAAc,CAAChB,IAAI,EAAEb,OAAO,CAAC;IACnD,OAAO4B,UAAU,KAAK,IAAI,IAAIA,UAAU,IAAIxC,OAAO,CAACwC,UAAU,CAACE,UAAU,EAAEsB,WAAW,CAACC,gBAAgB,CAAC,IAAI,CAAC;EAC/G,CAAC;EACD;AACF;AACA;AACA;AACA;EACEvD,kBAAkB,CAACW,SAAS,CAACoB,cAAc,GAAG,UAAUhB,IAAI,EAAEb,OAAO,EAAE;IACrE,IAAIsD,cAAc,GAAG,IAAI,CAACnD,eAAe;IACzC,IAAIC,SAAS,GAAGT,WAAW,CAACK,OAAO,EAAEa,IAAI,CAAC;IAC1C,KAAK,IAAI0C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,cAAc,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;MAC9C,IAAI3B,UAAU,GAAG0B,cAAc,CAACC,CAAC,CAAC;MAClC,IAAIE,WAAW,GAAG5C,IAAI,CAACsB,OAAO;MAC9B,IAAIsB,WAAW,EAAE;QACf,IAAI7B,UAAU,CAACO,OAAO,KAAKsB,WAAW,EAAE;UACtC,OAAO7B,UAAU;QACnB;MACF,CAAC,MAAM;QACL,KAAK,IAAI8B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,kBAAkB,CAACH,MAAM,EAAEE,CAAC,EAAE,EAAE;UAClD,IAAIC,kBAAkB,CAACD,CAAC,CAAC,CAACtD,SAAS,EAAEwB,UAAU,CAAC,EAAE;YAChD,OAAOA,UAAU;UACnB;QACF;MACF;IACF;IACA,OAAO,IAAI;EACb,CAAC;EACD,OAAO9B,kBAAkB;AAC3B,CAAC,CAAC,CAAC;AACH,SAAS8D,YAAYA,CAACC,MAAM,EAAE;EAC5BA,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,IAAIA,MAAM,CAACC,OAAO,CAAC,CAAC;EACzC,OAAOD,MAAM;AACf;AACA,SAASlE,WAAWA,CAACK,OAAO,EAAED,MAAM,EAAE;EACpC,OAAOH,oBAAoB,CAACI,OAAO,EAAED,MAAM,EAAE;IAC3CgE,gBAAgB,EAAElE;EACpB,CAAC,CAAC;AACJ;AACA,IAAIQ,kBAAkB,GAAG;EACvB2D,IAAI,EAAE,SAAAA,CAAU5D,SAAS,EAAEkD,cAAc,EAAE;IACzC,IAAIW,WAAW,GAAG7D,SAAS,CAAC6D,WAAW;IACvC,IAAIC,WAAW,GAAG9D,SAAS,CAAC8D,WAAW;IACvC,IAAIC,UAAU,GAAG/D,SAAS,CAAC+D,UAAU;IACrC;IACA,IAAIC,YAAY,GAAG5E,aAAa,CAAC,CAAC;IAClC,IAAI6E,QAAQ,GAAG,CAAC,CAAC;IACjB,IAAIC,QAAQ,GAAG,CAAC,CAAC;IACjB,IAAI,CAACL,WAAW,IAAI,CAACC,WAAW,IAAI,CAACC,UAAU,EAAE;MAC/C;IACF;IACAhF,IAAI,CAAC8E,WAAW,EAAE,UAAUM,SAAS,EAAE;MACrC,IAAIC,SAAS,GAAGD,SAAS,CAACE,IAAI,CAACT,IAAI,CAACU,KAAK;MACzCN,YAAY,CAACO,GAAG,CAACH,SAAS,CAACI,EAAE,EAAEJ,SAAS,CAAC;MACzCH,QAAQ,CAACG,SAAS,CAACI,EAAE,CAAC,GAAG,IAAI;IAC/B,CAAC,CAAC;IACFzF,IAAI,CAAC+E,WAAW,EAAE,UAAUK,SAAS,EAAE;MACrC,IAAIC,SAAS,GAAGD,SAAS,CAACE,IAAI,CAACT,IAAI,CAACU,KAAK;MACzCN,YAAY,CAACO,GAAG,CAACH,SAAS,CAACI,EAAE,EAAEJ,SAAS,CAAC;MACzCF,QAAQ,CAACE,SAAS,CAACI,EAAE,CAAC,GAAG,IAAI;IAC/B,CAAC,CAAC;IACFzF,IAAI,CAACgF,UAAU,EAAE,UAAUK,SAAS,EAAE;MACpCJ,YAAY,CAACO,GAAG,CAACH,SAAS,CAACI,EAAE,EAAEJ,SAAS,CAAC;MACzCH,QAAQ,CAACG,SAAS,CAACI,EAAE,CAAC,GAAG,IAAI;MAC7BN,QAAQ,CAACE,SAAS,CAACI,EAAE,CAAC,GAAG,IAAI;IAC/B,CAAC,CAAC;IACFR,YAAY,CAACjF,IAAI,CAAC,UAAUqF,SAAS,EAAE;MACrC,IAAIR,IAAI,GAAGQ,SAAS,CAACnB,gBAAgB;MACrC,IAAIwB,UAAU,GAAG,EAAE;MACnB1F,IAAI,CAAC6E,IAAI,CAACc,aAAa,CAAC,CAAC,EAAE,UAAUC,SAAS,EAAEC,KAAK,EAAE;QACrD,IAAI5F,OAAO,CAAC6E,WAAW,EAAEc,SAAS,CAACE,OAAO,CAAC,GAAG,CAAC,CAACP,KAAK,CAAC,IAAI,CAAC,IAAItF,OAAO,CAAC8E,WAAW,EAAEa,SAAS,CAACE,OAAO,CAAC,GAAG,CAAC,CAACP,KAAK,CAAC,IAAI,CAAC,EAAE;UACtHG,UAAU,CAAC5D,IAAI,CAAC8D,SAAS,CAAC;QAC5B;MACF,CAAC,CAAC;MACFzB,cAAc,CAACrC,IAAI,CAAC;QAClBkB,OAAO,EAAE,QAAQ,GAAGqC,SAAS,CAACI,EAAE;QAChCJ,SAAS,EAAEA,SAAS;QACpBxB,aAAa,EAAEwB,SAAS;QACxB;QACAzD,QAAQ,EAAE8D,UAAU,CAAC,CAAC,CAAC;QACvB/C,UAAU,EAAE+C,UAAU;QACtBnC,YAAY,EAAEwC,iBAAiB,CAAClB,IAAI;QACpCmB,aAAa,EAAEd,QAAQ,CAACG,SAAS,CAACI,EAAE,CAAC;QACrCQ,aAAa,EAAEd,QAAQ,CAACE,SAAS,CAACI,EAAE;MACtC,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACDS,GAAG,EAAE,SAAAA,CAAUjF,SAAS,EAAEkD,cAAc,EAAE;IACxCnE,IAAI,CAACiB,SAAS,CAACkF,SAAS,EAAE,UAAUC,QAAQ,EAAE;MAC5C,IAAIxE,QAAQ,GAAGwE,QAAQ,CAAClC,gBAAgB;MACxCC,cAAc,CAACrC,IAAI,CAAC;QAClBkB,OAAO,EAAE,OAAO,GAAGoD,QAAQ,CAACX,EAAE;QAC9BW,QAAQ,EAAEA,QAAQ;QAClBvC,aAAa,EAAEuC,QAAQ;QACvBxE,QAAQ,EAAEA,QAAQ;QAClBe,UAAU,EAAE,CAACf,QAAQ,CAAC;QACtB2B,YAAY,EAAEwC,iBAAiB,CAACG;MAClC,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;AACF,CAAC;AACD,IAAI1B,kBAAkB,GAAG;AACzB;AACA,UAAUvD,SAAS,EAAEwB,UAAU,EAAE;EAC/B,IAAI4D,UAAU,GAAGpF,SAAS,CAACoF,UAAU;EACrC,IAAIC,UAAU,GAAGrF,SAAS,CAACqF,UAAU;EACrC,IAAIjB,SAAS,GAAGpE,SAAS,CAACoE,SAAS;EACnC,CAACA,SAAS,IAAIgB,UAAU,KAAKhB,SAAS,GAAGgB,UAAU,CAACf,IAAI,CAACT,IAAI,CAACU,KAAK,CAAC;EACpE,CAACF,SAAS,IAAIiB,UAAU,KAAKjB,SAAS,GAAGiB,UAAU,CAAChB,IAAI,CAACT,IAAI,CAACU,KAAK,CAAC;EACpE,OAAOF,SAAS,IAAIA,SAAS,KAAK5C,UAAU,CAAC4C,SAAS;AACxD,CAAC;AACD;AACA,UAAUpE,SAAS,EAAEwB,UAAU,EAAE;EAC/B,IAAI2D,QAAQ,GAAGnF,SAAS,CAACmF,QAAQ;EACjC,OAAOA,QAAQ,IAAIA,QAAQ,KAAK3D,UAAU,CAAC2D,QAAQ;AACrD,CAAC,CAAC;AACF,IAAIL,iBAAiB,GAAG;EACtBlB,IAAI,EAAE,SAAAA,CAAA,EAAY;IAChB;IACA,OAAO,IAAI,CAACjD,QAAQ,CAAC2E,MAAM,CAACC,OAAO,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;EAC/C,CAAC;EACDP,GAAG,EAAE,SAAAA,CAAA,EAAY;IACf,IAAItE,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC5B,IAAI0B,IAAI,GAAG1B,QAAQ,CAAC8E,eAAe,CAAC,CAAC,CAACD,KAAK,CAAC,CAAC;IAC7C;IACAnD,IAAI,CAACqD,cAAc,CAACrG,OAAO,CAACsG,YAAY,CAAChF,QAAQ,CAAC,CAAC;IACnD,OAAO0B,IAAI;EACb;AACF,CAAC;AACD,IAAItB,YAAY,GAAG;EACjB6E,KAAK,EAAE3G,KAAK,CAAC4G,WAAW,EAAE,CAAC,CAAC;EAC5BC,KAAK,EAAE7G,KAAK,CAAC4G,WAAW,EAAE,CAAC,CAAC;EAC5BxD,IAAI,EAAE,SAAAA,CAAU0D,EAAE,EAAEpF,QAAQ,EAAEqF,iBAAiB,EAAEC,KAAK,EAAE;IACtD,IAAIC,QAAQ,GAAGH,EAAE,GAAGpF,QAAQ,CAACwF,WAAW,CAAC,CAACH,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEC,KAAK,CAAC,GAAGtF,QAAQ,CAACyF,WAAW,CAAC,CAACJ,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEC,KAAK,CAAC;IACrL,IAAII,QAAQ,GAAGN,EAAE,GAAGpF,QAAQ,CAACwF,WAAW,CAAC,CAACH,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEC,KAAK,CAAC,GAAGtF,QAAQ,CAACyF,WAAW,CAAC,CAACJ,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEC,KAAK,CAAC;IACrL,IAAI7E,MAAM,GAAG,CAACoC,YAAY,CAAC,CAAC0C,QAAQ,CAAC,CAAC,CAAC,EAAEG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE7C,YAAY,CAAC,CAAC0C,QAAQ,CAAC,CAAC,CAAC,EAAEG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjG,OAAO;MACLjF,MAAM,EAAEA,MAAM;MACdE,QAAQ,EAAEF;IACZ,CAAC;EACH,CAAC;EACDkF,OAAO,EAAE,SAAAA,CAAUP,EAAE,EAAEpF,QAAQ,EAAEqF,iBAAiB,EAAEC,KAAK,EAAE;IACzD,IAAI3E,QAAQ,GAAG,CAAC,CAACiF,QAAQ,EAAE,CAACA,QAAQ,CAAC,EAAE,CAACA,QAAQ,EAAE,CAACA,QAAQ,CAAC,CAAC;IAC7D,IAAInF,MAAM,GAAGjC,GAAG,CAAC6G,iBAAiB,EAAE,UAAUQ,IAAI,EAAE;MAClD,IAAIC,CAAC,GAAGV,EAAE,GAAGpF,QAAQ,CAACwF,WAAW,CAACK,IAAI,EAAEP,KAAK,CAAC,GAAGtF,QAAQ,CAACyF,WAAW,CAACI,IAAI,EAAEP,KAAK,CAAC;MAClF3E,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGoF,IAAI,CAACC,GAAG,CAACrF,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEmF,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/CnF,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGoF,IAAI,CAACC,GAAG,CAACrF,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEmF,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/CnF,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGoF,IAAI,CAACE,GAAG,CAACtF,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEmF,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/CnF,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGoF,IAAI,CAACE,GAAG,CAACtF,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEmF,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/C,OAAOA,CAAC;IACV,CAAC,CAAC;IACF,OAAO;MACLrF,MAAM,EAAEA,MAAM;MACdE,QAAQ,EAAEA;IACZ,CAAC;EACH;AACF,CAAC;AACD,SAASuE,WAAWA,CAACgB,aAAa,EAAEd,EAAE,EAAEpF,QAAQ,EAAEqF,iBAAiB,EAAE;EACnE,IAAIpE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC5C,MAAM,CAACyB,QAAQ,CAACR,IAAI,KAAK,aAAa,EAAE,qDAAqD,CAAC;EAChG;EACA,IAAIkE,IAAI,GAAG1D,QAAQ,CAACkE,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAACgC,aAAa,CAAC,CAAC;EACtD,IAAIzF,MAAM,GAAGoC,YAAY,CAACrE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,UAAUgE,CAAC,EAAE;IACjD,OAAO4C,EAAE,GAAG1B,IAAI,CAACyC,WAAW,CAACzC,IAAI,CAAC0C,YAAY,CAACf,iBAAiB,CAAC7C,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAGkB,IAAI,CAAC2C,aAAa,CAAC3C,IAAI,CAAC4C,WAAW,CAACjB,iBAAiB,CAAC7C,CAAC,CAAC,CAAC,CAAC;EAC1I,CAAC,CAAC,CAAC;EACH,IAAI7B,QAAQ,GAAG,EAAE;EACjBA,QAAQ,CAACuF,aAAa,CAAC,GAAGzF,MAAM;EAChCE,QAAQ,CAAC,CAAC,GAAGuF,aAAa,CAAC,GAAG,CAACK,GAAG,EAAEA,GAAG,CAAC;EACxC,OAAO;IACL9F,MAAM,EAAEA,MAAM;IACdE,QAAQ,EAAEA;EACZ,CAAC;AACH;AACA,IAAIH,aAAa,GAAG;EAClByE,KAAK,EAAE3G,KAAK,CAACkI,iBAAiB,EAAE,CAAC,CAAC;EAClCrB,KAAK,EAAE7G,KAAK,CAACkI,iBAAiB,EAAE,CAAC,CAAC;EAClC9E,IAAI,EAAE,SAAAA,CAAUjB,MAAM,EAAEgG,KAAK,EAAEC,MAAM,EAAE;IACrC,OAAO,CAAC,CAACjG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGiG,MAAM,CAAC,CAAC,CAAC,GAAGD,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEhG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGiG,MAAM,CAAC,CAAC,CAAC,GAAGD,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAChG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGiG,MAAM,CAAC,CAAC,CAAC,GAAGD,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEhG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGiG,MAAM,CAAC,CAAC,CAAC,GAAGD,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7K,CAAC;EACDd,OAAO,EAAE,SAAAA,CAAUlF,MAAM,EAAEgG,KAAK,EAAEC,MAAM,EAAE;IACxC,OAAOlI,GAAG,CAACiC,MAAM,EAAE,UAAUoF,IAAI,EAAEc,GAAG,EAAE;MACtC,OAAO,CAACd,IAAI,CAAC,CAAC,CAAC,GAAGa,MAAM,CAAC,CAAC,CAAC,GAAGD,KAAK,CAACE,GAAG,CAAC,CAAC,CAAC,CAAC,EAAEd,IAAI,CAAC,CAAC,CAAC,GAAGa,MAAM,CAAC,CAAC,CAAC,GAAGD,KAAK,CAACE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACnF,CAAC,CAAC;EACJ;AACF,CAAC;AACD,SAASH,iBAAiBA,CAACN,aAAa,EAAEzF,MAAM,EAAEgG,KAAK,EAAEC,MAAM,EAAE;EAC/D,OAAO,CAACjG,MAAM,CAAC,CAAC,CAAC,GAAGiG,MAAM,CAACR,aAAa,CAAC,GAAGO,KAAK,CAAC,CAAC,CAAC,EAAEhG,MAAM,CAAC,CAAC,CAAC,GAAGiG,MAAM,CAACR,aAAa,CAAC,GAAGO,KAAK,CAAC,CAAC,CAAC,CAAC;AACrG;AACA;AACA;AACA;AACA,SAASnF,SAASA,CAACsF,YAAY,EAAEC,cAAc,EAAE;EAC/C,IAAIC,QAAQ,GAAGC,OAAO,CAACH,YAAY,CAAC;EACpC,IAAII,UAAU,GAAGD,OAAO,CAACF,cAAc,CAAC;EACxC,IAAIH,MAAM,GAAG,CAACI,QAAQ,CAAC,CAAC,CAAC,GAAGE,UAAU,CAAC,CAAC,CAAC,EAAEF,QAAQ,CAAC,CAAC,CAAC,GAAGE,UAAU,CAAC,CAAC,CAAC,CAAC;EACvEC,KAAK,CAACP,MAAM,CAAC,CAAC,CAAC,CAAC,KAAKA,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACnCO,KAAK,CAACP,MAAM,CAAC,CAAC,CAAC,CAAC,KAAKA,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACnC,OAAOA,MAAM;AACf;AACA,SAASK,OAAOA,CAACpG,QAAQ,EAAE;EACzB,OAAOA,QAAQ,GAAG,CAACA,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC4F,GAAG,EAAEA,GAAG,CAAC;AACnG;AACA,eAAexH,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}