#!/usr/bin/env python3
"""
测试优化功能的脚本
"""
import os
import sys
import json
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from ai_optimizer import optimize_resume_with_ai
    from openai import AzureOpenAI
    print("✅ 模块导入成功")
except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    sys.exit(1)

def test_optimization():
    """测试优化功能"""
    print("🔧 开始测试优化功能...")
    
    # 初始化客户端
    try:
        client = AzureOpenAI(
            api_key=os.getenv("AZURE_OPENAI_API_KEY"),
            api_version=os.getenv("AZURE_OPENAI_API_VERSION"),
            azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT")
        )
        print("✅ Azure OpenAI 客户端初始化成功")
    except Exception as e:
        print(f"❌ Azure OpenAI 客户端初始化失败: {e}")
        return False
    
    # 测试数据
    resume_text = """
    John Doe
    Software Engineer
    
    Experience:
    - Worked on web development projects
    - Used Python and JavaScript
    - Collaborated with team members
    
    Education:
    - Bachelor's degree in Computer Science
    
    Skills:
    - Programming languages: Python, JavaScript
    - Web frameworks: Flask, React
    """
    
    job_description = """
    We are looking for a Senior Software Engineer with experience in:
    - Python development
    - Flask framework
    - React.js
    - Agile methodology
    - Team leadership
    - Database design
    """
    
    print("📝 测试数据准备完成")
    print(f"简历长度: {len(resume_text)} 字符")
    print(f"职位描述长度: {len(job_description)} 字符")
    
    # 执行优化
    try:
        print("🚀 开始执行优化...")
        result = optimize_resume_with_ai(client, resume_text, job_description)
        
        print("📊 优化结果分析:")
        print(f"- optimized_resume_json 长度: {len(result.get('optimized_resume_json', []))}")
        print(f"- optimized_resume 长度: {len(result.get('optimized_resume', ''))}")
        print(f"- jd_json 存在: {'jd_json' in result}")
        print(f"- match_json 存在: {'match_json' in result}")
        
        if result.get('optimized_resume'):
            print("✅ 优化文本生成成功")
            print(f"优化后文本前200字符: {result['optimized_resume'][:200]}...")
        else:
            print("❌ 优化文本为空")
            
        if result.get('optimized_resume_json'):
            print("✅ 优化JSON生成成功")
            print(f"JSON结构: {json.dumps(result['optimized_resume_json'][:1], ensure_ascii=False, indent=2)}")
        else:
            print("❌ 优化JSON为空")
            
        if 'error' in result:
            print(f"⚠️ 错误信息: {result['error']}")
            
        return True
        
    except Exception as e:
        print(f"❌ 优化执行失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_fallback():
    """测试备用优化功能"""
    print("\n🔄 测试备用优化功能...")
    
    try:
        from ai_optimizer import create_fallback_optimization
        
        jd_json = {
            "keywords": {"Python": 5, "Flask": 4, "React": 4},
            "core_skills": ["Python", "Flask", "React", "JavaScript"],
            "soft_skills": ["Leadership", "Communication"]
        }
        
        match_json = {
            "missing_keywords": ["Agile", "Database", "Leadership"]
        }
        
        resume_text = "Simple resume text for testing"
        job_description = "Job description for testing"
        
        result = create_fallback_optimization(resume_text, job_description, jd_json, match_json)
        
        print(f"✅ 备用优化成功，长度: {len(result)}")
        print(f"前200字符: {result[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 备用优化失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 开始测试优化系统...")
    print("=" * 50)
    
    # 检查环境变量
    required_vars = [
        "AZURE_OPENAI_API_KEY",
        "AZURE_OPENAI_ENDPOINT", 
        "AZURE_OPENAI_API_VERSION",
        "AZURE_OPENAI_GPT4_DEPLOYMENT"
    ]
    
    print("🔍 检查环境变量:")
    for var in required_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: {'*' * 10}...{value[-10:] if len(value) > 10 else value}")
        else:
            print(f"❌ {var}: 未设置")
    
    print("\n" + "=" * 50)
    
    # 测试备用功能
    fallback_success = test_fallback()
    
    # 测试主要功能
    main_success = test_optimization()
    
    print("\n" + "=" * 50)
    print("📋 测试总结:")
    print(f"备用优化: {'✅ 成功' if fallback_success else '❌ 失败'}")
    print(f"主要优化: {'✅ 成功' if main_success else '❌ 失败'}")
    
    if main_success and fallback_success:
        print("🎉 所有测试通过！优化系统工作正常。")
    else:
        print("⚠️ 部分测试失败，请检查配置和代码。")
