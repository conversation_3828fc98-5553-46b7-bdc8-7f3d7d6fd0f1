import React, { useRef, useState, useEffect } from 'react';

const FileUpload = ({ file, onFileSelect, accept, placeholder }) => {
  const fileInputRef = useRef(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const [isUploaded, setIsUploaded] = useState(false);
  const [imagePreview, setImagePreview] = useState(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadStatus, setUploadStatus] = useState('idle'); // 'idle', 'uploading', 'success', 'error'

  useEffect(() => {
    if (file && isImageFile(file)) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target.result);
      };
      reader.readAsDataURL(file);
    } else {
      setImagePreview(null);
    }
  }, [file]);

  const isImageFile = (file) => {
    return file && file.type.startsWith('image/');
  };

  const handleFileSelect = (selectedFile) => {
    console.log('handleFileSelect called with:', selectedFile);
    
    if (selectedFile && isValidFile(selectedFile)) {
      console.log('File is valid, calling onFileSelect');
      
      // 模拟上传进度
      setUploadStatus('uploading');
      setUploadProgress(0);
      
      // 进度条动画
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 100) {
            clearInterval(progressInterval);
            setUploadStatus('success');
            setIsUploaded(true);
            
            // 重置状态
            setTimeout(() => {
              setIsUploaded(false);
              setUploadProgress(0);
              setUploadStatus('idle');
            }, 3000);
            
            return 100;
          }
          return prev + Math.random() * 15 + 5; // 随机增加5-20%
        });
      }, 100);
      
      onFileSelect(selectedFile);
      
    } else {
      setUploadStatus('error');
      setTimeout(() => setUploadStatus('idle'), 2000);
      
      if (!selectedFile) {
        alert('No file selected');
      } else if (selectedFile.size > 16 * 1024 * 1024) {
        alert('File is too large. Please select a file smaller than 16MB.');
      } else {
        alert('Please select a valid file format (PDF, Word document, or image file)');
      }
    }
  };

  const isValidFile = (file) => {
    // 支持多种文件格式的验证逻辑
    if (!file) return false;
    
    // 获取文件扩展名
    const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
    const allowedExtensions = accept.split(',').map(ext => ext.trim().toLowerCase());
    
    // 检查文件扩展名
    const isValidExtension = allowedExtensions.includes(fileExtension);
    
    // 检查MIME类型
    const isValidMimeType = 
      file.type.startsWith('image/') || 
      file.type === 'application/pdf' ||
      file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
      file.type === 'application/msword';
    
    // 检查文件大小 (16MB)
    const maxSize = 16 * 1024 * 1024;
    if (file.size > maxSize) {
      console.log('File size check failed:', file.size, 'max:', maxSize);
      return false;
    }
    
    const isValid = isValidExtension && isValidMimeType;
    console.log('File validation:', {
      name: file.name,
      type: file.type,
      extension: fileExtension,
      isValidExtension,
      isValidMimeType,
      isValid
    });
    
    return isValid;
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragOver(false);
    const droppedFiles = Array.from(e.dataTransfer.files);
    if (droppedFiles.length > 0) {
      handleFileSelect(droppedFiles[0]);
    }
  };

  const handleClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e) => {
    const selectedFile = e.target.files[0];
    if (selectedFile) {
      handleFileSelect(selectedFile);
    }
  };

  const removeFile = () => {
    onFileSelect(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (file) => {
    if (!file) return null;
    
    const fileType = file.type.toLowerCase();
    const fileName = file.name.toLowerCase();
    
    if (fileType.startsWith('image/')) {
      return (
        <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      );
    } else if (fileType === 'application/pdf') {
      return (
        <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
        </svg>
      );
    } else if (fileType.includes('word') || fileName.endsWith('.docx') || fileName.endsWith('.doc')) {
      return (
        <svg className="w-5 h-5 text-blue-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      );
    } else {
      return (
        <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      );
    }
  };

  return (
    <div className="space-y-4">
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        onChange={handleFileChange}
        className="hidden"
      />
      
      {!file ? (
        <div
          className={`relative border-2 border-dashed rounded-lg p-6 text-center transition-all duration-300 cursor-pointer min-h-[240px] flex items-center justify-center ${
            isDragOver 
              ? 'border-red-400 bg-red-50 transform scale-105' 
              : 'border-gray-300 hover:border-orange-400 hover:bg-orange-50'
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={handleClick}
        >
          <div className="space-y-3">
            <div className={`mx-auto w-12 h-12 rounded-full flex items-center justify-center transition-colors duration-300 ${
              isDragOver ? 'bg-red-100' : 'bg-gray-100'
            }`}>
              <svg 
                className={`w-6 h-6 transition-colors duration-300 ${
                  isDragOver ? 'text-red-500' : 'text-gray-400'
                }`} 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
              </svg>
            </div>
            <div>
              <p className="text-base font-semibold text-gray-900 mb-1">
                Click or drag file here
              </p>
              <p className="text-xs text-gray-600 mb-1">{placeholder}</p>
              <p className="text-xs text-gray-500">Maximum file size: 16MB</p>
            </div>
          </div>
          
          {/* Visual enhancement for drag state */}
          {isDragOver && (
            <div className="absolute inset-0 rounded-lg bg-red-100/20 border-2 border-red-400 border-dashed flex items-center justify-center">
              <div className="text-red-600 font-semibold text-sm">Drop file here</div>
            </div>
          )}
        </div>
      ) : (
        <div className={`bg-white border rounded-lg shadow-sm transition-all duration-500 ${
          isUploaded ? 'border-green-400 bg-green-50 scale-105' : 'border-gray-200'
        }`}>
          {/* Image Preview */}
          {imagePreview && (
            <div className="p-4 border-b border-gray-200">
              <div className="relative w-full h-48 bg-gray-100 rounded-lg overflow-hidden">
                <img 
                  src={imagePreview} 
                  alt="Resume preview" 
                  className="w-full h-full object-contain"
                />
                <div className="absolute top-2 right-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                  Preview
                </div>
              </div>
            </div>
          )}
          
          {/* File Info */}
          <div className="p-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className={`w-10 h-10 rounded-lg flex items-center justify-center transition-all duration-500 ${
                  isUploaded 
                    ? 'bg-green-100' 
                    : 'bg-blue-100'
                }`}>
                  {isUploaded ? (
                    <svg className="w-5 h-5 text-green-600 animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  ) : (
                    getFileIcon(file)
                  )}
                </div>
                
                {/* 文件详细信息 */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-1">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {file.name}
                    </p>
                    {uploadStatus === 'uploading' && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        <svg className="w-3 h-3 mr-1 animate-spin" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Uploading {Math.round(uploadProgress)}%
                      </span>
                    )}
                    {uploadStatus === 'success' && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        Upload Complete
                      </span>
                    )}
                    {uploadStatus === 'error' && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                        Upload Failed
                      </span>
                    )}
                  </div>
                  
                  {/* 上传进度条 */}
                  {uploadStatus === 'uploading' && (
                    <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
                        style={{ width: `${uploadProgress}%` }}
                      ></div>
                    </div>
                  )}
                  
                  <div className="flex items-center space-x-4 text-xs text-gray-500">
                    <span className="flex items-center">
                      <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19V6l6-6" />
                      </svg>
                      {formatFileSize(file.size)}
                    </span>
                    
                    <span className="flex items-center">
                      <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                      </svg>
                      {file.name.split('.').pop()?.toUpperCase() || 'FILE'}
                    </span>
                    
                    <span className="flex items-center text-gray-400">
                      <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      {new Date().toLocaleTimeString()}
                    </span>
                  </div>
                  
                  {/* 文件类型和状态指示器 */}
                  <div className="flex items-center space-x-2 mt-2">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      file.type.startsWith('image/') ? 'bg-purple-100 text-purple-800' :
                      file.type === 'application/pdf' ? 'bg-red-100 text-red-800' :
                      file.type.includes('word') ? 'bg-blue-100 text-blue-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {file.type.startsWith('image/') ? '🖼️ Image' :
                       file.type === 'application/pdf' ? '📄 PDF' :
                       file.type.includes('word') ? '📝 Word' :
                       '📄 Document'}
                    </span>
                    
                    {file.size > 1024 * 1024 && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        📊 Large File
                      </span>
                    )}
                    
                    {uploadStatus === 'success' && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        🔒 Verified
                      </span>
                    )}
                    
                    {file.size < 50 * 1024 && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                        ⚠️ Small File
                      </span>
                    )}
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                {uploadStatus === 'error' && (
                  <button
                    onClick={() => handleFileSelect(file)}
                    className="p-1.5 text-blue-500 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors duration-200"
                    title="Retry upload"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                  </button>
                )}
                
                <button
                  onClick={removeFile}
                  className="p-1.5 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors duration-200"
                  title="Remove file"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FileUpload; 