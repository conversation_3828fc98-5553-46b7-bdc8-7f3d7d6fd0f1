import React, { useState } from 'react';
import FileUpload from './FileUpload';

const JobDescriptionInput = ({ textValue, onTextChange, file, onFileChange }) => {
  const [inputMode, setInputMode] = useState('text'); // 'text' or 'file'

  const handleModeChange = (mode) => {
    setInputMode(mode);
    if (mode === 'text') {
      onFileChange(null);
    } else {
      onTextChange('');
    }
  };

  return (
    <div className="space-y-4">
      {/* Mode Selector */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
        <button
          onClick={() => handleModeChange('text')}
          className={`flex-1 py-2 px-3 rounded-md text-xs font-semibold transition-all duration-200 ${
            inputMode === 'text'
              ? 'bg-white text-gray-900 shadow-sm transform scale-[0.98]'
              : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
          }`}
        >
          <span className="flex items-center justify-center space-x-1">
            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            <span>Text Input</span>
          </span>
        </button>
        <button
          onClick={() => handleModeChange('file')}
          className={`flex-1 py-2 px-3 rounded-md text-xs font-semibold transition-all duration-200 ${
            inputMode === 'file'
              ? 'bg-white text-gray-900 shadow-sm transform scale-[0.98]'
              : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
          }`}
        >
          <span className="flex items-center justify-center space-x-1">
            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
            </svg>
            <span>File Upload</span>
          </span>
        </button>
      </div>

      {/* Input Area */}
      {inputMode === 'text' ? (
        <div className="space-y-2">
          <textarea
            value={textValue}
            onChange={(e) => onTextChange(e.target.value)}
            placeholder="Paste the job description here, including requirements, skills, responsibilities, company culture, etc..."
            className="w-full min-h-[180px] p-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-y transition-all duration-200 text-sm leading-relaxed"
            rows={6}
          />
          <div className="flex justify-between items-center text-xs">
            <span className="text-gray-500">
              {textValue.length > 0 && `${textValue.length} characters`}
            </span>
          </div>
        </div>
      ) : (
        <div className="space-y-2">
          <FileUpload
            file={file}
            onFileSelect={onFileChange}
            accept=".pdf,.docx,.txt"
            placeholder="Supports PDF, DOCX and TXT formats"
          />
          <p className="text-xs text-gray-600 text-center">
            Upload a file containing the job description. We'll automatically extract the text content.
          </p>
        </div>
      )}
    </div>
  );
};

export default JobDescriptionInput; 