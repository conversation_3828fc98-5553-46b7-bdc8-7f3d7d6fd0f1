#!/usr/bin/env python3
"""
简化的测试服务器，专门用于测试AI优化功能
"""
import os
import sys
import tempfile
import logging
from flask import Flask, request, jsonify
from flask_cors import CORS
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入AI优化模块
try:
    from ai_optimizer import optimize_resume_with_ai
    from openai import AzureOpenAI
    logger.info("✅ AI优化模块导入成功")
except ImportError as e:
    logger.error(f"❌ AI优化模块导入失败: {e}")
    sys.exit(1)

# 初始化Flask应用
app = Flask(__name__)
CORS(app)

# 初始化Azure OpenAI客户端
try:
    client = AzureOpenAI(
        api_key=os.getenv("AZURE_OPENAI_API_KEY"),
        api_version="2024-02-15-preview",
        azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT")
    )
    logger.info("✅ Azure OpenAI客户端初始化成功")
except Exception as e:
    logger.error(f"❌ Azure OpenAI客户端初始化失败: {e}")
    client = None

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查"""
    return jsonify({
        "status": "healthy",
        "message": "Test server is running",
        "ai_client": "connected" if client else "disconnected"
    })

@app.route('/api/test-optimize', methods=['POST'])
def test_optimize():
    """测试优化接口 - 接受JSON数据"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({"success": False, "message": "No JSON data provided"}), 400

        resume_text = data.get('resume_text', '')
        job_description = data.get('job_description', '')

        if not resume_text.strip():
            return jsonify({"success": False, "message": "Resume text is required"}), 400

        if not job_description.strip():
            return jsonify({"success": False, "message": "Job description is required"}), 400

        logger.info(f"🔄 开始测试优化...")
        logger.info(f"📝 简历长度: {len(resume_text)} 字符")
        logger.info(f"📋 JD长度: {len(job_description)} 字符")

        # 调用AI优化
        if client:
            optimization_result = optimize_resume_with_ai(client, resume_text, job_description)
        else:
            return jsonify({"success": False, "message": "AI client not available"}), 500

        # 处理结果
        if isinstance(optimization_result, dict):
            optimized_text = optimization_result.get('optimized_resume', '')

            response_data = {
                "success": True,
                "optimized_resume": optimized_text,
                "optimized_resume_json": optimization_result.get('optimized_resume_json', []),
                "jd_json": optimization_result.get('jd_json', {}),
                "match_json": optimization_result.get('match_json', {}),
                "original_resume": resume_text,
                "original_length": len(resume_text),
                "optimized_length": len(optimized_text),
                "message": "Optimization completed successfully"
            }

            logger.info(f"✅ 优化完成，原始长度: {len(resume_text)}, 优化后长度: {len(optimized_text)}")
            return jsonify(response_data)
        else:
            return jsonify({"success": False, "message": "Invalid optimization result"}), 500

    except Exception as e:
        logger.error(f"❌ 优化过程出错: {str(e)}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return jsonify({"success": False, "message": f"Optimization failed: {str(e)}"}), 500

if __name__ == '__main__':
    logger.info("🚀 启动测试服务器...")
    logger.info(f"🔧 环境变量检查:")
    logger.info(f"   API Key: {'✅ 已设置' if os.getenv('AZURE_OPENAI_API_KEY') else '❌ 未设置'}")
    logger.info(f"   Endpoint: {os.getenv('AZURE_OPENAI_ENDPOINT')}")
    logger.info(f"   Deployment: {os.getenv('AZURE_OPENAI_GPT4_DEPLOYMENT')}")

    app.run(host='127.0.0.1', port=5001, debug=True)
