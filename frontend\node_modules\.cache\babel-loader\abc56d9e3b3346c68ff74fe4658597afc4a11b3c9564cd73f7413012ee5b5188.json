{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/*\r\n* A third-party license is embedded for some of the code in this file:\r\n* Some formulas were originally copied from \"d3.js\" with some\r\n* modifications made for this project.\r\n* (See more details in the comment of the method \"step\" below.)\r\n* The use of the source code of this file is also subject to the terms\r\n* and consitions of the license of \"d3.js\" (BSD-3Clause, see\r\n* </licenses/LICENSE-d3>).\r\n*/\nimport * as vec2 from 'zrender/lib/core/vector.js';\nvar scaleAndAdd = vec2.scaleAndAdd;\n// function adjacentNode(n, e) {\n//     return e.n1 === n ? e.n2 : e.n1;\n// }\nexport function forceLayout(inNodes, inEdges, opts) {\n  var nodes = inNodes;\n  var edges = inEdges;\n  var rect = opts.rect;\n  var width = rect.width;\n  var height = rect.height;\n  var center = [rect.x + width / 2, rect.y + height / 2];\n  // let scale = opts.scale || 1;\n  var gravity = opts.gravity == null ? 0.1 : opts.gravity;\n  // for (let i = 0; i < edges.length; i++) {\n  //     let e = edges[i];\n  //     let n1 = e.n1;\n  //     let n2 = e.n2;\n  //     n1.edges = n1.edges || [];\n  //     n2.edges = n2.edges || [];\n  //     n1.edges.push(e);\n  //     n2.edges.push(e);\n  // }\n  // Init position\n  for (var i = 0; i < nodes.length; i++) {\n    var n = nodes[i];\n    if (!n.p) {\n      n.p = vec2.create(width * (Math.random() - 0.5) + center[0], height * (Math.random() - 0.5) + center[1]);\n    }\n    n.pp = vec2.clone(n.p);\n    n.edges = null;\n  }\n  // Formula in 'Graph Drawing by Force-directed Placement'\n  // let k = scale * Math.sqrt(width * height / nodes.length);\n  // let k2 = k * k;\n  var initialFriction = opts.friction == null ? 0.6 : opts.friction;\n  var friction = initialFriction;\n  var beforeStepCallback;\n  var afterStepCallback;\n  return {\n    warmUp: function () {\n      friction = initialFriction * 0.8;\n    },\n    setFixed: function (idx) {\n      nodes[idx].fixed = true;\n    },\n    setUnfixed: function (idx) {\n      nodes[idx].fixed = false;\n    },\n    /**\r\n     * Before step hook\r\n     */\n    beforeStep: function (cb) {\n      beforeStepCallback = cb;\n    },\n    /**\r\n     * After step hook\r\n     */\n    afterStep: function (cb) {\n      afterStepCallback = cb;\n    },\n    /**\r\n     * Some formulas were originally copied from \"d3.js\"\r\n     * https://github.com/d3/d3/blob/b516d77fb8566b576088e73410437494717ada26/src/layout/force.js\r\n     * with some modifications made for this project.\r\n     * See the license statement at the head of this file.\r\n     */\n    step: function (cb) {\n      beforeStepCallback && beforeStepCallback(nodes, edges);\n      var v12 = [];\n      var nLen = nodes.length;\n      for (var i = 0; i < edges.length; i++) {\n        var e = edges[i];\n        if (e.ignoreForceLayout) {\n          continue;\n        }\n        var n1 = e.n1;\n        var n2 = e.n2;\n        vec2.sub(v12, n2.p, n1.p);\n        var d = vec2.len(v12) - e.d;\n        var w = n2.w / (n1.w + n2.w);\n        if (isNaN(w)) {\n          w = 0;\n        }\n        vec2.normalize(v12, v12);\n        !n1.fixed && scaleAndAdd(n1.p, n1.p, v12, w * d * friction);\n        !n2.fixed && scaleAndAdd(n2.p, n2.p, v12, -(1 - w) * d * friction);\n      }\n      // Gravity\n      for (var i = 0; i < nLen; i++) {\n        var n = nodes[i];\n        if (!n.fixed) {\n          vec2.sub(v12, center, n.p);\n          // let d = vec2.len(v12);\n          // vec2.scale(v12, v12, 1 / d);\n          // let gravityFactor = gravity;\n          scaleAndAdd(n.p, n.p, v12, gravity * friction);\n        }\n      }\n      // Repulsive\n      // PENDING\n      for (var i = 0; i < nLen; i++) {\n        var n1 = nodes[i];\n        for (var j = i + 1; j < nLen; j++) {\n          var n2 = nodes[j];\n          vec2.sub(v12, n2.p, n1.p);\n          var d = vec2.len(v12);\n          if (d === 0) {\n            // Random repulse\n            vec2.set(v12, Math.random() - 0.5, Math.random() - 0.5);\n            d = 1;\n          }\n          var repFact = (n1.rep + n2.rep) / d / d;\n          !n1.fixed && scaleAndAdd(n1.pp, n1.pp, v12, repFact);\n          !n2.fixed && scaleAndAdd(n2.pp, n2.pp, v12, -repFact);\n        }\n      }\n      var v = [];\n      for (var i = 0; i < nLen; i++) {\n        var n = nodes[i];\n        if (!n.fixed) {\n          vec2.sub(v, n.p, n.pp);\n          scaleAndAdd(n.p, n.p, v, friction);\n          vec2.copy(n.pp, n.p);\n        }\n      }\n      friction = friction * 0.992;\n      var finished = friction < 0.01;\n      afterStepCallback && afterStepCallback(nodes, edges, finished);\n      cb && cb(finished);\n    }\n  };\n}", "map": {"version": 3, "names": ["vec2", "scaleAndAdd", "forceLayout", "inNodes", "inEdges", "opts", "nodes", "edges", "rect", "width", "height", "center", "x", "y", "gravity", "i", "length", "n", "p", "create", "Math", "random", "pp", "clone", "initialFriction", "friction", "beforeStepCallback", "afterStepCallback", "warmUp", "setFixed", "idx", "fixed", "setUnfixed", "beforeStep", "cb", "afterStep", "step", "v12", "nLen", "e", "ignoreForceLayout", "n1", "n2", "sub", "d", "len", "w", "isNaN", "normalize", "j", "set", "repFact", "rep", "v", "copy", "finished"], "sources": ["E:/AI/SmartCV/node_modules/echarts/lib/chart/graph/forceHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/*\r\n* A third-party license is embedded for some of the code in this file:\r\n* Some formulas were originally copied from \"d3.js\" with some\r\n* modifications made for this project.\r\n* (See more details in the comment of the method \"step\" below.)\r\n* The use of the source code of this file is also subject to the terms\r\n* and consitions of the license of \"d3.js\" (BSD-3Clause, see\r\n* </licenses/LICENSE-d3>).\r\n*/\nimport * as vec2 from 'zrender/lib/core/vector.js';\nvar scaleAndAdd = vec2.scaleAndAdd;\n// function adjacentNode(n, e) {\n//     return e.n1 === n ? e.n2 : e.n1;\n// }\nexport function forceLayout(inNodes, inEdges, opts) {\n  var nodes = inNodes;\n  var edges = inEdges;\n  var rect = opts.rect;\n  var width = rect.width;\n  var height = rect.height;\n  var center = [rect.x + width / 2, rect.y + height / 2];\n  // let scale = opts.scale || 1;\n  var gravity = opts.gravity == null ? 0.1 : opts.gravity;\n  // for (let i = 0; i < edges.length; i++) {\n  //     let e = edges[i];\n  //     let n1 = e.n1;\n  //     let n2 = e.n2;\n  //     n1.edges = n1.edges || [];\n  //     n2.edges = n2.edges || [];\n  //     n1.edges.push(e);\n  //     n2.edges.push(e);\n  // }\n  // Init position\n  for (var i = 0; i < nodes.length; i++) {\n    var n = nodes[i];\n    if (!n.p) {\n      n.p = vec2.create(width * (Math.random() - 0.5) + center[0], height * (Math.random() - 0.5) + center[1]);\n    }\n    n.pp = vec2.clone(n.p);\n    n.edges = null;\n  }\n  // Formula in 'Graph Drawing by Force-directed Placement'\n  // let k = scale * Math.sqrt(width * height / nodes.length);\n  // let k2 = k * k;\n  var initialFriction = opts.friction == null ? 0.6 : opts.friction;\n  var friction = initialFriction;\n  var beforeStepCallback;\n  var afterStepCallback;\n  return {\n    warmUp: function () {\n      friction = initialFriction * 0.8;\n    },\n    setFixed: function (idx) {\n      nodes[idx].fixed = true;\n    },\n    setUnfixed: function (idx) {\n      nodes[idx].fixed = false;\n    },\n    /**\r\n     * Before step hook\r\n     */\n    beforeStep: function (cb) {\n      beforeStepCallback = cb;\n    },\n    /**\r\n     * After step hook\r\n     */\n    afterStep: function (cb) {\n      afterStepCallback = cb;\n    },\n    /**\r\n     * Some formulas were originally copied from \"d3.js\"\r\n     * https://github.com/d3/d3/blob/b516d77fb8566b576088e73410437494717ada26/src/layout/force.js\r\n     * with some modifications made for this project.\r\n     * See the license statement at the head of this file.\r\n     */\n    step: function (cb) {\n      beforeStepCallback && beforeStepCallback(nodes, edges);\n      var v12 = [];\n      var nLen = nodes.length;\n      for (var i = 0; i < edges.length; i++) {\n        var e = edges[i];\n        if (e.ignoreForceLayout) {\n          continue;\n        }\n        var n1 = e.n1;\n        var n2 = e.n2;\n        vec2.sub(v12, n2.p, n1.p);\n        var d = vec2.len(v12) - e.d;\n        var w = n2.w / (n1.w + n2.w);\n        if (isNaN(w)) {\n          w = 0;\n        }\n        vec2.normalize(v12, v12);\n        !n1.fixed && scaleAndAdd(n1.p, n1.p, v12, w * d * friction);\n        !n2.fixed && scaleAndAdd(n2.p, n2.p, v12, -(1 - w) * d * friction);\n      }\n      // Gravity\n      for (var i = 0; i < nLen; i++) {\n        var n = nodes[i];\n        if (!n.fixed) {\n          vec2.sub(v12, center, n.p);\n          // let d = vec2.len(v12);\n          // vec2.scale(v12, v12, 1 / d);\n          // let gravityFactor = gravity;\n          scaleAndAdd(n.p, n.p, v12, gravity * friction);\n        }\n      }\n      // Repulsive\n      // PENDING\n      for (var i = 0; i < nLen; i++) {\n        var n1 = nodes[i];\n        for (var j = i + 1; j < nLen; j++) {\n          var n2 = nodes[j];\n          vec2.sub(v12, n2.p, n1.p);\n          var d = vec2.len(v12);\n          if (d === 0) {\n            // Random repulse\n            vec2.set(v12, Math.random() - 0.5, Math.random() - 0.5);\n            d = 1;\n          }\n          var repFact = (n1.rep + n2.rep) / d / d;\n          !n1.fixed && scaleAndAdd(n1.pp, n1.pp, v12, repFact);\n          !n2.fixed && scaleAndAdd(n2.pp, n2.pp, v12, -repFact);\n        }\n      }\n      var v = [];\n      for (var i = 0; i < nLen; i++) {\n        var n = nodes[i];\n        if (!n.fixed) {\n          vec2.sub(v, n.p, n.pp);\n          scaleAndAdd(n.p, n.p, v, friction);\n          vec2.copy(n.pp, n.p);\n        }\n      }\n      friction = friction * 0.992;\n      var finished = friction < 0.01;\n      afterStepCallback && afterStepCallback(nodes, edges, finished);\n      cb && cb(finished);\n    }\n  };\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,IAAI,MAAM,4BAA4B;AAClD,IAAIC,WAAW,GAAGD,IAAI,CAACC,WAAW;AAClC;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,OAAO,EAAEC,OAAO,EAAEC,IAAI,EAAE;EAClD,IAAIC,KAAK,GAAGH,OAAO;EACnB,IAAII,KAAK,GAAGH,OAAO;EACnB,IAAII,IAAI,GAAGH,IAAI,CAACG,IAAI;EACpB,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;EACtB,IAAIC,MAAM,GAAGF,IAAI,CAACE,MAAM;EACxB,IAAIC,MAAM,GAAG,CAACH,IAAI,CAACI,CAAC,GAAGH,KAAK,GAAG,CAAC,EAAED,IAAI,CAACK,CAAC,GAAGH,MAAM,GAAG,CAAC,CAAC;EACtD;EACA,IAAII,OAAO,GAAGT,IAAI,CAACS,OAAO,IAAI,IAAI,GAAG,GAAG,GAAGT,IAAI,CAACS,OAAO;EACvD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,KAAK,CAACU,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,IAAIE,CAAC,GAAGX,KAAK,CAACS,CAAC,CAAC;IAChB,IAAI,CAACE,CAAC,CAACC,CAAC,EAAE;MACRD,CAAC,CAACC,CAAC,GAAGlB,IAAI,CAACmB,MAAM,CAACV,KAAK,IAAIW,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,GAAGV,MAAM,CAAC,CAAC,CAAC,EAAED,MAAM,IAAIU,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,GAAGV,MAAM,CAAC,CAAC,CAAC,CAAC;IAC1G;IACAM,CAAC,CAACK,EAAE,GAAGtB,IAAI,CAACuB,KAAK,CAACN,CAAC,CAACC,CAAC,CAAC;IACtBD,CAAC,CAACV,KAAK,GAAG,IAAI;EAChB;EACA;EACA;EACA;EACA,IAAIiB,eAAe,GAAGnB,IAAI,CAACoB,QAAQ,IAAI,IAAI,GAAG,GAAG,GAAGpB,IAAI,CAACoB,QAAQ;EACjE,IAAIA,QAAQ,GAAGD,eAAe;EAC9B,IAAIE,kBAAkB;EACtB,IAAIC,iBAAiB;EACrB,OAAO;IACLC,MAAM,EAAE,SAAAA,CAAA,EAAY;MAClBH,QAAQ,GAAGD,eAAe,GAAG,GAAG;IAClC,CAAC;IACDK,QAAQ,EAAE,SAAAA,CAAUC,GAAG,EAAE;MACvBxB,KAAK,CAACwB,GAAG,CAAC,CAACC,KAAK,GAAG,IAAI;IACzB,CAAC;IACDC,UAAU,EAAE,SAAAA,CAAUF,GAAG,EAAE;MACzBxB,KAAK,CAACwB,GAAG,CAAC,CAACC,KAAK,GAAG,KAAK;IAC1B,CAAC;IACD;AACJ;AACA;IACIE,UAAU,EAAE,SAAAA,CAAUC,EAAE,EAAE;MACxBR,kBAAkB,GAAGQ,EAAE;IACzB,CAAC;IACD;AACJ;AACA;IACIC,SAAS,EAAE,SAAAA,CAAUD,EAAE,EAAE;MACvBP,iBAAiB,GAAGO,EAAE;IACxB,CAAC;IACD;AACJ;AACA;AACA;AACA;AACA;IACIE,IAAI,EAAE,SAAAA,CAAUF,EAAE,EAAE;MAClBR,kBAAkB,IAAIA,kBAAkB,CAACpB,KAAK,EAAEC,KAAK,CAAC;MACtD,IAAI8B,GAAG,GAAG,EAAE;MACZ,IAAIC,IAAI,GAAGhC,KAAK,CAACU,MAAM;MACvB,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,KAAK,CAACS,MAAM,EAAED,CAAC,EAAE,EAAE;QACrC,IAAIwB,CAAC,GAAGhC,KAAK,CAACQ,CAAC,CAAC;QAChB,IAAIwB,CAAC,CAACC,iBAAiB,EAAE;UACvB;QACF;QACA,IAAIC,EAAE,GAAGF,CAAC,CAACE,EAAE;QACb,IAAIC,EAAE,GAAGH,CAAC,CAACG,EAAE;QACb1C,IAAI,CAAC2C,GAAG,CAACN,GAAG,EAAEK,EAAE,CAACxB,CAAC,EAAEuB,EAAE,CAACvB,CAAC,CAAC;QACzB,IAAI0B,CAAC,GAAG5C,IAAI,CAAC6C,GAAG,CAACR,GAAG,CAAC,GAAGE,CAAC,CAACK,CAAC;QAC3B,IAAIE,CAAC,GAAGJ,EAAE,CAACI,CAAC,IAAIL,EAAE,CAACK,CAAC,GAAGJ,EAAE,CAACI,CAAC,CAAC;QAC5B,IAAIC,KAAK,CAACD,CAAC,CAAC,EAAE;UACZA,CAAC,GAAG,CAAC;QACP;QACA9C,IAAI,CAACgD,SAAS,CAACX,GAAG,EAAEA,GAAG,CAAC;QACxB,CAACI,EAAE,CAACV,KAAK,IAAI9B,WAAW,CAACwC,EAAE,CAACvB,CAAC,EAAEuB,EAAE,CAACvB,CAAC,EAAEmB,GAAG,EAAES,CAAC,GAAGF,CAAC,GAAGnB,QAAQ,CAAC;QAC3D,CAACiB,EAAE,CAACX,KAAK,IAAI9B,WAAW,CAACyC,EAAE,CAACxB,CAAC,EAAEwB,EAAE,CAACxB,CAAC,EAAEmB,GAAG,EAAE,EAAE,CAAC,GAAGS,CAAC,CAAC,GAAGF,CAAC,GAAGnB,QAAQ,CAAC;MACpE;MACA;MACA,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuB,IAAI,EAAEvB,CAAC,EAAE,EAAE;QAC7B,IAAIE,CAAC,GAAGX,KAAK,CAACS,CAAC,CAAC;QAChB,IAAI,CAACE,CAAC,CAACc,KAAK,EAAE;UACZ/B,IAAI,CAAC2C,GAAG,CAACN,GAAG,EAAE1B,MAAM,EAAEM,CAAC,CAACC,CAAC,CAAC;UAC1B;UACA;UACA;UACAjB,WAAW,CAACgB,CAAC,CAACC,CAAC,EAAED,CAAC,CAACC,CAAC,EAAEmB,GAAG,EAAEvB,OAAO,GAAGW,QAAQ,CAAC;QAChD;MACF;MACA;MACA;MACA,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuB,IAAI,EAAEvB,CAAC,EAAE,EAAE;QAC7B,IAAI0B,EAAE,GAAGnC,KAAK,CAACS,CAAC,CAAC;QACjB,KAAK,IAAIkC,CAAC,GAAGlC,CAAC,GAAG,CAAC,EAAEkC,CAAC,GAAGX,IAAI,EAAEW,CAAC,EAAE,EAAE;UACjC,IAAIP,EAAE,GAAGpC,KAAK,CAAC2C,CAAC,CAAC;UACjBjD,IAAI,CAAC2C,GAAG,CAACN,GAAG,EAAEK,EAAE,CAACxB,CAAC,EAAEuB,EAAE,CAACvB,CAAC,CAAC;UACzB,IAAI0B,CAAC,GAAG5C,IAAI,CAAC6C,GAAG,CAACR,GAAG,CAAC;UACrB,IAAIO,CAAC,KAAK,CAAC,EAAE;YACX;YACA5C,IAAI,CAACkD,GAAG,CAACb,GAAG,EAAEjB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,EAAED,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC;YACvDuB,CAAC,GAAG,CAAC;UACP;UACA,IAAIO,OAAO,GAAG,CAACV,EAAE,CAACW,GAAG,GAAGV,EAAE,CAACU,GAAG,IAAIR,CAAC,GAAGA,CAAC;UACvC,CAACH,EAAE,CAACV,KAAK,IAAI9B,WAAW,CAACwC,EAAE,CAACnB,EAAE,EAAEmB,EAAE,CAACnB,EAAE,EAAEe,GAAG,EAAEc,OAAO,CAAC;UACpD,CAACT,EAAE,CAACX,KAAK,IAAI9B,WAAW,CAACyC,EAAE,CAACpB,EAAE,EAAEoB,EAAE,CAACpB,EAAE,EAAEe,GAAG,EAAE,CAACc,OAAO,CAAC;QACvD;MACF;MACA,IAAIE,CAAC,GAAG,EAAE;MACV,KAAK,IAAItC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuB,IAAI,EAAEvB,CAAC,EAAE,EAAE;QAC7B,IAAIE,CAAC,GAAGX,KAAK,CAACS,CAAC,CAAC;QAChB,IAAI,CAACE,CAAC,CAACc,KAAK,EAAE;UACZ/B,IAAI,CAAC2C,GAAG,CAACU,CAAC,EAAEpC,CAAC,CAACC,CAAC,EAAED,CAAC,CAACK,EAAE,CAAC;UACtBrB,WAAW,CAACgB,CAAC,CAACC,CAAC,EAAED,CAAC,CAACC,CAAC,EAAEmC,CAAC,EAAE5B,QAAQ,CAAC;UAClCzB,IAAI,CAACsD,IAAI,CAACrC,CAAC,CAACK,EAAE,EAAEL,CAAC,CAACC,CAAC,CAAC;QACtB;MACF;MACAO,QAAQ,GAAGA,QAAQ,GAAG,KAAK;MAC3B,IAAI8B,QAAQ,GAAG9B,QAAQ,GAAG,IAAI;MAC9BE,iBAAiB,IAAIA,iBAAiB,CAACrB,KAAK,EAAEC,KAAK,EAAEgD,QAAQ,CAAC;MAC9DrB,EAAE,IAAIA,EAAE,CAACqB,QAAQ,CAAC;IACpB;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}