{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.createSensor = void 0;\nvar _debounce = _interopRequireDefault(require(\"../debounce\"));\nvar _constant = require(\"../constant\");\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    \"default\": obj\n  };\n}\n/**\n * Created by hustcc on 18/6/9.\n * Contract: <EMAIL>\n */\n\nvar createSensor = function createSensor(element, whenDestroy) {\n  var sensor = undefined;\n  // callback\n  var listeners = [];\n\n  /**\n   * create object DOM of sensor\n   * @returns {HTMLObjectElement}\n   */\n  var newSensor = function newSensor() {\n    // adjust style\n    if (getComputedStyle(element).position === 'static') {\n      element.style.position = 'relative';\n    }\n    var obj = document.createElement('object');\n    obj.onload = function () {\n      obj.contentDocument.defaultView.addEventListener('resize', resizeListener);\n      // 直接触发一次 resize\n      resizeListener();\n    };\n    obj.style.display = 'block';\n    obj.style.position = 'absolute';\n    obj.style.top = '0';\n    obj.style.left = '0';\n    obj.style.height = '100%';\n    obj.style.width = '100%';\n    obj.style.overflow = 'hidden';\n    obj.style.pointerEvents = 'none';\n    obj.style.zIndex = '-1';\n    obj.style.opacity = '0';\n    obj.setAttribute('class', _constant.SensorClassName);\n    obj.setAttribute('tabindex', _constant.SensorTabIndex);\n    obj.type = 'text/html';\n\n    // append into dom\n    element.appendChild(obj);\n    // for ie, should set data attribute delay, or will be white screen\n    obj.data = 'about:blank';\n    return obj;\n  };\n\n  /**\n   * trigger listeners\n   */\n  var resizeListener = (0, _debounce[\"default\"])(function () {\n    // trigger all listener\n    listeners.forEach(function (listener) {\n      listener(element);\n    });\n  });\n\n  /**\n   * listen with one callback function\n   * @param cb\n   */\n  var bind = function bind(cb) {\n    // if not exist sensor, then create one\n    if (!sensor) {\n      sensor = newSensor();\n    }\n    if (listeners.indexOf(cb) === -1) {\n      listeners.push(cb);\n    }\n  };\n\n  /**\n   * destroy all\n   */\n  var destroy = function destroy() {\n    if (sensor && sensor.parentNode) {\n      if (sensor.contentDocument) {\n        // remote event\n        sensor.contentDocument.defaultView.removeEventListener('resize', resizeListener);\n      }\n      // remove dom\n      sensor.parentNode.removeChild(sensor);\n      // initial variable\n      element.removeAttribute(_constant.SizeSensorId);\n      sensor = undefined;\n      listeners = [];\n      whenDestroy && whenDestroy();\n    }\n  };\n\n  /**\n   * cancel listener bind\n   * @param cb\n   */\n  var unbind = function unbind(cb) {\n    var idx = listeners.indexOf(cb);\n    if (idx !== -1) {\n      listeners.splice(idx, 1);\n    }\n\n    // no listener, and sensor is exist\n    // then destroy the sensor\n    if (listeners.length === 0 && sensor) {\n      destroy();\n    }\n  };\n  return {\n    element: element,\n    bind: bind,\n    destroy: destroy,\n    unbind: unbind\n  };\n};\nexports.createSensor = createSensor;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "createSensor", "_debounce", "_interopRequireDefault", "require", "_constant", "obj", "__esModule", "element", "<PERSON><PERSON><PERSON><PERSON>", "sensor", "undefined", "listeners", "newSensor", "getComputedStyle", "position", "style", "document", "createElement", "onload", "contentDocument", "defaultView", "addEventListener", "resizeListener", "display", "top", "left", "height", "width", "overflow", "pointerEvents", "zIndex", "opacity", "setAttribute", "SensorClassName", "SensorTabIndex", "type", "append<PERSON><PERSON><PERSON>", "data", "for<PERSON>ach", "listener", "bind", "cb", "indexOf", "push", "destroy", "parentNode", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON>", "removeAttribute", "SizeSensorId", "unbind", "idx", "splice", "length"], "sources": ["E:/AI/SmartCV/node_modules/size-sensor/lib/sensors/object.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.createSensor = void 0;\nvar _debounce = _interopRequireDefault(require(\"../debounce\"));\nvar _constant = require(\"../constant\");\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\n/**\n * Created by hustcc on 18/6/9.\n * Contract: <EMAIL>\n */\n\nvar createSensor = function createSensor(element, whenDestroy) {\n  var sensor = undefined;\n  // callback\n  var listeners = [];\n\n  /**\n   * create object DOM of sensor\n   * @returns {HTMLObjectElement}\n   */\n  var newSensor = function newSensor() {\n    // adjust style\n    if (getComputedStyle(element).position === 'static') {\n      element.style.position = 'relative';\n    }\n    var obj = document.createElement('object');\n    obj.onload = function () {\n      obj.contentDocument.defaultView.addEventListener('resize', resizeListener);\n      // 直接触发一次 resize\n      resizeListener();\n    };\n    obj.style.display = 'block';\n    obj.style.position = 'absolute';\n    obj.style.top = '0';\n    obj.style.left = '0';\n    obj.style.height = '100%';\n    obj.style.width = '100%';\n    obj.style.overflow = 'hidden';\n    obj.style.pointerEvents = 'none';\n    obj.style.zIndex = '-1';\n    obj.style.opacity = '0';\n    obj.setAttribute('class', _constant.SensorClassName);\n    obj.setAttribute('tabindex', _constant.SensorTabIndex);\n    obj.type = 'text/html';\n\n    // append into dom\n    element.appendChild(obj);\n    // for ie, should set data attribute delay, or will be white screen\n    obj.data = 'about:blank';\n    return obj;\n  };\n\n  /**\n   * trigger listeners\n   */\n  var resizeListener = (0, _debounce[\"default\"])(function () {\n    // trigger all listener\n    listeners.forEach(function (listener) {\n      listener(element);\n    });\n  });\n\n  /**\n   * listen with one callback function\n   * @param cb\n   */\n  var bind = function bind(cb) {\n    // if not exist sensor, then create one\n    if (!sensor) {\n      sensor = newSensor();\n    }\n    if (listeners.indexOf(cb) === -1) {\n      listeners.push(cb);\n    }\n  };\n\n  /**\n   * destroy all\n   */\n  var destroy = function destroy() {\n    if (sensor && sensor.parentNode) {\n      if (sensor.contentDocument) {\n        // remote event\n        sensor.contentDocument.defaultView.removeEventListener('resize', resizeListener);\n      }\n      // remove dom\n      sensor.parentNode.removeChild(sensor);\n      // initial variable\n      element.removeAttribute(_constant.SizeSensorId);\n      sensor = undefined;\n      listeners = [];\n      whenDestroy && whenDestroy();\n    }\n  };\n\n  /**\n   * cancel listener bind\n   * @param cb\n   */\n  var unbind = function unbind(cb) {\n    var idx = listeners.indexOf(cb);\n    if (idx !== -1) {\n      listeners.splice(idx, 1);\n    }\n\n    // no listener, and sensor is exist\n    // then destroy the sensor\n    if (listeners.length === 0 && sensor) {\n      destroy();\n    }\n  };\n  return {\n    element: element,\n    bind: bind,\n    destroy: destroy,\n    unbind: unbind\n  };\n};\nexports.createSensor = createSensor;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,YAAY,GAAG,KAAK,CAAC;AAC7B,IAAIC,SAAS,GAAGC,sBAAsB,CAACC,OAAO,CAAC,aAAa,CAAC,CAAC;AAC9D,IAAIC,SAAS,GAAGD,OAAO,CAAC,aAAa,CAAC;AACtC,SAASD,sBAAsBA,CAACG,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAE,SAAS,EAAEA;EAAI,CAAC;AAAE;AAChG;AACA;AACA;AACA;;AAEA,IAAIL,YAAY,GAAG,SAASA,YAAYA,CAACO,OAAO,EAAEC,WAAW,EAAE;EAC7D,IAAIC,MAAM,GAAGC,SAAS;EACtB;EACA,IAAIC,SAAS,GAAG,EAAE;;EAElB;AACF;AACA;AACA;EACE,IAAIC,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;IACnC;IACA,IAAIC,gBAAgB,CAACN,OAAO,CAAC,CAACO,QAAQ,KAAK,QAAQ,EAAE;MACnDP,OAAO,CAACQ,KAAK,CAACD,QAAQ,GAAG,UAAU;IACrC;IACA,IAAIT,GAAG,GAAGW,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC1CZ,GAAG,CAACa,MAAM,GAAG,YAAY;MACvBb,GAAG,CAACc,eAAe,CAACC,WAAW,CAACC,gBAAgB,CAAC,QAAQ,EAAEC,cAAc,CAAC;MAC1E;MACAA,cAAc,CAAC,CAAC;IAClB,CAAC;IACDjB,GAAG,CAACU,KAAK,CAACQ,OAAO,GAAG,OAAO;IAC3BlB,GAAG,CAACU,KAAK,CAACD,QAAQ,GAAG,UAAU;IAC/BT,GAAG,CAACU,KAAK,CAACS,GAAG,GAAG,GAAG;IACnBnB,GAAG,CAACU,KAAK,CAACU,IAAI,GAAG,GAAG;IACpBpB,GAAG,CAACU,KAAK,CAACW,MAAM,GAAG,MAAM;IACzBrB,GAAG,CAACU,KAAK,CAACY,KAAK,GAAG,MAAM;IACxBtB,GAAG,CAACU,KAAK,CAACa,QAAQ,GAAG,QAAQ;IAC7BvB,GAAG,CAACU,KAAK,CAACc,aAAa,GAAG,MAAM;IAChCxB,GAAG,CAACU,KAAK,CAACe,MAAM,GAAG,IAAI;IACvBzB,GAAG,CAACU,KAAK,CAACgB,OAAO,GAAG,GAAG;IACvB1B,GAAG,CAAC2B,YAAY,CAAC,OAAO,EAAE5B,SAAS,CAAC6B,eAAe,CAAC;IACpD5B,GAAG,CAAC2B,YAAY,CAAC,UAAU,EAAE5B,SAAS,CAAC8B,cAAc,CAAC;IACtD7B,GAAG,CAAC8B,IAAI,GAAG,WAAW;;IAEtB;IACA5B,OAAO,CAAC6B,WAAW,CAAC/B,GAAG,CAAC;IACxB;IACAA,GAAG,CAACgC,IAAI,GAAG,aAAa;IACxB,OAAOhC,GAAG;EACZ,CAAC;;EAED;AACF;AACA;EACE,IAAIiB,cAAc,GAAG,CAAC,CAAC,EAAErB,SAAS,CAAC,SAAS,CAAC,EAAE,YAAY;IACzD;IACAU,SAAS,CAAC2B,OAAO,CAAC,UAAUC,QAAQ,EAAE;MACpCA,QAAQ,CAAChC,OAAO,CAAC;IACnB,CAAC,CAAC;EACJ,CAAC,CAAC;;EAEF;AACF;AACA;AACA;EACE,IAAIiC,IAAI,GAAG,SAASA,IAAIA,CAACC,EAAE,EAAE;IAC3B;IACA,IAAI,CAAChC,MAAM,EAAE;MACXA,MAAM,GAAGG,SAAS,CAAC,CAAC;IACtB;IACA,IAAID,SAAS,CAAC+B,OAAO,CAACD,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;MAChC9B,SAAS,CAACgC,IAAI,CAACF,EAAE,CAAC;IACpB;EACF,CAAC;;EAED;AACF;AACA;EACE,IAAIG,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG;IAC/B,IAAInC,MAAM,IAAIA,MAAM,CAACoC,UAAU,EAAE;MAC/B,IAAIpC,MAAM,CAACU,eAAe,EAAE;QAC1B;QACAV,MAAM,CAACU,eAAe,CAACC,WAAW,CAAC0B,mBAAmB,CAAC,QAAQ,EAAExB,cAAc,CAAC;MAClF;MACA;MACAb,MAAM,CAACoC,UAAU,CAACE,WAAW,CAACtC,MAAM,CAAC;MACrC;MACAF,OAAO,CAACyC,eAAe,CAAC5C,SAAS,CAAC6C,YAAY,CAAC;MAC/CxC,MAAM,GAAGC,SAAS;MAClBC,SAAS,GAAG,EAAE;MACdH,WAAW,IAAIA,WAAW,CAAC,CAAC;IAC9B;EACF,CAAC;;EAED;AACF;AACA;AACA;EACE,IAAI0C,MAAM,GAAG,SAASA,MAAMA,CAACT,EAAE,EAAE;IAC/B,IAAIU,GAAG,GAAGxC,SAAS,CAAC+B,OAAO,CAACD,EAAE,CAAC;IAC/B,IAAIU,GAAG,KAAK,CAAC,CAAC,EAAE;MACdxC,SAAS,CAACyC,MAAM,CAACD,GAAG,EAAE,CAAC,CAAC;IAC1B;;IAEA;IACA;IACA,IAAIxC,SAAS,CAAC0C,MAAM,KAAK,CAAC,IAAI5C,MAAM,EAAE;MACpCmC,OAAO,CAAC,CAAC;IACX;EACF,CAAC;EACD,OAAO;IACLrC,OAAO,EAAEA,OAAO;IAChBiC,IAAI,EAAEA,IAAI;IACVI,OAAO,EAAEA,OAAO;IAChBM,MAAM,EAAEA;EACV,CAAC;AACH,CAAC;AACDpD,OAAO,CAACE,YAAY,GAAGA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}