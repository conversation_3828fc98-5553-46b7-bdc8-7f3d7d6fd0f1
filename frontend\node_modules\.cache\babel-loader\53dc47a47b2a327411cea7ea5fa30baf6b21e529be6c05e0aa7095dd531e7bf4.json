{"ast": null, "code": "function isSafeNum(num) {\n  return isFinite(num);\n}\nexport function createLinearGradient(ctx, obj, rect) {\n  var x = obj.x == null ? 0 : obj.x;\n  var x2 = obj.x2 == null ? 1 : obj.x2;\n  var y = obj.y == null ? 0 : obj.y;\n  var y2 = obj.y2 == null ? 0 : obj.y2;\n  if (!obj.global) {\n    x = x * rect.width + rect.x;\n    x2 = x2 * rect.width + rect.x;\n    y = y * rect.height + rect.y;\n    y2 = y2 * rect.height + rect.y;\n  }\n  x = isSafeNum(x) ? x : 0;\n  x2 = isSafeNum(x2) ? x2 : 1;\n  y = isSafeNum(y) ? y : 0;\n  y2 = isSafeNum(y2) ? y2 : 0;\n  var canvasGradient = ctx.createLinearGradient(x, y, x2, y2);\n  return canvasGradient;\n}\nexport function createRadialGradient(ctx, obj, rect) {\n  var width = rect.width;\n  var height = rect.height;\n  var min = Math.min(width, height);\n  var x = obj.x == null ? 0.5 : obj.x;\n  var y = obj.y == null ? 0.5 : obj.y;\n  var r = obj.r == null ? 0.5 : obj.r;\n  if (!obj.global) {\n    x = x * width + rect.x;\n    y = y * height + rect.y;\n    r = r * min;\n  }\n  x = isSafeNum(x) ? x : 0.5;\n  y = isSafeNum(y) ? y : 0.5;\n  r = r >= 0 && isSafeNum(r) ? r : 0.5;\n  var canvasGradient = ctx.createRadialGradient(x, y, 0, x, y, r);\n  return canvasGradient;\n}\nexport function getCanvasGradient(ctx, obj, rect) {\n  var canvasGradient = obj.type === 'radial' ? createRadialGradient(ctx, obj, rect) : createLinearGradient(ctx, obj, rect);\n  var colorStops = obj.colorStops;\n  for (var i = 0; i < colorStops.length; i++) {\n    canvasGradient.addColorStop(colorStops[i].offset, colorStops[i].color);\n  }\n  return canvasGradient;\n}\nexport function isClipPathChanged(clipPaths, prevClipPaths) {\n  if (clipPaths === prevClipPaths || !clipPaths && !prevClipPaths) {\n    return false;\n  }\n  if (!clipPaths || !prevClipPaths || clipPaths.length !== prevClipPaths.length) {\n    return true;\n  }\n  for (var i = 0; i < clipPaths.length; i++) {\n    if (clipPaths[i] !== prevClipPaths[i]) {\n      return true;\n    }\n  }\n  return false;\n}\nfunction parseInt10(val) {\n  return parseInt(val, 10);\n}\nexport function getSize(root, whIdx, opts) {\n  var wh = ['width', 'height'][whIdx];\n  var cwh = ['clientWidth', 'clientHeight'][whIdx];\n  var plt = ['paddingLeft', 'paddingTop'][whIdx];\n  var prb = ['paddingRight', 'paddingBottom'][whIdx];\n  if (opts[wh] != null && opts[wh] !== 'auto') {\n    return parseFloat(opts[wh]);\n  }\n  var stl = document.defaultView.getComputedStyle(root);\n  return (root[cwh] || parseInt10(stl[wh]) || parseInt10(root.style[wh])) - (parseInt10(stl[plt]) || 0) - (parseInt10(stl[prb]) || 0) | 0;\n}", "map": {"version": 3, "names": ["isSafeNum", "num", "isFinite", "createLinearGradient", "ctx", "obj", "rect", "x", "x2", "y", "y2", "global", "width", "height", "canvasGradient", "createRadialGradient", "min", "Math", "r", "getCanvasGradient", "type", "colorStops", "i", "length", "addColorStop", "offset", "color", "isClipPath<PERSON><PERSON>ed", "clipPaths", "prevClipPaths", "parseInt10", "val", "parseInt", "getSize", "root", "whIdx", "opts", "wh", "cwh", "plt", "prb", "parseFloat", "stl", "document", "defaultView", "getComputedStyle", "style"], "sources": ["E:/AI/SmartCV/node_modules/zrender/lib/canvas/helper.js"], "sourcesContent": ["function isSafeNum(num) {\n    return isFinite(num);\n}\nexport function createLinearGradient(ctx, obj, rect) {\n    var x = obj.x == null ? 0 : obj.x;\n    var x2 = obj.x2 == null ? 1 : obj.x2;\n    var y = obj.y == null ? 0 : obj.y;\n    var y2 = obj.y2 == null ? 0 : obj.y2;\n    if (!obj.global) {\n        x = x * rect.width + rect.x;\n        x2 = x2 * rect.width + rect.x;\n        y = y * rect.height + rect.y;\n        y2 = y2 * rect.height + rect.y;\n    }\n    x = isSafeNum(x) ? x : 0;\n    x2 = isSafeNum(x2) ? x2 : 1;\n    y = isSafeNum(y) ? y : 0;\n    y2 = isSafeNum(y2) ? y2 : 0;\n    var canvasGradient = ctx.createLinearGradient(x, y, x2, y2);\n    return canvasGradient;\n}\nexport function createRadialGradient(ctx, obj, rect) {\n    var width = rect.width;\n    var height = rect.height;\n    var min = Math.min(width, height);\n    var x = obj.x == null ? 0.5 : obj.x;\n    var y = obj.y == null ? 0.5 : obj.y;\n    var r = obj.r == null ? 0.5 : obj.r;\n    if (!obj.global) {\n        x = x * width + rect.x;\n        y = y * height + rect.y;\n        r = r * min;\n    }\n    x = isSafeNum(x) ? x : 0.5;\n    y = isSafeNum(y) ? y : 0.5;\n    r = r >= 0 && isSafeNum(r) ? r : 0.5;\n    var canvasGradient = ctx.createRadialGradient(x, y, 0, x, y, r);\n    return canvasGradient;\n}\nexport function getCanvasGradient(ctx, obj, rect) {\n    var canvasGradient = obj.type === 'radial'\n        ? createRadialGradient(ctx, obj, rect)\n        : createLinearGradient(ctx, obj, rect);\n    var colorStops = obj.colorStops;\n    for (var i = 0; i < colorStops.length; i++) {\n        canvasGradient.addColorStop(colorStops[i].offset, colorStops[i].color);\n    }\n    return canvasGradient;\n}\nexport function isClipPathChanged(clipPaths, prevClipPaths) {\n    if (clipPaths === prevClipPaths || (!clipPaths && !prevClipPaths)) {\n        return false;\n    }\n    if (!clipPaths || !prevClipPaths || (clipPaths.length !== prevClipPaths.length)) {\n        return true;\n    }\n    for (var i = 0; i < clipPaths.length; i++) {\n        if (clipPaths[i] !== prevClipPaths[i]) {\n            return true;\n        }\n    }\n    return false;\n}\nfunction parseInt10(val) {\n    return parseInt(val, 10);\n}\nexport function getSize(root, whIdx, opts) {\n    var wh = ['width', 'height'][whIdx];\n    var cwh = ['clientWidth', 'clientHeight'][whIdx];\n    var plt = ['paddingLeft', 'paddingTop'][whIdx];\n    var prb = ['paddingRight', 'paddingBottom'][whIdx];\n    if (opts[wh] != null && opts[wh] !== 'auto') {\n        return parseFloat(opts[wh]);\n    }\n    var stl = document.defaultView.getComputedStyle(root);\n    return ((root[cwh] || parseInt10(stl[wh]) || parseInt10(root.style[wh]))\n        - (parseInt10(stl[plt]) || 0)\n        - (parseInt10(stl[prb]) || 0)) | 0;\n}\n"], "mappings": "AAAA,SAASA,SAASA,CAACC,GAAG,EAAE;EACpB,OAAOC,QAAQ,CAACD,GAAG,CAAC;AACxB;AACA,OAAO,SAASE,oBAAoBA,CAACC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACjD,IAAIC,CAAC,GAAGF,GAAG,CAACE,CAAC,IAAI,IAAI,GAAG,CAAC,GAAGF,GAAG,CAACE,CAAC;EACjC,IAAIC,EAAE,GAAGH,GAAG,CAACG,EAAE,IAAI,IAAI,GAAG,CAAC,GAAGH,GAAG,CAACG,EAAE;EACpC,IAAIC,CAAC,GAAGJ,GAAG,CAACI,CAAC,IAAI,IAAI,GAAG,CAAC,GAAGJ,GAAG,CAACI,CAAC;EACjC,IAAIC,EAAE,GAAGL,GAAG,CAACK,EAAE,IAAI,IAAI,GAAG,CAAC,GAAGL,GAAG,CAACK,EAAE;EACpC,IAAI,CAACL,GAAG,CAACM,MAAM,EAAE;IACbJ,CAAC,GAAGA,CAAC,GAAGD,IAAI,CAACM,KAAK,GAAGN,IAAI,CAACC,CAAC;IAC3BC,EAAE,GAAGA,EAAE,GAAGF,IAAI,CAACM,KAAK,GAAGN,IAAI,CAACC,CAAC;IAC7BE,CAAC,GAAGA,CAAC,GAAGH,IAAI,CAACO,MAAM,GAAGP,IAAI,CAACG,CAAC;IAC5BC,EAAE,GAAGA,EAAE,GAAGJ,IAAI,CAACO,MAAM,GAAGP,IAAI,CAACG,CAAC;EAClC;EACAF,CAAC,GAAGP,SAAS,CAACO,CAAC,CAAC,GAAGA,CAAC,GAAG,CAAC;EACxBC,EAAE,GAAGR,SAAS,CAACQ,EAAE,CAAC,GAAGA,EAAE,GAAG,CAAC;EAC3BC,CAAC,GAAGT,SAAS,CAACS,CAAC,CAAC,GAAGA,CAAC,GAAG,CAAC;EACxBC,EAAE,GAAGV,SAAS,CAACU,EAAE,CAAC,GAAGA,EAAE,GAAG,CAAC;EAC3B,IAAII,cAAc,GAAGV,GAAG,CAACD,oBAAoB,CAACI,CAAC,EAAEE,CAAC,EAAED,EAAE,EAAEE,EAAE,CAAC;EAC3D,OAAOI,cAAc;AACzB;AACA,OAAO,SAASC,oBAAoBA,CAACX,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACjD,IAAIM,KAAK,GAAGN,IAAI,CAACM,KAAK;EACtB,IAAIC,MAAM,GAAGP,IAAI,CAACO,MAAM;EACxB,IAAIG,GAAG,GAAGC,IAAI,CAACD,GAAG,CAACJ,KAAK,EAAEC,MAAM,CAAC;EACjC,IAAIN,CAAC,GAAGF,GAAG,CAACE,CAAC,IAAI,IAAI,GAAG,GAAG,GAAGF,GAAG,CAACE,CAAC;EACnC,IAAIE,CAAC,GAAGJ,GAAG,CAACI,CAAC,IAAI,IAAI,GAAG,GAAG,GAAGJ,GAAG,CAACI,CAAC;EACnC,IAAIS,CAAC,GAAGb,GAAG,CAACa,CAAC,IAAI,IAAI,GAAG,GAAG,GAAGb,GAAG,CAACa,CAAC;EACnC,IAAI,CAACb,GAAG,CAACM,MAAM,EAAE;IACbJ,CAAC,GAAGA,CAAC,GAAGK,KAAK,GAAGN,IAAI,CAACC,CAAC;IACtBE,CAAC,GAAGA,CAAC,GAAGI,MAAM,GAAGP,IAAI,CAACG,CAAC;IACvBS,CAAC,GAAGA,CAAC,GAAGF,GAAG;EACf;EACAT,CAAC,GAAGP,SAAS,CAACO,CAAC,CAAC,GAAGA,CAAC,GAAG,GAAG;EAC1BE,CAAC,GAAGT,SAAS,CAACS,CAAC,CAAC,GAAGA,CAAC,GAAG,GAAG;EAC1BS,CAAC,GAAGA,CAAC,IAAI,CAAC,IAAIlB,SAAS,CAACkB,CAAC,CAAC,GAAGA,CAAC,GAAG,GAAG;EACpC,IAAIJ,cAAc,GAAGV,GAAG,CAACW,oBAAoB,CAACR,CAAC,EAAEE,CAAC,EAAE,CAAC,EAAEF,CAAC,EAAEE,CAAC,EAAES,CAAC,CAAC;EAC/D,OAAOJ,cAAc;AACzB;AACA,OAAO,SAASK,iBAAiBA,CAACf,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAE;EAC9C,IAAIQ,cAAc,GAAGT,GAAG,CAACe,IAAI,KAAK,QAAQ,GACpCL,oBAAoB,CAACX,GAAG,EAAEC,GAAG,EAAEC,IAAI,CAAC,GACpCH,oBAAoB,CAACC,GAAG,EAAEC,GAAG,EAAEC,IAAI,CAAC;EAC1C,IAAIe,UAAU,GAAGhB,GAAG,CAACgB,UAAU;EAC/B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,UAAU,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IACxCR,cAAc,CAACU,YAAY,CAACH,UAAU,CAACC,CAAC,CAAC,CAACG,MAAM,EAAEJ,UAAU,CAACC,CAAC,CAAC,CAACI,KAAK,CAAC;EAC1E;EACA,OAAOZ,cAAc;AACzB;AACA,OAAO,SAASa,iBAAiBA,CAACC,SAAS,EAAEC,aAAa,EAAE;EACxD,IAAID,SAAS,KAAKC,aAAa,IAAK,CAACD,SAAS,IAAI,CAACC,aAAc,EAAE;IAC/D,OAAO,KAAK;EAChB;EACA,IAAI,CAACD,SAAS,IAAI,CAACC,aAAa,IAAKD,SAAS,CAACL,MAAM,KAAKM,aAAa,CAACN,MAAO,EAAE;IAC7E,OAAO,IAAI;EACf;EACA,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGM,SAAS,CAACL,MAAM,EAAED,CAAC,EAAE,EAAE;IACvC,IAAIM,SAAS,CAACN,CAAC,CAAC,KAAKO,aAAa,CAACP,CAAC,CAAC,EAAE;MACnC,OAAO,IAAI;IACf;EACJ;EACA,OAAO,KAAK;AAChB;AACA,SAASQ,UAAUA,CAACC,GAAG,EAAE;EACrB,OAAOC,QAAQ,CAACD,GAAG,EAAE,EAAE,CAAC;AAC5B;AACA,OAAO,SAASE,OAAOA,CAACC,IAAI,EAAEC,KAAK,EAAEC,IAAI,EAAE;EACvC,IAAIC,EAAE,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC,CAACF,KAAK,CAAC;EACnC,IAAIG,GAAG,GAAG,CAAC,aAAa,EAAE,cAAc,CAAC,CAACH,KAAK,CAAC;EAChD,IAAII,GAAG,GAAG,CAAC,aAAa,EAAE,YAAY,CAAC,CAACJ,KAAK,CAAC;EAC9C,IAAIK,GAAG,GAAG,CAAC,cAAc,EAAE,eAAe,CAAC,CAACL,KAAK,CAAC;EAClD,IAAIC,IAAI,CAACC,EAAE,CAAC,IAAI,IAAI,IAAID,IAAI,CAACC,EAAE,CAAC,KAAK,MAAM,EAAE;IACzC,OAAOI,UAAU,CAACL,IAAI,CAACC,EAAE,CAAC,CAAC;EAC/B;EACA,IAAIK,GAAG,GAAGC,QAAQ,CAACC,WAAW,CAACC,gBAAgB,CAACX,IAAI,CAAC;EACrD,OAAQ,CAACA,IAAI,CAACI,GAAG,CAAC,IAAIR,UAAU,CAACY,GAAG,CAACL,EAAE,CAAC,CAAC,IAAIP,UAAU,CAACI,IAAI,CAACY,KAAK,CAACT,EAAE,CAAC,CAAC,KAChEP,UAAU,CAACY,GAAG,CAACH,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,IAC1BT,UAAU,CAACY,GAAG,CAACF,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAI,CAAC;AAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}