{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { each, isString, createHashMap, hasOwn } from 'zrender/lib/core/util.js';\nimport parseGeoJson from './parseGeoJson.js';\n// Built-in GEO fixer.\nimport fixNanhai from './fix/nanhai.js';\nimport fixTextCoord from './fix/textCoord.js';\nimport fixDiaoyuIsland from './fix/diaoyuIsland.js';\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nvar DEFAULT_NAME_PROPERTY = 'name';\nvar GeoJSONResource = /** @class */function () {\n  function GeoJSONResource(mapName, geoJSON, specialAreas) {\n    this.type = 'geoJSON';\n    this._parsedMap = createHashMap();\n    this._mapName = mapName;\n    this._specialAreas = specialAreas;\n    // PENDING: delay the parse to the first usage to rapid up the FMP?\n    this._geoJSON = parseInput(geoJSON);\n  }\n  /**\r\n   * @param nameMap can be null/undefined\r\n   * @param nameProperty can be null/undefined\r\n   */\n  GeoJSONResource.prototype.load = function (nameMap, nameProperty) {\n    nameProperty = nameProperty || DEFAULT_NAME_PROPERTY;\n    var parsed = this._parsedMap.get(nameProperty);\n    if (!parsed) {\n      var rawRegions = this._parseToRegions(nameProperty);\n      parsed = this._parsedMap.set(nameProperty, {\n        regions: rawRegions,\n        boundingRect: calculateBoundingRect(rawRegions)\n      });\n    }\n    var regionsMap = createHashMap();\n    var finalRegions = [];\n    each(parsed.regions, function (region) {\n      var regionName = region.name;\n      // Try use the alias in geoNameMap\n      if (nameMap && hasOwn(nameMap, regionName)) {\n        region = region.cloneShallow(regionName = nameMap[regionName]);\n      }\n      finalRegions.push(region);\n      regionsMap.set(regionName, region);\n    });\n    return {\n      regions: finalRegions,\n      boundingRect: parsed.boundingRect || new BoundingRect(0, 0, 0, 0),\n      regionsMap: regionsMap\n    };\n  };\n  GeoJSONResource.prototype._parseToRegions = function (nameProperty) {\n    var mapName = this._mapName;\n    var geoJSON = this._geoJSON;\n    var rawRegions;\n    // https://jsperf.com/try-catch-performance-overhead\n    try {\n      rawRegions = geoJSON ? parseGeoJson(geoJSON, nameProperty) : [];\n    } catch (e) {\n      throw new Error('Invalid geoJson format\\n' + e.message);\n    }\n    fixNanhai(mapName, rawRegions);\n    each(rawRegions, function (region) {\n      var regionName = region.name;\n      fixTextCoord(mapName, region);\n      fixDiaoyuIsland(mapName, region);\n      // Some area like Alaska in USA map needs to be tansformed\n      // to look better\n      var specialArea = this._specialAreas && this._specialAreas[regionName];\n      if (specialArea) {\n        region.transformTo(specialArea.left, specialArea.top, specialArea.width, specialArea.height);\n      }\n    }, this);\n    return rawRegions;\n  };\n  /**\r\n   * Only for exporting to users.\r\n   * **MUST NOT** used internally.\r\n   */\n  GeoJSONResource.prototype.getMapForUser = function () {\n    return {\n      // For backward compatibility, use geoJson\n      // PENDING: it has been returning them without clone.\n      // do we need to avoid outsite modification?\n      geoJson: this._geoJSON,\n      geoJSON: this._geoJSON,\n      specialAreas: this._specialAreas\n    };\n  };\n  return GeoJSONResource;\n}();\nexport { GeoJSONResource };\nfunction calculateBoundingRect(regions) {\n  var rect;\n  for (var i = 0; i < regions.length; i++) {\n    var regionRect = regions[i].getBoundingRect();\n    rect = rect || regionRect.clone();\n    rect.union(regionRect);\n  }\n  return rect;\n}\nfunction parseInput(source) {\n  return !isString(source) ? source : typeof JSON !== 'undefined' && JSON.parse ? JSON.parse(source) : new Function('return (' + source + ');')();\n}", "map": {"version": 3, "names": ["each", "isString", "createHashMap", "hasOwn", "parseGeoJson", "<PERSON><PERSON><PERSON><PERSON>", "fixTextCoord", "fixDiaoyuIsland", "BoundingRect", "DEFAULT_NAME_PROPERTY", "GeoJSONResource", "mapName", "geoJSON", "<PERSON><PERSON><PERSON><PERSON>", "type", "_parsedMap", "_mapName", "_special<PERSON><PERSON>s", "_geoJSON", "parseInput", "prototype", "load", "nameMap", "nameProperty", "parsed", "get", "rawRegions", "_parseToRegions", "set", "regions", "boundingRect", "calculateBoundingRect", "regionsMap", "finalRegions", "region", "regionName", "name", "cloneShallow", "push", "e", "Error", "message", "special<PERSON><PERSON>", "transformTo", "left", "top", "width", "height", "getMapForUser", "geoJson", "rect", "i", "length", "regionRect", "getBoundingRect", "clone", "union", "source", "JSON", "parse", "Function"], "sources": ["E:/AI/SmartCV/node_modules/echarts/lib/coord/geo/GeoJSONResource.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { each, isString, createHashMap, hasOwn } from 'zrender/lib/core/util.js';\nimport parseGeoJson from './parseGeoJson.js';\n// Built-in GEO fixer.\nimport fixNanhai from './fix/nanhai.js';\nimport fixTextCoord from './fix/textCoord.js';\nimport fixDiaoyuIsland from './fix/diaoyuIsland.js';\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nvar DEFAULT_NAME_PROPERTY = 'name';\nvar GeoJSONResource = /** @class */function () {\n  function GeoJSONResource(mapName, geoJSON, specialAreas) {\n    this.type = 'geoJSON';\n    this._parsedMap = createHashMap();\n    this._mapName = mapName;\n    this._specialAreas = specialAreas;\n    // PENDING: delay the parse to the first usage to rapid up the FMP?\n    this._geoJSON = parseInput(geoJSON);\n  }\n  /**\r\n   * @param nameMap can be null/undefined\r\n   * @param nameProperty can be null/undefined\r\n   */\n  GeoJSONResource.prototype.load = function (nameMap, nameProperty) {\n    nameProperty = nameProperty || DEFAULT_NAME_PROPERTY;\n    var parsed = this._parsedMap.get(nameProperty);\n    if (!parsed) {\n      var rawRegions = this._parseToRegions(nameProperty);\n      parsed = this._parsedMap.set(nameProperty, {\n        regions: rawRegions,\n        boundingRect: calculateBoundingRect(rawRegions)\n      });\n    }\n    var regionsMap = createHashMap();\n    var finalRegions = [];\n    each(parsed.regions, function (region) {\n      var regionName = region.name;\n      // Try use the alias in geoNameMap\n      if (nameMap && hasOwn(nameMap, regionName)) {\n        region = region.cloneShallow(regionName = nameMap[regionName]);\n      }\n      finalRegions.push(region);\n      regionsMap.set(regionName, region);\n    });\n    return {\n      regions: finalRegions,\n      boundingRect: parsed.boundingRect || new BoundingRect(0, 0, 0, 0),\n      regionsMap: regionsMap\n    };\n  };\n  GeoJSONResource.prototype._parseToRegions = function (nameProperty) {\n    var mapName = this._mapName;\n    var geoJSON = this._geoJSON;\n    var rawRegions;\n    // https://jsperf.com/try-catch-performance-overhead\n    try {\n      rawRegions = geoJSON ? parseGeoJson(geoJSON, nameProperty) : [];\n    } catch (e) {\n      throw new Error('Invalid geoJson format\\n' + e.message);\n    }\n    fixNanhai(mapName, rawRegions);\n    each(rawRegions, function (region) {\n      var regionName = region.name;\n      fixTextCoord(mapName, region);\n      fixDiaoyuIsland(mapName, region);\n      // Some area like Alaska in USA map needs to be tansformed\n      // to look better\n      var specialArea = this._specialAreas && this._specialAreas[regionName];\n      if (specialArea) {\n        region.transformTo(specialArea.left, specialArea.top, specialArea.width, specialArea.height);\n      }\n    }, this);\n    return rawRegions;\n  };\n  /**\r\n   * Only for exporting to users.\r\n   * **MUST NOT** used internally.\r\n   */\n  GeoJSONResource.prototype.getMapForUser = function () {\n    return {\n      // For backward compatibility, use geoJson\n      // PENDING: it has been returning them without clone.\n      // do we need to avoid outsite modification?\n      geoJson: this._geoJSON,\n      geoJSON: this._geoJSON,\n      specialAreas: this._specialAreas\n    };\n  };\n  return GeoJSONResource;\n}();\nexport { GeoJSONResource };\nfunction calculateBoundingRect(regions) {\n  var rect;\n  for (var i = 0; i < regions.length; i++) {\n    var regionRect = regions[i].getBoundingRect();\n    rect = rect || regionRect.clone();\n    rect.union(regionRect);\n  }\n  return rect;\n}\nfunction parseInput(source) {\n  return !isString(source) ? source : typeof JSON !== 'undefined' && JSON.parse ? JSON.parse(source) : new Function('return (' + source + ');')();\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,IAAI,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,MAAM,QAAQ,0BAA0B;AAChF,OAAOC,YAAY,MAAM,mBAAmB;AAC5C;AACA,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,eAAe,MAAM,uBAAuB;AACnD,OAAOC,YAAY,MAAM,kCAAkC;AAC3D,IAAIC,qBAAqB,GAAG,MAAM;AAClC,IAAIC,eAAe,GAAG,aAAa,YAAY;EAC7C,SAASA,eAAeA,CAACC,OAAO,EAAEC,OAAO,EAAEC,YAAY,EAAE;IACvD,IAAI,CAACC,IAAI,GAAG,SAAS;IACrB,IAAI,CAACC,UAAU,GAAGb,aAAa,CAAC,CAAC;IACjC,IAAI,CAACc,QAAQ,GAAGL,OAAO;IACvB,IAAI,CAACM,aAAa,GAAGJ,YAAY;IACjC;IACA,IAAI,CAACK,QAAQ,GAAGC,UAAU,CAACP,OAAO,CAAC;EACrC;EACA;AACF;AACA;AACA;EACEF,eAAe,CAACU,SAAS,CAACC,IAAI,GAAG,UAAUC,OAAO,EAAEC,YAAY,EAAE;IAChEA,YAAY,GAAGA,YAAY,IAAId,qBAAqB;IACpD,IAAIe,MAAM,GAAG,IAAI,CAACT,UAAU,CAACU,GAAG,CAACF,YAAY,CAAC;IAC9C,IAAI,CAACC,MAAM,EAAE;MACX,IAAIE,UAAU,GAAG,IAAI,CAACC,eAAe,CAACJ,YAAY,CAAC;MACnDC,MAAM,GAAG,IAAI,CAACT,UAAU,CAACa,GAAG,CAACL,YAAY,EAAE;QACzCM,OAAO,EAAEH,UAAU;QACnBI,YAAY,EAAEC,qBAAqB,CAACL,UAAU;MAChD,CAAC,CAAC;IACJ;IACA,IAAIM,UAAU,GAAG9B,aAAa,CAAC,CAAC;IAChC,IAAI+B,YAAY,GAAG,EAAE;IACrBjC,IAAI,CAACwB,MAAM,CAACK,OAAO,EAAE,UAAUK,MAAM,EAAE;MACrC,IAAIC,UAAU,GAAGD,MAAM,CAACE,IAAI;MAC5B;MACA,IAAId,OAAO,IAAInB,MAAM,CAACmB,OAAO,EAAEa,UAAU,CAAC,EAAE;QAC1CD,MAAM,GAAGA,MAAM,CAACG,YAAY,CAACF,UAAU,GAAGb,OAAO,CAACa,UAAU,CAAC,CAAC;MAChE;MACAF,YAAY,CAACK,IAAI,CAACJ,MAAM,CAAC;MACzBF,UAAU,CAACJ,GAAG,CAACO,UAAU,EAAED,MAAM,CAAC;IACpC,CAAC,CAAC;IACF,OAAO;MACLL,OAAO,EAAEI,YAAY;MACrBH,YAAY,EAAEN,MAAM,CAACM,YAAY,IAAI,IAAItB,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACjEwB,UAAU,EAAEA;IACd,CAAC;EACH,CAAC;EACDtB,eAAe,CAACU,SAAS,CAACO,eAAe,GAAG,UAAUJ,YAAY,EAAE;IAClE,IAAIZ,OAAO,GAAG,IAAI,CAACK,QAAQ;IAC3B,IAAIJ,OAAO,GAAG,IAAI,CAACM,QAAQ;IAC3B,IAAIQ,UAAU;IACd;IACA,IAAI;MACFA,UAAU,GAAGd,OAAO,GAAGR,YAAY,CAACQ,OAAO,EAAEW,YAAY,CAAC,GAAG,EAAE;IACjE,CAAC,CAAC,OAAOgB,CAAC,EAAE;MACV,MAAM,IAAIC,KAAK,CAAC,0BAA0B,GAAGD,CAAC,CAACE,OAAO,CAAC;IACzD;IACApC,SAAS,CAACM,OAAO,EAAEe,UAAU,CAAC;IAC9B1B,IAAI,CAAC0B,UAAU,EAAE,UAAUQ,MAAM,EAAE;MACjC,IAAIC,UAAU,GAAGD,MAAM,CAACE,IAAI;MAC5B9B,YAAY,CAACK,OAAO,EAAEuB,MAAM,CAAC;MAC7B3B,eAAe,CAACI,OAAO,EAAEuB,MAAM,CAAC;MAChC;MACA;MACA,IAAIQ,WAAW,GAAG,IAAI,CAACzB,aAAa,IAAI,IAAI,CAACA,aAAa,CAACkB,UAAU,CAAC;MACtE,IAAIO,WAAW,EAAE;QACfR,MAAM,CAACS,WAAW,CAACD,WAAW,CAACE,IAAI,EAAEF,WAAW,CAACG,GAAG,EAAEH,WAAW,CAACI,KAAK,EAAEJ,WAAW,CAACK,MAAM,CAAC;MAC9F;IACF,CAAC,EAAE,IAAI,CAAC;IACR,OAAOrB,UAAU;EACnB,CAAC;EACD;AACF;AACA;AACA;EACEhB,eAAe,CAACU,SAAS,CAAC4B,aAAa,GAAG,YAAY;IACpD,OAAO;MACL;MACA;MACA;MACAC,OAAO,EAAE,IAAI,CAAC/B,QAAQ;MACtBN,OAAO,EAAE,IAAI,CAACM,QAAQ;MACtBL,YAAY,EAAE,IAAI,CAACI;IACrB,CAAC;EACH,CAAC;EACD,OAAOP,eAAe;AACxB,CAAC,CAAC,CAAC;AACH,SAASA,eAAe;AACxB,SAASqB,qBAAqBA,CAACF,OAAO,EAAE;EACtC,IAAIqB,IAAI;EACR,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtB,OAAO,CAACuB,MAAM,EAAED,CAAC,EAAE,EAAE;IACvC,IAAIE,UAAU,GAAGxB,OAAO,CAACsB,CAAC,CAAC,CAACG,eAAe,CAAC,CAAC;IAC7CJ,IAAI,GAAGA,IAAI,IAAIG,UAAU,CAACE,KAAK,CAAC,CAAC;IACjCL,IAAI,CAACM,KAAK,CAACH,UAAU,CAAC;EACxB;EACA,OAAOH,IAAI;AACb;AACA,SAAS/B,UAAUA,CAACsC,MAAM,EAAE;EAC1B,OAAO,CAACxD,QAAQ,CAACwD,MAAM,CAAC,GAAGA,MAAM,GAAG,OAAOC,IAAI,KAAK,WAAW,IAAIA,IAAI,CAACC,KAAK,GAAGD,IAAI,CAACC,KAAK,CAACF,MAAM,CAAC,GAAG,IAAIG,QAAQ,CAAC,UAAU,GAAGH,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC;AACjJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}