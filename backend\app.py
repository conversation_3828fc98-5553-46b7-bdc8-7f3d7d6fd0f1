from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
import os
from dotenv import load_dotenv
import tempfile
import logging
from openai import AzureOpenAI
from file_parser import extract_text_from_pdf, extract_text_from_docx
from ai_optimizer import optimize_resume_with_ai
from docx_optimizer import optimize_docx_resume_with_format_preservation

# 加载环境变量
load_dotenv()
print("API KEY:", os.getenv("AZURE_OPENAI_API_KEY"))

app = Flask(__name__)
CORS(app)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 初始化Azure OpenAI客户端
client = AzureOpenAI(
    api_key=os.getenv("AZURE_OPENAI_API_KEY"),
    api_version=os.getenv("AZURE_OPENAI_API_VERSION"),
    azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT")
)

# 配置上传文件限制
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB
app.config['UPLOAD_FOLDER'] = tempfile.gettempdir()  # 使用系统临时目录
ALLOWED_EXTENSIONS = {'pdf', 'docx', 'txt'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def extract_text_from_file(file_path, file_extension):
    """从文件提取文本内容"""
    try:
        if file_extension == 'pdf':
            return extract_text_from_pdf(file_path)
        elif file_extension == 'docx':
            return extract_text_from_docx(file_path)
        elif file_extension == 'txt':
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        else:
            raise ValueError(f"Unsupported file format: {file_extension}")
    except Exception as e:
        logger.error(f"File parsing error: {str(e)}")
        raise

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({"status": "healthy", "message": "Service is running normally"})

@app.route('/api/optimize-resume', methods=['POST'])
def optimize_resume():
    """优化简历主接口"""
    try:
        # 检查请求中是否包含文件
        if 'resume_file' not in request.files:
            return jsonify({"success": False, "message": "Please upload a resume file"}), 400
        
        resume_file = request.files['resume_file']
        if resume_file.filename == '':
            return jsonify({"success": False, "message": "No resume file selected"}), 400
        
        # 检查文件格式
        if not allowed_file(resume_file.filename):
            return jsonify({"success": False, "message": "Unsupported file format. Please upload PDF or DOCX files"}), 400
        
        # 获取职位描述
        job_description = ""
        if 'job_description_text' in request.form:
            job_description = request.form['job_description_text']
        elif 'job_description_file' in request.files:
            jd_file = request.files['job_description_file']
            if jd_file.filename != '' and allowed_file(jd_file.filename):
                # 保存JD文件到临时目录
                tmp_jd_path = None
                try:
                    with tempfile.NamedTemporaryFile(delete=False, suffix='.' + jd_file.filename.rsplit('.', 1)[1].lower()) as tmp_jd:
                        tmp_jd_path = tmp_jd.name
                        jd_file.save(tmp_jd_path)
                        jd_extension = jd_file.filename.rsplit('.', 1)[1].lower()
                        job_description = extract_text_from_file(tmp_jd_path, jd_extension)
                finally:
                    # 确保临时文件被删除
                    if tmp_jd_path and os.path.exists(tmp_jd_path):
                        try:
                            os.unlink(tmp_jd_path)
                            logger.info(f"Deleted JD temp file: {tmp_jd_path}")
                        except Exception as cleanup_error:
                            logger.warning(f"Failed to delete JD temp file: {cleanup_error}")
        
        if not job_description.strip():
            return jsonify({"success": False, "message": "Please provide job description"}), 400
        
        # 保存简历文件到临时目录
        tmp_resume_path = None
        try:
            with tempfile.NamedTemporaryFile(delete=False, suffix='.' + resume_file.filename.rsplit('.', 1)[1].lower()) as tmp_resume:
                tmp_resume_path = tmp_resume.name
                resume_file.save(tmp_resume_path)
                resume_extension = resume_file.filename.rsplit('.', 1)[1].lower()
            
            # 提取简历文本
            resume_text = extract_text_from_file(tmp_resume_path, resume_extension)
            
        finally:
            # 确保临时文件被删除
            if tmp_resume_path and os.path.exists(tmp_resume_path):
                try:
                    os.unlink(tmp_resume_path)
                    logger.info(f"Deleted temp file: {tmp_resume_path}")
                except Exception as cleanup_error:
                    logger.warning(f"Failed to delete temp file: {cleanup_error}")
        
        # 验证提取的内容
        if not resume_text.strip():
            return jsonify({"success": False, "message": "Unable to extract content from resume file. Please check file format"}), 400
        
        # 调用AI优化简历
        logger.info("Starting AI resume optimization...")
        optimization_result = optimize_resume_with_ai(client, resume_text, job_description)

        # 生成Word文档预览URL（如果是DOCX文件）
        optimized_docx_url = None
        if resume_extension == 'docx':
            try:
                logger.info("开始生成DOCX预览...")

                # 重新保存上传的文件到临时位置（用于DOCX优化）
                docx_input_path = os.path.join(tempfile.gettempdir(), f'temp_input_{os.urandom(8).hex()}.docx')
                resume_file.seek(0)  # 重置文件指针
                resume_file.save(docx_input_path)

                # 设置输出路径
                output_filename = f'optimized_resume_{os.urandom(8).hex()}.docx'
                docx_output_path = os.path.join(tempfile.gettempdir(), output_filename)

                logger.info(f"输入文件: {docx_input_path}")
                logger.info(f"输出文件: {docx_output_path}")

                # 优化文档
                success = optimize_docx_resume_with_format_preservation(
                    input_path=docx_input_path,
                    output_path=docx_output_path,
                    job_description=job_description,
                    api_key=os.getenv('AZURE_OPENAI_API_KEY')
                )

                if success and os.path.exists(docx_output_path):
                    # 生成可访问的URL
                    optimized_docx_url = f"{request.url_root}api/temp-file/{output_filename}"
                    logger.info(f"DOCX预览URL生成成功: {optimized_docx_url}")
                else:
                    logger.error("DOCX文档优化失败或输出文件不存在")

                # 清理临时输入文件
                if os.path.exists(docx_input_path):
                    os.remove(docx_input_path)

            except Exception as e:
                logger.error(f"生成DOCX预览失败: {str(e)}")
                import traceback
                logger.error(f"详细错误: {traceback.format_exc()}")

        # 处理结构化优化结果
        if isinstance(optimization_result, dict) and 'optimized_resume_json' in optimization_result:
            response_data = {
                "success": True,
                "optimized_resume_json": optimization_result.get('optimized_resume_json', []),
                "jd_json": optimization_result.get('jd_json', {}),
                "match_json": optimization_result.get('match_json', {}),
                "ats_risks": optimization_result.get('ats_risks', []),
                "original_resume": resume_text,
                "message": "Resume optimization completed",
                "original_length": len(resume_text),
                "job_match_analysis": optimization_result.get('original_match_score', {})
            }
            if optimized_docx_url:
                response_data["optimized_docx_url"] = optimized_docx_url
            return jsonify(response_data)
        else:
            # 旧格式或fallback：只有文本
            optimized_resume = optimization_result if isinstance(optimization_result, str) else str(optimization_result)

            response_data = {
                "success": True,
                "optimized_resume": optimized_resume,
                "original_resume": resume_text,
                "message": "Resume optimization completed",
                "original_length": len(resume_text),
                "optimized_length": len(optimized_resume),
                "job_match_analysis": None
            }

            # 添加DOCX预览URL（如果有）
            if optimized_docx_url:
                response_data["optimized_docx_url"] = optimized_docx_url

            return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"Error during resume optimization: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"Error during processing: {str(e)}"
        }), 500

@app.route('/api/optimize-resume-docx', methods=['POST'])
def optimize_resume_docx():
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'No file uploaded'}), 400
            
        file = request.files['file']
        job_description = request.form.get('job_description', '')
        
        if not file or not file.filename:
            return jsonify({'error': 'Invalid file'}), 400
            
        if not file.filename.endswith('.docx'):
            return jsonify({'error': 'Only .docx format is supported'}), 400
            
        # 保存上传的文件
        input_path = os.path.join(app.config['UPLOAD_FOLDER'], 'temp_input.docx')
        file.save(input_path)
        
        # 设置输出路径
        output_path = os.path.join(app.config['UPLOAD_FOLDER'], 'optimized_resume.docx')
        
        # 优化文档
        success = optimize_docx_resume_with_format_preservation(
            input_path=input_path,
            output_path=output_path,
            job_description=job_description,
            api_key=os.getenv('AZURE_OPENAI_API_KEY')
        )
        
        if not success:
            return jsonify({'error': 'Document optimization failed'}), 500
            
        # 返回优化后的文件
        return send_file(
            output_path,
            as_attachment=True,
            download_name='optimized_resume.docx',
            mimetype='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )
        
    except Exception as e:
        logger.error(f"DOCX resume optimization failed: {str(e)}")
        return jsonify({'error': str(e)}), 500
        
    finally:
        # 清理临时文件
        try:
            if os.path.exists(input_path):
                os.remove(input_path)
            if os.path.exists(output_path):
                os.remove(output_path)
        except Exception as e:
            logger.error(f"Failed to cleanup temp files: {str(e)}")

@app.route('/api/download-optimized-docx/<filename>', methods=['GET'])
def download_optimized_docx(filename):
    """下载优化后的DOCX文件"""
    try:
        # 构建文件路径（假设文件在临时目录中）
        file_path = os.path.join(tempfile.gettempdir(), filename)
        
        if not os.path.exists(file_path):
            return jsonify({"success": False, "message": "File not found"}), 404
        
        return send_file(
            file_path,
            as_attachment=True,
            download_name=f"optimized_resume_{filename}",
            mimetype='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )
        
    except Exception as e:
        logger.error(f"Error downloading file: {str(e)}")
        return jsonify({"success": False, "message": f"Download failed: {str(e)}"}), 500

@app.route('/api/upload-temp-file', methods=['POST'])
def upload_temp_file():
    """上传文件到临时位置，返回可公开访问的URL"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400
        
        # 创建临时文件
        file_extension = file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else ''
        temp_file = tempfile.NamedTemporaryFile(suffix=f'.{file_extension}', delete=False)
        file.save(temp_file.name)
        temp_file.close()
        
        # 生成文件ID（临时文件名的basename）
        file_id = os.path.basename(temp_file.name)
        
        # 返回可访问的URL
        file_url = f"{request.url_root}api/temp-file/{file_id}"
        
        return jsonify({
            'file_id': file_id,
            'file_url': file_url,
            'message': 'File uploaded successfully'
        })
        
    except Exception as e:
        logger.error(f"临时文件上传失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/temp-file/<string:file_id>', methods=['GET'])
def serve_temp_file(file_id):
    """提供临时文件的访问"""
    try:
        temp_file_path = os.path.join(tempfile.gettempdir(), file_id)
        
        if not os.path.exists(temp_file_path):
            return jsonify({'error': 'File not found'}), 404
        
        # 设置适当的MIME类型
        file_extension = file_id.split('.')[-1].lower()
        if file_extension == 'docx':
            mimetype = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        elif file_extension == 'pdf':
            mimetype = 'application/pdf'
        else:
            mimetype = 'application/octet-stream'
            
        response = send_file(
            temp_file_path,
            mimetype=mimetype,
            as_attachment=False,
            download_name=file_id
        )
        
        # 添加CORS头和缓存控制
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Methods'] = 'GET'
        response.headers['Access-Control-Allow-Headers'] = 'Content-Type'
        response.headers['Cache-Control'] = 'public, max-age=3600'  # 缓存1小时
        response.headers['X-Frame-Options'] = 'ALLOWALL'  # 允许在iframe中加载
        
        return response
        
    except Exception as e:
        logger.error(f"临时文件访问失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.errorhandler(413)
def too_large(e):
    return jsonify({"success": False, "message": "File too large. Please upload files smaller than 16MB"}), 413

@app.errorhandler(500)
def internal_error(e):
    return jsonify({"success": False, "message": "Internal server error"}), 500

if __name__ == '__main__':
    port = int(os.getenv('FLASK_PORT', 5000))
    debug = os.getenv('FLASK_ENV') == 'development'
    
    logger.info(f"Starting Flask application on port: {port}, debug mode: {debug}")
    app.run(host='0.0.0.0', port=port, debug=debug) 