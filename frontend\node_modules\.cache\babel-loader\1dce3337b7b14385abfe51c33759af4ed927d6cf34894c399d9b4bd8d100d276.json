{"ast": null, "code": "var Param = function () {\n  function Param(target, e) {\n    this.target = target;\n    this.topTarget = e && e.topTarget;\n  }\n  return Param;\n}();\nvar Draggable = function () {\n  function Draggable(handler) {\n    this.handler = handler;\n    handler.on('mousedown', this._dragStart, this);\n    handler.on('mousemove', this._drag, this);\n    handler.on('mouseup', this._dragEnd, this);\n  }\n  Draggable.prototype._dragStart = function (e) {\n    var draggingTarget = e.target;\n    while (draggingTarget && !draggingTarget.draggable) {\n      draggingTarget = draggingTarget.parent || draggingTarget.__hostTarget;\n    }\n    if (draggingTarget) {\n      this._draggingTarget = draggingTarget;\n      draggingTarget.dragging = true;\n      this._x = e.offsetX;\n      this._y = e.offsetY;\n      this.handler.dispatchToElement(new Param(draggingTarget, e), 'dragstart', e.event);\n    }\n  };\n  Draggable.prototype._drag = function (e) {\n    var draggingTarget = this._draggingTarget;\n    if (draggingTarget) {\n      var x = e.offsetX;\n      var y = e.offsetY;\n      var dx = x - this._x;\n      var dy = y - this._y;\n      this._x = x;\n      this._y = y;\n      draggingTarget.drift(dx, dy, e);\n      this.handler.dispatchToElement(new Param(draggingTarget, e), 'drag', e.event);\n      var dropTarget = this.handler.findHover(x, y, draggingTarget).target;\n      var lastDropTarget = this._dropTarget;\n      this._dropTarget = dropTarget;\n      if (draggingTarget !== dropTarget) {\n        if (lastDropTarget && dropTarget !== lastDropTarget) {\n          this.handler.dispatchToElement(new Param(lastDropTarget, e), 'dragleave', e.event);\n        }\n        if (dropTarget && dropTarget !== lastDropTarget) {\n          this.handler.dispatchToElement(new Param(dropTarget, e), 'dragenter', e.event);\n        }\n      }\n    }\n  };\n  Draggable.prototype._dragEnd = function (e) {\n    var draggingTarget = this._draggingTarget;\n    if (draggingTarget) {\n      draggingTarget.dragging = false;\n    }\n    this.handler.dispatchToElement(new Param(draggingTarget, e), 'dragend', e.event);\n    if (this._dropTarget) {\n      this.handler.dispatchToElement(new Param(this._dropTarget, e), 'drop', e.event);\n    }\n    this._draggingTarget = null;\n    this._dropTarget = null;\n  };\n  return Draggable;\n}();\nexport default Draggable;", "map": {"version": 3, "names": ["Param", "target", "e", "topTarget", "Draggable", "handler", "on", "_dragStart", "_drag", "_dragEnd", "prototype", "<PERSON><PERSON><PERSON><PERSON>", "draggable", "parent", "__host<PERSON><PERSON>get", "_dragging<PERSON><PERSON><PERSON>", "dragging", "_x", "offsetX", "_y", "offsetY", "dispatchToElement", "event", "x", "y", "dx", "dy", "drift", "drop<PERSON>ar<PERSON>", "findHover", "lastDropTarget", "_dropTarget"], "sources": ["E:/AI/SmartCV/node_modules/zrender/lib/mixin/Draggable.js"], "sourcesContent": ["var Param = (function () {\n    function Param(target, e) {\n        this.target = target;\n        this.topTarget = e && e.topTarget;\n    }\n    return Param;\n}());\nvar Draggable = (function () {\n    function Draggable(handler) {\n        this.handler = handler;\n        handler.on('mousedown', this._dragStart, this);\n        handler.on('mousemove', this._drag, this);\n        handler.on('mouseup', this._dragEnd, this);\n    }\n    Draggable.prototype._dragStart = function (e) {\n        var draggingTarget = e.target;\n        while (draggingTarget && !draggingTarget.draggable) {\n            draggingTarget = draggingTarget.parent || draggingTarget.__hostTarget;\n        }\n        if (draggingTarget) {\n            this._draggingTarget = draggingTarget;\n            draggingTarget.dragging = true;\n            this._x = e.offsetX;\n            this._y = e.offsetY;\n            this.handler.dispatchToElement(new Param(draggingTarget, e), 'dragstart', e.event);\n        }\n    };\n    Draggable.prototype._drag = function (e) {\n        var draggingTarget = this._draggingTarget;\n        if (draggingTarget) {\n            var x = e.offsetX;\n            var y = e.offsetY;\n            var dx = x - this._x;\n            var dy = y - this._y;\n            this._x = x;\n            this._y = y;\n            draggingTarget.drift(dx, dy, e);\n            this.handler.dispatchToElement(new Param(draggingTarget, e), 'drag', e.event);\n            var dropTarget = this.handler.findHover(x, y, draggingTarget).target;\n            var lastDropTarget = this._dropTarget;\n            this._dropTarget = dropTarget;\n            if (draggingTarget !== dropTarget) {\n                if (lastDropTarget && dropTarget !== lastDropTarget) {\n                    this.handler.dispatchToElement(new Param(lastDropTarget, e), 'dragleave', e.event);\n                }\n                if (dropTarget && dropTarget !== lastDropTarget) {\n                    this.handler.dispatchToElement(new Param(dropTarget, e), 'dragenter', e.event);\n                }\n            }\n        }\n    };\n    Draggable.prototype._dragEnd = function (e) {\n        var draggingTarget = this._draggingTarget;\n        if (draggingTarget) {\n            draggingTarget.dragging = false;\n        }\n        this.handler.dispatchToElement(new Param(draggingTarget, e), 'dragend', e.event);\n        if (this._dropTarget) {\n            this.handler.dispatchToElement(new Param(this._dropTarget, e), 'drop', e.event);\n        }\n        this._draggingTarget = null;\n        this._dropTarget = null;\n    };\n    return Draggable;\n}());\nexport default Draggable;\n"], "mappings": "AAAA,IAAIA,KAAK,GAAI,YAAY;EACrB,SAASA,KAAKA,CAACC,MAAM,EAAEC,CAAC,EAAE;IACtB,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACE,SAAS,GAAGD,CAAC,IAAIA,CAAC,CAACC,SAAS;EACrC;EACA,OAAOH,KAAK;AAChB,CAAC,CAAC,CAAE;AACJ,IAAII,SAAS,GAAI,YAAY;EACzB,SAASA,SAASA,CAACC,OAAO,EAAE;IACxB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtBA,OAAO,CAACC,EAAE,CAAC,WAAW,EAAE,IAAI,CAACC,UAAU,EAAE,IAAI,CAAC;IAC9CF,OAAO,CAACC,EAAE,CAAC,WAAW,EAAE,IAAI,CAACE,KAAK,EAAE,IAAI,CAAC;IACzCH,OAAO,CAACC,EAAE,CAAC,SAAS,EAAE,IAAI,CAACG,QAAQ,EAAE,IAAI,CAAC;EAC9C;EACAL,SAAS,CAACM,SAAS,CAACH,UAAU,GAAG,UAAUL,CAAC,EAAE;IAC1C,IAAIS,cAAc,GAAGT,CAAC,CAACD,MAAM;IAC7B,OAAOU,cAAc,IAAI,CAACA,cAAc,CAACC,SAAS,EAAE;MAChDD,cAAc,GAAGA,cAAc,CAACE,MAAM,IAAIF,cAAc,CAACG,YAAY;IACzE;IACA,IAAIH,cAAc,EAAE;MAChB,IAAI,CAACI,eAAe,GAAGJ,cAAc;MACrCA,cAAc,CAACK,QAAQ,GAAG,IAAI;MAC9B,IAAI,CAACC,EAAE,GAAGf,CAAC,CAACgB,OAAO;MACnB,IAAI,CAACC,EAAE,GAAGjB,CAAC,CAACkB,OAAO;MACnB,IAAI,CAACf,OAAO,CAACgB,iBAAiB,CAAC,IAAIrB,KAAK,CAACW,cAAc,EAAET,CAAC,CAAC,EAAE,WAAW,EAAEA,CAAC,CAACoB,KAAK,CAAC;IACtF;EACJ,CAAC;EACDlB,SAAS,CAACM,SAAS,CAACF,KAAK,GAAG,UAAUN,CAAC,EAAE;IACrC,IAAIS,cAAc,GAAG,IAAI,CAACI,eAAe;IACzC,IAAIJ,cAAc,EAAE;MAChB,IAAIY,CAAC,GAAGrB,CAAC,CAACgB,OAAO;MACjB,IAAIM,CAAC,GAAGtB,CAAC,CAACkB,OAAO;MACjB,IAAIK,EAAE,GAAGF,CAAC,GAAG,IAAI,CAACN,EAAE;MACpB,IAAIS,EAAE,GAAGF,CAAC,GAAG,IAAI,CAACL,EAAE;MACpB,IAAI,CAACF,EAAE,GAAGM,CAAC;MACX,IAAI,CAACJ,EAAE,GAAGK,CAAC;MACXb,cAAc,CAACgB,KAAK,CAACF,EAAE,EAAEC,EAAE,EAAExB,CAAC,CAAC;MAC/B,IAAI,CAACG,OAAO,CAACgB,iBAAiB,CAAC,IAAIrB,KAAK,CAACW,cAAc,EAAET,CAAC,CAAC,EAAE,MAAM,EAAEA,CAAC,CAACoB,KAAK,CAAC;MAC7E,IAAIM,UAAU,GAAG,IAAI,CAACvB,OAAO,CAACwB,SAAS,CAACN,CAAC,EAAEC,CAAC,EAAEb,cAAc,CAAC,CAACV,MAAM;MACpE,IAAI6B,cAAc,GAAG,IAAI,CAACC,WAAW;MACrC,IAAI,CAACA,WAAW,GAAGH,UAAU;MAC7B,IAAIjB,cAAc,KAAKiB,UAAU,EAAE;QAC/B,IAAIE,cAAc,IAAIF,UAAU,KAAKE,cAAc,EAAE;UACjD,IAAI,CAACzB,OAAO,CAACgB,iBAAiB,CAAC,IAAIrB,KAAK,CAAC8B,cAAc,EAAE5B,CAAC,CAAC,EAAE,WAAW,EAAEA,CAAC,CAACoB,KAAK,CAAC;QACtF;QACA,IAAIM,UAAU,IAAIA,UAAU,KAAKE,cAAc,EAAE;UAC7C,IAAI,CAACzB,OAAO,CAACgB,iBAAiB,CAAC,IAAIrB,KAAK,CAAC4B,UAAU,EAAE1B,CAAC,CAAC,EAAE,WAAW,EAAEA,CAAC,CAACoB,KAAK,CAAC;QAClF;MACJ;IACJ;EACJ,CAAC;EACDlB,SAAS,CAACM,SAAS,CAACD,QAAQ,GAAG,UAAUP,CAAC,EAAE;IACxC,IAAIS,cAAc,GAAG,IAAI,CAACI,eAAe;IACzC,IAAIJ,cAAc,EAAE;MAChBA,cAAc,CAACK,QAAQ,GAAG,KAAK;IACnC;IACA,IAAI,CAACX,OAAO,CAACgB,iBAAiB,CAAC,IAAIrB,KAAK,CAACW,cAAc,EAAET,CAAC,CAAC,EAAE,SAAS,EAAEA,CAAC,CAACoB,KAAK,CAAC;IAChF,IAAI,IAAI,CAACS,WAAW,EAAE;MAClB,IAAI,CAAC1B,OAAO,CAACgB,iBAAiB,CAAC,IAAIrB,KAAK,CAAC,IAAI,CAAC+B,WAAW,EAAE7B,CAAC,CAAC,EAAE,MAAM,EAAEA,CAAC,CAACoB,KAAK,CAAC;IACnF;IACA,IAAI,CAACP,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACgB,WAAW,GAAG,IAAI;EAC3B,CAAC;EACD,OAAO3B,SAAS;AACpB,CAAC,CAAC,CAAE;AACJ,eAAeA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}