{"ast": null, "code": "import { isAroundZero } from './helper.js';\nvar mathSin = Math.sin;\nvar mathCos = Math.cos;\nvar PI = Math.PI;\nvar PI2 = Math.PI * 2;\nvar degree = 180 / PI;\nvar SVGPathRebuilder = function () {\n  function SVGPathRebuilder() {}\n  SVGPathRebuilder.prototype.reset = function (precision) {\n    this._start = true;\n    this._d = [];\n    this._str = '';\n    this._p = Math.pow(10, precision || 4);\n  };\n  SVGPathRebuilder.prototype.moveTo = function (x, y) {\n    this._add('M', x, y);\n  };\n  SVGPathRebuilder.prototype.lineTo = function (x, y) {\n    this._add('L', x, y);\n  };\n  SVGPathRebuilder.prototype.bezierCurveTo = function (x, y, x2, y2, x3, y3) {\n    this._add('C', x, y, x2, y2, x3, y3);\n  };\n  SVGPathRebuilder.prototype.quadraticCurveTo = function (x, y, x2, y2) {\n    this._add('Q', x, y, x2, y2);\n  };\n  SVGPathRebuilder.prototype.arc = function (cx, cy, r, startAngle, endAngle, anticlockwise) {\n    this.ellipse(cx, cy, r, r, 0, startAngle, endAngle, anticlockwise);\n  };\n  SVGPathRebuilder.prototype.ellipse = function (cx, cy, rx, ry, psi, startAngle, endAngle, anticlockwise) {\n    var dTheta = endAngle - startAngle;\n    var clockwise = !anticlockwise;\n    var dThetaPositive = Math.abs(dTheta);\n    var isCircle = isAroundZero(dThetaPositive - PI2) || (clockwise ? dTheta >= PI2 : -dTheta >= PI2);\n    var unifiedTheta = dTheta > 0 ? dTheta % PI2 : dTheta % PI2 + PI2;\n    var large = false;\n    if (isCircle) {\n      large = true;\n    } else if (isAroundZero(dThetaPositive)) {\n      large = false;\n    } else {\n      large = unifiedTheta >= PI === !!clockwise;\n    }\n    var x0 = cx + rx * mathCos(startAngle);\n    var y0 = cy + ry * mathSin(startAngle);\n    if (this._start) {\n      this._add('M', x0, y0);\n    }\n    var xRot = Math.round(psi * degree);\n    if (isCircle) {\n      var p = 1 / this._p;\n      var dTheta_1 = (clockwise ? 1 : -1) * (PI2 - p);\n      this._add('A', rx, ry, xRot, 1, +clockwise, cx + rx * mathCos(startAngle + dTheta_1), cy + ry * mathSin(startAngle + dTheta_1));\n      if (p > 1e-2) {\n        this._add('A', rx, ry, xRot, 0, +clockwise, x0, y0);\n      }\n    } else {\n      var x = cx + rx * mathCos(endAngle);\n      var y = cy + ry * mathSin(endAngle);\n      this._add('A', rx, ry, xRot, +large, +clockwise, x, y);\n    }\n  };\n  SVGPathRebuilder.prototype.rect = function (x, y, w, h) {\n    this._add('M', x, y);\n    this._add('l', w, 0);\n    this._add('l', 0, h);\n    this._add('l', -w, 0);\n    this._add('Z');\n  };\n  SVGPathRebuilder.prototype.closePath = function () {\n    if (this._d.length > 0) {\n      this._add('Z');\n    }\n  };\n  SVGPathRebuilder.prototype._add = function (cmd, a, b, c, d, e, f, g, h) {\n    var vals = [];\n    var p = this._p;\n    for (var i = 1; i < arguments.length; i++) {\n      var val = arguments[i];\n      if (isNaN(val)) {\n        this._invalid = true;\n        return;\n      }\n      vals.push(Math.round(val * p) / p);\n    }\n    this._d.push(cmd + vals.join(' '));\n    this._start = cmd === 'Z';\n  };\n  SVGPathRebuilder.prototype.generateStr = function () {\n    this._str = this._invalid ? '' : this._d.join('');\n    this._d = [];\n  };\n  SVGPathRebuilder.prototype.getStr = function () {\n    return this._str;\n  };\n  return SVGPathRebuilder;\n}();\nexport default SVGPathRebuilder;", "map": {"version": 3, "names": ["isAroundZero", "mathSin", "Math", "sin", "mathCos", "cos", "PI", "PI2", "degree", "SVGPathRebuilder", "prototype", "reset", "precision", "_start", "_d", "_str", "_p", "pow", "moveTo", "x", "y", "_add", "lineTo", "bezierCurveTo", "x2", "y2", "x3", "y3", "quadraticCurveTo", "arc", "cx", "cy", "r", "startAngle", "endAngle", "anticlockwise", "ellipse", "rx", "ry", "psi", "d<PERSON><PERSON><PERSON>", "clockwise", "dThetaPositive", "abs", "isCircle", "unifiedTheta", "large", "x0", "y0", "xRot", "round", "p", "dTheta_1", "rect", "w", "h", "closePath", "length", "cmd", "a", "b", "c", "d", "e", "f", "g", "vals", "i", "arguments", "val", "isNaN", "_invalid", "push", "join", "generateStr", "getStr"], "sources": ["E:/AI/SmartCV/node_modules/zrender/lib/svg/SVGPathRebuilder.js"], "sourcesContent": ["import { isAroundZero } from './helper.js';\nvar mathSin = Math.sin;\nvar mathCos = Math.cos;\nvar PI = Math.PI;\nvar PI2 = Math.PI * 2;\nvar degree = 180 / PI;\nvar SVGPathRebuilder = (function () {\n    function SVGPathRebuilder() {\n    }\n    SVGPathRebuilder.prototype.reset = function (precision) {\n        this._start = true;\n        this._d = [];\n        this._str = '';\n        this._p = Math.pow(10, precision || 4);\n    };\n    SVGPathRebuilder.prototype.moveTo = function (x, y) {\n        this._add('M', x, y);\n    };\n    SVGPathRebuilder.prototype.lineTo = function (x, y) {\n        this._add('L', x, y);\n    };\n    SVGPathRebuilder.prototype.bezierCurveTo = function (x, y, x2, y2, x3, y3) {\n        this._add('C', x, y, x2, y2, x3, y3);\n    };\n    SVGPathRebuilder.prototype.quadraticCurveTo = function (x, y, x2, y2) {\n        this._add('Q', x, y, x2, y2);\n    };\n    SVGPathRebuilder.prototype.arc = function (cx, cy, r, startAngle, endAngle, anticlockwise) {\n        this.ellipse(cx, cy, r, r, 0, startAngle, endAngle, anticlockwise);\n    };\n    SVGPathRebuilder.prototype.ellipse = function (cx, cy, rx, ry, psi, startAngle, endAngle, anticlockwise) {\n        var dTheta = endAngle - startAngle;\n        var clockwise = !anticlockwise;\n        var dThetaPositive = Math.abs(dTheta);\n        var isCircle = isAroundZero(dThetaPositive - PI2)\n            || (clockwise ? dTheta >= PI2 : -dTheta >= PI2);\n        var unifiedTheta = dTheta > 0 ? dTheta % PI2 : (dTheta % PI2 + PI2);\n        var large = false;\n        if (isCircle) {\n            large = true;\n        }\n        else if (isAroundZero(dThetaPositive)) {\n            large = false;\n        }\n        else {\n            large = (unifiedTheta >= PI) === !!clockwise;\n        }\n        var x0 = cx + rx * mathCos(startAngle);\n        var y0 = cy + ry * mathSin(startAngle);\n        if (this._start) {\n            this._add('M', x0, y0);\n        }\n        var xRot = Math.round(psi * degree);\n        if (isCircle) {\n            var p = 1 / this._p;\n            var dTheta_1 = (clockwise ? 1 : -1) * (PI2 - p);\n            this._add('A', rx, ry, xRot, 1, +clockwise, cx + rx * mathCos(startAngle + dTheta_1), cy + ry * mathSin(startAngle + dTheta_1));\n            if (p > 1e-2) {\n                this._add('A', rx, ry, xRot, 0, +clockwise, x0, y0);\n            }\n        }\n        else {\n            var x = cx + rx * mathCos(endAngle);\n            var y = cy + ry * mathSin(endAngle);\n            this._add('A', rx, ry, xRot, +large, +clockwise, x, y);\n        }\n    };\n    SVGPathRebuilder.prototype.rect = function (x, y, w, h) {\n        this._add('M', x, y);\n        this._add('l', w, 0);\n        this._add('l', 0, h);\n        this._add('l', -w, 0);\n        this._add('Z');\n    };\n    SVGPathRebuilder.prototype.closePath = function () {\n        if (this._d.length > 0) {\n            this._add('Z');\n        }\n    };\n    SVGPathRebuilder.prototype._add = function (cmd, a, b, c, d, e, f, g, h) {\n        var vals = [];\n        var p = this._p;\n        for (var i = 1; i < arguments.length; i++) {\n            var val = arguments[i];\n            if (isNaN(val)) {\n                this._invalid = true;\n                return;\n            }\n            vals.push(Math.round(val * p) / p);\n        }\n        this._d.push(cmd + vals.join(' '));\n        this._start = cmd === 'Z';\n    };\n    SVGPathRebuilder.prototype.generateStr = function () {\n        this._str = this._invalid ? '' : this._d.join('');\n        this._d = [];\n    };\n    SVGPathRebuilder.prototype.getStr = function () {\n        return this._str;\n    };\n    return SVGPathRebuilder;\n}());\nexport default SVGPathRebuilder;\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,aAAa;AAC1C,IAAIC,OAAO,GAAGC,IAAI,CAACC,GAAG;AACtB,IAAIC,OAAO,GAAGF,IAAI,CAACG,GAAG;AACtB,IAAIC,EAAE,GAAGJ,IAAI,CAACI,EAAE;AAChB,IAAIC,GAAG,GAAGL,IAAI,CAACI,EAAE,GAAG,CAAC;AACrB,IAAIE,MAAM,GAAG,GAAG,GAAGF,EAAE;AACrB,IAAIG,gBAAgB,GAAI,YAAY;EAChC,SAASA,gBAAgBA,CAAA,EAAG,CAC5B;EACAA,gBAAgB,CAACC,SAAS,CAACC,KAAK,GAAG,UAAUC,SAAS,EAAE;IACpD,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,EAAE,GAAG,EAAE;IACZ,IAAI,CAACC,IAAI,GAAG,EAAE;IACd,IAAI,CAACC,EAAE,GAAGd,IAAI,CAACe,GAAG,CAAC,EAAE,EAAEL,SAAS,IAAI,CAAC,CAAC;EAC1C,CAAC;EACDH,gBAAgB,CAACC,SAAS,CAACQ,MAAM,GAAG,UAAUC,CAAC,EAAEC,CAAC,EAAE;IAChD,IAAI,CAACC,IAAI,CAAC,GAAG,EAAEF,CAAC,EAAEC,CAAC,CAAC;EACxB,CAAC;EACDX,gBAAgB,CAACC,SAAS,CAACY,MAAM,GAAG,UAAUH,CAAC,EAAEC,CAAC,EAAE;IAChD,IAAI,CAACC,IAAI,CAAC,GAAG,EAAEF,CAAC,EAAEC,CAAC,CAAC;EACxB,CAAC;EACDX,gBAAgB,CAACC,SAAS,CAACa,aAAa,GAAG,UAAUJ,CAAC,EAAEC,CAAC,EAAEI,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;IACvE,IAAI,CAACN,IAAI,CAAC,GAAG,EAAEF,CAAC,EAAEC,CAAC,EAAEI,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;EACxC,CAAC;EACDlB,gBAAgB,CAACC,SAAS,CAACkB,gBAAgB,GAAG,UAAUT,CAAC,EAAEC,CAAC,EAAEI,EAAE,EAAEC,EAAE,EAAE;IAClE,IAAI,CAACJ,IAAI,CAAC,GAAG,EAAEF,CAAC,EAAEC,CAAC,EAAEI,EAAE,EAAEC,EAAE,CAAC;EAChC,CAAC;EACDhB,gBAAgB,CAACC,SAAS,CAACmB,GAAG,GAAG,UAAUC,EAAE,EAAEC,EAAE,EAAEC,CAAC,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,aAAa,EAAE;IACvF,IAAI,CAACC,OAAO,CAACN,EAAE,EAAEC,EAAE,EAAEC,CAAC,EAAEA,CAAC,EAAE,CAAC,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,aAAa,CAAC;EACtE,CAAC;EACD1B,gBAAgB,CAACC,SAAS,CAAC0B,OAAO,GAAG,UAAUN,EAAE,EAAEC,EAAE,EAAEM,EAAE,EAAEC,EAAE,EAAEC,GAAG,EAAEN,UAAU,EAAEC,QAAQ,EAAEC,aAAa,EAAE;IACrG,IAAIK,MAAM,GAAGN,QAAQ,GAAGD,UAAU;IAClC,IAAIQ,SAAS,GAAG,CAACN,aAAa;IAC9B,IAAIO,cAAc,GAAGxC,IAAI,CAACyC,GAAG,CAACH,MAAM,CAAC;IACrC,IAAII,QAAQ,GAAG5C,YAAY,CAAC0C,cAAc,GAAGnC,GAAG,CAAC,KACzCkC,SAAS,GAAGD,MAAM,IAAIjC,GAAG,GAAG,CAACiC,MAAM,IAAIjC,GAAG,CAAC;IACnD,IAAIsC,YAAY,GAAGL,MAAM,GAAG,CAAC,GAAGA,MAAM,GAAGjC,GAAG,GAAIiC,MAAM,GAAGjC,GAAG,GAAGA,GAAI;IACnE,IAAIuC,KAAK,GAAG,KAAK;IACjB,IAAIF,QAAQ,EAAE;MACVE,KAAK,GAAG,IAAI;IAChB,CAAC,MACI,IAAI9C,YAAY,CAAC0C,cAAc,CAAC,EAAE;MACnCI,KAAK,GAAG,KAAK;IACjB,CAAC,MACI;MACDA,KAAK,GAAID,YAAY,IAAIvC,EAAE,KAAM,CAAC,CAACmC,SAAS;IAChD;IACA,IAAIM,EAAE,GAAGjB,EAAE,GAAGO,EAAE,GAAGjC,OAAO,CAAC6B,UAAU,CAAC;IACtC,IAAIe,EAAE,GAAGjB,EAAE,GAAGO,EAAE,GAAGrC,OAAO,CAACgC,UAAU,CAAC;IACtC,IAAI,IAAI,CAACpB,MAAM,EAAE;MACb,IAAI,CAACQ,IAAI,CAAC,GAAG,EAAE0B,EAAE,EAAEC,EAAE,CAAC;IAC1B;IACA,IAAIC,IAAI,GAAG/C,IAAI,CAACgD,KAAK,CAACX,GAAG,GAAG/B,MAAM,CAAC;IACnC,IAAIoC,QAAQ,EAAE;MACV,IAAIO,CAAC,GAAG,CAAC,GAAG,IAAI,CAACnC,EAAE;MACnB,IAAIoC,QAAQ,GAAG,CAACX,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC,KAAKlC,GAAG,GAAG4C,CAAC,CAAC;MAC/C,IAAI,CAAC9B,IAAI,CAAC,GAAG,EAAEgB,EAAE,EAAEC,EAAE,EAAEW,IAAI,EAAE,CAAC,EAAE,CAACR,SAAS,EAAEX,EAAE,GAAGO,EAAE,GAAGjC,OAAO,CAAC6B,UAAU,GAAGmB,QAAQ,CAAC,EAAErB,EAAE,GAAGO,EAAE,GAAGrC,OAAO,CAACgC,UAAU,GAAGmB,QAAQ,CAAC,CAAC;MAC/H,IAAID,CAAC,GAAG,IAAI,EAAE;QACV,IAAI,CAAC9B,IAAI,CAAC,GAAG,EAAEgB,EAAE,EAAEC,EAAE,EAAEW,IAAI,EAAE,CAAC,EAAE,CAACR,SAAS,EAAEM,EAAE,EAAEC,EAAE,CAAC;MACvD;IACJ,CAAC,MACI;MACD,IAAI7B,CAAC,GAAGW,EAAE,GAAGO,EAAE,GAAGjC,OAAO,CAAC8B,QAAQ,CAAC;MACnC,IAAId,CAAC,GAAGW,EAAE,GAAGO,EAAE,GAAGrC,OAAO,CAACiC,QAAQ,CAAC;MACnC,IAAI,CAACb,IAAI,CAAC,GAAG,EAAEgB,EAAE,EAAEC,EAAE,EAAEW,IAAI,EAAE,CAACH,KAAK,EAAE,CAACL,SAAS,EAAEtB,CAAC,EAAEC,CAAC,CAAC;IAC1D;EACJ,CAAC;EACDX,gBAAgB,CAACC,SAAS,CAAC2C,IAAI,GAAG,UAAUlC,CAAC,EAAEC,CAAC,EAAEkC,CAAC,EAAEC,CAAC,EAAE;IACpD,IAAI,CAAClC,IAAI,CAAC,GAAG,EAAEF,CAAC,EAAEC,CAAC,CAAC;IACpB,IAAI,CAACC,IAAI,CAAC,GAAG,EAAEiC,CAAC,EAAE,CAAC,CAAC;IACpB,IAAI,CAACjC,IAAI,CAAC,GAAG,EAAE,CAAC,EAAEkC,CAAC,CAAC;IACpB,IAAI,CAAClC,IAAI,CAAC,GAAG,EAAE,CAACiC,CAAC,EAAE,CAAC,CAAC;IACrB,IAAI,CAACjC,IAAI,CAAC,GAAG,CAAC;EAClB,CAAC;EACDZ,gBAAgB,CAACC,SAAS,CAAC8C,SAAS,GAAG,YAAY;IAC/C,IAAI,IAAI,CAAC1C,EAAE,CAAC2C,MAAM,GAAG,CAAC,EAAE;MACpB,IAAI,CAACpC,IAAI,CAAC,GAAG,CAAC;IAClB;EACJ,CAAC;EACDZ,gBAAgB,CAACC,SAAS,CAACW,IAAI,GAAG,UAAUqC,GAAG,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEV,CAAC,EAAE;IACrE,IAAIW,IAAI,GAAG,EAAE;IACb,IAAIf,CAAC,GAAG,IAAI,CAACnC,EAAE;IACf,KAAK,IAAImD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACX,MAAM,EAAEU,CAAC,EAAE,EAAE;MACvC,IAAIE,GAAG,GAAGD,SAAS,CAACD,CAAC,CAAC;MACtB,IAAIG,KAAK,CAACD,GAAG,CAAC,EAAE;QACZ,IAAI,CAACE,QAAQ,GAAG,IAAI;QACpB;MACJ;MACAL,IAAI,CAACM,IAAI,CAACtE,IAAI,CAACgD,KAAK,CAACmB,GAAG,GAAGlB,CAAC,CAAC,GAAGA,CAAC,CAAC;IACtC;IACA,IAAI,CAACrC,EAAE,CAAC0D,IAAI,CAACd,GAAG,GAAGQ,IAAI,CAACO,IAAI,CAAC,GAAG,CAAC,CAAC;IAClC,IAAI,CAAC5D,MAAM,GAAG6C,GAAG,KAAK,GAAG;EAC7B,CAAC;EACDjD,gBAAgB,CAACC,SAAS,CAACgE,WAAW,GAAG,YAAY;IACjD,IAAI,CAAC3D,IAAI,GAAG,IAAI,CAACwD,QAAQ,GAAG,EAAE,GAAG,IAAI,CAACzD,EAAE,CAAC2D,IAAI,CAAC,EAAE,CAAC;IACjD,IAAI,CAAC3D,EAAE,GAAG,EAAE;EAChB,CAAC;EACDL,gBAAgB,CAACC,SAAS,CAACiE,MAAM,GAAG,YAAY;IAC5C,OAAO,IAAI,CAAC5D,IAAI;EACpB,CAAC;EACD,OAAON,gBAAgB;AAC3B,CAAC,CAAC,CAAE;AACJ,eAAeA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}