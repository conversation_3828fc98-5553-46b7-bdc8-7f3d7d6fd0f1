#!/usr/bin/env python3
"""
测试完整的优化流程
"""

import json
from app import convert_json_to_resume_text
from ai_optimizer import create_enhanced_fallback_optimization

def test_full_optimization_flow():
    """测试完整的优化流程"""
    
    # 模拟AI优化器返回的结构化数据
    mock_optimization_result = {
        'optimized_resume_json': [
            {
                "section": "Professional Summary",
                "original": "Software developer with experience.",
                "optimized": "Experienced Software Developer with 3+ years in full-stack development, specializing in Python, React, and cloud technologies.",
                "jd_keywords": ["Python", "React", "cloud technologies"]
            },
            {
                "section": "Experience",
                "original": "Developer at XYZ Corp",
                "optimized": "Software Developer | XYZ Corp | 2021-2024\n• Developed scalable web applications using Python and React\n• Improved system performance by 30%",
                "jd_keywords": ["Python", "React", "web applications"]
            }
        ],
        'jd_json': {
            "title": "Software Developer",
            "core_skills": ["Python", "React", "JavaScript"],
            "soft_skills": ["teamwork", "communication"],
            "keywords": {"Python": 5, "React": 4, "JavaScript": 3}
        },
        'match_json': {
            "matched_keywords": ["Python", "React"],
            "missing_keywords": ["JavaScript", "Docker"],
            "suggested_sections": []
        },
        'original_match_score': {
            'overall_match_score': 65.0,
            'breakdown': {
                'hard_skills': {'score': 70.0, 'matched': ['Python'], 'total': ['Python', 'React', 'JavaScript'], 'weight': '40%'}
            }
        },
        'ats_risks': [],
        'error': ''
    }
    
    print("Testing full optimization flow...")
    print("=" * 60)
    
    # 测试正常情况
    print("1. Testing normal case with valid JSON:")
    optimized_resume_json = mock_optimization_result.get('optimized_resume_json', [])
    if isinstance(optimized_resume_json, list) and optimized_resume_json:
        optimized_resume = convert_json_to_resume_text(optimized_resume_json)
        print("✓ Conversion successful")
        print(f"Resume length: {len(optimized_resume)} characters")
        print("First 200 characters:")
        print(optimized_resume[:200] + "...")
    else:
        print("✗ No valid JSON data")
    
    print("\n" + "=" * 60)
    
    # 测试空JSON情况
    print("2. Testing empty JSON case:")
    empty_result = {
        'optimized_resume_json': [],
        'error': 'AI optimization returned empty result'
    }
    
    optimized_resume_json = empty_result.get('optimized_resume_json', [])
    if isinstance(optimized_resume_json, list) and optimized_resume_json:
        optimized_resume = convert_json_to_resume_text(optimized_resume_json)
    else:
        error_msg = empty_result.get('error', 'AI optimization returned empty result')
        print(f"Using fallback optimization due to: {error_msg}")
        resume_text = "John Doe\nSoftware Developer\nExperience: 3 years in web development"
        job_description = "We are looking for a Python developer with React experience"
        optimized_resume = create_enhanced_fallback_optimization(resume_text, job_description, error_msg)
        print("✓ Fallback optimization successful")
        print(f"Fallback resume length: {len(optimized_resume)} characters")
    
    print("\n" + "=" * 60)
    
    # 测试错误情况
    print("3. Testing error case:")
    error_result = {
        'optimized_resume_json': {"error": "JSON parsing failed"},
        'error': 'Invalid JSON format'
    }
    
    optimized_resume_json = error_result.get('optimized_resume_json', [])
    if isinstance(optimized_resume_json, list) and optimized_resume_json:
        optimized_resume = convert_json_to_resume_text(optimized_resume_json)
    else:
        error_msg = error_result.get('error', 'Unknown error')
        print(f"Using fallback optimization due to: {error_msg}")
        resume_text = "Jane Smith\nData Analyst\nSkills: Python, SQL"
        job_description = "Looking for a data scientist with machine learning experience"
        optimized_resume = create_enhanced_fallback_optimization(resume_text, job_description, error_msg)
        print("✓ Error handling successful")
    
    print("\n" + "=" * 60)
    print("All tests completed successfully!")

if __name__ == "__main__":
    test_full_optimization_flow()
