#!/bin/bash

echo "========================================"
echo "   SmartCV AI简历优化平台 - 启动脚本"
echo "========================================"
echo

echo "正在检查项目环境..."

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "[错误] 未检测到Python3，请先安装Python 3.8+"
    exit 1
fi

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "[错误] 未检测到Node.js，请先安装Node.js 16+"
    exit 1
fi

echo "[✓] Python和Node.js环境检查通过"

# 启动后端服务
echo
echo "正在启动后端服务..."
cd backend

if [ ! -f .env ]; then
    echo "[警告] 未找到.env文件，请复制env_example.txt为.env并配置OpenAI API Key"
    echo "按Enter键继续..."
    read
fi

echo "安装后端依赖..."
pip3 install -r requirements.txt

echo "启动Flask后端服务..."
python3 app.py &
BACKEND_PID=$!

# 等待后端启动
sleep 3

# 启动前端服务
echo
echo "正在启动前端服务..."
cd ../frontend

echo "安装前端依赖..."
npm install

echo "启动React前端服务..."
npm start &
FRONTEND_PID=$!

echo
echo "========================================"
echo "    启动完成！"
echo "========================================"
echo "后端服务: http://localhost:5000"
echo "前端服务: http://localhost:3000"
echo
echo "请确保已在backend/.env文件中配置OpenAI API Key"
echo
echo "按Ctrl+C停止所有服务"

# 等待用户中断
trap "echo '正在停止服务...'; kill $BACKEND_PID $FRONTEND_PID; exit" INT
wait 