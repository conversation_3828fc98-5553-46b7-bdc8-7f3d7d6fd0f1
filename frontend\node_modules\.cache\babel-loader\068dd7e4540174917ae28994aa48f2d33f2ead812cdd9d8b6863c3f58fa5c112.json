{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/**\r\n * LegendVisualProvider is an bridge that pick encoded color from data and\r\n * provide to the legend component.\r\n */\nvar LegendVisualProvider = /** @class */function () {\n  function LegendVisualProvider(\n  // Function to get data after filtered. It stores all the encoding info\n  getDataWithEncodedVisual,\n  // Function to get raw data before filtered.\n  getRawData) {\n    this._getDataWithEncodedVisual = getDataWithEncodedVisual;\n    this._getRawData = getRawData;\n  }\n  LegendVisualProvider.prototype.getAllNames = function () {\n    var rawData = this._getRawData();\n    // We find the name from the raw data. In case it's filtered by the legend component.\n    // Normally, the name can be found in rawData, but can't be found in filtered data will display as gray.\n    return rawData.mapArray(rawData.getName);\n  };\n  LegendVisualProvider.prototype.containName = function (name) {\n    var rawData = this._getRawData();\n    return rawData.indexOfName(name) >= 0;\n  };\n  LegendVisualProvider.prototype.indexOfName = function (name) {\n    // Only get data when necessary.\n    // Because LegendVisualProvider constructor may be new in the stage that data is not prepared yet.\n    // Invoking Series#getData immediately will throw an error.\n    var dataWithEncodedVisual = this._getDataWithEncodedVisual();\n    return dataWithEncodedVisual.indexOfName(name);\n  };\n  LegendVisualProvider.prototype.getItemVisual = function (dataIndex, key) {\n    // Get encoded visual properties from final filtered data.\n    var dataWithEncodedVisual = this._getDataWithEncodedVisual();\n    return dataWithEncodedVisual.getItemVisual(dataIndex, key);\n  };\n  return LegendVisualProvider;\n}();\nexport default LegendVisualProvider;", "map": {"version": 3, "names": ["LegendVisualProvider", "getDataWithEncodedVisual", "getRawData", "_getDataWithEncodedVisual", "_getRawData", "prototype", "getAllNames", "rawData", "mapArray", "getName", "containName", "name", "indexOfName", "dataWithEncodedVisual", "getItemVisual", "dataIndex", "key"], "sources": ["E:/AI/SmartCV/node_modules/echarts/lib/visual/LegendVisualProvider.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/**\r\n * LegendVisualProvider is an bridge that pick encoded color from data and\r\n * provide to the legend component.\r\n */\nvar LegendVisualProvider = /** @class */function () {\n  function LegendVisualProvider(\n  // Function to get data after filtered. It stores all the encoding info\n  getDataWithEncodedVisual,\n  // Function to get raw data before filtered.\n  getRawData) {\n    this._getDataWithEncodedVisual = getDataWithEncodedVisual;\n    this._getRawData = getRawData;\n  }\n  LegendVisualProvider.prototype.getAllNames = function () {\n    var rawData = this._getRawData();\n    // We find the name from the raw data. In case it's filtered by the legend component.\n    // Normally, the name can be found in rawData, but can't be found in filtered data will display as gray.\n    return rawData.mapArray(rawData.getName);\n  };\n  LegendVisualProvider.prototype.containName = function (name) {\n    var rawData = this._getRawData();\n    return rawData.indexOfName(name) >= 0;\n  };\n  LegendVisualProvider.prototype.indexOfName = function (name) {\n    // Only get data when necessary.\n    // Because LegendVisualProvider constructor may be new in the stage that data is not prepared yet.\n    // Invoking Series#getData immediately will throw an error.\n    var dataWithEncodedVisual = this._getDataWithEncodedVisual();\n    return dataWithEncodedVisual.indexOfName(name);\n  };\n  LegendVisualProvider.prototype.getItemVisual = function (dataIndex, key) {\n    // Get encoded visual properties from final filtered data.\n    var dataWithEncodedVisual = this._getDataWithEncodedVisual();\n    return dataWithEncodedVisual.getItemVisual(dataIndex, key);\n  };\n  return LegendVisualProvider;\n}();\nexport default LegendVisualProvider;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,oBAAoB,GAAG,aAAa,YAAY;EAClD,SAASA,oBAAoBA;EAC7B;EACAC,wBAAwB;EACxB;EACAC,UAAU,EAAE;IACV,IAAI,CAACC,yBAAyB,GAAGF,wBAAwB;IACzD,IAAI,CAACG,WAAW,GAAGF,UAAU;EAC/B;EACAF,oBAAoB,CAACK,SAAS,CAACC,WAAW,GAAG,YAAY;IACvD,IAAIC,OAAO,GAAG,IAAI,CAACH,WAAW,CAAC,CAAC;IAChC;IACA;IACA,OAAOG,OAAO,CAACC,QAAQ,CAACD,OAAO,CAACE,OAAO,CAAC;EAC1C,CAAC;EACDT,oBAAoB,CAACK,SAAS,CAACK,WAAW,GAAG,UAAUC,IAAI,EAAE;IAC3D,IAAIJ,OAAO,GAAG,IAAI,CAACH,WAAW,CAAC,CAAC;IAChC,OAAOG,OAAO,CAACK,WAAW,CAACD,IAAI,CAAC,IAAI,CAAC;EACvC,CAAC;EACDX,oBAAoB,CAACK,SAAS,CAACO,WAAW,GAAG,UAAUD,IAAI,EAAE;IAC3D;IACA;IACA;IACA,IAAIE,qBAAqB,GAAG,IAAI,CAACV,yBAAyB,CAAC,CAAC;IAC5D,OAAOU,qBAAqB,CAACD,WAAW,CAACD,IAAI,CAAC;EAChD,CAAC;EACDX,oBAAoB,CAACK,SAAS,CAACS,aAAa,GAAG,UAAUC,SAAS,EAAEC,GAAG,EAAE;IACvE;IACA,IAAIH,qBAAqB,GAAG,IAAI,CAACV,yBAAyB,CAAC,CAAC;IAC5D,OAAOU,qBAAqB,CAACC,aAAa,CAACC,SAAS,EAAEC,GAAG,CAAC;EAC5D,CAAC;EACD,OAAOhB,oBAAoB;AAC7B,CAAC,CAAC,CAAC;AACH,eAAeA,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}