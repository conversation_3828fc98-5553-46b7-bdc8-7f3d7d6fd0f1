{"ast": null, "code": "export function containStroke(x0, y0, x1, y1, lineWidth, x, y) {\n  if (lineWidth === 0) {\n    return false;\n  }\n  var _l = lineWidth;\n  var _a = 0;\n  var _b = x0;\n  if (y > y0 + _l && y > y1 + _l || y < y0 - _l && y < y1 - _l || x > x0 + _l && x > x1 + _l || x < x0 - _l && x < x1 - _l) {\n    return false;\n  }\n  if (x0 !== x1) {\n    _a = (y0 - y1) / (x0 - x1);\n    _b = (x0 * y1 - x1 * y0) / (x0 - x1);\n  } else {\n    return Math.abs(x - x0) <= _l / 2;\n  }\n  var tmp = _a * x - y + _b;\n  var _s = tmp * tmp / (_a * _a + 1);\n  return _s <= _l / 2 * _l / 2;\n}", "map": {"version": 3, "names": ["containStroke", "x0", "y0", "x1", "y1", "lineWidth", "x", "y", "_l", "_a", "_b", "Math", "abs", "tmp", "_s"], "sources": ["E:/AI/SmartCV/node_modules/zrender/lib/contain/line.js"], "sourcesContent": ["export function containStroke(x0, y0, x1, y1, lineWidth, x, y) {\n    if (lineWidth === 0) {\n        return false;\n    }\n    var _l = lineWidth;\n    var _a = 0;\n    var _b = x0;\n    if ((y > y0 + _l && y > y1 + _l)\n        || (y < y0 - _l && y < y1 - _l)\n        || (x > x0 + _l && x > x1 + _l)\n        || (x < x0 - _l && x < x1 - _l)) {\n        return false;\n    }\n    if (x0 !== x1) {\n        _a = (y0 - y1) / (x0 - x1);\n        _b = (x0 * y1 - x1 * y0) / (x0 - x1);\n    }\n    else {\n        return Math.abs(x - x0) <= _l / 2;\n    }\n    var tmp = _a * x - y + _b;\n    var _s = tmp * tmp / (_a * _a + 1);\n    return _s <= _l / 2 * _l / 2;\n}\n"], "mappings": "AAAA,OAAO,SAASA,aAAaA,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,SAAS,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAC3D,IAAIF,SAAS,KAAK,CAAC,EAAE;IACjB,OAAO,KAAK;EAChB;EACA,IAAIG,EAAE,GAAGH,SAAS;EAClB,IAAII,EAAE,GAAG,CAAC;EACV,IAAIC,EAAE,GAAGT,EAAE;EACX,IAAKM,CAAC,GAAGL,EAAE,GAAGM,EAAE,IAAID,CAAC,GAAGH,EAAE,GAAGI,EAAE,IACvBD,CAAC,GAAGL,EAAE,GAAGM,EAAE,IAAID,CAAC,GAAGH,EAAE,GAAGI,EAAG,IAC3BF,CAAC,GAAGL,EAAE,GAAGO,EAAE,IAAIF,CAAC,GAAGH,EAAE,GAAGK,EAAG,IAC3BF,CAAC,GAAGL,EAAE,GAAGO,EAAE,IAAIF,CAAC,GAAGH,EAAE,GAAGK,EAAG,EAAE;IACjC,OAAO,KAAK;EAChB;EACA,IAAIP,EAAE,KAAKE,EAAE,EAAE;IACXM,EAAE,GAAG,CAACP,EAAE,GAAGE,EAAE,KAAKH,EAAE,GAAGE,EAAE,CAAC;IAC1BO,EAAE,GAAG,CAACT,EAAE,GAAGG,EAAE,GAAGD,EAAE,GAAGD,EAAE,KAAKD,EAAE,GAAGE,EAAE,CAAC;EACxC,CAAC,MACI;IACD,OAAOQ,IAAI,CAACC,GAAG,CAACN,CAAC,GAAGL,EAAE,CAAC,IAAIO,EAAE,GAAG,CAAC;EACrC;EACA,IAAIK,GAAG,GAAGJ,EAAE,GAAGH,CAAC,GAAGC,CAAC,GAAGG,EAAE;EACzB,IAAII,EAAE,GAAGD,GAAG,GAAGA,GAAG,IAAIJ,EAAE,GAAGA,EAAE,GAAG,CAAC,CAAC;EAClC,OAAOK,EAAE,IAAIN,EAAE,GAAG,CAAC,GAAGA,EAAE,GAAG,CAAC;AAChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}