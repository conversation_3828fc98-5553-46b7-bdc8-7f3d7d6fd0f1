{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { createHashMap, isObject, retrieve2 } from 'zrender/lib/core/util.js';\nimport { makeInner } from '../../util/model.js';\nimport { shouldRetrieveDataByName } from '../Source.js';\nvar inner = makeInner();\nvar dimTypeShort = {\n  float: 'f',\n  int: 'i',\n  ordinal: 'o',\n  number: 'n',\n  time: 't'\n};\n/**\r\n * Represents the dimension requirement of a series.\r\n *\r\n * NOTICE:\r\n * When there are too many dimensions in dataset and many series, only the used dimensions\r\n * (i.e., used by coord sys and declared in `series.encode`) are add to `dimensionDefineList`.\r\n * But users may query data by other unused dimension names.\r\n * In this case, users can only query data if and only if they have defined dimension names\r\n * via ec option, so we provide `getDimensionIndexFromSource`, which only query them from\r\n * `source` dimensions.\r\n */\nvar SeriesDataSchema = /** @class */function () {\n  function SeriesDataSchema(opt) {\n    this.dimensions = opt.dimensions;\n    this._dimOmitted = opt.dimensionOmitted;\n    this.source = opt.source;\n    this._fullDimCount = opt.fullDimensionCount;\n    this._updateDimOmitted(opt.dimensionOmitted);\n  }\n  SeriesDataSchema.prototype.isDimensionOmitted = function () {\n    return this._dimOmitted;\n  };\n  SeriesDataSchema.prototype._updateDimOmitted = function (dimensionOmitted) {\n    this._dimOmitted = dimensionOmitted;\n    if (!dimensionOmitted) {\n      return;\n    }\n    if (!this._dimNameMap) {\n      this._dimNameMap = ensureSourceDimNameMap(this.source);\n    }\n  };\n  /**\r\n   * @caution Can only be used when `dimensionOmitted: true`.\r\n   *\r\n   * Get index by user defined dimension name (i.e., not internal generate name).\r\n   * That is, get index from `dimensionsDefine`.\r\n   * If no `dimensionsDefine`, or no name get, return -1.\r\n   */\n  SeriesDataSchema.prototype.getSourceDimensionIndex = function (dimName) {\n    return retrieve2(this._dimNameMap.get(dimName), -1);\n  };\n  /**\r\n   * @caution Can only be used when `dimensionOmitted: true`.\r\n   *\r\n   * Notice: may return `null`/`undefined` if user not specify dimension names.\r\n   */\n  SeriesDataSchema.prototype.getSourceDimension = function (dimIndex) {\n    var dimensionsDefine = this.source.dimensionsDefine;\n    if (dimensionsDefine) {\n      return dimensionsDefine[dimIndex];\n    }\n  };\n  SeriesDataSchema.prototype.makeStoreSchema = function () {\n    var dimCount = this._fullDimCount;\n    var willRetrieveDataByName = shouldRetrieveDataByName(this.source);\n    var makeHashStrict = !shouldOmitUnusedDimensions(dimCount);\n    // If source don't have dimensions or series don't omit unsed dimensions.\n    // Generate from seriesDimList directly\n    var dimHash = '';\n    var dims = [];\n    for (var fullDimIdx = 0, seriesDimIdx = 0; fullDimIdx < dimCount; fullDimIdx++) {\n      var property = void 0;\n      var type = void 0;\n      var ordinalMeta = void 0;\n      var seriesDimDef = this.dimensions[seriesDimIdx];\n      // The list has been sorted by `storeDimIndex` asc.\n      if (seriesDimDef && seriesDimDef.storeDimIndex === fullDimIdx) {\n        property = willRetrieveDataByName ? seriesDimDef.name : null;\n        type = seriesDimDef.type;\n        ordinalMeta = seriesDimDef.ordinalMeta;\n        seriesDimIdx++;\n      } else {\n        var sourceDimDef = this.getSourceDimension(fullDimIdx);\n        if (sourceDimDef) {\n          property = willRetrieveDataByName ? sourceDimDef.name : null;\n          type = sourceDimDef.type;\n        }\n      }\n      dims.push({\n        property: property,\n        type: type,\n        ordinalMeta: ordinalMeta\n      });\n      // If retrieving data by index,\n      //   use <index, type, ordinalMeta> to determine whether data can be shared.\n      //   (Because in this case there might be no dimension name defined in dataset, but indices always exists).\n      //   (Indices are always 0, 1, 2, ..., so we can ignore them to shorten the hash).\n      // Otherwise if retrieving data by property name (like `data: [{aa: 123, bb: 765}, ...]`),\n      //   use <property, type, ordinalMeta> in hash.\n      if (willRetrieveDataByName && property != null\n      // For data stack, we have make sure each series has its own dim on this store.\n      // So we do not add property to hash to make sure they can share this store.\n      && (!seriesDimDef || !seriesDimDef.isCalculationCoord)) {\n        dimHash += makeHashStrict\n        // Use escape character '`' in case that property name contains '$'.\n        ? property.replace(/\\`/g, '`1').replace(/\\$/g, '`2')\n        // For better performance, when there are large dimensions, tolerant this defects that hardly meet.\n        : property;\n      }\n      dimHash += '$';\n      dimHash += dimTypeShort[type] || 'f';\n      if (ordinalMeta) {\n        dimHash += ordinalMeta.uid;\n      }\n      dimHash += '$';\n    }\n    // Source from endpoint(usually series) will be read differently\n    // when seriesLayoutBy or startIndex(which is affected by sourceHeader) are different.\n    // So we use this three props as key.\n    var source = this.source;\n    var hash = [source.seriesLayoutBy, source.startIndex, dimHash].join('$$');\n    return {\n      dimensions: dims,\n      hash: hash\n    };\n  };\n  SeriesDataSchema.prototype.makeOutputDimensionNames = function () {\n    var result = [];\n    for (var fullDimIdx = 0, seriesDimIdx = 0; fullDimIdx < this._fullDimCount; fullDimIdx++) {\n      var name_1 = void 0;\n      var seriesDimDef = this.dimensions[seriesDimIdx];\n      // The list has been sorted by `storeDimIndex` asc.\n      if (seriesDimDef && seriesDimDef.storeDimIndex === fullDimIdx) {\n        if (!seriesDimDef.isCalculationCoord) {\n          name_1 = seriesDimDef.name;\n        }\n        seriesDimIdx++;\n      } else {\n        var sourceDimDef = this.getSourceDimension(fullDimIdx);\n        if (sourceDimDef) {\n          name_1 = sourceDimDef.name;\n        }\n      }\n      result.push(name_1);\n    }\n    return result;\n  };\n  SeriesDataSchema.prototype.appendCalculationDimension = function (dimDef) {\n    this.dimensions.push(dimDef);\n    dimDef.isCalculationCoord = true;\n    this._fullDimCount++;\n    // If append dimension on a data store, consider the store\n    // might be shared by different series, series dimensions not\n    // really map to store dimensions.\n    this._updateDimOmitted(true);\n  };\n  return SeriesDataSchema;\n}();\nexport { SeriesDataSchema };\nexport function isSeriesDataSchema(schema) {\n  return schema instanceof SeriesDataSchema;\n}\nexport function createDimNameMap(dimsDef) {\n  var dataDimNameMap = createHashMap();\n  for (var i = 0; i < (dimsDef || []).length; i++) {\n    var dimDefItemRaw = dimsDef[i];\n    var userDimName = isObject(dimDefItemRaw) ? dimDefItemRaw.name : dimDefItemRaw;\n    if (userDimName != null && dataDimNameMap.get(userDimName) == null) {\n      dataDimNameMap.set(userDimName, i);\n    }\n  }\n  return dataDimNameMap;\n}\nexport function ensureSourceDimNameMap(source) {\n  var innerSource = inner(source);\n  return innerSource.dimNameMap || (innerSource.dimNameMap = createDimNameMap(source.dimensionsDefine));\n}\nexport function shouldOmitUnusedDimensions(dimCount) {\n  return dimCount > 30;\n}", "map": {"version": 3, "names": ["createHashMap", "isObject", "retrieve2", "makeInner", "shouldRetrieveDataByName", "inner", "dimTypeShort", "float", "int", "ordinal", "number", "time", "SeriesDataSchema", "opt", "dimensions", "_dimOmitted", "dimensionOmitted", "source", "_fullDimCount", "fullDimensionCount", "_updateDimOmitted", "prototype", "isDimensionOmitted", "_dimNameMap", "ensureSourceDimNameMap", "getSourceDimensionIndex", "dimName", "get", "getSourceDimension", "dimIndex", "dimensionsDefine", "makeStoreSchema", "dimCount", "willRetrieveDataByName", "makeHashStrict", "shouldOmitUnusedDimensions", "<PERSON><PERSON><PERSON>", "dims", "fullDimIdx", "seriesDimIdx", "property", "type", "ordinalMeta", "seriesDimDef", "storeDimIndex", "name", "sourceDimDef", "push", "isCalculationCoord", "replace", "uid", "hash", "seriesLayoutBy", "startIndex", "join", "makeOutputDimensionNames", "result", "name_1", "appendCalculationDimension", "dimDef", "isSeriesDataSchema", "schema", "createDimNameMap", "dimsDef", "dataDimNameMap", "i", "length", "dimDefItemRaw", "userDimName", "set", "innerSource", "dimNameMap"], "sources": ["E:/AI/SmartCV/node_modules/echarts/lib/data/helper/SeriesDataSchema.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { createHashMap, isObject, retrieve2 } from 'zrender/lib/core/util.js';\nimport { makeInner } from '../../util/model.js';\nimport { shouldRetrieveDataByName } from '../Source.js';\nvar inner = makeInner();\nvar dimTypeShort = {\n  float: 'f',\n  int: 'i',\n  ordinal: 'o',\n  number: 'n',\n  time: 't'\n};\n/**\r\n * Represents the dimension requirement of a series.\r\n *\r\n * NOTICE:\r\n * When there are too many dimensions in dataset and many series, only the used dimensions\r\n * (i.e., used by coord sys and declared in `series.encode`) are add to `dimensionDefineList`.\r\n * But users may query data by other unused dimension names.\r\n * In this case, users can only query data if and only if they have defined dimension names\r\n * via ec option, so we provide `getDimensionIndexFromSource`, which only query them from\r\n * `source` dimensions.\r\n */\nvar SeriesDataSchema = /** @class */function () {\n  function SeriesDataSchema(opt) {\n    this.dimensions = opt.dimensions;\n    this._dimOmitted = opt.dimensionOmitted;\n    this.source = opt.source;\n    this._fullDimCount = opt.fullDimensionCount;\n    this._updateDimOmitted(opt.dimensionOmitted);\n  }\n  SeriesDataSchema.prototype.isDimensionOmitted = function () {\n    return this._dimOmitted;\n  };\n  SeriesDataSchema.prototype._updateDimOmitted = function (dimensionOmitted) {\n    this._dimOmitted = dimensionOmitted;\n    if (!dimensionOmitted) {\n      return;\n    }\n    if (!this._dimNameMap) {\n      this._dimNameMap = ensureSourceDimNameMap(this.source);\n    }\n  };\n  /**\r\n   * @caution Can only be used when `dimensionOmitted: true`.\r\n   *\r\n   * Get index by user defined dimension name (i.e., not internal generate name).\r\n   * That is, get index from `dimensionsDefine`.\r\n   * If no `dimensionsDefine`, or no name get, return -1.\r\n   */\n  SeriesDataSchema.prototype.getSourceDimensionIndex = function (dimName) {\n    return retrieve2(this._dimNameMap.get(dimName), -1);\n  };\n  /**\r\n   * @caution Can only be used when `dimensionOmitted: true`.\r\n   *\r\n   * Notice: may return `null`/`undefined` if user not specify dimension names.\r\n   */\n  SeriesDataSchema.prototype.getSourceDimension = function (dimIndex) {\n    var dimensionsDefine = this.source.dimensionsDefine;\n    if (dimensionsDefine) {\n      return dimensionsDefine[dimIndex];\n    }\n  };\n  SeriesDataSchema.prototype.makeStoreSchema = function () {\n    var dimCount = this._fullDimCount;\n    var willRetrieveDataByName = shouldRetrieveDataByName(this.source);\n    var makeHashStrict = !shouldOmitUnusedDimensions(dimCount);\n    // If source don't have dimensions or series don't omit unsed dimensions.\n    // Generate from seriesDimList directly\n    var dimHash = '';\n    var dims = [];\n    for (var fullDimIdx = 0, seriesDimIdx = 0; fullDimIdx < dimCount; fullDimIdx++) {\n      var property = void 0;\n      var type = void 0;\n      var ordinalMeta = void 0;\n      var seriesDimDef = this.dimensions[seriesDimIdx];\n      // The list has been sorted by `storeDimIndex` asc.\n      if (seriesDimDef && seriesDimDef.storeDimIndex === fullDimIdx) {\n        property = willRetrieveDataByName ? seriesDimDef.name : null;\n        type = seriesDimDef.type;\n        ordinalMeta = seriesDimDef.ordinalMeta;\n        seriesDimIdx++;\n      } else {\n        var sourceDimDef = this.getSourceDimension(fullDimIdx);\n        if (sourceDimDef) {\n          property = willRetrieveDataByName ? sourceDimDef.name : null;\n          type = sourceDimDef.type;\n        }\n      }\n      dims.push({\n        property: property,\n        type: type,\n        ordinalMeta: ordinalMeta\n      });\n      // If retrieving data by index,\n      //   use <index, type, ordinalMeta> to determine whether data can be shared.\n      //   (Because in this case there might be no dimension name defined in dataset, but indices always exists).\n      //   (Indices are always 0, 1, 2, ..., so we can ignore them to shorten the hash).\n      // Otherwise if retrieving data by property name (like `data: [{aa: 123, bb: 765}, ...]`),\n      //   use <property, type, ordinalMeta> in hash.\n      if (willRetrieveDataByName && property != null\n      // For data stack, we have make sure each series has its own dim on this store.\n      // So we do not add property to hash to make sure they can share this store.\n      && (!seriesDimDef || !seriesDimDef.isCalculationCoord)) {\n        dimHash += makeHashStrict\n        // Use escape character '`' in case that property name contains '$'.\n        ? property.replace(/\\`/g, '`1').replace(/\\$/g, '`2')\n        // For better performance, when there are large dimensions, tolerant this defects that hardly meet.\n        : property;\n      }\n      dimHash += '$';\n      dimHash += dimTypeShort[type] || 'f';\n      if (ordinalMeta) {\n        dimHash += ordinalMeta.uid;\n      }\n      dimHash += '$';\n    }\n    // Source from endpoint(usually series) will be read differently\n    // when seriesLayoutBy or startIndex(which is affected by sourceHeader) are different.\n    // So we use this three props as key.\n    var source = this.source;\n    var hash = [source.seriesLayoutBy, source.startIndex, dimHash].join('$$');\n    return {\n      dimensions: dims,\n      hash: hash\n    };\n  };\n  SeriesDataSchema.prototype.makeOutputDimensionNames = function () {\n    var result = [];\n    for (var fullDimIdx = 0, seriesDimIdx = 0; fullDimIdx < this._fullDimCount; fullDimIdx++) {\n      var name_1 = void 0;\n      var seriesDimDef = this.dimensions[seriesDimIdx];\n      // The list has been sorted by `storeDimIndex` asc.\n      if (seriesDimDef && seriesDimDef.storeDimIndex === fullDimIdx) {\n        if (!seriesDimDef.isCalculationCoord) {\n          name_1 = seriesDimDef.name;\n        }\n        seriesDimIdx++;\n      } else {\n        var sourceDimDef = this.getSourceDimension(fullDimIdx);\n        if (sourceDimDef) {\n          name_1 = sourceDimDef.name;\n        }\n      }\n      result.push(name_1);\n    }\n    return result;\n  };\n  SeriesDataSchema.prototype.appendCalculationDimension = function (dimDef) {\n    this.dimensions.push(dimDef);\n    dimDef.isCalculationCoord = true;\n    this._fullDimCount++;\n    // If append dimension on a data store, consider the store\n    // might be shared by different series, series dimensions not\n    // really map to store dimensions.\n    this._updateDimOmitted(true);\n  };\n  return SeriesDataSchema;\n}();\nexport { SeriesDataSchema };\nexport function isSeriesDataSchema(schema) {\n  return schema instanceof SeriesDataSchema;\n}\nexport function createDimNameMap(dimsDef) {\n  var dataDimNameMap = createHashMap();\n  for (var i = 0; i < (dimsDef || []).length; i++) {\n    var dimDefItemRaw = dimsDef[i];\n    var userDimName = isObject(dimDefItemRaw) ? dimDefItemRaw.name : dimDefItemRaw;\n    if (userDimName != null && dataDimNameMap.get(userDimName) == null) {\n      dataDimNameMap.set(userDimName, i);\n    }\n  }\n  return dataDimNameMap;\n}\nexport function ensureSourceDimNameMap(source) {\n  var innerSource = inner(source);\n  return innerSource.dimNameMap || (innerSource.dimNameMap = createDimNameMap(source.dimensionsDefine));\n}\nexport function shouldOmitUnusedDimensions(dimCount) {\n  return dimCount > 30;\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,aAAa,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,0BAA0B;AAC7E,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,SAASC,wBAAwB,QAAQ,cAAc;AACvD,IAAIC,KAAK,GAAGF,SAAS,CAAC,CAAC;AACvB,IAAIG,YAAY,GAAG;EACjBC,KAAK,EAAE,GAAG;EACVC,GAAG,EAAE,GAAG;EACRC,OAAO,EAAE,GAAG;EACZC,MAAM,EAAE,GAAG;EACXC,IAAI,EAAE;AACR,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,gBAAgB,GAAG,aAAa,YAAY;EAC9C,SAASA,gBAAgBA,CAACC,GAAG,EAAE;IAC7B,IAAI,CAACC,UAAU,GAAGD,GAAG,CAACC,UAAU;IAChC,IAAI,CAACC,WAAW,GAAGF,GAAG,CAACG,gBAAgB;IACvC,IAAI,CAACC,MAAM,GAAGJ,GAAG,CAACI,MAAM;IACxB,IAAI,CAACC,aAAa,GAAGL,GAAG,CAACM,kBAAkB;IAC3C,IAAI,CAACC,iBAAiB,CAACP,GAAG,CAACG,gBAAgB,CAAC;EAC9C;EACAJ,gBAAgB,CAACS,SAAS,CAACC,kBAAkB,GAAG,YAAY;IAC1D,OAAO,IAAI,CAACP,WAAW;EACzB,CAAC;EACDH,gBAAgB,CAACS,SAAS,CAACD,iBAAiB,GAAG,UAAUJ,gBAAgB,EAAE;IACzE,IAAI,CAACD,WAAW,GAAGC,gBAAgB;IACnC,IAAI,CAACA,gBAAgB,EAAE;MACrB;IACF;IACA,IAAI,CAAC,IAAI,CAACO,WAAW,EAAE;MACrB,IAAI,CAACA,WAAW,GAAGC,sBAAsB,CAAC,IAAI,CAACP,MAAM,CAAC;IACxD;EACF,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;EACEL,gBAAgB,CAACS,SAAS,CAACI,uBAAuB,GAAG,UAAUC,OAAO,EAAE;IACtE,OAAOxB,SAAS,CAAC,IAAI,CAACqB,WAAW,CAACI,GAAG,CAACD,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;EACrD,CAAC;EACD;AACF;AACA;AACA;AACA;EACEd,gBAAgB,CAACS,SAAS,CAACO,kBAAkB,GAAG,UAAUC,QAAQ,EAAE;IAClE,IAAIC,gBAAgB,GAAG,IAAI,CAACb,MAAM,CAACa,gBAAgB;IACnD,IAAIA,gBAAgB,EAAE;MACpB,OAAOA,gBAAgB,CAACD,QAAQ,CAAC;IACnC;EACF,CAAC;EACDjB,gBAAgB,CAACS,SAAS,CAACU,eAAe,GAAG,YAAY;IACvD,IAAIC,QAAQ,GAAG,IAAI,CAACd,aAAa;IACjC,IAAIe,sBAAsB,GAAG7B,wBAAwB,CAAC,IAAI,CAACa,MAAM,CAAC;IAClE,IAAIiB,cAAc,GAAG,CAACC,0BAA0B,CAACH,QAAQ,CAAC;IAC1D;IACA;IACA,IAAII,OAAO,GAAG,EAAE;IAChB,IAAIC,IAAI,GAAG,EAAE;IACb,KAAK,IAAIC,UAAU,GAAG,CAAC,EAAEC,YAAY,GAAG,CAAC,EAAED,UAAU,GAAGN,QAAQ,EAAEM,UAAU,EAAE,EAAE;MAC9E,IAAIE,QAAQ,GAAG,KAAK,CAAC;MACrB,IAAIC,IAAI,GAAG,KAAK,CAAC;MACjB,IAAIC,WAAW,GAAG,KAAK,CAAC;MACxB,IAAIC,YAAY,GAAG,IAAI,CAAC7B,UAAU,CAACyB,YAAY,CAAC;MAChD;MACA,IAAII,YAAY,IAAIA,YAAY,CAACC,aAAa,KAAKN,UAAU,EAAE;QAC7DE,QAAQ,GAAGP,sBAAsB,GAAGU,YAAY,CAACE,IAAI,GAAG,IAAI;QAC5DJ,IAAI,GAAGE,YAAY,CAACF,IAAI;QACxBC,WAAW,GAAGC,YAAY,CAACD,WAAW;QACtCH,YAAY,EAAE;MAChB,CAAC,MAAM;QACL,IAAIO,YAAY,GAAG,IAAI,CAAClB,kBAAkB,CAACU,UAAU,CAAC;QACtD,IAAIQ,YAAY,EAAE;UAChBN,QAAQ,GAAGP,sBAAsB,GAAGa,YAAY,CAACD,IAAI,GAAG,IAAI;UAC5DJ,IAAI,GAAGK,YAAY,CAACL,IAAI;QAC1B;MACF;MACAJ,IAAI,CAACU,IAAI,CAAC;QACRP,QAAQ,EAAEA,QAAQ;QAClBC,IAAI,EAAEA,IAAI;QACVC,WAAW,EAAEA;MACf,CAAC,CAAC;MACF;MACA;MACA;MACA;MACA;MACA;MACA,IAAIT,sBAAsB,IAAIO,QAAQ,IAAI;MAC1C;MACA;MAAA,IACI,CAACG,YAAY,IAAI,CAACA,YAAY,CAACK,kBAAkB,CAAC,EAAE;QACtDZ,OAAO,IAAIF;QACX;QAAA,EACEM,QAAQ,CAACS,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,IAAI;QACnD;QAAA,EACET,QAAQ;MACZ;MACAJ,OAAO,IAAI,GAAG;MACdA,OAAO,IAAI9B,YAAY,CAACmC,IAAI,CAAC,IAAI,GAAG;MACpC,IAAIC,WAAW,EAAE;QACfN,OAAO,IAAIM,WAAW,CAACQ,GAAG;MAC5B;MACAd,OAAO,IAAI,GAAG;IAChB;IACA;IACA;IACA;IACA,IAAInB,MAAM,GAAG,IAAI,CAACA,MAAM;IACxB,IAAIkC,IAAI,GAAG,CAAClC,MAAM,CAACmC,cAAc,EAAEnC,MAAM,CAACoC,UAAU,EAAEjB,OAAO,CAAC,CAACkB,IAAI,CAAC,IAAI,CAAC;IACzE,OAAO;MACLxC,UAAU,EAAEuB,IAAI;MAChBc,IAAI,EAAEA;IACR,CAAC;EACH,CAAC;EACDvC,gBAAgB,CAACS,SAAS,CAACkC,wBAAwB,GAAG,YAAY;IAChE,IAAIC,MAAM,GAAG,EAAE;IACf,KAAK,IAAIlB,UAAU,GAAG,CAAC,EAAEC,YAAY,GAAG,CAAC,EAAED,UAAU,GAAG,IAAI,CAACpB,aAAa,EAAEoB,UAAU,EAAE,EAAE;MACxF,IAAImB,MAAM,GAAG,KAAK,CAAC;MACnB,IAAId,YAAY,GAAG,IAAI,CAAC7B,UAAU,CAACyB,YAAY,CAAC;MAChD;MACA,IAAII,YAAY,IAAIA,YAAY,CAACC,aAAa,KAAKN,UAAU,EAAE;QAC7D,IAAI,CAACK,YAAY,CAACK,kBAAkB,EAAE;UACpCS,MAAM,GAAGd,YAAY,CAACE,IAAI;QAC5B;QACAN,YAAY,EAAE;MAChB,CAAC,MAAM;QACL,IAAIO,YAAY,GAAG,IAAI,CAAClB,kBAAkB,CAACU,UAAU,CAAC;QACtD,IAAIQ,YAAY,EAAE;UAChBW,MAAM,GAAGX,YAAY,CAACD,IAAI;QAC5B;MACF;MACAW,MAAM,CAACT,IAAI,CAACU,MAAM,CAAC;IACrB;IACA,OAAOD,MAAM;EACf,CAAC;EACD5C,gBAAgB,CAACS,SAAS,CAACqC,0BAA0B,GAAG,UAAUC,MAAM,EAAE;IACxE,IAAI,CAAC7C,UAAU,CAACiC,IAAI,CAACY,MAAM,CAAC;IAC5BA,MAAM,CAACX,kBAAkB,GAAG,IAAI;IAChC,IAAI,CAAC9B,aAAa,EAAE;IACpB;IACA;IACA;IACA,IAAI,CAACE,iBAAiB,CAAC,IAAI,CAAC;EAC9B,CAAC;EACD,OAAOR,gBAAgB;AACzB,CAAC,CAAC,CAAC;AACH,SAASA,gBAAgB;AACzB,OAAO,SAASgD,kBAAkBA,CAACC,MAAM,EAAE;EACzC,OAAOA,MAAM,YAAYjD,gBAAgB;AAC3C;AACA,OAAO,SAASkD,gBAAgBA,CAACC,OAAO,EAAE;EACxC,IAAIC,cAAc,GAAGhE,aAAa,CAAC,CAAC;EACpC,KAAK,IAAIiE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAACF,OAAO,IAAI,EAAE,EAAEG,MAAM,EAAED,CAAC,EAAE,EAAE;IAC/C,IAAIE,aAAa,GAAGJ,OAAO,CAACE,CAAC,CAAC;IAC9B,IAAIG,WAAW,GAAGnE,QAAQ,CAACkE,aAAa,CAAC,GAAGA,aAAa,CAACtB,IAAI,GAAGsB,aAAa;IAC9E,IAAIC,WAAW,IAAI,IAAI,IAAIJ,cAAc,CAACrC,GAAG,CAACyC,WAAW,CAAC,IAAI,IAAI,EAAE;MAClEJ,cAAc,CAACK,GAAG,CAACD,WAAW,EAAEH,CAAC,CAAC;IACpC;EACF;EACA,OAAOD,cAAc;AACvB;AACA,OAAO,SAASxC,sBAAsBA,CAACP,MAAM,EAAE;EAC7C,IAAIqD,WAAW,GAAGjE,KAAK,CAACY,MAAM,CAAC;EAC/B,OAAOqD,WAAW,CAACC,UAAU,KAAKD,WAAW,CAACC,UAAU,GAAGT,gBAAgB,CAAC7C,MAAM,CAACa,gBAAgB,CAAC,CAAC;AACvG;AACA,OAAO,SAASK,0BAA0BA,CAACH,QAAQ,EAAE;EACnD,OAAOA,QAAQ,GAAG,EAAE;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}