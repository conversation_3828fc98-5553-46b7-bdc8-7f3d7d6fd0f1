{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport Axis from '../Axis.js';\nvar IndicatorAxis = /** @class */function (_super) {\n  __extends(IndicatorAxis, _super);\n  function IndicatorAxis(dim, scale, radiusExtent) {\n    var _this = _super.call(this, dim, scale, radiusExtent) || this;\n    _this.type = 'value';\n    _this.angle = 0;\n    _this.name = '';\n    return _this;\n  }\n  return IndicatorAxis;\n}(Axis);\nexport default IndicatorAxis;", "map": {"version": 3, "names": ["__extends", "Axis", "IndicatorAxis", "_super", "dim", "scale", "radiusExtent", "_this", "call", "type", "angle", "name"], "sources": ["E:/AI/SmartCV/node_modules/echarts/lib/coord/radar/IndicatorAxis.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport Axis from '../Axis.js';\nvar IndicatorAxis = /** @class */function (_super) {\n  __extends(IndicatorAxis, _super);\n  function IndicatorAxis(dim, scale, radiusExtent) {\n    var _this = _super.call(this, dim, scale, radiusExtent) || this;\n    _this.type = 'value';\n    _this.angle = 0;\n    _this.name = '';\n    return _this;\n  }\n  return IndicatorAxis;\n}(Axis);\nexport default IndicatorAxis;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,IAAI,MAAM,YAAY;AAC7B,IAAIC,aAAa,GAAG,aAAa,UAAUC,MAAM,EAAE;EACjDH,SAAS,CAACE,aAAa,EAAEC,MAAM,CAAC;EAChC,SAASD,aAAaA,CAACE,GAAG,EAAEC,KAAK,EAAEC,YAAY,EAAE;IAC/C,IAAIC,KAAK,GAAGJ,MAAM,CAACK,IAAI,CAAC,IAAI,EAAEJ,GAAG,EAAEC,KAAK,EAAEC,YAAY,CAAC,IAAI,IAAI;IAC/DC,KAAK,CAACE,IAAI,GAAG,OAAO;IACpBF,KAAK,CAACG,KAAK,GAAG,CAAC;IACfH,KAAK,CAACI,IAAI,GAAG,EAAE;IACf,OAAOJ,KAAK;EACd;EACA,OAAOL,aAAa;AACtB,CAAC,CAACD,IAAI,CAAC;AACP,eAAeC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}