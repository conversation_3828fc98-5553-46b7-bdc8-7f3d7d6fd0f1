{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { assert, isArray, eqNaN, isFunction } from 'zrender/lib/core/util.js';\nimport { parsePercent } from 'zrender/lib/contain/text.js';\nvar ScaleRawExtentInfo = /** @class */function () {\n  function ScaleRawExtentInfo(scale, model,\n  // Usually: data extent from all series on this axis.\n  originalExtent) {\n    this._prepareParams(scale, model, originalExtent);\n  }\n  /**\r\n   * Parameters depending on outside (like model, user callback)\r\n   * are prepared and fixed here.\r\n   */\n  ScaleRawExtentInfo.prototype._prepareParams = function (scale, model,\n  // Usually: data extent from all series on this axis.\n  dataExtent) {\n    if (dataExtent[1] < dataExtent[0]) {\n      dataExtent = [NaN, NaN];\n    }\n    this._dataMin = dataExtent[0];\n    this._dataMax = dataExtent[1];\n    var isOrdinal = this._isOrdinal = scale.type === 'ordinal';\n    this._needCrossZero = scale.type === 'interval' && model.getNeedCrossZero && model.getNeedCrossZero();\n    var axisMinValue = model.get('min', true);\n    if (axisMinValue == null) {\n      axisMinValue = model.get('startValue', true);\n    }\n    var modelMinRaw = this._modelMinRaw = axisMinValue;\n    if (isFunction(modelMinRaw)) {\n      // This callback always provides users the full data extent (before data is filtered).\n      this._modelMinNum = parseAxisModelMinMax(scale, modelMinRaw({\n        min: dataExtent[0],\n        max: dataExtent[1]\n      }));\n    } else if (modelMinRaw !== 'dataMin') {\n      this._modelMinNum = parseAxisModelMinMax(scale, modelMinRaw);\n    }\n    var modelMaxRaw = this._modelMaxRaw = model.get('max', true);\n    if (isFunction(modelMaxRaw)) {\n      // This callback always provides users the full data extent (before data is filtered).\n      this._modelMaxNum = parseAxisModelMinMax(scale, modelMaxRaw({\n        min: dataExtent[0],\n        max: dataExtent[1]\n      }));\n    } else if (modelMaxRaw !== 'dataMax') {\n      this._modelMaxNum = parseAxisModelMinMax(scale, modelMaxRaw);\n    }\n    if (isOrdinal) {\n      // FIXME: there is a flaw here: if there is no \"block\" data processor like `dataZoom`,\n      // and progressive rendering is using, here the category result might just only contain\n      // the processed chunk rather than the entire result.\n      this._axisDataLen = model.getCategories().length;\n    } else {\n      var boundaryGap = model.get('boundaryGap');\n      var boundaryGapArr = isArray(boundaryGap) ? boundaryGap : [boundaryGap || 0, boundaryGap || 0];\n      if (typeof boundaryGapArr[0] === 'boolean' || typeof boundaryGapArr[1] === 'boolean') {\n        if (process.env.NODE_ENV !== 'production') {\n          console.warn('Boolean type for boundaryGap is only ' + 'allowed for ordinal axis. Please use string in ' + 'percentage instead, e.g., \"20%\". Currently, ' + 'boundaryGap is set to be 0.');\n        }\n        this._boundaryGapInner = [0, 0];\n      } else {\n        this._boundaryGapInner = [parsePercent(boundaryGapArr[0], 1), parsePercent(boundaryGapArr[1], 1)];\n      }\n    }\n  };\n  /**\r\n   * Calculate extent by prepared parameters.\r\n   * This method has no external dependency and can be called duplicatedly,\r\n   * getting the same result.\r\n   * If parameters changed, should call this method to recalcuate.\r\n   */\n  ScaleRawExtentInfo.prototype.calculate = function () {\n    // Notice: When min/max is not set (that is, when there are null/undefined,\n    // which is the most common case), these cases should be ensured:\n    // (1) For 'ordinal', show all axis.data.\n    // (2) For others:\n    //      + `boundaryGap` is applied (if min/max set, boundaryGap is\n    //      disabled).\n    //      + If `needCrossZero`, min/max should be zero, otherwise, min/max should\n    //      be the result that originalExtent enlarged by boundaryGap.\n    // (3) If no data, it should be ensured that `scale.setBlank` is set.\n    var isOrdinal = this._isOrdinal;\n    var dataMin = this._dataMin;\n    var dataMax = this._dataMax;\n    var axisDataLen = this._axisDataLen;\n    var boundaryGapInner = this._boundaryGapInner;\n    var span = !isOrdinal ? dataMax - dataMin || Math.abs(dataMin) : null;\n    // Currently if a `'value'` axis model min is specified as 'dataMin'/'dataMax',\n    // `boundaryGap` will not be used. It's the different from specifying as `null`/`undefined`.\n    var min = this._modelMinRaw === 'dataMin' ? dataMin : this._modelMinNum;\n    var max = this._modelMaxRaw === 'dataMax' ? dataMax : this._modelMaxNum;\n    // If `_modelMinNum`/`_modelMaxNum` is `null`/`undefined`, should not be fixed.\n    var minFixed = min != null;\n    var maxFixed = max != null;\n    if (min == null) {\n      min = isOrdinal ? axisDataLen ? 0 : NaN : dataMin - boundaryGapInner[0] * span;\n    }\n    if (max == null) {\n      max = isOrdinal ? axisDataLen ? axisDataLen - 1 : NaN : dataMax + boundaryGapInner[1] * span;\n    }\n    (min == null || !isFinite(min)) && (min = NaN);\n    (max == null || !isFinite(max)) && (max = NaN);\n    var isBlank = eqNaN(min) || eqNaN(max) || isOrdinal && !axisDataLen;\n    // If data extent modified, need to recalculated to ensure cross zero.\n    if (this._needCrossZero) {\n      // Axis is over zero and min is not set\n      if (min > 0 && max > 0 && !minFixed) {\n        min = 0;\n        // minFixed = true;\n      }\n      // Axis is under zero and max is not set\n      if (min < 0 && max < 0 && !maxFixed) {\n        max = 0;\n        // maxFixed = true;\n      }\n      // PENDING:\n      // When `needCrossZero` and all data is positive/negative, should it be ensured\n      // that the results processed by boundaryGap are positive/negative?\n      // If so, here `minFixed`/`maxFixed` need to be set.\n    }\n    var determinedMin = this._determinedMin;\n    var determinedMax = this._determinedMax;\n    if (determinedMin != null) {\n      min = determinedMin;\n      minFixed = true;\n    }\n    if (determinedMax != null) {\n      max = determinedMax;\n      maxFixed = true;\n    }\n    // Ensure min/max be finite number or NaN here. (not to be null/undefined)\n    // `NaN` means min/max axis is blank.\n    return {\n      min: min,\n      max: max,\n      minFixed: minFixed,\n      maxFixed: maxFixed,\n      isBlank: isBlank\n    };\n  };\n  ScaleRawExtentInfo.prototype.modifyDataMinMax = function (minMaxName, val) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(!this.frozen);\n    }\n    this[DATA_MIN_MAX_ATTR[minMaxName]] = val;\n  };\n  ScaleRawExtentInfo.prototype.setDeterminedMinMax = function (minMaxName, val) {\n    var attr = DETERMINED_MIN_MAX_ATTR[minMaxName];\n    if (process.env.NODE_ENV !== 'production') {\n      assert(!this.frozen\n      // Earse them usually means logic flaw.\n      && this[attr] == null);\n    }\n    this[attr] = val;\n  };\n  ScaleRawExtentInfo.prototype.freeze = function () {\n    // @ts-ignore\n    this.frozen = true;\n  };\n  return ScaleRawExtentInfo;\n}();\nexport { ScaleRawExtentInfo };\nvar DETERMINED_MIN_MAX_ATTR = {\n  min: '_determinedMin',\n  max: '_determinedMax'\n};\nvar DATA_MIN_MAX_ATTR = {\n  min: '_dataMin',\n  max: '_dataMax'\n};\n/**\r\n * Get scale min max and related info only depends on model settings.\r\n * This method can be called after coordinate system created.\r\n * For example, in data processing stage.\r\n *\r\n * Scale extent info probably be required multiple times during a workflow.\r\n * For example:\r\n * (1) `dataZoom` depends it to get the axis extent in \"100%\" state.\r\n * (2) `processor/extentCalculator` depends it to make sure whether axis extent is specified.\r\n * (3) `coordSys.update` use it to finally decide the scale extent.\r\n * But the callback of `min`/`max` should not be called multiple times.\r\n * The code below should not be implemented repeatedly either.\r\n * So we cache the result in the scale instance, which will be recreated at the beginning\r\n * of the workflow (because `scale` instance will be recreated each round of the workflow).\r\n */\nexport function ensureScaleRawExtentInfo(scale, model,\n// Usually: data extent from all series on this axis.\noriginalExtent) {\n  // Do not permit to recreate.\n  var rawExtentInfo = scale.rawExtentInfo;\n  if (rawExtentInfo) {\n    return rawExtentInfo;\n  }\n  rawExtentInfo = new ScaleRawExtentInfo(scale, model, originalExtent);\n  // @ts-ignore\n  scale.rawExtentInfo = rawExtentInfo;\n  return rawExtentInfo;\n}\nexport function parseAxisModelMinMax(scale, minMax) {\n  return minMax == null ? null : eqNaN(minMax) ? NaN : scale.parse(minMax);\n}", "map": {"version": 3, "names": ["assert", "isArray", "eqNaN", "isFunction", "parsePercent", "ScaleRawExtentInfo", "scale", "model", "originalExtent", "_prepareParams", "prototype", "dataExtent", "NaN", "_dataMin", "_dataMax", "isOrdinal", "_isOrdinal", "type", "_needCrossZero", "getNeedCrossZero", "axisMinValue", "get", "modelMinRaw", "_modelMinRaw", "_modelMinNum", "parseAxisModelMinMax", "min", "max", "modelMaxRaw", "_modelMaxRaw", "_modelMaxNum", "_axisDataLen", "getCategories", "length", "boundaryGap", "boundaryGapArr", "process", "env", "NODE_ENV", "console", "warn", "_boundaryGapInner", "calculate", "dataMin", "dataMax", "axisDataLen", "boundaryGapInner", "span", "Math", "abs", "minFixed", "maxFixed", "isFinite", "isBlank", "determinedMin", "_determinedMin", "determinedMax", "_determinedMax", "modifyDataMinMax", "minMaxName", "val", "frozen", "DATA_MIN_MAX_ATTR", "setDeterminedMinMax", "attr", "DETERMINED_MIN_MAX_ATTR", "freeze", "ensureScaleRawExtentInfo", "rawExtentInfo", "minMax", "parse"], "sources": ["E:/AI/SmartCV/node_modules/echarts/lib/coord/scaleRawExtentInfo.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { assert, isArray, eqNaN, isFunction } from 'zrender/lib/core/util.js';\nimport { parsePercent } from 'zrender/lib/contain/text.js';\nvar ScaleRawExtentInfo = /** @class */function () {\n  function ScaleRawExtentInfo(scale, model,\n  // Usually: data extent from all series on this axis.\n  originalExtent) {\n    this._prepareParams(scale, model, originalExtent);\n  }\n  /**\r\n   * Parameters depending on outside (like model, user callback)\r\n   * are prepared and fixed here.\r\n   */\n  ScaleRawExtentInfo.prototype._prepareParams = function (scale, model,\n  // Usually: data extent from all series on this axis.\n  dataExtent) {\n    if (dataExtent[1] < dataExtent[0]) {\n      dataExtent = [NaN, NaN];\n    }\n    this._dataMin = dataExtent[0];\n    this._dataMax = dataExtent[1];\n    var isOrdinal = this._isOrdinal = scale.type === 'ordinal';\n    this._needCrossZero = scale.type === 'interval' && model.getNeedCrossZero && model.getNeedCrossZero();\n    var axisMinValue = model.get('min', true);\n    if (axisMinValue == null) {\n      axisMinValue = model.get('startValue', true);\n    }\n    var modelMinRaw = this._modelMinRaw = axisMinValue;\n    if (isFunction(modelMinRaw)) {\n      // This callback always provides users the full data extent (before data is filtered).\n      this._modelMinNum = parseAxisModelMinMax(scale, modelMinRaw({\n        min: dataExtent[0],\n        max: dataExtent[1]\n      }));\n    } else if (modelMinRaw !== 'dataMin') {\n      this._modelMinNum = parseAxisModelMinMax(scale, modelMinRaw);\n    }\n    var modelMaxRaw = this._modelMaxRaw = model.get('max', true);\n    if (isFunction(modelMaxRaw)) {\n      // This callback always provides users the full data extent (before data is filtered).\n      this._modelMaxNum = parseAxisModelMinMax(scale, modelMaxRaw({\n        min: dataExtent[0],\n        max: dataExtent[1]\n      }));\n    } else if (modelMaxRaw !== 'dataMax') {\n      this._modelMaxNum = parseAxisModelMinMax(scale, modelMaxRaw);\n    }\n    if (isOrdinal) {\n      // FIXME: there is a flaw here: if there is no \"block\" data processor like `dataZoom`,\n      // and progressive rendering is using, here the category result might just only contain\n      // the processed chunk rather than the entire result.\n      this._axisDataLen = model.getCategories().length;\n    } else {\n      var boundaryGap = model.get('boundaryGap');\n      var boundaryGapArr = isArray(boundaryGap) ? boundaryGap : [boundaryGap || 0, boundaryGap || 0];\n      if (typeof boundaryGapArr[0] === 'boolean' || typeof boundaryGapArr[1] === 'boolean') {\n        if (process.env.NODE_ENV !== 'production') {\n          console.warn('Boolean type for boundaryGap is only ' + 'allowed for ordinal axis. Please use string in ' + 'percentage instead, e.g., \"20%\". Currently, ' + 'boundaryGap is set to be 0.');\n        }\n        this._boundaryGapInner = [0, 0];\n      } else {\n        this._boundaryGapInner = [parsePercent(boundaryGapArr[0], 1), parsePercent(boundaryGapArr[1], 1)];\n      }\n    }\n  };\n  /**\r\n   * Calculate extent by prepared parameters.\r\n   * This method has no external dependency and can be called duplicatedly,\r\n   * getting the same result.\r\n   * If parameters changed, should call this method to recalcuate.\r\n   */\n  ScaleRawExtentInfo.prototype.calculate = function () {\n    // Notice: When min/max is not set (that is, when there are null/undefined,\n    // which is the most common case), these cases should be ensured:\n    // (1) For 'ordinal', show all axis.data.\n    // (2) For others:\n    //      + `boundaryGap` is applied (if min/max set, boundaryGap is\n    //      disabled).\n    //      + If `needCrossZero`, min/max should be zero, otherwise, min/max should\n    //      be the result that originalExtent enlarged by boundaryGap.\n    // (3) If no data, it should be ensured that `scale.setBlank` is set.\n    var isOrdinal = this._isOrdinal;\n    var dataMin = this._dataMin;\n    var dataMax = this._dataMax;\n    var axisDataLen = this._axisDataLen;\n    var boundaryGapInner = this._boundaryGapInner;\n    var span = !isOrdinal ? dataMax - dataMin || Math.abs(dataMin) : null;\n    // Currently if a `'value'` axis model min is specified as 'dataMin'/'dataMax',\n    // `boundaryGap` will not be used. It's the different from specifying as `null`/`undefined`.\n    var min = this._modelMinRaw === 'dataMin' ? dataMin : this._modelMinNum;\n    var max = this._modelMaxRaw === 'dataMax' ? dataMax : this._modelMaxNum;\n    // If `_modelMinNum`/`_modelMaxNum` is `null`/`undefined`, should not be fixed.\n    var minFixed = min != null;\n    var maxFixed = max != null;\n    if (min == null) {\n      min = isOrdinal ? axisDataLen ? 0 : NaN : dataMin - boundaryGapInner[0] * span;\n    }\n    if (max == null) {\n      max = isOrdinal ? axisDataLen ? axisDataLen - 1 : NaN : dataMax + boundaryGapInner[1] * span;\n    }\n    (min == null || !isFinite(min)) && (min = NaN);\n    (max == null || !isFinite(max)) && (max = NaN);\n    var isBlank = eqNaN(min) || eqNaN(max) || isOrdinal && !axisDataLen;\n    // If data extent modified, need to recalculated to ensure cross zero.\n    if (this._needCrossZero) {\n      // Axis is over zero and min is not set\n      if (min > 0 && max > 0 && !minFixed) {\n        min = 0;\n        // minFixed = true;\n      }\n      // Axis is under zero and max is not set\n      if (min < 0 && max < 0 && !maxFixed) {\n        max = 0;\n        // maxFixed = true;\n      }\n      // PENDING:\n      // When `needCrossZero` and all data is positive/negative, should it be ensured\n      // that the results processed by boundaryGap are positive/negative?\n      // If so, here `minFixed`/`maxFixed` need to be set.\n    }\n    var determinedMin = this._determinedMin;\n    var determinedMax = this._determinedMax;\n    if (determinedMin != null) {\n      min = determinedMin;\n      minFixed = true;\n    }\n    if (determinedMax != null) {\n      max = determinedMax;\n      maxFixed = true;\n    }\n    // Ensure min/max be finite number or NaN here. (not to be null/undefined)\n    // `NaN` means min/max axis is blank.\n    return {\n      min: min,\n      max: max,\n      minFixed: minFixed,\n      maxFixed: maxFixed,\n      isBlank: isBlank\n    };\n  };\n  ScaleRawExtentInfo.prototype.modifyDataMinMax = function (minMaxName, val) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(!this.frozen);\n    }\n    this[DATA_MIN_MAX_ATTR[minMaxName]] = val;\n  };\n  ScaleRawExtentInfo.prototype.setDeterminedMinMax = function (minMaxName, val) {\n    var attr = DETERMINED_MIN_MAX_ATTR[minMaxName];\n    if (process.env.NODE_ENV !== 'production') {\n      assert(!this.frozen\n      // Earse them usually means logic flaw.\n      && this[attr] == null);\n    }\n    this[attr] = val;\n  };\n  ScaleRawExtentInfo.prototype.freeze = function () {\n    // @ts-ignore\n    this.frozen = true;\n  };\n  return ScaleRawExtentInfo;\n}();\nexport { ScaleRawExtentInfo };\nvar DETERMINED_MIN_MAX_ATTR = {\n  min: '_determinedMin',\n  max: '_determinedMax'\n};\nvar DATA_MIN_MAX_ATTR = {\n  min: '_dataMin',\n  max: '_dataMax'\n};\n/**\r\n * Get scale min max and related info only depends on model settings.\r\n * This method can be called after coordinate system created.\r\n * For example, in data processing stage.\r\n *\r\n * Scale extent info probably be required multiple times during a workflow.\r\n * For example:\r\n * (1) `dataZoom` depends it to get the axis extent in \"100%\" state.\r\n * (2) `processor/extentCalculator` depends it to make sure whether axis extent is specified.\r\n * (3) `coordSys.update` use it to finally decide the scale extent.\r\n * But the callback of `min`/`max` should not be called multiple times.\r\n * The code below should not be implemented repeatedly either.\r\n * So we cache the result in the scale instance, which will be recreated at the beginning\r\n * of the workflow (because `scale` instance will be recreated each round of the workflow).\r\n */\nexport function ensureScaleRawExtentInfo(scale, model,\n// Usually: data extent from all series on this axis.\noriginalExtent) {\n  // Do not permit to recreate.\n  var rawExtentInfo = scale.rawExtentInfo;\n  if (rawExtentInfo) {\n    return rawExtentInfo;\n  }\n  rawExtentInfo = new ScaleRawExtentInfo(scale, model, originalExtent);\n  // @ts-ignore\n  scale.rawExtentInfo = rawExtentInfo;\n  return rawExtentInfo;\n}\nexport function parseAxisModelMinMax(scale, minMax) {\n  return minMax == null ? null : eqNaN(minMax) ? NaN : scale.parse(minMax);\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,MAAM,EAAEC,OAAO,EAAEC,KAAK,EAAEC,UAAU,QAAQ,0BAA0B;AAC7E,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,IAAIC,kBAAkB,GAAG,aAAa,YAAY;EAChD,SAASA,kBAAkBA,CAACC,KAAK,EAAEC,KAAK;EACxC;EACAC,cAAc,EAAE;IACd,IAAI,CAACC,cAAc,CAACH,KAAK,EAAEC,KAAK,EAAEC,cAAc,CAAC;EACnD;EACA;AACF;AACA;AACA;EACEH,kBAAkB,CAACK,SAAS,CAACD,cAAc,GAAG,UAAUH,KAAK,EAAEC,KAAK;EACpE;EACAI,UAAU,EAAE;IACV,IAAIA,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,EAAE;MACjCA,UAAU,GAAG,CAACC,GAAG,EAAEA,GAAG,CAAC;IACzB;IACA,IAAI,CAACC,QAAQ,GAAGF,UAAU,CAAC,CAAC,CAAC;IAC7B,IAAI,CAACG,QAAQ,GAAGH,UAAU,CAAC,CAAC,CAAC;IAC7B,IAAII,SAAS,GAAG,IAAI,CAACC,UAAU,GAAGV,KAAK,CAACW,IAAI,KAAK,SAAS;IAC1D,IAAI,CAACC,cAAc,GAAGZ,KAAK,CAACW,IAAI,KAAK,UAAU,IAAIV,KAAK,CAACY,gBAAgB,IAAIZ,KAAK,CAACY,gBAAgB,CAAC,CAAC;IACrG,IAAIC,YAAY,GAAGb,KAAK,CAACc,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC;IACzC,IAAID,YAAY,IAAI,IAAI,EAAE;MACxBA,YAAY,GAAGb,KAAK,CAACc,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC;IAC9C;IACA,IAAIC,WAAW,GAAG,IAAI,CAACC,YAAY,GAAGH,YAAY;IAClD,IAAIjB,UAAU,CAACmB,WAAW,CAAC,EAAE;MAC3B;MACA,IAAI,CAACE,YAAY,GAAGC,oBAAoB,CAACnB,KAAK,EAAEgB,WAAW,CAAC;QAC1DI,GAAG,EAAEf,UAAU,CAAC,CAAC,CAAC;QAClBgB,GAAG,EAAEhB,UAAU,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC;IACL,CAAC,MAAM,IAAIW,WAAW,KAAK,SAAS,EAAE;MACpC,IAAI,CAACE,YAAY,GAAGC,oBAAoB,CAACnB,KAAK,EAAEgB,WAAW,CAAC;IAC9D;IACA,IAAIM,WAAW,GAAG,IAAI,CAACC,YAAY,GAAGtB,KAAK,CAACc,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC;IAC5D,IAAIlB,UAAU,CAACyB,WAAW,CAAC,EAAE;MAC3B;MACA,IAAI,CAACE,YAAY,GAAGL,oBAAoB,CAACnB,KAAK,EAAEsB,WAAW,CAAC;QAC1DF,GAAG,EAAEf,UAAU,CAAC,CAAC,CAAC;QAClBgB,GAAG,EAAEhB,UAAU,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC;IACL,CAAC,MAAM,IAAIiB,WAAW,KAAK,SAAS,EAAE;MACpC,IAAI,CAACE,YAAY,GAAGL,oBAAoB,CAACnB,KAAK,EAAEsB,WAAW,CAAC;IAC9D;IACA,IAAIb,SAAS,EAAE;MACb;MACA;MACA;MACA,IAAI,CAACgB,YAAY,GAAGxB,KAAK,CAACyB,aAAa,CAAC,CAAC,CAACC,MAAM;IAClD,CAAC,MAAM;MACL,IAAIC,WAAW,GAAG3B,KAAK,CAACc,GAAG,CAAC,aAAa,CAAC;MAC1C,IAAIc,cAAc,GAAGlC,OAAO,CAACiC,WAAW,CAAC,GAAGA,WAAW,GAAG,CAACA,WAAW,IAAI,CAAC,EAAEA,WAAW,IAAI,CAAC,CAAC;MAC9F,IAAI,OAAOC,cAAc,CAAC,CAAC,CAAC,KAAK,SAAS,IAAI,OAAOA,cAAc,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;QACpF,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzCC,OAAO,CAACC,IAAI,CAAC,uCAAuC,GAAG,iDAAiD,GAAG,8CAA8C,GAAG,6BAA6B,CAAC;QAC5L;QACA,IAAI,CAACC,iBAAiB,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;MACjC,CAAC,MAAM;QACL,IAAI,CAACA,iBAAiB,GAAG,CAACrC,YAAY,CAAC+B,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE/B,YAAY,CAAC+B,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACnG;IACF;EACF,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;EACE9B,kBAAkB,CAACK,SAAS,CAACgC,SAAS,GAAG,YAAY;IACnD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI3B,SAAS,GAAG,IAAI,CAACC,UAAU;IAC/B,IAAI2B,OAAO,GAAG,IAAI,CAAC9B,QAAQ;IAC3B,IAAI+B,OAAO,GAAG,IAAI,CAAC9B,QAAQ;IAC3B,IAAI+B,WAAW,GAAG,IAAI,CAACd,YAAY;IACnC,IAAIe,gBAAgB,GAAG,IAAI,CAACL,iBAAiB;IAC7C,IAAIM,IAAI,GAAG,CAAChC,SAAS,GAAG6B,OAAO,GAAGD,OAAO,IAAIK,IAAI,CAACC,GAAG,CAACN,OAAO,CAAC,GAAG,IAAI;IACrE;IACA;IACA,IAAIjB,GAAG,GAAG,IAAI,CAACH,YAAY,KAAK,SAAS,GAAGoB,OAAO,GAAG,IAAI,CAACnB,YAAY;IACvE,IAAIG,GAAG,GAAG,IAAI,CAACE,YAAY,KAAK,SAAS,GAAGe,OAAO,GAAG,IAAI,CAACd,YAAY;IACvE;IACA,IAAIoB,QAAQ,GAAGxB,GAAG,IAAI,IAAI;IAC1B,IAAIyB,QAAQ,GAAGxB,GAAG,IAAI,IAAI;IAC1B,IAAID,GAAG,IAAI,IAAI,EAAE;MACfA,GAAG,GAAGX,SAAS,GAAG8B,WAAW,GAAG,CAAC,GAAGjC,GAAG,GAAG+B,OAAO,GAAGG,gBAAgB,CAAC,CAAC,CAAC,GAAGC,IAAI;IAChF;IACA,IAAIpB,GAAG,IAAI,IAAI,EAAE;MACfA,GAAG,GAAGZ,SAAS,GAAG8B,WAAW,GAAGA,WAAW,GAAG,CAAC,GAAGjC,GAAG,GAAGgC,OAAO,GAAGE,gBAAgB,CAAC,CAAC,CAAC,GAAGC,IAAI;IAC9F;IACA,CAACrB,GAAG,IAAI,IAAI,IAAI,CAAC0B,QAAQ,CAAC1B,GAAG,CAAC,MAAMA,GAAG,GAAGd,GAAG,CAAC;IAC9C,CAACe,GAAG,IAAI,IAAI,IAAI,CAACyB,QAAQ,CAACzB,GAAG,CAAC,MAAMA,GAAG,GAAGf,GAAG,CAAC;IAC9C,IAAIyC,OAAO,GAAGnD,KAAK,CAACwB,GAAG,CAAC,IAAIxB,KAAK,CAACyB,GAAG,CAAC,IAAIZ,SAAS,IAAI,CAAC8B,WAAW;IACnE;IACA,IAAI,IAAI,CAAC3B,cAAc,EAAE;MACvB;MACA,IAAIQ,GAAG,GAAG,CAAC,IAAIC,GAAG,GAAG,CAAC,IAAI,CAACuB,QAAQ,EAAE;QACnCxB,GAAG,GAAG,CAAC;QACP;MACF;MACA;MACA,IAAIA,GAAG,GAAG,CAAC,IAAIC,GAAG,GAAG,CAAC,IAAI,CAACwB,QAAQ,EAAE;QACnCxB,GAAG,GAAG,CAAC;QACP;MACF;MACA;MACA;MACA;MACA;IACF;IACA,IAAI2B,aAAa,GAAG,IAAI,CAACC,cAAc;IACvC,IAAIC,aAAa,GAAG,IAAI,CAACC,cAAc;IACvC,IAAIH,aAAa,IAAI,IAAI,EAAE;MACzB5B,GAAG,GAAG4B,aAAa;MACnBJ,QAAQ,GAAG,IAAI;IACjB;IACA,IAAIM,aAAa,IAAI,IAAI,EAAE;MACzB7B,GAAG,GAAG6B,aAAa;MACnBL,QAAQ,GAAG,IAAI;IACjB;IACA;IACA;IACA,OAAO;MACLzB,GAAG,EAAEA,GAAG;MACRC,GAAG,EAAEA,GAAG;MACRuB,QAAQ,EAAEA,QAAQ;MAClBC,QAAQ,EAAEA,QAAQ;MAClBE,OAAO,EAAEA;IACX,CAAC;EACH,CAAC;EACDhD,kBAAkB,CAACK,SAAS,CAACgD,gBAAgB,GAAG,UAAUC,UAAU,EAAEC,GAAG,EAAE;IACzE,IAAIxB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCtC,MAAM,CAAC,CAAC,IAAI,CAAC6D,MAAM,CAAC;IACtB;IACA,IAAI,CAACC,iBAAiB,CAACH,UAAU,CAAC,CAAC,GAAGC,GAAG;EAC3C,CAAC;EACDvD,kBAAkB,CAACK,SAAS,CAACqD,mBAAmB,GAAG,UAAUJ,UAAU,EAAEC,GAAG,EAAE;IAC5E,IAAII,IAAI,GAAGC,uBAAuB,CAACN,UAAU,CAAC;IAC9C,IAAIvB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCtC,MAAM,CAAC,CAAC,IAAI,CAAC6D;MACb;MAAA,GACG,IAAI,CAACG,IAAI,CAAC,IAAI,IAAI,CAAC;IACxB;IACA,IAAI,CAACA,IAAI,CAAC,GAAGJ,GAAG;EAClB,CAAC;EACDvD,kBAAkB,CAACK,SAAS,CAACwD,MAAM,GAAG,YAAY;IAChD;IACA,IAAI,CAACL,MAAM,GAAG,IAAI;EACpB,CAAC;EACD,OAAOxD,kBAAkB;AAC3B,CAAC,CAAC,CAAC;AACH,SAASA,kBAAkB;AAC3B,IAAI4D,uBAAuB,GAAG;EAC5BvC,GAAG,EAAE,gBAAgB;EACrBC,GAAG,EAAE;AACP,CAAC;AACD,IAAImC,iBAAiB,GAAG;EACtBpC,GAAG,EAAE,UAAU;EACfC,GAAG,EAAE;AACP,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASwC,wBAAwBA,CAAC7D,KAAK,EAAEC,KAAK;AACrD;AACAC,cAAc,EAAE;EACd;EACA,IAAI4D,aAAa,GAAG9D,KAAK,CAAC8D,aAAa;EACvC,IAAIA,aAAa,EAAE;IACjB,OAAOA,aAAa;EACtB;EACAA,aAAa,GAAG,IAAI/D,kBAAkB,CAACC,KAAK,EAAEC,KAAK,EAAEC,cAAc,CAAC;EACpE;EACAF,KAAK,CAAC8D,aAAa,GAAGA,aAAa;EACnC,OAAOA,aAAa;AACtB;AACA,OAAO,SAAS3C,oBAAoBA,CAACnB,KAAK,EAAE+D,MAAM,EAAE;EAClD,OAAOA,MAAM,IAAI,IAAI,GAAG,IAAI,GAAGnE,KAAK,CAACmE,MAAM,CAAC,GAAGzD,GAAG,GAAGN,KAAK,CAACgE,KAAK,CAACD,MAAM,CAAC;AAC1E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}