<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI优化修复验证</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
            line-height: 1.6;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
        .test-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 14px;
        }
        .test-button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result-box {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        .comparison-item h4 {
            margin-top: 0;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 AI优化修复验证</h1>
            <p>测试不同JD是否产生不同的优化结果</p>
        </div>

        <div class="test-section">
            <h3>📝 测试简历内容</h3>
            <textarea id="resumeText" class="test-input" rows="8" placeholder="输入简历内容...">John Smith
Software Engineer

Experience:
- 3 years of Python development experience
- Worked on web applications using Django
- Familiar with JavaScript and React
- Database experience with MySQL

Education:
- Bachelor's degree in Computer Science

Skills:
- Python, JavaScript, HTML, CSS
- Django, React, MySQL
- Problem solving, teamwork</textarea>
        </div>

        <div class="test-section">
            <h3>🎯 测试职位描述</h3>
            <div style="margin-bottom: 15px;">
                <button class="test-button" onclick="setJD1()">测试JD 1: Python后端开发</button>
                <button class="test-button" onclick="setJD2()">测试JD 2: 全栈工程师</button>
                <button class="test-button" onclick="setJD3()">测试JD 3: 数据科学家</button>
            </div>
            <textarea id="jobDescription" class="test-input" rows="6" placeholder="输入职位描述...">我们正在寻找一名高级Python开发工程师，要求5年以上Python开发经验，熟悉Django或Flask框架，具备PostgreSQL数据库经验，优秀的团队协作能力。</textarea>
        </div>

        <div class="test-section">
            <h3>🚀 执行测试</h3>
            <button class="test-button" onclick="testOptimization()">开始优化测试</button>
            <button class="test-button" onclick="clearResults()">清除结果</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        let testResults = [];

        function setJD1() {
            document.getElementById('jobDescription').value = `我们正在寻找一名高级Python后端开发工程师。

要求：
- 5年以上Python开发经验
- 熟悉Django或Flask框架
- PostgreSQL数据库经验
- RESTful API设计经验
- 微服务架构经验
- 优秀的团队协作能力
- 计算机科学相关学位`;
        }

        function setJD2() {
            document.getElementById('jobDescription').value = `招聘全栈工程师，负责前后端开发。

职位要求：
- 3年以上全栈开发经验
- 前端：React, JavaScript, HTML, CSS
- 后端：Node.js, Python
- 数据库：MongoDB, MySQL
- 云服务：AWS或Azure经验
- 敏捷开发经验
- 良好的沟通能力`;
        }

        function setJD3() {
            document.getElementById('jobDescription').value = `数据科学家职位，专注于机器学习和数据分析。

岗位需求：
- 数据科学或相关专业硕士学位
- Python数据分析经验（pandas, numpy）
- 机器学习框架：TensorFlow, PyTorch
- 统计学和数学基础
- SQL数据库查询能力
- 数据可视化经验
- 研究和分析能力`;
        }

        async function testOptimization() {
            const resumeText = document.getElementById('resumeText').value.trim();
            const jobDescription = document.getElementById('jobDescription').value.trim();

            if (!resumeText || !jobDescription) {
                alert('请填写简历内容和职位描述');
                return;
            }

            const resultsDiv = document.getElementById('results');
            
            // 添加测试进行中的提示
            const testingDiv = document.createElement('div');
            testingDiv.className = 'result-box';
            testingDiv.innerHTML = '🔄 正在进行AI优化测试，请稍候...';
            resultsDiv.appendChild(testingDiv);

            try {
                // 模拟AI优化过程（实际应该调用后端API）
                const result = await simulateOptimization(resumeText, jobDescription);
                
                // 移除进行中提示
                resultsDiv.removeChild(testingDiv);
                
                // 显示结果
                displayResult(result, jobDescription);
                
            } catch (error) {
                testingDiv.className = 'result-box error';
                testingDiv.innerHTML = `❌ 测试失败: ${error.message}`;
            }
        }

        async function simulateOptimization(resumeText, jobDescription) {
            // 模拟AI优化逻辑（基于我们修复的备用优化逻辑）
            await new Promise(resolve => setTimeout(resolve, 2000)); // 模拟处理时间

            // 基础JD分析
            const jdAnalysis = analyzeJobDescription(jobDescription);
            
            // 生成优化内容
            const optimizedContent = generateOptimizedContent(resumeText, jobDescription, jdAnalysis);
            
            return {
                success: true,
                optimized_resume: optimizedContent,
                jd_json: jdAnalysis,
                original_length: resumeText.length,
                optimized_length: optimizedContent.length
            };
        }

        function analyzeJobDescription(jobDescription) {
            const jdLower = jobDescription.toLowerCase();
            
            // 技术技能检测
            const techSkills = ['python', 'javascript', 'react', 'django', 'flask', 'node.js', 'mysql', 'postgresql', 'mongodb', 'aws', 'azure', 'tensorflow', 'pytorch', 'pandas', 'numpy'];
            const foundTechSkills = techSkills.filter(skill => jdLower.includes(skill));
            
            // 软技能检测
            const softSkills = ['teamwork', 'communication', 'leadership', 'problem solving', 'collaboration'];
            const foundSoftSkills = softSkills.filter(skill => jdLower.includes(skill));
            
            // 职位标题提取
            let title = 'Target Position';
            if (jdLower.includes('python')) title = 'Python Developer';
            if (jdLower.includes('全栈')) title = 'Full Stack Developer';
            if (jdLower.includes('数据科学')) title = 'Data Scientist';
            
            return {
                title: title,
                core_skills: foundTechSkills,
                soft_skills: foundSoftSkills,
                keywords: foundTechSkills.reduce((acc, skill) => {
                    acc[skill] = 4;
                    return acc;
                }, {})
            };
        }

        function generateOptimizedContent(resumeText, jobDescription, jdAnalysis) {
            const sections = [];
            
            sections.push("# 📄 OPTIMIZED RESUME");
            sections.push(`*Tailored for: ${jdAnalysis.title}*`);
            sections.push("");
            
            sections.push("## 🎯 KEY OPTIMIZATION RECOMMENDATIONS");
            sections.push(`*Based on analysis of: ${jobDescription.substring(0, 100)}...*`);
            sections.push("");
            
            if (jdAnalysis.core_skills.length > 0) {
                sections.push(`**Essential Technical Skills to Highlight:** ${jdAnalysis.core_skills.join(', ')}`);
            }
            
            if (jdAnalysis.soft_skills.length > 0) {
                sections.push(`**Soft Skills to Emphasize:** ${jdAnalysis.soft_skills.join(', ')}`);
            }
            
            sections.push("");
            sections.push("## 🚀 TARGETED OPTIMIZATION STRATEGY");
            sections.push("");
            
            // 根据JD内容生成具体建议
            if (jobDescription.toLowerCase().includes('senior') || jobDescription.toLowerCase().includes('高级')) {
                sections.push("**Leadership Focus:** Emphasize management experience, team leadership, and strategic thinking.");
            }
            
            if (jdAnalysis.core_skills.length > 0) {
                sections.push(`**Technical Focus:** Highlight experience with ${jdAnalysis.core_skills.slice(0, 3).join(', ')} and related projects.`);
            }
            
            sections.push("");
            sections.push("## 📋 ENHANCED RESUME CONTENT");
            sections.push("");
            sections.push(resumeText);
            
            return sections.join('\n');
        }

        function displayResult(result, jobDescription) {
            const resultsDiv = document.getElementById('results');
            
            const resultDiv = document.createElement('div');
            resultDiv.className = 'test-section';
            
            const timestamp = new Date().toLocaleTimeString();
            const jdPreview = jobDescription.substring(0, 50) + '...';
            
            resultDiv.innerHTML = `
                <h4>🎯 测试结果 - ${timestamp}</h4>
                <p><strong>JD预览:</strong> ${jdPreview}</p>
                <div class="comparison">
                    <div class="comparison-item">
                        <h4>📊 统计信息</h4>
                        <p>原始长度: ${result.original_length} 字符</p>
                        <p>优化后长度: ${result.optimized_length} 字符</p>
                        <p>变化: +${result.optimized_length - result.original_length} 字符</p>
                    </div>
                    <div class="comparison-item">
                        <h4>🔍 JD分析结果</h4>
                        <p>职位: ${result.jd_json.title}</p>
                        <p>技术技能: ${result.jd_json.core_skills.join(', ') || '无'}</p>
                        <p>软技能: ${result.jd_json.soft_skills.join(', ') || '无'}</p>
                    </div>
                </div>
                <div class="result-box success">
${result.optimized_resume}
                </div>
            `;
            
            resultsDiv.appendChild(resultDiv);
            testResults.push({timestamp, jobDescription, result});
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            testResults = [];
        }
    </script>
</body>
</html>
