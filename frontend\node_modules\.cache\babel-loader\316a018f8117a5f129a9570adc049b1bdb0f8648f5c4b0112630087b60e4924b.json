{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.createSensor = void 0;\nvar _object = require(\"./object\");\nvar _resizeObserver = require(\"./resizeObserver\");\n/**\n * Created by hustcc on 18/7/5.\n * Contract: <EMAIL>\n */\n\n/**\n * sensor strategies\n */\n// export const createSensor = createObjectSensor;\nvar createSensor = typeof ResizeObserver !== 'undefined' ? _resizeObserver.createSensor : _object.createSensor;\nexports.createSensor = createSensor;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "createSensor", "_object", "require", "_resizeObserver", "ResizeObserver"], "sources": ["E:/AI/SmartCV/node_modules/size-sensor/lib/sensors/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.createSensor = void 0;\nvar _object = require(\"./object\");\nvar _resizeObserver = require(\"./resizeObserver\");\n/**\n * Created by hustcc on 18/7/5.\n * Contract: <EMAIL>\n */\n\n/**\n * sensor strategies\n */\n// export const createSensor = createObjectSensor;\nvar createSensor = typeof ResizeObserver !== 'undefined' ? _resizeObserver.createSensor : _object.createSensor;\nexports.createSensor = createSensor;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,YAAY,GAAG,KAAK,CAAC;AAC7B,IAAIC,OAAO,GAAGC,OAAO,CAAC,UAAU,CAAC;AACjC,IAAIC,eAAe,GAAGD,OAAO,CAAC,kBAAkB,CAAC;AACjD;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,IAAIF,YAAY,GAAG,OAAOI,cAAc,KAAK,WAAW,GAAGD,eAAe,CAACH,YAAY,GAAGC,OAAO,CAACD,YAAY;AAC9GF,OAAO,CAACE,YAAY,GAAGA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}