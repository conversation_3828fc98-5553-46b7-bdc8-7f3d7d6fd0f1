import os
import json
import logging
import re
from openai import AzureOpenAI
from dotenv import load_dotenv
from typing import Dict, List, Tuple

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 初始化Azure OpenAI客户端
client = AzureOpenAI(
    api_key=os.getenv("AZURE_OPENAI_API_KEY"),
    api_version=os.getenv("AZURE_OPENAI_API_VERSION"),
    azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT")
)

def optimize_resume_with_ai(client, resume_text: str, job_description: str):
    """
    优化简历主流程：JD解析->匹配分析->结构化优化建议->输出JSON
    """
    try:
        logger.info("Starting professional resume optimization, applying ATS best practices...")
        # 1. JD结构化解析
        jd_json = analyze_job_description(job_description)
        # 2. 匹配度分析
        match_json = analyze_resume_match(resume_text, jd_json)
        # 3. 匹配度评分（可选，保留原有评分算法）
        original_match_score = calculate_job_match_score(resume_text, job_description, jd_json)
        # 4. 构建结构化优化Prompt
        system_prompt = "你是一个专为HR系统优化简历的语言专家，所有内容必须基于原文，不得虚构。输出JSON。"
        user_prompt = create_optimization_prompt(resume_text, job_description, jd_json, match_json)
        # 5. 调用GPT生成结构化优化建议
        response = client.chat.completions.create(
            model=os.getenv("AZURE_OPENAI_GPT4_DEPLOYMENT"),
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            max_tokens=4000,
            temperature=0.3,
            top_p=0.8
        )
        result_text = response.choices[0].message.content.strip()
        optimized_json = parse_gpt_json_response(result_text)
        # 6. 结构化输出
        return {
            'optimized_resume_json': optimized_json if isinstance(optimized_json, list) else [],
            'jd_json': jd_json,
            'match_json': match_json,
            'original_match_score': original_match_score,
            'ats_risks': [],  # ATS结构风险后续补充
            'error': optimized_json.get('error') if isinstance(optimized_json, dict) and 'error' in optimized_json else ''
        }
    except Exception as e:
        logger.error(f"AI resume optimization failed: {str(e)}")
        return {
            'optimized_resume_json': [],
            'jd_json': {},
            'match_json': {},
            'original_match_score': {},
            'ats_risks': [],
            'error': str(e)
        }

def analyze_job_description(job_description: str) -> dict:
    """
    用GPT-4解析JD，输出结构化JSON，包含title、core_skills、soft_skills、keywords（带权重）。
    """
    prompt = f"""
请分析以下职位描述，提取：
1. 职位名称（title）
2. 核心硬技能（core_skills）
3. 所需软技能（soft_skills）
4. 关键词及其重要性（keywords，格式为{{"关键词": 权重}}，权重为1-5，综合出现频率和语义相关性）

请严格以如下JSON格式输出：
{{
  "title": "",
  "core_skills": [],
  "soft_skills": [],
  "keywords": {{}}
}}

职位描述：
{job_description}
"""
    try:
        response = client.chat.completions.create(
            model=os.getenv("AZURE_OPENAI_GPT4_DEPLOYMENT"),
            messages=[
                {"role": "system", "content": "你是一个专业的JD解析专家，擅长结构化提取职位描述关键信息。"},
                {"role": "user", "content": prompt}
            ],
            temperature=0.2,
            max_tokens=800
        )
        result_text = response.choices[0].message.content.strip()
        # 尝试解析JSON
        try:
            jd_json = json.loads(result_text)
            logger.info("JD解析成功，已提取结构化数据")
            return jd_json
        except json.JSONDecodeError:
            logger.warning("JD解析结果不是有效JSON，尝试用正则修复")
            # 可选：用正则提取JSON片段再解析
            match = re.search(r'\{[\s\S]+\}', result_text)
            if match:
                try:
                    jd_json = json.loads(match.group())
                    return jd_json
                except Exception:
                    pass
            # fallback
            return {
                "title": "",
                "core_skills": [],
                "soft_skills": [],
                "keywords": {}
            }
    except Exception as e:
        logger.error(f"JD解析失败: {str(e)}")
        return {
            "title": "",
            "core_skills": [],
            "soft_skills": [],
            "keywords": {}
        }

def analyze_resume_structure(resume_text: str) -> Dict:
    """分析简历结构和现有内容"""
    analysis = {
        'has_contact_info': bool(re.search(r'(email|phone|linkedin)', resume_text, re.IGNORECASE)),
        'has_summary': bool(re.search(r'(summary|profile|objective)', resume_text, re.IGNORECASE)),
        'has_experience': bool(re.search(r'(experience|employment|work)', resume_text, re.IGNORECASE)),
        'has_education': bool(re.search(r'(education|degree|university)', resume_text, re.IGNORECASE)),
        'has_skills': bool(re.search(r'(skills|technical|competencies)', resume_text, re.IGNORECASE)),
        'has_quantified_achievements': bool(re.search(r'\d+(%|k|million|billion|\$)', resume_text)),
        'length': len(resume_text),
        'sections_identified': identify_resume_sections(resume_text)
    }
    return analysis

def create_ats_optimized_system_prompt() -> str:
    """Create an English system prompt for ATS-optimized resume rewriting."""
    return (
        "You are a world-class resume optimization expert, specializing in ATS (Applicant Tracking System) optimization. "
        "You deeply understand how ATS works, including keyword matching, format parsing, semantic understanding, and ranking factors. "
        "Your goal is to rewrite resumes to maximize ATS compatibility and job matching, while keeping all content strictly factual and based on the original resume. "
        "Always output in rich markdown format: use clear English section headings, bold key points, bullet lists, tables, and emojis for section headers. "
        "Never fabricate or add any information not present in the original resume."
    )

def create_optimization_prompt(resume_text, job_description, jd_json, match_json):
    return (
        "你是一个专为HR系统优化简历的语言专家。请根据以下简历和职位描述，优化简历内容以提升通过ATS系统的概率，但**严禁编造虚假内容**，必须基于原内容合理扩展或重写。\n\n"
        f"简历原文：\n{resume_text}\n\n"
        f"职位描述：\n{job_description}\n\n"
        f"JD结构化信息：\n{json.dumps(jd_json, ensure_ascii=False)}\n\n"
        f"匹配分析结果：\n{json.dumps(match_json, ensure_ascii=False)}\n\n"
        "请输出优化建议内容，保持原有段落结构，每段注明所依据的JD关键词（用jd_keywords字段标注），并以**严格JSON数组**格式返回，示例：\n"
        "[\n"
        "  {\"section\": \"Experience\", \"original\": \"...\", \"optimized\": \"...\", \"jd_keywords\": [\"Agile\", \"Scrum\"]},\n"
        "  {\"section\": \"Education\", \"original\": \"...\", \"optimized\": \"...\", \"jd_keywords\": [\"Bachelor\"]}\n"
        "]\n"
        "不要输出任何多余内容。"
    )

def validate_optimization_result(optimized_resume: str, jd_analysis: Dict) -> Dict:
    """验证优化结果质量"""
    issues = []
    
    # 检查关键词包含情况
    resume_lower = optimized_resume.lower()
    key_skills = jd_analysis.get('hard_skills', [])
    missing_keywords = [skill for skill in key_skills[:5] if skill.lower() not in resume_lower]
    
    if missing_keywords:
        issues.append(f"Missing key skills: {', '.join(missing_keywords)}")
    
    # 检查格式标准性
    if not re.search(r'(EXPERIENCE|WORK EXPERIENCE|PROFESSIONAL EXPERIENCE)', optimized_resume, re.IGNORECASE):
        issues.append("Missing standard work experience section")
    
    if not re.search(r'(SKILLS|TECHNICAL SKILLS|CORE COMPETENCIES)', optimized_resume, re.IGNORECASE):
        issues.append("Missing skills section")
    
    # 检查量化成就
    if not re.search(r'\d+(%|k|million|billion|\$|year|month)', optimized_resume):
        issues.append("Missing quantified achievements")
    
    return {
        'is_valid': len(issues) == 0,
        'issues': issues
    }

def fix_optimization_issues(client, optimized_resume: str, issues: List[str]) -> str:
    """修复优化问题"""
    try:
        fix_prompt = f"""
Please fix the following resume optimization issues:

Issues to address:
{chr(10).join(f'- {issue}' for issue in issues)}

Current resume:
{optimized_resume}

Please return the complete fixed resume in English using markdown format. Do not fabricate any content - all modifications must be based on the current resume and original information.
"""
        
        response = client.chat.completions.create(
            model=os.getenv("AZURE_OPENAI_GPT4_DEPLOYMENT"),
            messages=[
                {"role": "system", "content": "You are a professional resume optimization expert, specializing in fixing ATS compatibility issues. Always output in English using rich markdown formatting."},
                {"role": "user", "content": fix_prompt}
            ],
            max_tokens=3000,
            temperature=0.2
        )
        
        return response.choices[0].message.content.strip()
        
    except Exception as e:
        logger.error(f"Failed to fix optimization issues: {str(e)}")
        return optimized_resume

def extract_keywords_advanced(job_description: str) -> Dict:
    """高级关键词提取（fallback方法）"""
    text_lower = job_description.lower()
    
    # 扩展的技术关键词库
    tech_keywords = [
        'python', 'java', 'javascript', 'typescript', 'react', 'angular', 'vue',
        'node.js', 'express', 'django', 'flask', 'spring', 'sql', 'mysql', 
        'postgresql', 'mongodb', 'redis', 'aws', 'azure', 'gcp', 'docker', 
        'kubernetes', 'jenkins', 'git', 'ci/cd', 'agile', 'scrum', 'rest api',
        'microservices', 'machine learning', 'data science', 'ai', 'tensorflow',
        'pytorch', 'pandas', 'numpy', 'html', 'css', 'sass', 'webpack',
        'blockchain', 'devops', 'cloud computing', 'big data', 'analytics'
    ]
    
    soft_skills = [
        'leadership', 'communication', 'teamwork', 'problem solving',
        'analytical thinking', 'creativity', 'collaboration', 'adaptability',
        'project management', 'time management', 'critical thinking',
        'decision making', 'mentoring', 'coaching', 'presentation'
    ]
    
    action_verbs = [
        'developed', 'implemented', 'designed', 'created', 'managed', 'led',
        'optimized', 'improved', 'increased', 'reduced', 'built', 'delivered',
        'achieved', 'collaborated', 'coordinated', 'analyzed', 'researched'
    ]
    
    found_tech = [kw for kw in tech_keywords if kw in text_lower]
    found_soft = [kw for kw in soft_skills if kw in text_lower]
    found_verbs = [kw for kw in action_verbs if kw in text_lower]
    
    return {
        'hard_skills': found_tech,
        'soft_skills': found_soft,
        'action_verbs': found_verbs,
        'industry_terms': [],
        'education_requirements': [],
        'experience_requirements': [],
        'quantifiable_metrics': []
    }

def identify_resume_sections(resume_text: str) -> List[str]:
    """识别简历中的各个部分"""
    sections = []
    common_sections = [
        'contact', 'summary', 'objective', 'experience', 'education', 
        'skills', 'projects', 'certifications', 'awards', 'languages'
    ]
    
    for section in common_sections:
        if re.search(rf'\b{section}\b', resume_text, re.IGNORECASE):
            sections.append(section)
    
    return sections

def create_enhanced_fallback_optimization(resume_text: str, job_description: str, error_msg: str) -> str:
    """Enhanced fallback optimization plan in English"""
    logger.info("Using enhanced fallback optimization...")
    
    # Basic keyword extraction
    jd_analysis = extract_keywords_advanced(job_description)
    
    return f"""
# 📄 Resume Optimization Results

> **System Notice**: AI optimization service is temporarily unavailable. Below are algorithm-based optimization suggestions and your original resume.

## 🎯 Key Optimization Recommendations

### 🔧 Essential Technical Keywords:
{', '.join(jd_analysis['hard_skills'][:10]) if jd_analysis['hard_skills'] else 'Please carefully review technical requirements in the job description'}

### 💡 Important Soft Skills:
{', '.join(jd_analysis['soft_skills'][:5]) if jd_analysis['soft_skills'] else 'Communication, Teamwork, Problem-solving, Leadership, Adaptability'}

### ⚡ Recommended Action Verbs:
{', '.join(jd_analysis['action_verbs'][:8]) if jd_analysis['action_verbs'] else 'Developed, Implemented, Managed, Led, Optimized, Created, Delivered, Achieved'}

## 📄 Your Original Resume

{resume_text}

## 🔧 ATS Optimization Checklist

- [ ] **Keyword Density**: Ensure your resume includes important keywords from the job description
- [ ] **Format Standardization**: Use standard section headers (EXPERIENCE, EDUCATION, SKILLS)
- [ ] **Achievement Quantification**: Include 2-3 quantified achievements for each position
- [ ] **Action Words**: Start each responsibility description with strong action verbs
- [ ] **Relevance Ranking**: Place most relevant experience and skills at the top
- [ ] **Length Control**: Maintain 1-2 page length
- [ ] **Contact Information**: Ensure contact details are clearly visible and professional

## 📊 Optimization Suggestions

### For Work Experience:
- Use bullet points starting with action verbs
- Include specific metrics and numbers where possible
- Focus on achievements rather than just responsibilities
- Tailor descriptions to match job requirements

### For Skills Section:
- List technical skills first, matching job requirements
- Group related skills together
- Include proficiency levels if relevant
- Remove outdated or irrelevant skills

### For Professional Summary:
- Write 2-3 sentences highlighting your key qualifications
- Include years of experience and main expertise areas
- Mention specific technologies or methodologies relevant to the target role

## 🚀 Next Steps

Please retry the AI optimization feature later, or manually adjust your resume based on the above recommendations.

---

**Technical Details**: {error_msg}
"""

def calculate_job_match_score(resume_text: str, job_description: str, jd_analysis: Dict) -> Dict:
    """计算简历与职位描述的匹配度"""
    try:
        resume_lower = resume_text.lower()
        jd_lower = job_description.lower()
        
        # 1. 技能匹配度 (40%权重)
        hard_skills = jd_analysis.get('hard_skills', [])
        matched_hard_skills = [skill for skill in hard_skills if skill.lower() in resume_lower]
        hard_skills_score = (len(matched_hard_skills) / max(len(hard_skills), 1)) * 100 if hard_skills else 0
        
        # 2. 软技能匹配度 (20%权重)
        soft_skills = jd_analysis.get('soft_skills', [])
        matched_soft_skills = [skill for skill in soft_skills if skill.lower() in resume_lower]
        soft_skills_score = (len(matched_soft_skills) / max(len(soft_skills), 1)) * 100 if soft_skills else 0
        
        # 3. 行业术语匹配度 (15%权重)
        industry_terms = jd_analysis.get('industry_terms', [])
        matched_industry_terms = [term for term in industry_terms if term.lower() in resume_lower]
        industry_score = (len(matched_industry_terms) / max(len(industry_terms), 1)) * 100 if industry_terms else 0
        
        # 4. 动作词匹配度 (10%权重)
        action_verbs = jd_analysis.get('action_verbs', [])
        matched_action_verbs = [verb for verb in action_verbs if verb.lower() in resume_lower]
        action_verbs_score = (len(matched_action_verbs) / max(len(action_verbs), 1)) * 100 if action_verbs else 0
        
        # 5. 教育背景匹配度 (10%权重)
        education_requirements = jd_analysis.get('education_requirements', [])
        education_score = 0
        if education_requirements:
            education_keywords = ['degree', 'bachelor', 'master', 'phd', 'university', 'college', 'education']
            resume_has_education = any(keyword in resume_lower for keyword in education_keywords)
            education_score = 80 if resume_has_education else 40
        else:
            education_score = 70  # 没有特定要求时给中等分
        
        # 6. 经验相关性 (5%权重)
        experience_score = 60  # 基础分，可以通过更复杂的算法改进
        if any(keyword in resume_lower for keyword in ['experience', 'worked', 'years', 'developed', 'managed']):
            experience_score = 80
        
        # 计算加权总分
        total_score = (
            hard_skills_score * 0.40 +
            soft_skills_score * 0.20 +
            industry_score * 0.15 +
            action_verbs_score * 0.10 +
            education_score * 0.10 +
            experience_score * 0.05
        )
        
        # 确保分数在0-100之间
        total_score = max(0, min(100, total_score))
        
        return {
            'overall_match_score': round(total_score, 1),
            'breakdown': {
                'hard_skills': {
                    'score': round(hard_skills_score, 1),
                    'matched': matched_hard_skills,
                    'total': hard_skills,
                    'weight': '40%'
                },
                'soft_skills': {
                    'score': round(soft_skills_score, 1),
                    'matched': matched_soft_skills,
                    'total': soft_skills,
                    'weight': '20%'
                },
                'industry_terms': {
                    'score': round(industry_score, 1),
                    'matched': matched_industry_terms,
                    'total': industry_terms,
                    'weight': '15%'
                },
                'action_verbs': {
                    'score': round(action_verbs_score, 1),
                    'matched': matched_action_verbs,
                    'total': action_verbs,
                    'weight': '10%'
                },
                'education': {
                    'score': round(education_score, 1),
                    'weight': '10%'
                },
                'experience': {
                    'score': round(experience_score, 1),
                    'weight': '5%'
                }
            }
        }
        
    except Exception as e:
        logger.error(f"Error calculating job match score: {str(e)}")
        return {
            'overall_match_score': 0,
            'breakdown': {},
            'error': str(e)
        }

def analyze_resume_match(resume_text: str, jd_json: dict) -> dict:
    """
    用GPT分析简历与JD的匹配度，输出结构化JSON：
    {
      "matched_keywords": [...],
      "missing_keywords": [...],
      "suggested_sections": [
        {"section": "Experience", "line": 12, "suggestion": "..."}
      ]
    }
    """
    prompt = f"""
请对以下简历与JD结构化信息进行比对，输出：
1. 已匹配的JD关键词（matched_keywords，数组）
2. 缺失或不够突出的JD关键词（missing_keywords，数组）
3. 建议优化的简历段落（suggested_sections，数组，每项包含section、line（如能定位）、suggestion）

JD结构化信息：
{json.dumps(jd_json, ensure_ascii=False)}

简历原文：
{resume_text}

请以如下JSON格式输出：
{
  "matched_keywords": [...],
  "missing_keywords": [...],
  "suggested_sections": [
    {"section": "Experience", "line": 12, "suggestion": "..."}
  ]
}
"""
    try:
        response = client.chat.completions.create(
            model=os.getenv("AZURE_OPENAI_GPT4_DEPLOYMENT"),
            messages=[
                {"role": "system", "content": "你是一个专业的简历与JD匹配分析专家，输出结构化JSON。"},
                {"role": "user", "content": prompt}
            ],
            temperature=0.2,
            max_tokens=1200
        )
        result_text = response.choices[0].message.content.strip()
        try:
            match_json = json.loads(result_text)
            return match_json
        except json.JSONDecodeError:
            match = re.search(r'\{[\s\S]+\}', result_text)
            if match:
                try:
                    match_json = json.loads(match.group())
                    return match_json
                except Exception:
                    pass
            return {
                "matched_keywords": [],
                "missing_keywords": [],
                "suggested_sections": []
            }
    except Exception as e:
        logger.error(f"简历匹配度分析失败: {str(e)}")
        return {
            "matched_keywords": [],
            "missing_keywords": [],
            "suggested_sections": []
        }

def parse_gpt_json_response(response_text):
    """健壮解析GPT返回的JSON，失败时返回详细错误信息"""
    import json, re
    try:
        # 先尝试直接解析
        return json.loads(response_text)
    except Exception:
        # 尝试用正则提取JSON数组
        match = re.search(r'(\[.*\])', response_text, re.DOTALL)
        if match:
            try:
                return json.loads(match.group(1))
            except Exception as e:
                return {"error": f"JSON解析失败: {str(e)}\n原始内容: {match.group(1)[:500]}..."}
        return {"error": f"GPT返回内容不是有效JSON: {response_text[:500]}..."} 