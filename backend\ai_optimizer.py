import os
import json
import logging
import re
from openai import AzureOpenAI
from dotenv import load_dotenv
from typing import Dict, List, Tuple

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 初始化Azure OpenAI客户端
client = AzureOpenAI(
    api_key=os.getenv("AZURE_OPENAI_API_KEY"),
    api_version=os.getenv("AZURE_OPENAI_API_VERSION"),
    azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT")
)

def optimize_resume_with_ai(client, resume_text: str, job_description: str):
    """使用OpenAI GPT-4o优化简历，专注于ATS兼容性和关键词匹配"""
    try:
        logger.info("Starting professional resume optimization, applying ATS best practices...")
        
        # 第一步：分析JD提取关键信息
        jd_analysis = analyze_job_description(job_description)
        
        # 第二步：计算原始简历与JD的匹配度
        original_match_score = calculate_job_match_score(resume_text, job_description, jd_analysis)
        logger.info(f"Original resume job match score: {original_match_score['overall_match_score']}%")
        
        # 第三步：分析简历结构和内容
        resume_analysis = analyze_resume_structure(resume_text)
        
        # 第四步：构建专业优化提示词
        system_prompt = create_ats_optimized_system_prompt()
        user_prompt = create_optimization_prompt(resume_text, job_description, jd_analysis, resume_analysis)
        
        # 调用OpenAI API进行优化
        response = client.chat.completions.create(
            model=os.getenv("AZURE_OPENAI_GPT4_DEPLOYMENT"),
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            max_tokens=4000,
            temperature=0.3,  # 降低温度确保更精确的输出
            top_p=0.8
        )
        
        optimized_resume = response.choices[0].message.content.strip()
        
        # 验证优化结果（但不因验证失败而走fallback）
        validation_result = validate_optimization_result(optimized_resume, jd_analysis)
        if not validation_result['is_valid']:
            logger.warning(f"Optimization result validation failed: {validation_result['issues']}")
            # 尝试修复但不强制要求成功
            try:
                fixed_resume = fix_optimization_issues(client, optimized_resume, validation_result['issues'])
                if len(fixed_resume) > len(optimized_resume) * 0.8:  # 确保修复后的内容不会太短
                    optimized_resume = fixed_resume
                    logger.info("Optimization issues fixed successfully")
            except Exception as fix_error:
                logger.warning(f"Error fixing optimization issues, using original optimized result: {str(fix_error)}")
        
        # 计算优化后简历与JD的匹配度
        optimized_match_score = calculate_job_match_score(optimized_resume, job_description, jd_analysis)
        logger.info(f"Optimized resume job match score: {optimized_match_score['overall_match_score']}%")
        
        logger.info(f"Professional resume optimization completed, optimized length: {len(optimized_resume)}")
        
        # 返回包含匹配度信息的结果
        return {
            'optimized_resume': optimized_resume,
            'original_match_score': original_match_score,
            'optimized_match_score': optimized_match_score,
            'match_improvement': round(optimized_match_score['overall_match_score'] - original_match_score['overall_match_score'], 1)
        }
        
    except Exception as e:
        logger.error(f"AI resume optimization failed: {str(e)}")
        # 即使使用fallback，也要计算匹配度
        fallback_result = create_enhanced_fallback_optimization(resume_text, job_description, str(e))
        try:
            jd_analysis = extract_keywords_advanced(job_description)
            original_match_score = calculate_job_match_score(resume_text, job_description, jd_analysis)
            return {
                'optimized_resume': fallback_result,
                'original_match_score': original_match_score,
                'optimized_match_score': original_match_score,  # fallback时使用相同分数
                'match_improvement': 0
            }
        except:
            return fallback_result  # 如果匹配度计算也失败，返回原始fallback

def analyze_job_description(job_description: str) -> Dict:
    """深度分析职位描述，提取ATS关键信息"""
    try:
        response = client.chat.completions.create(
            model=os.getenv("AZURE_OPENAI_GPT4_DEPLOYMENT"),
            messages=[
                {"role": "system", "content": "You are a professional resume optimization expert. Analyze job descriptions and extract key information in JSON format."},
                {"role": "user", "content": f"""Analyze the following job description and extract key information. Return ONLY a valid JSON object with this structure:
{{
    "hard_skills": ["skill1", "skill2", ...],
    "soft_skills": ["skill1", "skill2", ...],
    "action_verbs": ["verb1", "verb2", ...],
    "industry_terms": ["term1", "term2", ...],
    "education_requirements": ["requirement1", ...],
    "experience_requirements": ["requirement1", ...],
    "quantifiable_metrics": ["metric1", ...]
}}

Job Description:
{job_description}"""}
            ],
            temperature=0.2,
            max_tokens=800
        )
        
        result_text = response.choices[0].message.content.strip()
        
        # 尝试解析JSON
        try:
            jd_analysis = json.loads(result_text)
            logger.info("JD analysis successful, extracted structured data")
            return jd_analysis
        except json.JSONDecodeError:
            logger.warning("JD analysis result is not valid JSON, using text parsing")
            # 如果JSON解析失败，使用fallback方法
            return extract_keywords_advanced(job_description)
            
    except Exception as e:
        logger.error(f"JD analysis failed: {str(e)}")
        # 如果API调用失败，使用fallback方法
        return extract_keywords_advanced(job_description)

def analyze_resume_structure(resume_text: str) -> Dict:
    """分析简历结构和现有内容"""
    analysis = {
        'has_contact_info': bool(re.search(r'(email|phone|linkedin)', resume_text, re.IGNORECASE)),
        'has_summary': bool(re.search(r'(summary|profile|objective)', resume_text, re.IGNORECASE)),
        'has_experience': bool(re.search(r'(experience|employment|work)', resume_text, re.IGNORECASE)),
        'has_education': bool(re.search(r'(education|degree|university)', resume_text, re.IGNORECASE)),
        'has_skills': bool(re.search(r'(skills|technical|competencies)', resume_text, re.IGNORECASE)),
        'has_quantified_achievements': bool(re.search(r'\d+(%|k|million|billion|\$)', resume_text)),
        'length': len(resume_text),
        'sections_identified': identify_resume_sections(resume_text)
    }
    return analysis

def create_ats_optimized_system_prompt() -> str:
    """Create an English system prompt for ATS-optimized resume rewriting."""
    return (
        "You are a world-class resume optimization expert, specializing in ATS (Applicant Tracking System) optimization. "
        "You deeply understand how ATS works, including keyword matching, format parsing, semantic understanding, and ranking factors. "
        "Your goal is to rewrite resumes to maximize ATS compatibility and job matching, while keeping all content strictly factual and based on the original resume. "
        "Always output in rich markdown format: use clear English section headings, bold key points, bullet lists, tables, and emojis for section headers. "
        "Never fabricate or add any information not present in the original resume."
    )

def create_optimization_prompt(resume_text: str, job_description: str, jd_analysis: dict, resume_analysis: dict) -> str:
    """Create a detailed English prompt for resume optimization."""
    return f"""
Please optimize the following resume for the target job description, ensuring maximum ATS compatibility and job relevance. All output must be in English and use rich markdown formatting (headings, bold, bullet points, tables, and emojis for section headers). Do not fabricate or add any information not present in the original resume.

## 🎯 Target Job Analysis
- **Key Skills:** {', '.join(jd_analysis.get('hard_skills', [])[:10])}
- **Soft Skills:** {', '.join(jd_analysis.get('soft_skills', [])[:5])}
- **Industry Terms:** {', '.join(jd_analysis.get('industry_terms', [])[:5])}
- **Action Verbs:** {', '.join(jd_analysis.get('action_verbs', [])[:8])}

## 📋 Current Resume Status
- Contact Info: {'✓' if resume_analysis['has_contact_info'] else '✗'}
- Professional Summary: {'✓' if resume_analysis['has_summary'] else '✗'}
- Work Experience: {'✓' if resume_analysis['has_experience'] else '✗'}
- Quantified Achievements: {'✓' if resume_analysis['has_quantified_achievements'] else '✗'}

## 🛠️ Optimization Requirements
1. **Integrate keywords** from the target job naturally.
2. **Quantify achievements** for each experience. If the original text lacks quantifiable data, do NOT fabricate, but list suggestions in a separate "Revision Note" section at the end.
3. **Highlight skills** matching the job description.
4. **Standardize format** for ATS-friendliness (use standard English section titles: Experience, Education, Skills, etc.).
5. **Order by relevance** (most relevant experience first).
6. **Strict authenticity**: Never add or invent any experience, certificate, project, or achievement not present in the original resume.

## 📝 Job Description
{job_description}

## 📄 Original Resume
{resume_text}

---

**Return the fully optimized resume in English, using markdown. If any content cannot be quantified, add a final section titled "Revision Note" with two paragraphs: one for "Modification Summary" and one for "Optimization Suggestions" (both in English).**
"""

def validate_optimization_result(optimized_resume: str, jd_analysis: Dict) -> Dict:
    """验证优化结果质量"""
    issues = []
    
    # 检查关键词包含情况
    resume_lower = optimized_resume.lower()
    key_skills = jd_analysis.get('hard_skills', [])
    missing_keywords = [skill for skill in key_skills[:5] if skill.lower() not in resume_lower]
    
    if missing_keywords:
        issues.append(f"Missing key skills: {', '.join(missing_keywords)}")
    
    # 检查格式标准性
    if not re.search(r'(EXPERIENCE|WORK EXPERIENCE|PROFESSIONAL EXPERIENCE)', optimized_resume, re.IGNORECASE):
        issues.append("Missing standard work experience section")
    
    if not re.search(r'(SKILLS|TECHNICAL SKILLS|CORE COMPETENCIES)', optimized_resume, re.IGNORECASE):
        issues.append("Missing skills section")
    
    # 检查量化成就
    if not re.search(r'\d+(%|k|million|billion|\$|year|month)', optimized_resume):
        issues.append("Missing quantified achievements")
    
    return {
        'is_valid': len(issues) == 0,
        'issues': issues
    }

def fix_optimization_issues(client, optimized_resume: str, issues: List[str]) -> str:
    """修复优化问题"""
    try:
        fix_prompt = f"""
Please fix the following resume optimization issues:

Issues to address:
{chr(10).join(f'- {issue}' for issue in issues)}

Current resume:
{optimized_resume}

Please return the complete fixed resume in English using markdown format. Do not fabricate any content - all modifications must be based on the current resume and original information.
"""
        
        response = client.chat.completions.create(
            model=os.getenv("AZURE_OPENAI_GPT4_DEPLOYMENT"),
            messages=[
                {"role": "system", "content": "You are a professional resume optimization expert, specializing in fixing ATS compatibility issues. Always output in English using rich markdown formatting."},
                {"role": "user", "content": fix_prompt}
            ],
            max_tokens=3000,
            temperature=0.2
        )
        
        return response.choices[0].message.content.strip()
        
    except Exception as e:
        logger.error(f"Failed to fix optimization issues: {str(e)}")
        return optimized_resume

def extract_keywords_advanced(job_description: str) -> Dict:
    """高级关键词提取（fallback方法）"""
    text_lower = job_description.lower()
    
    # 扩展的技术关键词库
    tech_keywords = [
        'python', 'java', 'javascript', 'typescript', 'react', 'angular', 'vue',
        'node.js', 'express', 'django', 'flask', 'spring', 'sql', 'mysql', 
        'postgresql', 'mongodb', 'redis', 'aws', 'azure', 'gcp', 'docker', 
        'kubernetes', 'jenkins', 'git', 'ci/cd', 'agile', 'scrum', 'rest api',
        'microservices', 'machine learning', 'data science', 'ai', 'tensorflow',
        'pytorch', 'pandas', 'numpy', 'html', 'css', 'sass', 'webpack',
        'blockchain', 'devops', 'cloud computing', 'big data', 'analytics'
    ]
    
    soft_skills = [
        'leadership', 'communication', 'teamwork', 'problem solving',
        'analytical thinking', 'creativity', 'collaboration', 'adaptability',
        'project management', 'time management', 'critical thinking',
        'decision making', 'mentoring', 'coaching', 'presentation'
    ]
    
    action_verbs = [
        'developed', 'implemented', 'designed', 'created', 'managed', 'led',
        'optimized', 'improved', 'increased', 'reduced', 'built', 'delivered',
        'achieved', 'collaborated', 'coordinated', 'analyzed', 'researched'
    ]
    
    found_tech = [kw for kw in tech_keywords if kw in text_lower]
    found_soft = [kw for kw in soft_skills if kw in text_lower]
    found_verbs = [kw for kw in action_verbs if kw in text_lower]
    
    return {
        'hard_skills': found_tech,
        'soft_skills': found_soft,
        'action_verbs': found_verbs,
        'industry_terms': [],
        'education_requirements': [],
        'experience_requirements': [],
        'quantifiable_metrics': []
    }

def identify_resume_sections(resume_text: str) -> List[str]:
    """识别简历中的各个部分"""
    sections = []
    common_sections = [
        'contact', 'summary', 'objective', 'experience', 'education', 
        'skills', 'projects', 'certifications', 'awards', 'languages'
    ]
    
    for section in common_sections:
        if re.search(rf'\b{section}\b', resume_text, re.IGNORECASE):
            sections.append(section)
    
    return sections

def create_enhanced_fallback_optimization(resume_text: str, job_description: str, error_msg: str) -> str:
    """Enhanced fallback optimization plan in English"""
    logger.info("Using enhanced fallback optimization...")
    
    # Basic keyword extraction
    jd_analysis = extract_keywords_advanced(job_description)
    
    return f"""
# 📄 Resume Optimization Results

> **System Notice**: AI optimization service is temporarily unavailable. Below are algorithm-based optimization suggestions and your original resume.

## 🎯 Key Optimization Recommendations

### 🔧 Essential Technical Keywords:
{', '.join(jd_analysis['hard_skills'][:10]) if jd_analysis['hard_skills'] else 'Please carefully review technical requirements in the job description'}

### 💡 Important Soft Skills:
{', '.join(jd_analysis['soft_skills'][:5]) if jd_analysis['soft_skills'] else 'Communication, Teamwork, Problem-solving, Leadership, Adaptability'}

### ⚡ Recommended Action Verbs:
{', '.join(jd_analysis['action_verbs'][:8]) if jd_analysis['action_verbs'] else 'Developed, Implemented, Managed, Led, Optimized, Created, Delivered, Achieved'}

## 📄 Your Original Resume

{resume_text}

## 🔧 ATS Optimization Checklist

- [ ] **Keyword Density**: Ensure your resume includes important keywords from the job description
- [ ] **Format Standardization**: Use standard section headers (EXPERIENCE, EDUCATION, SKILLS)
- [ ] **Achievement Quantification**: Include 2-3 quantified achievements for each position
- [ ] **Action Words**: Start each responsibility description with strong action verbs
- [ ] **Relevance Ranking**: Place most relevant experience and skills at the top
- [ ] **Length Control**: Maintain 1-2 page length
- [ ] **Contact Information**: Ensure contact details are clearly visible and professional

## 📊 Optimization Suggestions

### For Work Experience:
- Use bullet points starting with action verbs
- Include specific metrics and numbers where possible
- Focus on achievements rather than just responsibilities
- Tailor descriptions to match job requirements

### For Skills Section:
- List technical skills first, matching job requirements
- Group related skills together
- Include proficiency levels if relevant
- Remove outdated or irrelevant skills

### For Professional Summary:
- Write 2-3 sentences highlighting your key qualifications
- Include years of experience and main expertise areas
- Mention specific technologies or methodologies relevant to the target role

## 🚀 Next Steps

Please retry the AI optimization feature later, or manually adjust your resume based on the above recommendations.

---

**Technical Details**: {error_msg}
"""

def calculate_job_match_score(resume_text: str, job_description: str, jd_analysis: Dict) -> Dict:
    """计算简历与职位描述的匹配度"""
    try:
        resume_lower = resume_text.lower()
        jd_lower = job_description.lower()
        
        # 1. 技能匹配度 (40%权重)
        hard_skills = jd_analysis.get('hard_skills', [])
        matched_hard_skills = [skill for skill in hard_skills if skill.lower() in resume_lower]
        hard_skills_score = (len(matched_hard_skills) / max(len(hard_skills), 1)) * 100 if hard_skills else 0
        
        # 2. 软技能匹配度 (20%权重)
        soft_skills = jd_analysis.get('soft_skills', [])
        matched_soft_skills = [skill for skill in soft_skills if skill.lower() in resume_lower]
        soft_skills_score = (len(matched_soft_skills) / max(len(soft_skills), 1)) * 100 if soft_skills else 0
        
        # 3. 行业术语匹配度 (15%权重)
        industry_terms = jd_analysis.get('industry_terms', [])
        matched_industry_terms = [term for term in industry_terms if term.lower() in resume_lower]
        industry_score = (len(matched_industry_terms) / max(len(industry_terms), 1)) * 100 if industry_terms else 0
        
        # 4. 动作词匹配度 (10%权重)
        action_verbs = jd_analysis.get('action_verbs', [])
        matched_action_verbs = [verb for verb in action_verbs if verb.lower() in resume_lower]
        action_verbs_score = (len(matched_action_verbs) / max(len(action_verbs), 1)) * 100 if action_verbs else 0
        
        # 5. 教育背景匹配度 (10%权重)
        education_requirements = jd_analysis.get('education_requirements', [])
        education_score = 0
        if education_requirements:
            education_keywords = ['degree', 'bachelor', 'master', 'phd', 'university', 'college', 'education']
            resume_has_education = any(keyword in resume_lower for keyword in education_keywords)
            education_score = 80 if resume_has_education else 40
        else:
            education_score = 70  # 没有特定要求时给中等分
        
        # 6. 经验相关性 (5%权重)
        experience_score = 60  # 基础分，可以通过更复杂的算法改进
        if any(keyword in resume_lower for keyword in ['experience', 'worked', 'years', 'developed', 'managed']):
            experience_score = 80
        
        # 计算加权总分
        total_score = (
            hard_skills_score * 0.40 +
            soft_skills_score * 0.20 +
            industry_score * 0.15 +
            action_verbs_score * 0.10 +
            education_score * 0.10 +
            experience_score * 0.05
        )
        
        # 确保分数在0-100之间
        total_score = max(0, min(100, total_score))
        
        return {
            'overall_match_score': round(total_score, 1),
            'breakdown': {
                'hard_skills': {
                    'score': round(hard_skills_score, 1),
                    'matched': matched_hard_skills,
                    'total': hard_skills,
                    'weight': '40%'
                },
                'soft_skills': {
                    'score': round(soft_skills_score, 1),
                    'matched': matched_soft_skills,
                    'total': soft_skills,
                    'weight': '20%'
                },
                'industry_terms': {
                    'score': round(industry_score, 1),
                    'matched': matched_industry_terms,
                    'total': industry_terms,
                    'weight': '15%'
                },
                'action_verbs': {
                    'score': round(action_verbs_score, 1),
                    'matched': matched_action_verbs,
                    'total': action_verbs,
                    'weight': '10%'
                },
                'education': {
                    'score': round(education_score, 1),
                    'weight': '10%'
                },
                'experience': {
                    'score': round(experience_score, 1),
                    'weight': '5%'
                }
            }
        }
        
    except Exception as e:
        logger.error(f"Error calculating job match score: {str(e)}")
        return {
            'overall_match_score': 0,
            'breakdown': {},
            'error': str(e)
        } 