import os
import json
import logging
import re
from openai import AzureOpenAI
from dotenv import load_dotenv
from typing import Dict, List, Tuple

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 初始化Azure OpenAI客户端
client = AzureOpenAI(
    api_key=os.getenv("AZURE_OPENAI_API_KEY"),
    api_version=os.getenv("AZURE_OPENAI_API_VERSION"),
    azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT")
)

def optimize_resume_with_ai(client, resume_text: str, job_description: str):
    """
    优化简历主流程：JD解析->匹配分析->结构化优化建议->输出JSON
    """
    try:
        logger.info("Starting professional resume optimization, applying ATS best practices...")
        # 1. JD结构化解析
        jd_json = analyze_job_description(job_description)
        # 2. 匹配度分析
        match_json = analyze_resume_match(resume_text, jd_json)
        # 3. 匹配度评分（可选，保留原有评分算法）
        original_match_score = calculate_job_match_score(resume_text, job_description, jd_json)
        # 4. 构建结构化优化Prompt
        system_prompt = "你是一个专为HR系统优化简历的语言专家，所有内容必须基于原文，不得虚构。输出JSON。"
        user_prompt = create_optimization_prompt(resume_text, job_description, jd_json, match_json)
        # 5. 调用GPT生成结构化优化建议
        logger.info(f"🤖 准备调用AI模型: {os.getenv('AZURE_OPENAI_GPT4_DEPLOYMENT')}")
        logger.info(f"📝 用户提示词长度: {len(user_prompt)} 字符")
        logger.info(f"📋 JD解析结果: {jd_json}")
        logger.info(f"🔍 匹配分析结果: {match_json}")

        try:
            response = client.chat.completions.create(
                model=os.getenv("AZURE_OPENAI_GPT4_DEPLOYMENT"),
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                max_tokens=4000,
                temperature=0.3,
                top_p=0.8
            )
            result_text = response.choices[0].message.content.strip()
            logger.info(f"✅ AI响应成功，长度: {len(result_text)} 字符")
            logger.info(f"📄 AI响应完整内容: {result_text}")
        except Exception as api_error:
            logger.error(f"❌ AI API调用失败: {str(api_error)}")
            raise api_error

        try:
            # 先用正则提取以 [ 开头的JSON数组
            match = re.search(r'\[.*\]', result_text, re.DOTALL)
            if match:
                json_text = match.group()
                logger.info(f"提取的JSON文本: {json_text[:200]}...")
                try:
                    optimized_json = json.loads(json_text)
                    logger.info(f"JSON解析成功，包含 {len(optimized_json)} 个项目")
                except Exception as json_error:
                    logger.error(f"JSON解析失败: {str(json_error)}")
                    # 尝试用ast.literal_eval兜底
                    import ast
                    try:
                        optimized_json = ast.literal_eval(json_text)
                        logger.info(f"AST解析成功，包含 {len(optimized_json)} 个项目")
                    except Exception as ast_error:
                        logger.error(f"AST解析也失败: {str(ast_error)}")
                        optimized_json = []
            else:
                logger.warning("未找到JSON数组格式，尝试直接解析整个响应")
                try:
                    optimized_json = json.loads(result_text)
                    logger.info(f"直接JSON解析成功，包含 {len(optimized_json)} 个项目")
                except Exception:
                    logger.error("直接JSON解析失败，返回空数组")
                    optimized_json = []
        except Exception as e:
            optimized_json = []
            logger.error(f"优化内容解析失败: {str(e)}")
        # 6. 从结构化数据生成文本版本
        optimized_text = ""
        if optimized_json and isinstance(optimized_json, list) and len(optimized_json) > 0:
            text_parts = []
            for item in optimized_json:
                if isinstance(item, dict):
                    section = item.get('section', '')
                    optimized = item.get('optimized', '')
                    if section and optimized:
                        text_parts.append(f"{section}:\n{optimized}")
                    elif optimized:
                        text_parts.append(optimized)
            optimized_text = '\n\n'.join(text_parts)
            logger.info(f"从JSON生成文本版本，长度: {len(optimized_text)}")
        else:
            # 如果AI返回空结果，使用备用优化逻辑
            logger.warning("AI返回空结果，使用备用优化逻辑")
            optimized_text = create_fallback_optimization(resume_text, job_description, jd_json, match_json)
            logger.info(f"备用优化完成，长度: {len(optimized_text)}")

            # 同时创建对应的JSON结构
            optimized_json = [
                {
                    "section": "Complete Resume",
                    "original": resume_text[:200] + "...",
                    "optimized": optimized_text,
                    "jd_keywords": list(jd_json.get('keywords', {}).keys())[:5]
                }
            ]

        # 7. 结构化输出
        return {
            'optimized_resume_json': optimized_json,
            'optimized_resume': optimized_text,  # 添加文本版本
            'jd_json': jd_json,
            'match_json': match_json,
            'original_match_score': original_match_score,
            'ats_risks': []  # ATS结构风险后续补充
        }
    except Exception as e:
        logger.error(f"AI resume optimization failed: {str(e)}")
        return {
            'optimized_resume_json': [],
            'optimized_resume': '',  # 添加空的文本版本
            'jd_json': {},
            'match_json': {},
            'original_match_score': {},
            'ats_risks': [],
            'error': str(e)
        }

def analyze_job_description(job_description: str) -> dict:
    """
    用GPT-4解析JD，输出结构化JSON，包含title、core_skills、soft_skills、keywords（带权重）。
    """
    prompt = f"""
请分析以下职位描述，提取：
1. 职位名称（title）
2. 核心硬技能（core_skills）
3. 所需软技能（soft_skills）
4. 关键词及其重要性（keywords，格式为{{"关键词": 权重}}，权重为1-5，综合出现频率和语义相关性）

请严格以如下JSON格式输出：
{{
  "title": "",
  "core_skills": [],
  "soft_skills": [],
  "keywords": {{}}
}}

职位描述：
{job_description}
"""
    try:
        logger.info(f"🔍 开始JD解析，职位描述长度: {len(job_description)} 字符")
        response = client.chat.completions.create(
            model=os.getenv("AZURE_OPENAI_GPT4_DEPLOYMENT"),
            messages=[
                {"role": "system", "content": "你是一个专业的JD解析专家，擅长结构化提取职位描述关键信息。"},
                {"role": "user", "content": prompt}
            ],
            temperature=0.2,
            max_tokens=800
        )
        result_text = response.choices[0].message.content.strip()
        logger.info(f"📋 JD解析AI响应: {result_text}")

        # 尝试解析JSON
        try:
            jd_json = json.loads(result_text)
            logger.info(f"✅ JD解析成功，提取到: title='{jd_json.get('title', '')}', core_skills={len(jd_json.get('core_skills', []))}, keywords={len(jd_json.get('keywords', {}))}")
            return jd_json
        except json.JSONDecodeError:
            logger.warning("JD解析结果不是有效JSON，尝试用正则修复")
            # 可选：用正则提取JSON片段再解析
            match = re.search(r'\{[\s\S]+\}', result_text)
            if match:
                try:
                    jd_json = json.loads(match.group())
                    return jd_json
                except Exception:
                    pass
            # fallback - 使用基础关键词提取
            logger.info("使用基础关键词提取作为JD解析备用方案")
            return create_basic_jd_analysis(job_description)
    except Exception as e:
        logger.error(f"JD解析失败: {str(e)}")
        logger.info("使用基础关键词提取作为JD解析备用方案")
        return create_basic_jd_analysis(job_description)

def create_basic_jd_analysis(job_description: str) -> dict:
    """基础JD分析，当AI解析失败时使用"""
    logger.info("执行基础JD分析...")

    jd_lower = job_description.lower()

    # 提取职位标题（通常在开头）
    title_patterns = [
        r'(?:position|role|job|title):\s*([^\n\r]+)',
        r'(?:we are looking for|seeking|hiring)\s+(?:a|an)?\s*([^\n\r,]+)',
        r'^([^\n\r]+?)(?:position|role|job)',
        r'([^\n\r]+?)(?:\s*-\s*job|\s*job\s*description)'
    ]

    title = ""
    for pattern in title_patterns:
        match = re.search(pattern, jd_lower)
        if match:
            title = match.group(1).strip()
            break

    if not title:
        # 如果没找到，取第一行作为标题
        first_line = job_description.split('\n')[0].strip()
        if len(first_line) < 100:  # 合理的标题长度
            title = first_line

    # 技术技能关键词库
    tech_skills = [
        'python', 'java', 'javascript', 'typescript', 'react', 'angular', 'vue',
        'node.js', 'express', 'django', 'flask', 'spring', 'sql', 'mysql',
        'postgresql', 'mongodb', 'redis', 'aws', 'azure', 'gcp', 'docker',
        'kubernetes', 'jenkins', 'git', 'ci/cd', 'agile', 'scrum', 'rest api',
        'microservices', 'machine learning', 'data science', 'ai', 'tensorflow',
        'pytorch', 'pandas', 'numpy', 'html', 'css', 'sass', 'webpack',
        'blockchain', 'devops', 'cloud computing', 'big data', 'analytics'
    ]

    # 软技能关键词库
    soft_skills_keywords = [
        'leadership', 'communication', 'teamwork', 'problem solving',
        'analytical thinking', 'creativity', 'collaboration', 'adaptability',
        'project management', 'time management', 'critical thinking',
        'decision making', 'mentoring', 'coaching', 'presentation'
    ]

    # 提取匹配的技能
    found_tech_skills = [skill for skill in tech_skills if skill in jd_lower]
    found_soft_skills = [skill for skill in soft_skills_keywords if skill in jd_lower]

    # 创建关键词权重字典
    keywords = {}

    # 技术技能权重较高
    for skill in found_tech_skills:
        count = jd_lower.count(skill)
        keywords[skill] = min(5, max(3, count))  # 权重3-5

    # 软技能权重中等
    for skill in found_soft_skills:
        count = jd_lower.count(skill)
        keywords[skill] = min(4, max(2, count))  # 权重2-4

    # 添加一些通用重要关键词
    important_terms = ['experience', 'years', 'degree', 'bachelor', 'master', 'certification']
    for term in important_terms:
        if term in jd_lower:
            keywords[term] = 3

    result = {
        "title": title,
        "core_skills": found_tech_skills[:10],  # 限制数量
        "soft_skills": found_soft_skills[:8],
        "keywords": keywords
    }

    logger.info(f"基础JD分析完成: title='{title}', core_skills={len(found_tech_skills)}, keywords={len(keywords)}")
    return result

def analyze_resume_structure(resume_text: str) -> Dict:
    """分析简历结构和现有内容"""
    analysis = {
        'has_contact_info': bool(re.search(r'(email|phone|linkedin)', resume_text, re.IGNORECASE)),
        'has_summary': bool(re.search(r'(summary|profile|objective)', resume_text, re.IGNORECASE)),
        'has_experience': bool(re.search(r'(experience|employment|work)', resume_text, re.IGNORECASE)),
        'has_education': bool(re.search(r'(education|degree|university)', resume_text, re.IGNORECASE)),
        'has_skills': bool(re.search(r'(skills|technical|competencies)', resume_text, re.IGNORECASE)),
        'has_quantified_achievements': bool(re.search(r'\d+(%|k|million|billion|\$)', resume_text)),
        'length': len(resume_text),
        'sections_identified': identify_resume_sections(resume_text)
    }
    return analysis

def create_ats_optimized_system_prompt() -> str:
    """Create an English system prompt for ATS-optimized resume rewriting."""
    return (
        "You are a world-class resume optimization expert, specializing in ATS (Applicant Tracking System) optimization. "
        "You deeply understand how ATS works, including keyword matching, format parsing, semantic understanding, and ranking factors. "
        "Your goal is to rewrite resumes to maximize ATS compatibility and job matching, while keeping all content strictly factual and based on the original resume. "
        "Always output in rich markdown format: use clear English section headings, bold key points, bullet lists, tables, and emojis for section headers. "
        "Never fabricate or add any information not present in the original resume."
    )

def create_optimization_prompt(resume_text, job_description, jd_json, match_json):
    return (
        "你是一个专为HR系统优化简历的语言专家。请根据以下简历和职位描述，优化简历内容以提升通过ATS系统的概率，但**严禁编造虚假内容**，必须基于原内容合理扩展或重写。\n\n"
        f"简历原文：\n{resume_text}\n\n"
        f"职位描述：\n{job_description}\n\n"
        f"JD结构化信息：\n{json.dumps(jd_json, ensure_ascii=False)}\n\n"
        f"匹配分析结果：\n{json.dumps(match_json, ensure_ascii=False)}\n\n"
        "请输出优化建议内容，保持原有段落结构，每段注明所依据的JD关键词（用jd_keywords字段标注），并以**严格JSON数组**格式返回，示例：\n"
        "[\n"
        "  {\"section\": \"Experience\", \"original\": \"...\", \"optimized\": \"...\", \"jd_keywords\": [\"Agile\", \"Scrum\"]},\n"
        "  {\"section\": \"Education\", \"original\": \"...\", \"optimized\": \"...\", \"jd_keywords\": [\"Bachelor\"]}\n"
        "]\n"
        "不要输出任何多余内容。"
    )

def validate_optimization_result(optimized_resume: str, jd_analysis: Dict) -> Dict:
    """验证优化结果质量"""
    issues = []
    
    # 检查关键词包含情况
    resume_lower = optimized_resume.lower()
    key_skills = jd_analysis.get('hard_skills', [])
    missing_keywords = [skill for skill in key_skills[:5] if skill.lower() not in resume_lower]
    
    if missing_keywords:
        issues.append(f"Missing key skills: {', '.join(missing_keywords)}")
    
    # 检查格式标准性
    if not re.search(r'(EXPERIENCE|WORK EXPERIENCE|PROFESSIONAL EXPERIENCE)', optimized_resume, re.IGNORECASE):
        issues.append("Missing standard work experience section")
    
    if not re.search(r'(SKILLS|TECHNICAL SKILLS|CORE COMPETENCIES)', optimized_resume, re.IGNORECASE):
        issues.append("Missing skills section")
    
    # 检查量化成就
    if not re.search(r'\d+(%|k|million|billion|\$|year|month)', optimized_resume):
        issues.append("Missing quantified achievements")
    
    return {
        'is_valid': len(issues) == 0,
        'issues': issues
    }

def fix_optimization_issues(client, optimized_resume: str, issues: List[str]) -> str:
    """修复优化问题"""
    try:
        fix_prompt = f"""
Please fix the following resume optimization issues:

Issues to address:
{chr(10).join(f'- {issue}' for issue in issues)}

Current resume:
{optimized_resume}

Please return the complete fixed resume in English using markdown format. Do not fabricate any content - all modifications must be based on the current resume and original information.
"""
        
        response = client.chat.completions.create(
            model=os.getenv("AZURE_OPENAI_GPT4_DEPLOYMENT"),
            messages=[
                {"role": "system", "content": "You are a professional resume optimization expert, specializing in fixing ATS compatibility issues. Always output in English using rich markdown formatting."},
                {"role": "user", "content": fix_prompt}
            ],
            max_tokens=3000,
            temperature=0.2
        )
        
        return response.choices[0].message.content.strip()
        
    except Exception as e:
        logger.error(f"Failed to fix optimization issues: {str(e)}")
        return optimized_resume

def extract_keywords_advanced(job_description: str) -> Dict:
    """高级关键词提取（fallback方法）"""
    text_lower = job_description.lower()
    
    # 扩展的技术关键词库
    tech_keywords = [
        'python', 'java', 'javascript', 'typescript', 'react', 'angular', 'vue',
        'node.js', 'express', 'django', 'flask', 'spring', 'sql', 'mysql', 
        'postgresql', 'mongodb', 'redis', 'aws', 'azure', 'gcp', 'docker', 
        'kubernetes', 'jenkins', 'git', 'ci/cd', 'agile', 'scrum', 'rest api',
        'microservices', 'machine learning', 'data science', 'ai', 'tensorflow',
        'pytorch', 'pandas', 'numpy', 'html', 'css', 'sass', 'webpack',
        'blockchain', 'devops', 'cloud computing', 'big data', 'analytics'
    ]
    
    soft_skills = [
        'leadership', 'communication', 'teamwork', 'problem solving',
        'analytical thinking', 'creativity', 'collaboration', 'adaptability',
        'project management', 'time management', 'critical thinking',
        'decision making', 'mentoring', 'coaching', 'presentation'
    ]
    
    action_verbs = [
        'developed', 'implemented', 'designed', 'created', 'managed', 'led',
        'optimized', 'improved', 'increased', 'reduced', 'built', 'delivered',
        'achieved', 'collaborated', 'coordinated', 'analyzed', 'researched'
    ]
    
    found_tech = [kw for kw in tech_keywords if kw in text_lower]
    found_soft = [kw for kw in soft_skills if kw in text_lower]
    found_verbs = [kw for kw in action_verbs if kw in text_lower]
    
    return {
        'hard_skills': found_tech,
        'soft_skills': found_soft,
        'action_verbs': found_verbs,
        'industry_terms': [],
        'education_requirements': [],
        'experience_requirements': [],
        'quantifiable_metrics': []
    }

def identify_resume_sections(resume_text: str) -> List[str]:
    """识别简历中的各个部分"""
    sections = []
    common_sections = [
        'contact', 'summary', 'objective', 'experience', 'education', 
        'skills', 'projects', 'certifications', 'awards', 'languages'
    ]
    
    for section in common_sections:
        if re.search(rf'\b{section}\b', resume_text, re.IGNORECASE):
            sections.append(section)
    
    return sections

def create_fallback_optimization(resume_text: str, job_description: str, jd_json: dict, match_json: dict) -> str:
    """创建备用优化版本，当AI返回空结果时使用 - 根据JD动态生成内容"""
    logger.info("创建备用优化版本...")

    # 提取关键信息
    keywords = list(jd_json.get('keywords', {}).keys())[:10]
    core_skills = jd_json.get('core_skills', [])[:8]
    soft_skills = jd_json.get('soft_skills', [])[:5]
    missing_keywords = match_json.get('missing_keywords', [])[:8]
    job_title = jd_json.get('title', 'Target Position')

    # 基础优化：在原简历基础上添加关键词建议
    optimized_sections = []

    # 添加优化建议标题
    optimized_sections.append("# 📄 OPTIMIZED RESUME")
    optimized_sections.append(f"*Tailored for: {job_title}*")
    optimized_sections.append("")

    # 添加关键词优化建议
    if keywords or core_skills:
        optimized_sections.append("## 🎯 KEY OPTIMIZATION RECOMMENDATIONS")
        optimized_sections.append(f"*Based on analysis of: {job_description[:100]}...*")
        optimized_sections.append("")

        if core_skills:
            optimized_sections.append(f"**Essential Technical Skills to Highlight:** {', '.join(core_skills)}")

        if keywords:
            optimized_sections.append(f"**Important Keywords to Include:** {', '.join(keywords)}")

        if missing_keywords:
            optimized_sections.append(f"**Missing Keywords to Add:** {', '.join(missing_keywords)}")

        if soft_skills:
            optimized_sections.append(f"**Soft Skills to Emphasize:** {', '.join(soft_skills)}")

        optimized_sections.append("")

    # 添加针对性的优化建议
    optimized_sections.append("## 🚀 TARGETED OPTIMIZATION STRATEGY")
    optimized_sections.append("")

    # 根据JD内容生成具体建议
    if 'senior' in job_description.lower() or 'lead' in job_description.lower():
        optimized_sections.append("**Leadership Focus:** Emphasize management experience, team leadership, and strategic thinking.")

    if any(tech in job_description.lower() for tech in ['python', 'java', 'javascript', 'react', 'django']):
        tech_found = [tech for tech in ['Python', 'Java', 'JavaScript', 'React', 'Django'] if tech.lower() in job_description.lower()]
        optimized_sections.append(f"**Technical Focus:** Highlight experience with {', '.join(tech_found)} and related projects.")

    if 'years' in job_description.lower():
        optimized_sections.append("**Experience Emphasis:** Quantify your years of experience and highlight progressive responsibility.")

    optimized_sections.append("")

    # 添加原简历内容
    optimized_sections.append("## 📋 ENHANCED RESUME CONTENT")
    optimized_sections.append("")
    optimized_sections.append(resume_text)

    # 添加针对性的优化建议
    optimized_sections.append("")
    optimized_sections.append("## 💡 SPECIFIC OPTIMIZATION ACTIONS")
    optimized_sections.append("")
    optimized_sections.append("1. **Incorporate the technical skills** mentioned above into your experience descriptions")
    optimized_sections.append("2. **Use action verbs** like: Developed, Implemented, Managed, Led, Optimized, Created")
    optimized_sections.append("3. **Add quantifiable achievements** with numbers, percentages, or metrics")
    optimized_sections.append("4. **Include relevant keywords** naturally throughout your resume")
    optimized_sections.append("5. **Tailor your summary** to match the job requirements")

    # 添加基于JD的具体建议
    if keywords:
        optimized_sections.append(f"6. **Priority Keywords:** Focus on including these high-value terms: {', '.join(keywords[:5])}")

    if missing_keywords:
        optimized_sections.append(f"7. **Gap Analysis:** Consider adding experience or training in: {', '.join(missing_keywords[:3])}")

    return '\n'.join(optimized_sections)

def create_enhanced_fallback_optimization(resume_text: str, job_description: str, error_msg: str) -> str:
    """Enhanced fallback optimization plan in English"""
    logger.info("Using enhanced fallback optimization...")
    
    # Basic keyword extraction
    jd_analysis = extract_keywords_advanced(job_description)
    
    return f"""
# 📄 Resume Optimization Results

> **System Notice**: AI optimization service is temporarily unavailable. Below are algorithm-based optimization suggestions and your original resume.

## 🎯 Key Optimization Recommendations

### 🔧 Essential Technical Keywords:
{', '.join(jd_analysis['hard_skills'][:10]) if jd_analysis['hard_skills'] else 'Please carefully review technical requirements in the job description'}

### 💡 Important Soft Skills:
{', '.join(jd_analysis['soft_skills'][:5]) if jd_analysis['soft_skills'] else 'Communication, Teamwork, Problem-solving, Leadership, Adaptability'}

### ⚡ Recommended Action Verbs:
{', '.join(jd_analysis['action_verbs'][:8]) if jd_analysis['action_verbs'] else 'Developed, Implemented, Managed, Led, Optimized, Created, Delivered, Achieved'}

## 📄 Your Original Resume

{resume_text}

## 🔧 ATS Optimization Checklist

- [ ] **Keyword Density**: Ensure your resume includes important keywords from the job description
- [ ] **Format Standardization**: Use standard section headers (EXPERIENCE, EDUCATION, SKILLS)
- [ ] **Achievement Quantification**: Include 2-3 quantified achievements for each position
- [ ] **Action Words**: Start each responsibility description with strong action verbs
- [ ] **Relevance Ranking**: Place most relevant experience and skills at the top
- [ ] **Length Control**: Maintain 1-2 page length
- [ ] **Contact Information**: Ensure contact details are clearly visible and professional

## 📊 Optimization Suggestions

### For Work Experience:
- Use bullet points starting with action verbs
- Include specific metrics and numbers where possible
- Focus on achievements rather than just responsibilities
- Tailor descriptions to match job requirements

### For Skills Section:
- List technical skills first, matching job requirements
- Group related skills together
- Include proficiency levels if relevant
- Remove outdated or irrelevant skills

### For Professional Summary:
- Write 2-3 sentences highlighting your key qualifications
- Include years of experience and main expertise areas
- Mention specific technologies or methodologies relevant to the target role

## 🚀 Next Steps

Please retry the AI optimization feature later, or manually adjust your resume based on the above recommendations.

---

**Technical Details**: {error_msg}
"""

def calculate_job_match_score(resume_text: str, job_description: str, jd_analysis: Dict) -> Dict:
    """计算简历与职位描述的匹配度"""
    try:
        resume_lower = resume_text.lower()
        jd_lower = job_description.lower()
        
        # 1. 技能匹配度 (40%权重)
        hard_skills = jd_analysis.get('hard_skills', [])
        matched_hard_skills = [skill for skill in hard_skills if skill.lower() in resume_lower]
        hard_skills_score = (len(matched_hard_skills) / max(len(hard_skills), 1)) * 100 if hard_skills else 0
        
        # 2. 软技能匹配度 (20%权重)
        soft_skills = jd_analysis.get('soft_skills', [])
        matched_soft_skills = [skill for skill in soft_skills if skill.lower() in resume_lower]
        soft_skills_score = (len(matched_soft_skills) / max(len(soft_skills), 1)) * 100 if soft_skills else 0
        
        # 3. 行业术语匹配度 (15%权重)
        industry_terms = jd_analysis.get('industry_terms', [])
        matched_industry_terms = [term for term in industry_terms if term.lower() in resume_lower]
        industry_score = (len(matched_industry_terms) / max(len(industry_terms), 1)) * 100 if industry_terms else 0
        
        # 4. 动作词匹配度 (10%权重)
        action_verbs = jd_analysis.get('action_verbs', [])
        matched_action_verbs = [verb for verb in action_verbs if verb.lower() in resume_lower]
        action_verbs_score = (len(matched_action_verbs) / max(len(action_verbs), 1)) * 100 if action_verbs else 0
        
        # 5. 教育背景匹配度 (10%权重)
        education_requirements = jd_analysis.get('education_requirements', [])
        education_score = 0
        if education_requirements:
            education_keywords = ['degree', 'bachelor', 'master', 'phd', 'university', 'college', 'education']
            resume_has_education = any(keyword in resume_lower for keyword in education_keywords)
            education_score = 80 if resume_has_education else 40
        else:
            education_score = 70  # 没有特定要求时给中等分
        
        # 6. 经验相关性 (5%权重)
        experience_score = 60  # 基础分，可以通过更复杂的算法改进
        if any(keyword in resume_lower for keyword in ['experience', 'worked', 'years', 'developed', 'managed']):
            experience_score = 80
        
        # 计算加权总分
        total_score = (
            hard_skills_score * 0.40 +
            soft_skills_score * 0.20 +
            industry_score * 0.15 +
            action_verbs_score * 0.10 +
            education_score * 0.10 +
            experience_score * 0.05
        )
        
        # 确保分数在0-100之间
        total_score = max(0, min(100, total_score))
        
        return {
            'overall_match_score': round(total_score, 1),
            'breakdown': {
                'hard_skills': {
                    'score': round(hard_skills_score, 1),
                    'matched': matched_hard_skills,
                    'total': hard_skills,
                    'weight': '40%'
                },
                'soft_skills': {
                    'score': round(soft_skills_score, 1),
                    'matched': matched_soft_skills,
                    'total': soft_skills,
                    'weight': '20%'
                },
                'industry_terms': {
                    'score': round(industry_score, 1),
                    'matched': matched_industry_terms,
                    'total': industry_terms,
                    'weight': '15%'
                },
                'action_verbs': {
                    'score': round(action_verbs_score, 1),
                    'matched': matched_action_verbs,
                    'total': action_verbs,
                    'weight': '10%'
                },
                'education': {
                    'score': round(education_score, 1),
                    'weight': '10%'
                },
                'experience': {
                    'score': round(experience_score, 1),
                    'weight': '5%'
                }
            }
        }
        
    except Exception as e:
        logger.error(f"Error calculating job match score: {str(e)}")
        return {
            'overall_match_score': 0,
            'breakdown': {},
            'error': str(e)
        }

def analyze_resume_match(resume_text: str, jd_json: dict) -> dict:
    """
    用GPT分析简历与JD的匹配度，输出结构化JSON：
    {
      "matched_keywords": [...],
      "missing_keywords": [...],
      "suggested_sections": [
        {"section": "Experience", "line": 12, "suggestion": "..."}
      ]
    }
    """
    prompt = f"""
请对以下简历与JD结构化信息进行比对，输出：
1. 已匹配的JD关键词（matched_keywords，数组）
2. 缺失或不够突出的JD关键词（missing_keywords，数组）
3. 建议优化的简历段落（suggested_sections，数组，每项包含section、line（如能定位）、suggestion）

JD结构化信息：
{json.dumps(jd_json, ensure_ascii=False)}

简历原文：
{resume_text}

请以如下JSON格式输出：
{
  "matched_keywords": [...],
  "missing_keywords": [...],
  "suggested_sections": [
    {"section": "Experience", "line": 12, "suggestion": "..."}
  ]
}
"""
    try:
        response = client.chat.completions.create(
            model=os.getenv("AZURE_OPENAI_GPT4_DEPLOYMENT"),
            messages=[
                {"role": "system", "content": "你是一个专业的简历与JD匹配分析专家，输出结构化JSON。"},
                {"role": "user", "content": prompt}
            ],
            temperature=0.2,
            max_tokens=1200
        )
        result_text = response.choices[0].message.content.strip()
        try:
            match_json = json.loads(result_text)
            return match_json
        except json.JSONDecodeError:
            match = re.search(r'\{[\s\S]+\}', result_text)
            if match:
                try:
                    match_json = json.loads(match.group())
                    return match_json
                except Exception:
                    pass
            return {
                "matched_keywords": [],
                "missing_keywords": [],
                "suggested_sections": []
            }
    except Exception as e:
        logger.error(f"简历匹配度分析失败: {str(e)}")
        return {
            "matched_keywords": [],
            "missing_keywords": [],
            "suggested_sections": []
        } 