{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport Model from '../../model/Model.js';\nimport { isNameSpecified } from '../../util/model.js';\nimport ComponentModel from '../../model/Component.js';\nvar getDefaultSelectorOptions = function (ecModel, type) {\n  if (type === 'all') {\n    return {\n      type: 'all',\n      title: ecModel.getLocaleModel().get(['legend', 'selector', 'all'])\n    };\n  } else if (type === 'inverse') {\n    return {\n      type: 'inverse',\n      title: ecModel.getLocaleModel().get(['legend', 'selector', 'inverse'])\n    };\n  }\n};\nvar LegendModel = /** @class */function (_super) {\n  __extends(LegendModel, _super);\n  function LegendModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = LegendModel.type;\n    _this.layoutMode = {\n      type: 'box',\n      // legend.width/height are maxWidth/maxHeight actually,\n      // whereas real width/height is calculated by its content.\n      // (Setting {left: 10, right: 10} does not make sense).\n      // So consider the case:\n      // `setOption({legend: {left: 10});`\n      // then `setOption({legend: {right: 10});`\n      // The previous `left` should be cleared by setting `ignoreSize`.\n      ignoreSize: true\n    };\n    return _this;\n  }\n  LegendModel.prototype.init = function (option, parentModel, ecModel) {\n    this.mergeDefaultAndTheme(option, ecModel);\n    option.selected = option.selected || {};\n    this._updateSelector(option);\n  };\n  LegendModel.prototype.mergeOption = function (option, ecModel) {\n    _super.prototype.mergeOption.call(this, option, ecModel);\n    this._updateSelector(option);\n  };\n  LegendModel.prototype._updateSelector = function (option) {\n    var selector = option.selector;\n    var ecModel = this.ecModel;\n    if (selector === true) {\n      selector = option.selector = ['all', 'inverse'];\n    }\n    if (zrUtil.isArray(selector)) {\n      zrUtil.each(selector, function (item, index) {\n        zrUtil.isString(item) && (item = {\n          type: item\n        });\n        selector[index] = zrUtil.merge(item, getDefaultSelectorOptions(ecModel, item.type));\n      });\n    }\n  };\n  LegendModel.prototype.optionUpdated = function () {\n    this._updateData(this.ecModel);\n    var legendData = this._data;\n    // If selectedMode is single, try to select one\n    if (legendData[0] && this.get('selectedMode') === 'single') {\n      var hasSelected = false;\n      // If has any selected in option.selected\n      for (var i = 0; i < legendData.length; i++) {\n        var name_1 = legendData[i].get('name');\n        if (this.isSelected(name_1)) {\n          // Force to unselect others\n          this.select(name_1);\n          hasSelected = true;\n          break;\n        }\n      }\n      // Try select the first if selectedMode is single\n      !hasSelected && this.select(legendData[0].get('name'));\n    }\n  };\n  LegendModel.prototype._updateData = function (ecModel) {\n    var potentialData = [];\n    var availableNames = [];\n    ecModel.eachRawSeries(function (seriesModel) {\n      var seriesName = seriesModel.name;\n      availableNames.push(seriesName);\n      var isPotential;\n      if (seriesModel.legendVisualProvider) {\n        var provider = seriesModel.legendVisualProvider;\n        var names = provider.getAllNames();\n        if (!ecModel.isSeriesFiltered(seriesModel)) {\n          availableNames = availableNames.concat(names);\n        }\n        if (names.length) {\n          potentialData = potentialData.concat(names);\n        } else {\n          isPotential = true;\n        }\n      } else {\n        isPotential = true;\n      }\n      if (isPotential && isNameSpecified(seriesModel)) {\n        potentialData.push(seriesModel.name);\n      }\n    });\n    /**\r\n     * @type {Array.<string>}\r\n     * @private\r\n     */\n    this._availableNames = availableNames;\n    // If legend.data is not specified in option, use availableNames as data,\n    // which is convenient for user preparing option.\n    var rawData = this.get('data') || potentialData;\n    var legendNameMap = zrUtil.createHashMap();\n    var legendData = zrUtil.map(rawData, function (dataItem) {\n      // Can be string or number\n      if (zrUtil.isString(dataItem) || zrUtil.isNumber(dataItem)) {\n        dataItem = {\n          name: dataItem\n        };\n      }\n      if (legendNameMap.get(dataItem.name)) {\n        // remove legend name duplicate\n        return null;\n      }\n      legendNameMap.set(dataItem.name, true);\n      return new Model(dataItem, this, this.ecModel);\n    }, this);\n    /**\r\n     * @type {Array.<module:echarts/model/Model>}\r\n     * @private\r\n     */\n    this._data = zrUtil.filter(legendData, function (item) {\n      return !!item;\n    });\n  };\n  LegendModel.prototype.getData = function () {\n    return this._data;\n  };\n  LegendModel.prototype.select = function (name) {\n    var selected = this.option.selected;\n    var selectedMode = this.get('selectedMode');\n    if (selectedMode === 'single') {\n      var data = this._data;\n      zrUtil.each(data, function (dataItem) {\n        selected[dataItem.get('name')] = false;\n      });\n    }\n    selected[name] = true;\n  };\n  LegendModel.prototype.unSelect = function (name) {\n    if (this.get('selectedMode') !== 'single') {\n      this.option.selected[name] = false;\n    }\n  };\n  LegendModel.prototype.toggleSelected = function (name) {\n    var selected = this.option.selected;\n    // Default is true\n    if (!selected.hasOwnProperty(name)) {\n      selected[name] = true;\n    }\n    this[selected[name] ? 'unSelect' : 'select'](name);\n  };\n  LegendModel.prototype.allSelect = function () {\n    var data = this._data;\n    var selected = this.option.selected;\n    zrUtil.each(data, function (dataItem) {\n      selected[dataItem.get('name', true)] = true;\n    });\n  };\n  LegendModel.prototype.inverseSelect = function () {\n    var data = this._data;\n    var selected = this.option.selected;\n    zrUtil.each(data, function (dataItem) {\n      var name = dataItem.get('name', true);\n      // Initially, default value is true\n      if (!selected.hasOwnProperty(name)) {\n        selected[name] = true;\n      }\n      selected[name] = !selected[name];\n    });\n  };\n  LegendModel.prototype.isSelected = function (name) {\n    var selected = this.option.selected;\n    return !(selected.hasOwnProperty(name) && !selected[name]) && zrUtil.indexOf(this._availableNames, name) >= 0;\n  };\n  LegendModel.prototype.getOrient = function () {\n    return this.get('orient') === 'vertical' ? {\n      index: 1,\n      name: 'vertical'\n    } : {\n      index: 0,\n      name: 'horizontal'\n    };\n  };\n  LegendModel.type = 'legend.plain';\n  LegendModel.dependencies = ['series'];\n  LegendModel.defaultOption = {\n    // zlevel: 0,\n    z: 4,\n    show: true,\n    orient: 'horizontal',\n    left: 'center',\n    // right: 'center',\n    top: 0,\n    // bottom: null,\n    align: 'auto',\n    backgroundColor: 'rgba(0,0,0,0)',\n    borderColor: '#ccc',\n    borderRadius: 0,\n    borderWidth: 0,\n    padding: 5,\n    itemGap: 10,\n    itemWidth: 25,\n    itemHeight: 14,\n    symbolRotate: 'inherit',\n    symbolKeepAspect: true,\n    inactiveColor: '#ccc',\n    inactiveBorderColor: '#ccc',\n    inactiveBorderWidth: 'auto',\n    itemStyle: {\n      color: 'inherit',\n      opacity: 'inherit',\n      borderColor: 'inherit',\n      borderWidth: 'auto',\n      borderCap: 'inherit',\n      borderJoin: 'inherit',\n      borderDashOffset: 'inherit',\n      borderMiterLimit: 'inherit'\n    },\n    lineStyle: {\n      width: 'auto',\n      color: 'inherit',\n      inactiveColor: '#ccc',\n      inactiveWidth: 2,\n      opacity: 'inherit',\n      type: 'inherit',\n      cap: 'inherit',\n      join: 'inherit',\n      dashOffset: 'inherit',\n      miterLimit: 'inherit'\n    },\n    textStyle: {\n      color: '#333'\n    },\n    selectedMode: true,\n    selector: false,\n    selectorLabel: {\n      show: true,\n      borderRadius: 10,\n      padding: [3, 5, 3, 5],\n      fontSize: 12,\n      fontFamily: 'sans-serif',\n      color: '#666',\n      borderWidth: 1,\n      borderColor: '#666'\n    },\n    emphasis: {\n      selectorLabel: {\n        show: true,\n        color: '#eee',\n        backgroundColor: '#666'\n      }\n    },\n    selectorPosition: 'auto',\n    selectorItemGap: 7,\n    selectorButtonGap: 10,\n    tooltip: {\n      show: false\n    }\n  };\n  return LegendModel;\n}(ComponentModel);\nexport default LegendModel;", "map": {"version": 3, "names": ["__extends", "zrUtil", "Model", "isNameSpecified", "ComponentModel", "getDefaultSelectorOptions", "ecModel", "type", "title", "getLocaleModel", "get", "LegendModel", "_super", "_this", "apply", "arguments", "layoutMode", "ignoreSize", "prototype", "init", "option", "parentModel", "mergeDefaultAndTheme", "selected", "_updateSelector", "mergeOption", "call", "selector", "isArray", "each", "item", "index", "isString", "merge", "optionUpdated", "_updateData", "legendData", "_data", "hasSelected", "i", "length", "name_1", "isSelected", "select", "potentialData", "availableNames", "eachRawSeries", "seriesModel", "seriesName", "name", "push", "isPotential", "legend<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "provider", "names", "getAllNames", "isSeriesFiltered", "concat", "_availableNames", "rawData", "legendNameMap", "createHashMap", "map", "dataItem", "isNumber", "set", "filter", "getData", "selectedMode", "data", "unSelect", "toggleSelected", "hasOwnProperty", "allSelect", "inverseSelect", "indexOf", "getOrient", "dependencies", "defaultOption", "z", "show", "orient", "left", "top", "align", "backgroundColor", "borderColor", "borderRadius", "borderWidth", "padding", "itemGap", "itemWidth", "itemHeight", "symbolRotate", "symbolKeepAspect", "inactiveColor", "inactiveBorderColor", "inactiveBorderWidth", "itemStyle", "color", "opacity", "borderCap", "borderJoin", "borderDashOffset", "borderMiterLimit", "lineStyle", "width", "inactiveWidth", "cap", "join", "dashOffset", "miterLimit", "textStyle", "selector<PERSON><PERSON><PERSON>", "fontSize", "fontFamily", "emphasis", "selectorPosition", "selectorItemGap", "selectorButtonGap", "tooltip"], "sources": ["E:/AI/SmartCV/node_modules/echarts/lib/component/legend/LegendModel.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport Model from '../../model/Model.js';\nimport { isNameSpecified } from '../../util/model.js';\nimport ComponentModel from '../../model/Component.js';\nvar getDefaultSelectorOptions = function (ecModel, type) {\n  if (type === 'all') {\n    return {\n      type: 'all',\n      title: ecModel.getLocaleModel().get(['legend', 'selector', 'all'])\n    };\n  } else if (type === 'inverse') {\n    return {\n      type: 'inverse',\n      title: ecModel.getLocaleModel().get(['legend', 'selector', 'inverse'])\n    };\n  }\n};\nvar LegendModel = /** @class */function (_super) {\n  __extends(LegendModel, _super);\n  function LegendModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = LegendModel.type;\n    _this.layoutMode = {\n      type: 'box',\n      // legend.width/height are maxWidth/maxHeight actually,\n      // whereas real width/height is calculated by its content.\n      // (Setting {left: 10, right: 10} does not make sense).\n      // So consider the case:\n      // `setOption({legend: {left: 10});`\n      // then `setOption({legend: {right: 10});`\n      // The previous `left` should be cleared by setting `ignoreSize`.\n      ignoreSize: true\n    };\n    return _this;\n  }\n  LegendModel.prototype.init = function (option, parentModel, ecModel) {\n    this.mergeDefaultAndTheme(option, ecModel);\n    option.selected = option.selected || {};\n    this._updateSelector(option);\n  };\n  LegendModel.prototype.mergeOption = function (option, ecModel) {\n    _super.prototype.mergeOption.call(this, option, ecModel);\n    this._updateSelector(option);\n  };\n  LegendModel.prototype._updateSelector = function (option) {\n    var selector = option.selector;\n    var ecModel = this.ecModel;\n    if (selector === true) {\n      selector = option.selector = ['all', 'inverse'];\n    }\n    if (zrUtil.isArray(selector)) {\n      zrUtil.each(selector, function (item, index) {\n        zrUtil.isString(item) && (item = {\n          type: item\n        });\n        selector[index] = zrUtil.merge(item, getDefaultSelectorOptions(ecModel, item.type));\n      });\n    }\n  };\n  LegendModel.prototype.optionUpdated = function () {\n    this._updateData(this.ecModel);\n    var legendData = this._data;\n    // If selectedMode is single, try to select one\n    if (legendData[0] && this.get('selectedMode') === 'single') {\n      var hasSelected = false;\n      // If has any selected in option.selected\n      for (var i = 0; i < legendData.length; i++) {\n        var name_1 = legendData[i].get('name');\n        if (this.isSelected(name_1)) {\n          // Force to unselect others\n          this.select(name_1);\n          hasSelected = true;\n          break;\n        }\n      }\n      // Try select the first if selectedMode is single\n      !hasSelected && this.select(legendData[0].get('name'));\n    }\n  };\n  LegendModel.prototype._updateData = function (ecModel) {\n    var potentialData = [];\n    var availableNames = [];\n    ecModel.eachRawSeries(function (seriesModel) {\n      var seriesName = seriesModel.name;\n      availableNames.push(seriesName);\n      var isPotential;\n      if (seriesModel.legendVisualProvider) {\n        var provider = seriesModel.legendVisualProvider;\n        var names = provider.getAllNames();\n        if (!ecModel.isSeriesFiltered(seriesModel)) {\n          availableNames = availableNames.concat(names);\n        }\n        if (names.length) {\n          potentialData = potentialData.concat(names);\n        } else {\n          isPotential = true;\n        }\n      } else {\n        isPotential = true;\n      }\n      if (isPotential && isNameSpecified(seriesModel)) {\n        potentialData.push(seriesModel.name);\n      }\n    });\n    /**\r\n     * @type {Array.<string>}\r\n     * @private\r\n     */\n    this._availableNames = availableNames;\n    // If legend.data is not specified in option, use availableNames as data,\n    // which is convenient for user preparing option.\n    var rawData = this.get('data') || potentialData;\n    var legendNameMap = zrUtil.createHashMap();\n    var legendData = zrUtil.map(rawData, function (dataItem) {\n      // Can be string or number\n      if (zrUtil.isString(dataItem) || zrUtil.isNumber(dataItem)) {\n        dataItem = {\n          name: dataItem\n        };\n      }\n      if (legendNameMap.get(dataItem.name)) {\n        // remove legend name duplicate\n        return null;\n      }\n      legendNameMap.set(dataItem.name, true);\n      return new Model(dataItem, this, this.ecModel);\n    }, this);\n    /**\r\n     * @type {Array.<module:echarts/model/Model>}\r\n     * @private\r\n     */\n    this._data = zrUtil.filter(legendData, function (item) {\n      return !!item;\n    });\n  };\n  LegendModel.prototype.getData = function () {\n    return this._data;\n  };\n  LegendModel.prototype.select = function (name) {\n    var selected = this.option.selected;\n    var selectedMode = this.get('selectedMode');\n    if (selectedMode === 'single') {\n      var data = this._data;\n      zrUtil.each(data, function (dataItem) {\n        selected[dataItem.get('name')] = false;\n      });\n    }\n    selected[name] = true;\n  };\n  LegendModel.prototype.unSelect = function (name) {\n    if (this.get('selectedMode') !== 'single') {\n      this.option.selected[name] = false;\n    }\n  };\n  LegendModel.prototype.toggleSelected = function (name) {\n    var selected = this.option.selected;\n    // Default is true\n    if (!selected.hasOwnProperty(name)) {\n      selected[name] = true;\n    }\n    this[selected[name] ? 'unSelect' : 'select'](name);\n  };\n  LegendModel.prototype.allSelect = function () {\n    var data = this._data;\n    var selected = this.option.selected;\n    zrUtil.each(data, function (dataItem) {\n      selected[dataItem.get('name', true)] = true;\n    });\n  };\n  LegendModel.prototype.inverseSelect = function () {\n    var data = this._data;\n    var selected = this.option.selected;\n    zrUtil.each(data, function (dataItem) {\n      var name = dataItem.get('name', true);\n      // Initially, default value is true\n      if (!selected.hasOwnProperty(name)) {\n        selected[name] = true;\n      }\n      selected[name] = !selected[name];\n    });\n  };\n  LegendModel.prototype.isSelected = function (name) {\n    var selected = this.option.selected;\n    return !(selected.hasOwnProperty(name) && !selected[name]) && zrUtil.indexOf(this._availableNames, name) >= 0;\n  };\n  LegendModel.prototype.getOrient = function () {\n    return this.get('orient') === 'vertical' ? {\n      index: 1,\n      name: 'vertical'\n    } : {\n      index: 0,\n      name: 'horizontal'\n    };\n  };\n  LegendModel.type = 'legend.plain';\n  LegendModel.dependencies = ['series'];\n  LegendModel.defaultOption = {\n    // zlevel: 0,\n    z: 4,\n    show: true,\n    orient: 'horizontal',\n    left: 'center',\n    // right: 'center',\n    top: 0,\n    // bottom: null,\n    align: 'auto',\n    backgroundColor: 'rgba(0,0,0,0)',\n    borderColor: '#ccc',\n    borderRadius: 0,\n    borderWidth: 0,\n    padding: 5,\n    itemGap: 10,\n    itemWidth: 25,\n    itemHeight: 14,\n    symbolRotate: 'inherit',\n    symbolKeepAspect: true,\n    inactiveColor: '#ccc',\n    inactiveBorderColor: '#ccc',\n    inactiveBorderWidth: 'auto',\n    itemStyle: {\n      color: 'inherit',\n      opacity: 'inherit',\n      borderColor: 'inherit',\n      borderWidth: 'auto',\n      borderCap: 'inherit',\n      borderJoin: 'inherit',\n      borderDashOffset: 'inherit',\n      borderMiterLimit: 'inherit'\n    },\n    lineStyle: {\n      width: 'auto',\n      color: 'inherit',\n      inactiveColor: '#ccc',\n      inactiveWidth: 2,\n      opacity: 'inherit',\n      type: 'inherit',\n      cap: 'inherit',\n      join: 'inherit',\n      dashOffset: 'inherit',\n      miterLimit: 'inherit'\n    },\n    textStyle: {\n      color: '#333'\n    },\n    selectedMode: true,\n    selector: false,\n    selectorLabel: {\n      show: true,\n      borderRadius: 10,\n      padding: [3, 5, 3, 5],\n      fontSize: 12,\n      fontFamily: 'sans-serif',\n      color: '#666',\n      borderWidth: 1,\n      borderColor: '#666'\n    },\n    emphasis: {\n      selectorLabel: {\n        show: true,\n        color: '#eee',\n        backgroundColor: '#666'\n      }\n    },\n    selectorPosition: 'auto',\n    selectorItemGap: 7,\n    selectorButtonGap: 10,\n    tooltip: {\n      show: false\n    }\n  };\n  return LegendModel;\n}(ComponentModel);\nexport default LegendModel;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,OAAOC,KAAK,MAAM,sBAAsB;AACxC,SAASC,eAAe,QAAQ,qBAAqB;AACrD,OAAOC,cAAc,MAAM,0BAA0B;AACrD,IAAIC,yBAAyB,GAAG,SAAAA,CAAUC,OAAO,EAAEC,IAAI,EAAE;EACvD,IAAIA,IAAI,KAAK,KAAK,EAAE;IAClB,OAAO;MACLA,IAAI,EAAE,KAAK;MACXC,KAAK,EAAEF,OAAO,CAACG,cAAc,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE,KAAK,CAAC;IACnE,CAAC;EACH,CAAC,MAAM,IAAIH,IAAI,KAAK,SAAS,EAAE;IAC7B,OAAO;MACLA,IAAI,EAAE,SAAS;MACfC,KAAK,EAAEF,OAAO,CAACG,cAAc,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC;IACvE,CAAC;EACH;AACF,CAAC;AACD,IAAIC,WAAW,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC/CZ,SAAS,CAACW,WAAW,EAAEC,MAAM,CAAC;EAC9B,SAASD,WAAWA,CAAA,EAAG;IACrB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACN,IAAI,GAAGI,WAAW,CAACJ,IAAI;IAC7BM,KAAK,CAACG,UAAU,GAAG;MACjBT,IAAI,EAAE,KAAK;MACX;MACA;MACA;MACA;MACA;MACA;MACA;MACAU,UAAU,EAAE;IACd,CAAC;IACD,OAAOJ,KAAK;EACd;EACAF,WAAW,CAACO,SAAS,CAACC,IAAI,GAAG,UAAUC,MAAM,EAAEC,WAAW,EAAEf,OAAO,EAAE;IACnE,IAAI,CAACgB,oBAAoB,CAACF,MAAM,EAAEd,OAAO,CAAC;IAC1Cc,MAAM,CAACG,QAAQ,GAAGH,MAAM,CAACG,QAAQ,IAAI,CAAC,CAAC;IACvC,IAAI,CAACC,eAAe,CAACJ,MAAM,CAAC;EAC9B,CAAC;EACDT,WAAW,CAACO,SAAS,CAACO,WAAW,GAAG,UAAUL,MAAM,EAAEd,OAAO,EAAE;IAC7DM,MAAM,CAACM,SAAS,CAACO,WAAW,CAACC,IAAI,CAAC,IAAI,EAAEN,MAAM,EAAEd,OAAO,CAAC;IACxD,IAAI,CAACkB,eAAe,CAACJ,MAAM,CAAC;EAC9B,CAAC;EACDT,WAAW,CAACO,SAAS,CAACM,eAAe,GAAG,UAAUJ,MAAM,EAAE;IACxD,IAAIO,QAAQ,GAAGP,MAAM,CAACO,QAAQ;IAC9B,IAAIrB,OAAO,GAAG,IAAI,CAACA,OAAO;IAC1B,IAAIqB,QAAQ,KAAK,IAAI,EAAE;MACrBA,QAAQ,GAAGP,MAAM,CAACO,QAAQ,GAAG,CAAC,KAAK,EAAE,SAAS,CAAC;IACjD;IACA,IAAI1B,MAAM,CAAC2B,OAAO,CAACD,QAAQ,CAAC,EAAE;MAC5B1B,MAAM,CAAC4B,IAAI,CAACF,QAAQ,EAAE,UAAUG,IAAI,EAAEC,KAAK,EAAE;QAC3C9B,MAAM,CAAC+B,QAAQ,CAACF,IAAI,CAAC,KAAKA,IAAI,GAAG;UAC/BvB,IAAI,EAAEuB;QACR,CAAC,CAAC;QACFH,QAAQ,CAACI,KAAK,CAAC,GAAG9B,MAAM,CAACgC,KAAK,CAACH,IAAI,EAAEzB,yBAAyB,CAACC,OAAO,EAAEwB,IAAI,CAACvB,IAAI,CAAC,CAAC;MACrF,CAAC,CAAC;IACJ;EACF,CAAC;EACDI,WAAW,CAACO,SAAS,CAACgB,aAAa,GAAG,YAAY;IAChD,IAAI,CAACC,WAAW,CAAC,IAAI,CAAC7B,OAAO,CAAC;IAC9B,IAAI8B,UAAU,GAAG,IAAI,CAACC,KAAK;IAC3B;IACA,IAAID,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC1B,GAAG,CAAC,cAAc,CAAC,KAAK,QAAQ,EAAE;MAC1D,IAAI4B,WAAW,GAAG,KAAK;MACvB;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,UAAU,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;QAC1C,IAAIE,MAAM,GAAGL,UAAU,CAACG,CAAC,CAAC,CAAC7B,GAAG,CAAC,MAAM,CAAC;QACtC,IAAI,IAAI,CAACgC,UAAU,CAACD,MAAM,CAAC,EAAE;UAC3B;UACA,IAAI,CAACE,MAAM,CAACF,MAAM,CAAC;UACnBH,WAAW,GAAG,IAAI;UAClB;QACF;MACF;MACA;MACA,CAACA,WAAW,IAAI,IAAI,CAACK,MAAM,CAACP,UAAU,CAAC,CAAC,CAAC,CAAC1B,GAAG,CAAC,MAAM,CAAC,CAAC;IACxD;EACF,CAAC;EACDC,WAAW,CAACO,SAAS,CAACiB,WAAW,GAAG,UAAU7B,OAAO,EAAE;IACrD,IAAIsC,aAAa,GAAG,EAAE;IACtB,IAAIC,cAAc,GAAG,EAAE;IACvBvC,OAAO,CAACwC,aAAa,CAAC,UAAUC,WAAW,EAAE;MAC3C,IAAIC,UAAU,GAAGD,WAAW,CAACE,IAAI;MACjCJ,cAAc,CAACK,IAAI,CAACF,UAAU,CAAC;MAC/B,IAAIG,WAAW;MACf,IAAIJ,WAAW,CAACK,oBAAoB,EAAE;QACpC,IAAIC,QAAQ,GAAGN,WAAW,CAACK,oBAAoB;QAC/C,IAAIE,KAAK,GAAGD,QAAQ,CAACE,WAAW,CAAC,CAAC;QAClC,IAAI,CAACjD,OAAO,CAACkD,gBAAgB,CAACT,WAAW,CAAC,EAAE;UAC1CF,cAAc,GAAGA,cAAc,CAACY,MAAM,CAACH,KAAK,CAAC;QAC/C;QACA,IAAIA,KAAK,CAACd,MAAM,EAAE;UAChBI,aAAa,GAAGA,aAAa,CAACa,MAAM,CAACH,KAAK,CAAC;QAC7C,CAAC,MAAM;UACLH,WAAW,GAAG,IAAI;QACpB;MACF,CAAC,MAAM;QACLA,WAAW,GAAG,IAAI;MACpB;MACA,IAAIA,WAAW,IAAIhD,eAAe,CAAC4C,WAAW,CAAC,EAAE;QAC/CH,aAAa,CAACM,IAAI,CAACH,WAAW,CAACE,IAAI,CAAC;MACtC;IACF,CAAC,CAAC;IACF;AACJ;AACA;AACA;IACI,IAAI,CAACS,eAAe,GAAGb,cAAc;IACrC;IACA;IACA,IAAIc,OAAO,GAAG,IAAI,CAACjD,GAAG,CAAC,MAAM,CAAC,IAAIkC,aAAa;IAC/C,IAAIgB,aAAa,GAAG3D,MAAM,CAAC4D,aAAa,CAAC,CAAC;IAC1C,IAAIzB,UAAU,GAAGnC,MAAM,CAAC6D,GAAG,CAACH,OAAO,EAAE,UAAUI,QAAQ,EAAE;MACvD;MACA,IAAI9D,MAAM,CAAC+B,QAAQ,CAAC+B,QAAQ,CAAC,IAAI9D,MAAM,CAAC+D,QAAQ,CAACD,QAAQ,CAAC,EAAE;QAC1DA,QAAQ,GAAG;UACTd,IAAI,EAAEc;QACR,CAAC;MACH;MACA,IAAIH,aAAa,CAAClD,GAAG,CAACqD,QAAQ,CAACd,IAAI,CAAC,EAAE;QACpC;QACA,OAAO,IAAI;MACb;MACAW,aAAa,CAACK,GAAG,CAACF,QAAQ,CAACd,IAAI,EAAE,IAAI,CAAC;MACtC,OAAO,IAAI/C,KAAK,CAAC6D,QAAQ,EAAE,IAAI,EAAE,IAAI,CAACzD,OAAO,CAAC;IAChD,CAAC,EAAE,IAAI,CAAC;IACR;AACJ;AACA;AACA;IACI,IAAI,CAAC+B,KAAK,GAAGpC,MAAM,CAACiE,MAAM,CAAC9B,UAAU,EAAE,UAAUN,IAAI,EAAE;MACrD,OAAO,CAAC,CAACA,IAAI;IACf,CAAC,CAAC;EACJ,CAAC;EACDnB,WAAW,CAACO,SAAS,CAACiD,OAAO,GAAG,YAAY;IAC1C,OAAO,IAAI,CAAC9B,KAAK;EACnB,CAAC;EACD1B,WAAW,CAACO,SAAS,CAACyB,MAAM,GAAG,UAAUM,IAAI,EAAE;IAC7C,IAAI1B,QAAQ,GAAG,IAAI,CAACH,MAAM,CAACG,QAAQ;IACnC,IAAI6C,YAAY,GAAG,IAAI,CAAC1D,GAAG,CAAC,cAAc,CAAC;IAC3C,IAAI0D,YAAY,KAAK,QAAQ,EAAE;MAC7B,IAAIC,IAAI,GAAG,IAAI,CAAChC,KAAK;MACrBpC,MAAM,CAAC4B,IAAI,CAACwC,IAAI,EAAE,UAAUN,QAAQ,EAAE;QACpCxC,QAAQ,CAACwC,QAAQ,CAACrD,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,KAAK;MACxC,CAAC,CAAC;IACJ;IACAa,QAAQ,CAAC0B,IAAI,CAAC,GAAG,IAAI;EACvB,CAAC;EACDtC,WAAW,CAACO,SAAS,CAACoD,QAAQ,GAAG,UAAUrB,IAAI,EAAE;IAC/C,IAAI,IAAI,CAACvC,GAAG,CAAC,cAAc,CAAC,KAAK,QAAQ,EAAE;MACzC,IAAI,CAACU,MAAM,CAACG,QAAQ,CAAC0B,IAAI,CAAC,GAAG,KAAK;IACpC;EACF,CAAC;EACDtC,WAAW,CAACO,SAAS,CAACqD,cAAc,GAAG,UAAUtB,IAAI,EAAE;IACrD,IAAI1B,QAAQ,GAAG,IAAI,CAACH,MAAM,CAACG,QAAQ;IACnC;IACA,IAAI,CAACA,QAAQ,CAACiD,cAAc,CAACvB,IAAI,CAAC,EAAE;MAClC1B,QAAQ,CAAC0B,IAAI,CAAC,GAAG,IAAI;IACvB;IACA,IAAI,CAAC1B,QAAQ,CAAC0B,IAAI,CAAC,GAAG,UAAU,GAAG,QAAQ,CAAC,CAACA,IAAI,CAAC;EACpD,CAAC;EACDtC,WAAW,CAACO,SAAS,CAACuD,SAAS,GAAG,YAAY;IAC5C,IAAIJ,IAAI,GAAG,IAAI,CAAChC,KAAK;IACrB,IAAId,QAAQ,GAAG,IAAI,CAACH,MAAM,CAACG,QAAQ;IACnCtB,MAAM,CAAC4B,IAAI,CAACwC,IAAI,EAAE,UAAUN,QAAQ,EAAE;MACpCxC,QAAQ,CAACwC,QAAQ,CAACrD,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI;IAC7C,CAAC,CAAC;EACJ,CAAC;EACDC,WAAW,CAACO,SAAS,CAACwD,aAAa,GAAG,YAAY;IAChD,IAAIL,IAAI,GAAG,IAAI,CAAChC,KAAK;IACrB,IAAId,QAAQ,GAAG,IAAI,CAACH,MAAM,CAACG,QAAQ;IACnCtB,MAAM,CAAC4B,IAAI,CAACwC,IAAI,EAAE,UAAUN,QAAQ,EAAE;MACpC,IAAId,IAAI,GAAGc,QAAQ,CAACrD,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC;MACrC;MACA,IAAI,CAACa,QAAQ,CAACiD,cAAc,CAACvB,IAAI,CAAC,EAAE;QAClC1B,QAAQ,CAAC0B,IAAI,CAAC,GAAG,IAAI;MACvB;MACA1B,QAAQ,CAAC0B,IAAI,CAAC,GAAG,CAAC1B,QAAQ,CAAC0B,IAAI,CAAC;IAClC,CAAC,CAAC;EACJ,CAAC;EACDtC,WAAW,CAACO,SAAS,CAACwB,UAAU,GAAG,UAAUO,IAAI,EAAE;IACjD,IAAI1B,QAAQ,GAAG,IAAI,CAACH,MAAM,CAACG,QAAQ;IACnC,OAAO,EAAEA,QAAQ,CAACiD,cAAc,CAACvB,IAAI,CAAC,IAAI,CAAC1B,QAAQ,CAAC0B,IAAI,CAAC,CAAC,IAAIhD,MAAM,CAAC0E,OAAO,CAAC,IAAI,CAACjB,eAAe,EAAET,IAAI,CAAC,IAAI,CAAC;EAC/G,CAAC;EACDtC,WAAW,CAACO,SAAS,CAAC0D,SAAS,GAAG,YAAY;IAC5C,OAAO,IAAI,CAAClE,GAAG,CAAC,QAAQ,CAAC,KAAK,UAAU,GAAG;MACzCqB,KAAK,EAAE,CAAC;MACRkB,IAAI,EAAE;IACR,CAAC,GAAG;MACFlB,KAAK,EAAE,CAAC;MACRkB,IAAI,EAAE;IACR,CAAC;EACH,CAAC;EACDtC,WAAW,CAACJ,IAAI,GAAG,cAAc;EACjCI,WAAW,CAACkE,YAAY,GAAG,CAAC,QAAQ,CAAC;EACrClE,WAAW,CAACmE,aAAa,GAAG;IAC1B;IACAC,CAAC,EAAE,CAAC;IACJC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE,YAAY;IACpBC,IAAI,EAAE,QAAQ;IACd;IACAC,GAAG,EAAE,CAAC;IACN;IACAC,KAAK,EAAE,MAAM;IACbC,eAAe,EAAE,eAAe;IAChCC,WAAW,EAAE,MAAM;IACnBC,YAAY,EAAE,CAAC;IACfC,WAAW,EAAE,CAAC;IACdC,OAAO,EAAE,CAAC;IACVC,OAAO,EAAE,EAAE;IACXC,SAAS,EAAE,EAAE;IACbC,UAAU,EAAE,EAAE;IACdC,YAAY,EAAE,SAAS;IACvBC,gBAAgB,EAAE,IAAI;IACtBC,aAAa,EAAE,MAAM;IACrBC,mBAAmB,EAAE,MAAM;IAC3BC,mBAAmB,EAAE,MAAM;IAC3BC,SAAS,EAAE;MACTC,KAAK,EAAE,SAAS;MAChBC,OAAO,EAAE,SAAS;MAClBd,WAAW,EAAE,SAAS;MACtBE,WAAW,EAAE,MAAM;MACnBa,SAAS,EAAE,SAAS;MACpBC,UAAU,EAAE,SAAS;MACrBC,gBAAgB,EAAE,SAAS;MAC3BC,gBAAgB,EAAE;IACpB,CAAC;IACDC,SAAS,EAAE;MACTC,KAAK,EAAE,MAAM;MACbP,KAAK,EAAE,SAAS;MAChBJ,aAAa,EAAE,MAAM;MACrBY,aAAa,EAAE,CAAC;MAChBP,OAAO,EAAE,SAAS;MAClB7F,IAAI,EAAE,SAAS;MACfqG,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,UAAU,EAAE,SAAS;MACrBC,UAAU,EAAE;IACd,CAAC;IACDC,SAAS,EAAE;MACTb,KAAK,EAAE;IACT,CAAC;IACD/B,YAAY,EAAE,IAAI;IAClBzC,QAAQ,EAAE,KAAK;IACfsF,aAAa,EAAE;MACbjC,IAAI,EAAE,IAAI;MACVO,YAAY,EAAE,EAAE;MAChBE,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACrByB,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE,YAAY;MACxBhB,KAAK,EAAE,MAAM;MACbX,WAAW,EAAE,CAAC;MACdF,WAAW,EAAE;IACf,CAAC;IACD8B,QAAQ,EAAE;MACRH,aAAa,EAAE;QACbjC,IAAI,EAAE,IAAI;QACVmB,KAAK,EAAE,MAAM;QACbd,eAAe,EAAE;MACnB;IACF,CAAC;IACDgC,gBAAgB,EAAE,MAAM;IACxBC,eAAe,EAAE,CAAC;IAClBC,iBAAiB,EAAE,EAAE;IACrBC,OAAO,EAAE;MACPxC,IAAI,EAAE;IACR;EACF,CAAC;EACD,OAAOrE,WAAW;AACpB,CAAC,CAACP,cAAc,CAAC;AACjB,eAAeO,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}