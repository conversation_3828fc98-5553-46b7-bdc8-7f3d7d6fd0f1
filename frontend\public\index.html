<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/ICON.png?v=2" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#3b82f6" />
    <meta name="description" content="SmartCV - AI-Powered Resume Optimization Platform. Transform your resume to stand out." />
    <title>SmartCV - AI-Powered Resume Optimization Platform</title>
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="SmartCV - AI-Powered Resume Optimization Platform" />
    <meta property="og:description" content="Upload your resume and job description for AI-powered optimization recommendations" />
    <meta property="og:type" content="website" />
    
    <!-- Preload critical resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- PDF.js worker preload -->
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js" as="script">
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this application.</noscript>
    <div id="root"></div>
    
    <!-- Enhanced Loading Screen -->
    <style>
      #initial-loader {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        color: white;
        font-family: 'Inter', sans-serif;
        overflow: hidden;
      }
      
      /* Enhanced floating particles */
      #initial-loader::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: 
          radial-gradient(circle at 20% 50%, rgba(255,255,255,0.1) 1px, transparent 1px),
          radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 1px, transparent 1px),
          radial-gradient(circle at 40% 80%, rgba(255,255,255,0.08) 1px, transparent 1px),
          radial-gradient(circle at 90% 40%, rgba(255,255,255,0.1) 1px, transparent 1px),
          radial-gradient(circle at 10% 90%, rgba(255,255,255,0.08) 1px, transparent 1px);
        background-size: 100px 100px, 150px 150px, 120px 120px, 80px 80px, 200px 200px;
        animation: float 20s ease-in-out infinite;
      }
      
      @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-20px); }
      }
      
      .loader-container {
        position: relative;
        z-index: 1;
        text-align: center;
      }
      
      .logo-container {
        margin-bottom: 30px;
        animation: logoFloat 3s ease-in-out infinite;
      }
      
      @keyframes logoFloat {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-8px); }
      }
      
      .logo-icon {
        width: 64px;
        height: 64px;
        background: rgba(255,255,255,0.15);
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
      }
      
      .logo-icon img {
        width: 40px;
        height: 40px;
        object-fit: contain;
      }
      
      .spinner {
        width: 50px;
        height: 50px;
        border: 3px solid rgba(255,255,255,0.2);
        border-top: 3px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 30px;
        position: relative;
      }
      
      .spinner::after {
        content: '';
        position: absolute;
        top: -3px;
        left: -3px;
        right: -3px;
        bottom: -3px;
        border: 2px solid transparent;
        border-top: 2px solid rgba(255,255,255,0.4);
        border-radius: 50%;
        animation: spin 2s linear infinite reverse;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .loader-title {
        font-size: 32px;
        font-weight: 700;
        margin-bottom: 12px;
        letter-spacing: -0.02em;
        background: linear-gradient(45deg, #ffffff, #e2e8f0);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        animation: titleGlow 2s ease-in-out infinite alternate;
      }
      
      @keyframes titleGlow {
        0% { filter: brightness(1); }
        100% { filter: brightness(1.1); }
      }
      
      .loader-subtitle {
        font-size: 16px;
        font-weight: 400;
        opacity: 0.9;
        margin-bottom: 20px;
        animation: subtitleFade 2s ease-in-out infinite alternate;
      }
      
      @keyframes subtitleFade {
        0% { opacity: 0.7; }
        100% { opacity: 1; }
      }
      
      .progress-bar {
        width: 200px;
        height: 4px;
        background: rgba(255,255,255,0.2);
        border-radius: 2px;
        margin: 0 auto;
        overflow: hidden;
        position: relative;
      }
      
      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #ffffff, #e2e8f0, #ffffff);
        background-size: 200% 100%;
        border-radius: 2px;
        animation: progress 2s ease-in-out infinite;
      }
      
      @keyframes progress {
        0% { width: 0%; background-position: 200% 0; }
        50% { width: 70%; background-position: 0% 0; }
        100% { width: 100%; background-position: -200% 0; }
      }
      
      .loading-dots {
        margin-top: 20px;
        font-size: 14px;
        font-weight: 500;
      }
      
      .loading-dots::after {
        content: '';
        animation: dots 1.5s steps(4, end) infinite;
      }
      
      @keyframes dots {
        0%, 20% { content: ''; }
        40% { content: '.'; }
        60% { content: '..'; }
        80%, 100% { content: '...'; }
      }
      
      .fade-out {
        opacity: 0;
        transform: scale(0.95);
        transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
      }

      @media (max-width: 768px) {
        .loader-title {
          font-size: 28px;
        }
        .loader-subtitle {
          font-size: 14px;
        }
        .progress-bar {
          width: 160px;
        }
      }
    </style>
    
    <div id="initial-loader">
      <div class="loader-container">
        <div class="logo-container">
          <div class="logo-icon">
            <img src="/ICON.png" alt="SmartCV Logo" />
          </div>
        </div>
        
        <div class="spinner"></div>
        
        <h2 class="loader-title">SmartCV</h2>
        <p class="loader-subtitle">AI-Powered Resume Optimization Platform</p>
        
        <div class="progress-bar">
          <div class="progress-fill"></div>
        </div>
        
        <div class="loading-dots">Loading your experience</div>
      </div>
    </div>
    
    <script>
      // Enhanced page load handler with progress simulation
      let progress = 0;
      const progressFill = document.querySelector('.progress-fill');
      const loadingText = document.querySelector('.loading-dots');
      
      const loadingMessages = [
        'Initializing AI engine',
        'Loading components',
        'Preparing workspace',
        'Almost ready'
      ];
      
      let messageIndex = 0;
      
      const updateProgress = () => {
        progress += Math.random() * 30;
        if (progress > 100) progress = 100;
        
        if (progressFill) {
          progressFill.style.width = progress + '%';
        }
        
        if (progress < 100) {
          setTimeout(updateProgress, 200 + Math.random() * 300);
        }
      };
      
      const updateMessage = () => {
        if (loadingText && messageIndex < loadingMessages.length) {
          loadingText.textContent = loadingMessages[messageIndex];
          messageIndex++;
          setTimeout(updateMessage, 800);
        }
      };
      
      // Start progress simulation
      setTimeout(updateProgress, 300);
      setTimeout(updateMessage, 1000);
      
      // Hide loader when page is fully loaded
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loader = document.getElementById('initial-loader');
          if (loader) {
            loader.classList.add('fade-out');
            setTimeout(function() {
              loader.style.display = 'none';
            }, 600);
          }
        }, 1500); // Show loader for at least 1.5s for better UX
      });
      
      // Fallback: hide loader after max 5 seconds
      setTimeout(function() {
        const loader = document.getElementById('initial-loader');
        if (loader && loader.style.display !== 'none') {
          loader.classList.add('fade-out');
          setTimeout(function() {
            loader.style.display = 'none';
          }, 600);
        }
      }, 5000);
    </script>
  </body>
</html> 