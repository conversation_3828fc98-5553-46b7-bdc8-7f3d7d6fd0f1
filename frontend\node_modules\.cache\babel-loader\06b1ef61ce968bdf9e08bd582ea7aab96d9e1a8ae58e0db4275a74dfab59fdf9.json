{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { isArray, map } from 'zrender/lib/core/util.js';\nimport { parsePercent } from 'zrender/lib/contain/text.js';\nexport function getSectorCornerRadius(model, shape, zeroIfNull) {\n  var cornerRadius = model.get('borderRadius');\n  if (cornerRadius == null) {\n    return zeroIfNull ? {\n      cornerRadius: 0\n    } : null;\n  }\n  if (!isArray(cornerRadius)) {\n    cornerRadius = [cornerRadius, cornerRadius, cornerRadius, cornerRadius];\n  }\n  var dr = Math.abs(shape.r || 0 - shape.r0 || 0);\n  return {\n    cornerRadius: map(cornerRadius, function (cr) {\n      return parsePercent(cr, dr);\n    })\n  };\n}", "map": {"version": 3, "names": ["isArray", "map", "parsePercent", "getSectorCornerRadius", "model", "shape", "zeroIfNull", "cornerRadius", "get", "dr", "Math", "abs", "r", "r0", "cr"], "sources": ["E:/AI/SmartCV/node_modules/echarts/lib/chart/helper/sectorHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { isArray, map } from 'zrender/lib/core/util.js';\nimport { parsePercent } from 'zrender/lib/contain/text.js';\nexport function getSectorCornerRadius(model, shape, zeroIfNull) {\n  var cornerRadius = model.get('borderRadius');\n  if (cornerRadius == null) {\n    return zeroIfNull ? {\n      cornerRadius: 0\n    } : null;\n  }\n  if (!isArray(cornerRadius)) {\n    cornerRadius = [cornerRadius, cornerRadius, cornerRadius, cornerRadius];\n  }\n  var dr = Math.abs(shape.r || 0 - shape.r0 || 0);\n  return {\n    cornerRadius: map(cornerRadius, function (cr) {\n      return parsePercent(cr, dr);\n    })\n  };\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,OAAO,EAAEC,GAAG,QAAQ,0BAA0B;AACvD,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,OAAO,SAASC,qBAAqBA,CAACC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAE;EAC9D,IAAIC,YAAY,GAAGH,KAAK,CAACI,GAAG,CAAC,cAAc,CAAC;EAC5C,IAAID,YAAY,IAAI,IAAI,EAAE;IACxB,OAAOD,UAAU,GAAG;MAClBC,YAAY,EAAE;IAChB,CAAC,GAAG,IAAI;EACV;EACA,IAAI,CAACP,OAAO,CAACO,YAAY,CAAC,EAAE;IAC1BA,YAAY,GAAG,CAACA,YAAY,EAAEA,YAAY,EAAEA,YAAY,EAAEA,YAAY,CAAC;EACzE;EACA,IAAIE,EAAE,GAAGC,IAAI,CAACC,GAAG,CAACN,KAAK,CAACO,CAAC,IAAI,CAAC,GAAGP,KAAK,CAACQ,EAAE,IAAI,CAAC,CAAC;EAC/C,OAAO;IACLN,YAAY,EAAEN,GAAG,CAACM,YAAY,EAAE,UAAUO,EAAE,EAAE;MAC5C,OAAOZ,YAAY,CAACY,EAAE,EAAEL,EAAE,CAAC;IAC7B,CAAC;EACH,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}