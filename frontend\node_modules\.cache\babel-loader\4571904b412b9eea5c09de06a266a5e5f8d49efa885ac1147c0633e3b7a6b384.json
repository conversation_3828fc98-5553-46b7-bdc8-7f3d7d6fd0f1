{"ast": null, "code": "import env from './env.js';\nimport { buildTransformer } from './fourPointsTransform.js';\nvar EVENT_SAVED_PROP = '___zrEVENTSAVED';\nvar _calcOut = [];\nexport function transformLocalCoord(out, elFrom, elTarget, inX, inY) {\n  return transformCoordWithViewport(_calcOut, elFrom, inX, inY, true) && transformCoordWithViewport(out, elTarget, _calcOut[0], _calcOut[1]);\n}\nexport function transformCoordWithViewport(out, el, inX, inY, inverse) {\n  if (el.getBoundingClientRect && env.domSupported && !isCanvasEl(el)) {\n    var saved = el[EVENT_SAVED_PROP] || (el[EVENT_SAVED_PROP] = {});\n    var markers = prepareCoordMarkers(el, saved);\n    var transformer = preparePointerTransformer(markers, saved, inverse);\n    if (transformer) {\n      transformer(out, inX, inY);\n      return true;\n    }\n  }\n  return false;\n}\nfunction prepareCoordMarkers(el, saved) {\n  var markers = saved.markers;\n  if (markers) {\n    return markers;\n  }\n  markers = saved.markers = [];\n  var propLR = ['left', 'right'];\n  var propTB = ['top', 'bottom'];\n  for (var i = 0; i < 4; i++) {\n    var marker = document.createElement('div');\n    var stl = marker.style;\n    var idxLR = i % 2;\n    var idxTB = (i >> 1) % 2;\n    stl.cssText = ['position: absolute', 'visibility: hidden', 'padding: 0', 'margin: 0', 'border-width: 0', 'user-select: none', 'width:0', 'height:0', propLR[idxLR] + ':0', propTB[idxTB] + ':0', propLR[1 - idxLR] + ':auto', propTB[1 - idxTB] + ':auto', ''].join('!important;');\n    el.appendChild(marker);\n    markers.push(marker);\n  }\n  return markers;\n}\nfunction preparePointerTransformer(markers, saved, inverse) {\n  var transformerName = inverse ? 'invTrans' : 'trans';\n  var transformer = saved[transformerName];\n  var oldSrcCoords = saved.srcCoords;\n  var srcCoords = [];\n  var destCoords = [];\n  var oldCoordTheSame = true;\n  for (var i = 0; i < 4; i++) {\n    var rect = markers[i].getBoundingClientRect();\n    var ii = 2 * i;\n    var x = rect.left;\n    var y = rect.top;\n    srcCoords.push(x, y);\n    oldCoordTheSame = oldCoordTheSame && oldSrcCoords && x === oldSrcCoords[ii] && y === oldSrcCoords[ii + 1];\n    destCoords.push(markers[i].offsetLeft, markers[i].offsetTop);\n  }\n  return oldCoordTheSame && transformer ? transformer : (saved.srcCoords = srcCoords, saved[transformerName] = inverse ? buildTransformer(destCoords, srcCoords) : buildTransformer(srcCoords, destCoords));\n}\nexport function isCanvasEl(el) {\n  return el.nodeName.toUpperCase() === 'CANVAS';\n}\nvar replaceReg = /([&<>\"'])/g;\nvar replaceMap = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;',\n  '\\'': '&#39;'\n};\nexport function encodeHTML(source) {\n  return source == null ? '' : (source + '').replace(replaceReg, function (str, c) {\n    return replaceMap[c];\n  });\n}", "map": {"version": 3, "names": ["env", "buildTransformer", "EVENT_SAVED_PROP", "_calcOut", "transformLocalCoord", "out", "el<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "inX", "inY", "transformCoordWithViewport", "el", "inverse", "getBoundingClientRect", "domSupported", "isCanvasEl", "saved", "markers", "prepareCoordMarkers", "transformer", "preparePointerTransformer", "propLR", "propTB", "i", "marker", "document", "createElement", "stl", "style", "idxLR", "idxTB", "cssText", "join", "append<PERSON><PERSON><PERSON>", "push", "transformerName", "oldSrcCoords", "srcCoords", "destCoords", "oldCoordTheSame", "rect", "ii", "x", "left", "y", "top", "offsetLeft", "offsetTop", "nodeName", "toUpperCase", "replaceReg", "replaceMap", "encodeHTML", "source", "replace", "str", "c"], "sources": ["E:/AI/SmartCV/node_modules/zrender/lib/core/dom.js"], "sourcesContent": ["import env from './env.js';\nimport { buildTransformer } from './fourPointsTransform.js';\nvar EVENT_SAVED_PROP = '___zrEVENTSAVED';\nvar _calcOut = [];\nexport function transformLocalCoord(out, elFrom, elTarget, inX, inY) {\n    return transformCoordWithViewport(_calcOut, elFrom, inX, inY, true)\n        && transformCoordWithViewport(out, elTarget, _calcOut[0], _calcOut[1]);\n}\nexport function transformCoordWithViewport(out, el, inX, inY, inverse) {\n    if (el.getBoundingClientRect && env.domSupported && !isCanvasEl(el)) {\n        var saved = el[EVENT_SAVED_PROP] || (el[EVENT_SAVED_PROP] = {});\n        var markers = prepareCoordMarkers(el, saved);\n        var transformer = preparePointerTransformer(markers, saved, inverse);\n        if (transformer) {\n            transformer(out, inX, inY);\n            return true;\n        }\n    }\n    return false;\n}\nfunction prepareCoordMarkers(el, saved) {\n    var markers = saved.markers;\n    if (markers) {\n        return markers;\n    }\n    markers = saved.markers = [];\n    var propLR = ['left', 'right'];\n    var propTB = ['top', 'bottom'];\n    for (var i = 0; i < 4; i++) {\n        var marker = document.createElement('div');\n        var stl = marker.style;\n        var idxLR = i % 2;\n        var idxTB = (i >> 1) % 2;\n        stl.cssText = [\n            'position: absolute',\n            'visibility: hidden',\n            'padding: 0',\n            'margin: 0',\n            'border-width: 0',\n            'user-select: none',\n            'width:0',\n            'height:0',\n            propLR[idxLR] + ':0',\n            propTB[idxTB] + ':0',\n            propLR[1 - idxLR] + ':auto',\n            propTB[1 - idxTB] + ':auto',\n            ''\n        ].join('!important;');\n        el.appendChild(marker);\n        markers.push(marker);\n    }\n    return markers;\n}\nfunction preparePointerTransformer(markers, saved, inverse) {\n    var transformerName = inverse ? 'invTrans' : 'trans';\n    var transformer = saved[transformerName];\n    var oldSrcCoords = saved.srcCoords;\n    var srcCoords = [];\n    var destCoords = [];\n    var oldCoordTheSame = true;\n    for (var i = 0; i < 4; i++) {\n        var rect = markers[i].getBoundingClientRect();\n        var ii = 2 * i;\n        var x = rect.left;\n        var y = rect.top;\n        srcCoords.push(x, y);\n        oldCoordTheSame = oldCoordTheSame && oldSrcCoords && x === oldSrcCoords[ii] && y === oldSrcCoords[ii + 1];\n        destCoords.push(markers[i].offsetLeft, markers[i].offsetTop);\n    }\n    return (oldCoordTheSame && transformer)\n        ? transformer\n        : (saved.srcCoords = srcCoords,\n            saved[transformerName] = inverse\n                ? buildTransformer(destCoords, srcCoords)\n                : buildTransformer(srcCoords, destCoords));\n}\nexport function isCanvasEl(el) {\n    return el.nodeName.toUpperCase() === 'CANVAS';\n}\nvar replaceReg = /([&<>\"'])/g;\nvar replaceMap = {\n    '&': '&amp;',\n    '<': '&lt;',\n    '>': '&gt;',\n    '\"': '&quot;',\n    '\\'': '&#39;'\n};\nexport function encodeHTML(source) {\n    return source == null\n        ? ''\n        : (source + '').replace(replaceReg, function (str, c) {\n            return replaceMap[c];\n        });\n}\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,UAAU;AAC1B,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D,IAAIC,gBAAgB,GAAG,iBAAiB;AACxC,IAAIC,QAAQ,GAAG,EAAE;AACjB,OAAO,SAASC,mBAAmBA,CAACC,GAAG,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,GAAG,EAAE;EACjE,OAAOC,0BAA0B,CAACP,QAAQ,EAAEG,MAAM,EAAEE,GAAG,EAAEC,GAAG,EAAE,IAAI,CAAC,IAC5DC,0BAA0B,CAACL,GAAG,EAAEE,QAAQ,EAAEJ,QAAQ,CAAC,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC9E;AACA,OAAO,SAASO,0BAA0BA,CAACL,GAAG,EAAEM,EAAE,EAAEH,GAAG,EAAEC,GAAG,EAAEG,OAAO,EAAE;EACnE,IAAID,EAAE,CAACE,qBAAqB,IAAIb,GAAG,CAACc,YAAY,IAAI,CAACC,UAAU,CAACJ,EAAE,CAAC,EAAE;IACjE,IAAIK,KAAK,GAAGL,EAAE,CAACT,gBAAgB,CAAC,KAAKS,EAAE,CAACT,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC;IAC/D,IAAIe,OAAO,GAAGC,mBAAmB,CAACP,EAAE,EAAEK,KAAK,CAAC;IAC5C,IAAIG,WAAW,GAAGC,yBAAyB,CAACH,OAAO,EAAED,KAAK,EAAEJ,OAAO,CAAC;IACpE,IAAIO,WAAW,EAAE;MACbA,WAAW,CAACd,GAAG,EAAEG,GAAG,EAAEC,GAAG,CAAC;MAC1B,OAAO,IAAI;IACf;EACJ;EACA,OAAO,KAAK;AAChB;AACA,SAASS,mBAAmBA,CAACP,EAAE,EAAEK,KAAK,EAAE;EACpC,IAAIC,OAAO,GAAGD,KAAK,CAACC,OAAO;EAC3B,IAAIA,OAAO,EAAE;IACT,OAAOA,OAAO;EAClB;EACAA,OAAO,GAAGD,KAAK,CAACC,OAAO,GAAG,EAAE;EAC5B,IAAII,MAAM,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC;EAC9B,IAAIC,MAAM,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC;EAC9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;IACxB,IAAIC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC1C,IAAIC,GAAG,GAAGH,MAAM,CAACI,KAAK;IACtB,IAAIC,KAAK,GAAGN,CAAC,GAAG,CAAC;IACjB,IAAIO,KAAK,GAAG,CAACP,CAAC,IAAI,CAAC,IAAI,CAAC;IACxBI,GAAG,CAACI,OAAO,GAAG,CACV,oBAAoB,EACpB,oBAAoB,EACpB,YAAY,EACZ,WAAW,EACX,iBAAiB,EACjB,mBAAmB,EACnB,SAAS,EACT,UAAU,EACVV,MAAM,CAACQ,KAAK,CAAC,GAAG,IAAI,EACpBP,MAAM,CAACQ,KAAK,CAAC,GAAG,IAAI,EACpBT,MAAM,CAAC,CAAC,GAAGQ,KAAK,CAAC,GAAG,OAAO,EAC3BP,MAAM,CAAC,CAAC,GAAGQ,KAAK,CAAC,GAAG,OAAO,EAC3B,EAAE,CACL,CAACE,IAAI,CAAC,aAAa,CAAC;IACrBrB,EAAE,CAACsB,WAAW,CAACT,MAAM,CAAC;IACtBP,OAAO,CAACiB,IAAI,CAACV,MAAM,CAAC;EACxB;EACA,OAAOP,OAAO;AAClB;AACA,SAASG,yBAAyBA,CAACH,OAAO,EAAED,KAAK,EAAEJ,OAAO,EAAE;EACxD,IAAIuB,eAAe,GAAGvB,OAAO,GAAG,UAAU,GAAG,OAAO;EACpD,IAAIO,WAAW,GAAGH,KAAK,CAACmB,eAAe,CAAC;EACxC,IAAIC,YAAY,GAAGpB,KAAK,CAACqB,SAAS;EAClC,IAAIA,SAAS,GAAG,EAAE;EAClB,IAAIC,UAAU,GAAG,EAAE;EACnB,IAAIC,eAAe,GAAG,IAAI;EAC1B,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;IACxB,IAAIiB,IAAI,GAAGvB,OAAO,CAACM,CAAC,CAAC,CAACV,qBAAqB,CAAC,CAAC;IAC7C,IAAI4B,EAAE,GAAG,CAAC,GAAGlB,CAAC;IACd,IAAImB,CAAC,GAAGF,IAAI,CAACG,IAAI;IACjB,IAAIC,CAAC,GAAGJ,IAAI,CAACK,GAAG;IAChBR,SAAS,CAACH,IAAI,CAACQ,CAAC,EAAEE,CAAC,CAAC;IACpBL,eAAe,GAAGA,eAAe,IAAIH,YAAY,IAAIM,CAAC,KAAKN,YAAY,CAACK,EAAE,CAAC,IAAIG,CAAC,KAAKR,YAAY,CAACK,EAAE,GAAG,CAAC,CAAC;IACzGH,UAAU,CAACJ,IAAI,CAACjB,OAAO,CAACM,CAAC,CAAC,CAACuB,UAAU,EAAE7B,OAAO,CAACM,CAAC,CAAC,CAACwB,SAAS,CAAC;EAChE;EACA,OAAQR,eAAe,IAAIpB,WAAW,GAChCA,WAAW,IACVH,KAAK,CAACqB,SAAS,GAAGA,SAAS,EAC1BrB,KAAK,CAACmB,eAAe,CAAC,GAAGvB,OAAO,GAC1BX,gBAAgB,CAACqC,UAAU,EAAED,SAAS,CAAC,GACvCpC,gBAAgB,CAACoC,SAAS,EAAEC,UAAU,CAAC,CAAC;AAC1D;AACA,OAAO,SAASvB,UAAUA,CAACJ,EAAE,EAAE;EAC3B,OAAOA,EAAE,CAACqC,QAAQ,CAACC,WAAW,CAAC,CAAC,KAAK,QAAQ;AACjD;AACA,IAAIC,UAAU,GAAG,YAAY;AAC7B,IAAIC,UAAU,GAAG;EACb,GAAG,EAAE,OAAO;EACZ,GAAG,EAAE,MAAM;EACX,GAAG,EAAE,MAAM;EACX,GAAG,EAAE,QAAQ;EACb,IAAI,EAAE;AACV,CAAC;AACD,OAAO,SAASC,UAAUA,CAACC,MAAM,EAAE;EAC/B,OAAOA,MAAM,IAAI,IAAI,GACf,EAAE,GACF,CAACA,MAAM,GAAG,EAAE,EAAEC,OAAO,CAACJ,UAAU,EAAE,UAAUK,GAAG,EAAEC,CAAC,EAAE;IAClD,OAAOL,UAAU,CAACK,CAAC,CAAC;EACxB,CAAC,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}