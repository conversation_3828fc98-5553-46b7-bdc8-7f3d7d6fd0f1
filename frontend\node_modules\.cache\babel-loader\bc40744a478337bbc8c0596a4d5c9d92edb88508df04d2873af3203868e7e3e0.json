{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { curry, each, hasOwn } from 'zrender/lib/core/util.js';\nfunction legendSelectActionHandler(methodName, payload, ecModel) {\n  var isAllSelect = methodName === 'allSelect' || methodName === 'inverseSelect';\n  var selectedMap = {};\n  var actionLegendIndices = [];\n  ecModel.eachComponent({\n    mainType: 'legend',\n    query: payload\n  }, function (legendModel) {\n    if (isAllSelect) {\n      legendModel[methodName]();\n    } else {\n      legendModel[methodName](payload.name);\n    }\n    makeSelectedMap(legendModel, selectedMap);\n    actionLegendIndices.push(legendModel.componentIndex);\n  });\n  var allSelectedMap = {};\n  // make selectedMap from all legend components\n  ecModel.eachComponent('legend', function (legendModel) {\n    each(selectedMap, function (isSelected, name) {\n      // Force other legend has same selected status\n      // Or the first is toggled to true and other are toggled to false\n      // In the case one legend has some item unSelected in option. And if other legend\n      // doesn't has the item, they will assume it is selected.\n      legendModel[isSelected ? 'select' : 'unSelect'](name);\n    });\n    makeSelectedMap(legendModel, allSelectedMap);\n  });\n  // Return the event explicitly\n  return isAllSelect ? {\n    selected: allSelectedMap,\n    // return legendIndex array to tell the developers which legends are allSelect / inverseSelect\n    legendIndex: actionLegendIndices\n  } : {\n    name: payload.name,\n    selected: allSelectedMap\n  };\n}\nfunction makeSelectedMap(legendModel, out) {\n  var selectedMap = out || {};\n  each(legendModel.getData(), function (model) {\n    var name = model.get('name');\n    // Wrap element\n    if (name === '\\n' || name === '') {\n      return;\n    }\n    var isItemSelected = legendModel.isSelected(name);\n    if (hasOwn(selectedMap, name)) {\n      // Unselected if any legend is unselected\n      selectedMap[name] = selectedMap[name] && isItemSelected;\n    } else {\n      selectedMap[name] = isItemSelected;\n    }\n  });\n  return selectedMap;\n}\nexport function installLegendAction(registers) {\n  /**\r\n   * @event legendToggleSelect\r\n   * @type {Object}\r\n   * @property {string} type 'legendToggleSelect'\r\n   * @property {string} [from]\r\n   * @property {string} name Series name or data item name\r\n   */\n  registers.registerAction('legendToggleSelect', 'legendselectchanged', curry(legendSelectActionHandler, 'toggleSelected'));\n  registers.registerAction('legendAllSelect', 'legendselectall', curry(legendSelectActionHandler, 'allSelect'));\n  registers.registerAction('legendInverseSelect', 'legendinverseselect', curry(legendSelectActionHandler, 'inverseSelect'));\n  /**\r\n   * @event legendSelect\r\n   * @type {Object}\r\n   * @property {string} type 'legendSelect'\r\n   * @property {string} name Series name or data item name\r\n   */\n  registers.registerAction('legendSelect', 'legendselected', curry(legendSelectActionHandler, 'select'));\n  /**\r\n   * @event legendUnSelect\r\n   * @type {Object}\r\n   * @property {string} type 'legendUnSelect'\r\n   * @property {string} name Series name or data item name\r\n   */\n  registers.registerAction('legendUnSelect', 'legendunselected', curry(legendSelectActionHandler, 'unSelect'));\n}", "map": {"version": 3, "names": ["curry", "each", "hasOwn", "legendSelectActionHandler", "methodName", "payload", "ecModel", "isAllSelect", "selectedMap", "actionLegendIndices", "eachComponent", "mainType", "query", "legend<PERSON><PERSON><PERSON>", "name", "makeSelectedMap", "push", "componentIndex", "allSelectedMap", "isSelected", "selected", "legendIndex", "out", "getData", "model", "get", "isItemSelected", "installLegendAction", "registers", "registerAction"], "sources": ["E:/AI/SmartCV/node_modules/echarts/lib/component/legend/legendAction.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { curry, each, hasOwn } from 'zrender/lib/core/util.js';\nfunction legendSelectActionHandler(methodName, payload, ecModel) {\n  var isAllSelect = methodName === 'allSelect' || methodName === 'inverseSelect';\n  var selectedMap = {};\n  var actionLegendIndices = [];\n  ecModel.eachComponent({\n    mainType: 'legend',\n    query: payload\n  }, function (legendModel) {\n    if (isAllSelect) {\n      legendModel[methodName]();\n    } else {\n      legendModel[methodName](payload.name);\n    }\n    makeSelectedMap(legendModel, selectedMap);\n    actionLegendIndices.push(legendModel.componentIndex);\n  });\n  var allSelectedMap = {};\n  // make selectedMap from all legend components\n  ecModel.eachComponent('legend', function (legendModel) {\n    each(selectedMap, function (isSelected, name) {\n      // Force other legend has same selected status\n      // Or the first is toggled to true and other are toggled to false\n      // In the case one legend has some item unSelected in option. And if other legend\n      // doesn't has the item, they will assume it is selected.\n      legendModel[isSelected ? 'select' : 'unSelect'](name);\n    });\n    makeSelectedMap(legendModel, allSelectedMap);\n  });\n  // Return the event explicitly\n  return isAllSelect ? {\n    selected: allSelectedMap,\n    // return legendIndex array to tell the developers which legends are allSelect / inverseSelect\n    legendIndex: actionLegendIndices\n  } : {\n    name: payload.name,\n    selected: allSelectedMap\n  };\n}\nfunction makeSelectedMap(legendModel, out) {\n  var selectedMap = out || {};\n  each(legendModel.getData(), function (model) {\n    var name = model.get('name');\n    // Wrap element\n    if (name === '\\n' || name === '') {\n      return;\n    }\n    var isItemSelected = legendModel.isSelected(name);\n    if (hasOwn(selectedMap, name)) {\n      // Unselected if any legend is unselected\n      selectedMap[name] = selectedMap[name] && isItemSelected;\n    } else {\n      selectedMap[name] = isItemSelected;\n    }\n  });\n  return selectedMap;\n}\nexport function installLegendAction(registers) {\n  /**\r\n   * @event legendToggleSelect\r\n   * @type {Object}\r\n   * @property {string} type 'legendToggleSelect'\r\n   * @property {string} [from]\r\n   * @property {string} name Series name or data item name\r\n   */\n  registers.registerAction('legendToggleSelect', 'legendselectchanged', curry(legendSelectActionHandler, 'toggleSelected'));\n  registers.registerAction('legendAllSelect', 'legendselectall', curry(legendSelectActionHandler, 'allSelect'));\n  registers.registerAction('legendInverseSelect', 'legendinverseselect', curry(legendSelectActionHandler, 'inverseSelect'));\n  /**\r\n   * @event legendSelect\r\n   * @type {Object}\r\n   * @property {string} type 'legendSelect'\r\n   * @property {string} name Series name or data item name\r\n   */\n  registers.registerAction('legendSelect', 'legendselected', curry(legendSelectActionHandler, 'select'));\n  /**\r\n   * @event legendUnSelect\r\n   * @type {Object}\r\n   * @property {string} type 'legendUnSelect'\r\n   * @property {string} name Series name or data item name\r\n   */\n  registers.registerAction('legendUnSelect', 'legendunselected', curry(legendSelectActionHandler, 'unSelect'));\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,KAAK,EAAEC,IAAI,EAAEC,MAAM,QAAQ,0BAA0B;AAC9D,SAASC,yBAAyBA,CAACC,UAAU,EAAEC,OAAO,EAAEC,OAAO,EAAE;EAC/D,IAAIC,WAAW,GAAGH,UAAU,KAAK,WAAW,IAAIA,UAAU,KAAK,eAAe;EAC9E,IAAII,WAAW,GAAG,CAAC,CAAC;EACpB,IAAIC,mBAAmB,GAAG,EAAE;EAC5BH,OAAO,CAACI,aAAa,CAAC;IACpBC,QAAQ,EAAE,QAAQ;IAClBC,KAAK,EAAEP;EACT,CAAC,EAAE,UAAUQ,WAAW,EAAE;IACxB,IAAIN,WAAW,EAAE;MACfM,WAAW,CAACT,UAAU,CAAC,CAAC,CAAC;IAC3B,CAAC,MAAM;MACLS,WAAW,CAACT,UAAU,CAAC,CAACC,OAAO,CAACS,IAAI,CAAC;IACvC;IACAC,eAAe,CAACF,WAAW,EAAEL,WAAW,CAAC;IACzCC,mBAAmB,CAACO,IAAI,CAACH,WAAW,CAACI,cAAc,CAAC;EACtD,CAAC,CAAC;EACF,IAAIC,cAAc,GAAG,CAAC,CAAC;EACvB;EACAZ,OAAO,CAACI,aAAa,CAAC,QAAQ,EAAE,UAAUG,WAAW,EAAE;IACrDZ,IAAI,CAACO,WAAW,EAAE,UAAUW,UAAU,EAAEL,IAAI,EAAE;MAC5C;MACA;MACA;MACA;MACAD,WAAW,CAACM,UAAU,GAAG,QAAQ,GAAG,UAAU,CAAC,CAACL,IAAI,CAAC;IACvD,CAAC,CAAC;IACFC,eAAe,CAACF,WAAW,EAAEK,cAAc,CAAC;EAC9C,CAAC,CAAC;EACF;EACA,OAAOX,WAAW,GAAG;IACnBa,QAAQ,EAAEF,cAAc;IACxB;IACAG,WAAW,EAAEZ;EACf,CAAC,GAAG;IACFK,IAAI,EAAET,OAAO,CAACS,IAAI;IAClBM,QAAQ,EAAEF;EACZ,CAAC;AACH;AACA,SAASH,eAAeA,CAACF,WAAW,EAAES,GAAG,EAAE;EACzC,IAAId,WAAW,GAAGc,GAAG,IAAI,CAAC,CAAC;EAC3BrB,IAAI,CAACY,WAAW,CAACU,OAAO,CAAC,CAAC,EAAE,UAAUC,KAAK,EAAE;IAC3C,IAAIV,IAAI,GAAGU,KAAK,CAACC,GAAG,CAAC,MAAM,CAAC;IAC5B;IACA,IAAIX,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,EAAE,EAAE;MAChC;IACF;IACA,IAAIY,cAAc,GAAGb,WAAW,CAACM,UAAU,CAACL,IAAI,CAAC;IACjD,IAAIZ,MAAM,CAACM,WAAW,EAAEM,IAAI,CAAC,EAAE;MAC7B;MACAN,WAAW,CAACM,IAAI,CAAC,GAAGN,WAAW,CAACM,IAAI,CAAC,IAAIY,cAAc;IACzD,CAAC,MAAM;MACLlB,WAAW,CAACM,IAAI,CAAC,GAAGY,cAAc;IACpC;EACF,CAAC,CAAC;EACF,OAAOlB,WAAW;AACpB;AACA,OAAO,SAASmB,mBAAmBA,CAACC,SAAS,EAAE;EAC7C;AACF;AACA;AACA;AACA;AACA;AACA;EACEA,SAAS,CAACC,cAAc,CAAC,oBAAoB,EAAE,qBAAqB,EAAE7B,KAAK,CAACG,yBAAyB,EAAE,gBAAgB,CAAC,CAAC;EACzHyB,SAAS,CAACC,cAAc,CAAC,iBAAiB,EAAE,iBAAiB,EAAE7B,KAAK,CAACG,yBAAyB,EAAE,WAAW,CAAC,CAAC;EAC7GyB,SAAS,CAACC,cAAc,CAAC,qBAAqB,EAAE,qBAAqB,EAAE7B,KAAK,CAACG,yBAAyB,EAAE,eAAe,CAAC,CAAC;EACzH;AACF;AACA;AACA;AACA;AACA;EACEyB,SAAS,CAACC,cAAc,CAAC,cAAc,EAAE,gBAAgB,EAAE7B,KAAK,CAACG,yBAAyB,EAAE,QAAQ,CAAC,CAAC;EACtG;AACF;AACA;AACA;AACA;AACA;EACEyB,SAAS,CAACC,cAAc,CAAC,gBAAgB,EAAE,kBAAkB,EAAE7B,KAAK,CAACG,yBAAyB,EAAE,UAAU,CAAC,CAAC;AAC9G", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}