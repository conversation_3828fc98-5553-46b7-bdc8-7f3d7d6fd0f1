import axios from 'axios';

// Configure base URL
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';

// Create axios instance
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 120000, // 2 minutes timeout
  headers: {
    'Content-Type': 'multipart/form-data',
  },
});

// Request interceptor
apiClient.interceptors.request.use(
  (config) => {
    console.log('API Request:', config.method?.toUpperCase(), config.url);
    return config;
  },
  (error) => {
    console.error('Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor
apiClient.interceptors.response.use(
  (response) => {
    console.log('API Response:', response.status, response.config.url);
    return response;
  },
  (error) => {
    console.error('Response Error:', error);
    
    if (error.response) {
      // Server returned error status code
      const { status, data } = error.response;
      
      switch (status) {
        case 400:
          throw new Error(data.message || 'Invalid request parameters');
        case 413:
          throw new Error('File too large. Please upload a file smaller than 16MB');
        case 500:
          throw new Error(data.message || 'Internal server error');
        default:
          throw new Error(data.message || `Request failed (${status})`);
      }
    } else if (error.request) {
      // Network error
      throw new Error('Network connection failed. Please check your connection and try again');
    } else {
      // Other errors
      throw new Error(error.message || 'An unknown error occurred');
    }
  }
);

/**
 * Resume optimization API call
 * @param {File} resumeFile - Resume file (supports PDF, DOCX, DOC, and image formats)
 * @param {string} jobDescriptionText - Job description text
 * @param {File} jobDescriptionFile - Job description file
 * @returns {Promise} - Optimization result
 */
export const optimizeResume = async (resumeFile, jobDescriptionText, jobDescriptionFile) => {
  try {
    // Create FormData object
    const formData = new FormData();
    
    // Add resume file
    if (!resumeFile) {
      throw new Error('Please select a resume file');
    }
    formData.append('resume_file', resumeFile);
    
    // Add job description
    if (jobDescriptionText && jobDescriptionText.trim()) {
      formData.append('job_description_text', jobDescriptionText.trim());
    } else if (jobDescriptionFile) {
      formData.append('job_description_file', jobDescriptionFile);
    } else {
      throw new Error('Please provide a job description');
    }
    
    // Send request
    const response = await apiClient.post('/api/optimize-resume', formData);
    
    // Check response
    if (!response.data.success) {
      throw new Error(response.data.message || 'Optimization failed');
    }
    
    return response.data;
    
  } catch (error) {
    console.error('Optimize Resume Error:', error);
    throw error;
  }
};

/**
 * Health check API
 * @returns {Promise} - Service status
 */
export const healthCheck = async () => {
  try {
    const response = await apiClient.get('/api/health');
    return response.data;
  } catch (error) {
    console.error('Health Check Error:', error);
    throw error;
  }
};

/**
 * Check API connection status
 * @returns {Promise<boolean>} - Connection status
 */
export const checkApiConnection = async () => {
  try {
    await healthCheck();
    return true;
  } catch (error) {
    console.warn('API connection check failed:', error.message);
    return false;
  }
};

// File size limit (16MB)
export const MAX_FILE_SIZE = 16 * 1024 * 1024;

/**
 * Validate file size
 * @param {File} file - File to validate
 * @returns {boolean} - Whether file meets size requirements
 */
export const validateFileSize = (file) => {
  return file && file.size <= MAX_FILE_SIZE;
};

/**
 * Validate file type
 * @param {File} file - File to validate
 * @param {string[]} allowedTypes - Allowed file types
 * @returns {boolean} - Whether file type is allowed
 */
export const validateFileType = (file, allowedTypes) => {
  if (!file) return false;
  
  const fileExtension = file.name.split('.').pop()?.toLowerCase();
  return allowedTypes.includes(fileExtension);
};

// 下载优化后的DOCX文件
export const downloadOptimizedDocx = async (filename) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/download-optimized-docx/${filename}`, {
      responseType: 'blob'
    });
    
    return response.data;
  } catch (error) {
    console.error('Download error:', error);
    throw new Error(error.response?.data?.message || 'Download failed');
  }
};

// 上传文件用于预览
export const uploadFileForPreview = async (file) => {
  try {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await axios.post(`${API_BASE_URL}/upload-for-preview`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('Upload for preview error:', error);
    throw new Error(error.response?.data?.error || 'Upload failed');
  }
};

// 获取文档预览图片
export const getDocumentPreview = async (fileId) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/document-preview/${fileId}`);
    return response.data;
  } catch (error) {
    console.error('Get document preview error:', error);
    throw new Error(error.response?.data?.error || 'Preview generation failed');
  }
};

// 上传临时文件用于预览
export const uploadTempFile = async (file) => {
  try {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await axios.post(`${API_BASE_URL}/api/upload-temp-file`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('Upload temp file error:', error);
    throw new Error(error.response?.data?.error || 'Temp file upload failed');
  }
};

export default apiClient; 