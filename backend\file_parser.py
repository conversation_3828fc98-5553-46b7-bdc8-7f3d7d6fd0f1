import io
import logging
from pdfminer.high_level import extract_text
from pdfminer.pdfpage import PDFPage
from pdfminer.pdfinterp import PDFResourceManager, PDFPageInterpreter
from pdfminer.converter import TextConverter
from pdfminer.layout import LAParams
from docx import Document

logger = logging.getLogger(__name__)

def extract_text_from_pdf(file_path):
    """从PDF文件提取文本内容"""
    try:
        logger.info(f"开始解析PDF文件: {file_path}")
        
        # 使用pdfminer.six提取文本
        text = extract_text(file_path)
        
        if not text.strip():
            # 如果第一种方法没有提取到内容，尝试第二种方法
            logger.info("尝试使用替代方法提取PDF文本...")
            text = extract_text_alternative(file_path)
        
        # 清理文本
        cleaned_text = clean_extracted_text(text)
        logger.info(f"PDF文本提取完成，内容长度: {len(cleaned_text)}")
        
        return cleaned_text
        
    except Exception as e:
        logger.error(f"PDF文件解析失败: {str(e)}")
        raise Exception(f"PDF文件解析失败: {str(e)}")

def extract_text_alternative(file_path):
    """PDF文本提取的替代方法"""
    try:
        output_string = io.StringIO()
        with open(file_path, 'rb') as fp:
            rsrcmgr = PDFResourceManager()
            device = TextConverter(rsrcmgr, output_string, laparams=LAParams())
            interpreter = PDFPageInterpreter(rsrcmgr, device)
            
            for page in PDFPage.get_pages(fp):
                interpreter.process_page(page)
        
        text = output_string.getvalue()
        device.close()
        output_string.close()
        
        return text
        
    except Exception as e:
        logger.error(f"PDF替代解析方法失败: {str(e)}")
        return ""

def extract_text_from_docx(file_path):
    """从DOCX文件提取文本内容，保留更多结构信息"""
    try:
        logger.info(f"开始解析DOCX文件: {file_path}")
        
        text_content = []
        
        try:
            doc = Document(file_path)
            
            # 提取段落文本
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_content.append(paragraph.text)
            
            # 提取表格文本，保留表格结构
            for table_idx, table in enumerate(doc.tables):
                table_text = f"\n=== 表格 {table_idx + 1} ===\n"
                
                for row_idx, row in enumerate(table.rows):
                    row_texts = []
                    for cell in row.cells:
                        cell_text = cell.text.strip()
                        if cell_text:
                            row_texts.append(cell_text)
                        else:
                            row_texts.append("")
                    
                    if any(row_texts):  # 只有当行中有内容时才添加
                        # 使用制表符分隔单元格内容
                        table_text += " | ".join(row_texts) + "\n"
                
                table_text += "=== 表格结束 ===\n"
                text_content.append(table_text)
            
            # 合并所有文本内容
            full_text = '\n'.join(text_content)
            
            # 使用改进的清理函数
            cleaned_text = clean_extracted_text_with_structure(full_text)
            logger.info(f"DOCX文本提取完成，内容长度: {len(cleaned_text)}")
            
            return cleaned_text
            
        except Exception as doc_error:
            logger.error(f"DOCX文档处理错误: {str(doc_error)}")
            raise doc_error
        
    except Exception as e:
        logger.error(f"DOCX文件解析失败: {str(e)}")
        raise Exception(f"DOCX文件解析失败: {str(e)}")

def clean_extracted_text(text):
    """清理提取的文本内容"""
    if not text:
        return ""
    
    # 移除过多的空白字符
    lines = text.split('\n')
    cleaned_lines = []
    
    for line in lines:
        # 移除行首尾空白
        cleaned_line = line.strip()
        if cleaned_line:  # 只保留非空行
            cleaned_lines.append(cleaned_line)
    
    # 合并连续的单行
    result_lines = []
    for i, line in enumerate(cleaned_lines):
        # 如果当前行很短且下一行存在，可能是被错误分割的行
        if (i < len(cleaned_lines) - 1 and 
            len(line) < 80 and 
            not line.endswith(('.', ':', '!', '?', ';')) and
            not cleaned_lines[i + 1].startswith(('•', '-', '*', '1.', '2.', '3.'))):
            # 将当前行与下一行合并
            if result_lines:
                result_lines[-1] += ' ' + line
            else:
                result_lines.append(line)
        else:
            result_lines.append(line)
    
    return '\n'.join(result_lines)

def clean_extracted_text_with_structure(text):
    """清理提取的文本内容，保留结构信息"""
    if not text:
        return ""
    
    lines = text.split('\n')
    cleaned_lines = []
    
    for line in lines:
        # 移除行首尾空白
        cleaned_line = line.strip()
        
        # 保留表格标记和分隔符
        if cleaned_line and (
            cleaned_line.startswith('===') or 
            '|' in cleaned_line or 
            len(cleaned_line) > 5
        ):
            cleaned_lines.append(cleaned_line)
    
    return '\n'.join(cleaned_lines)

def validate_extracted_content(text, min_length=50):
    """验证提取的内容是否有效"""
    if not text or len(text.strip()) < min_length:
        return False, "提取的内容太短，可能解析失败"
    
    # 检查是否包含基本的简历信息
    text_lower = text.lower()
    resume_keywords = ['experience', 'education', 'skill', 'work', 'project', 
                      '经验', '教育', '技能', '工作', '项目', '经历']
    
    if not any(keyword in text_lower for keyword in resume_keywords):
        return False, "内容可能不是有效的简历格式"
    
    return True, "内容验证通过" 