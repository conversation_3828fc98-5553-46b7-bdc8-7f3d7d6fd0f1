{"ast": null, "code": "var _jsxFileName = \"E:\\\\AI\\\\SmartCV\\\\frontend\\\\src\\\\components\\\\ResultDisplay.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { exportToPDF, exportToWord, exportToText, printResume } from '../utils/exportUtils';\nimport { downloadOptimizedDocx } from '../services/api';\nimport DocumentPreview from './DocumentPreview';\n// import ReactECharts from 'echarts-for-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ResultDisplay = ({\n  result,\n  originalFile,\n  jobDescription\n}) => {\n  _s();\n  var _result$job_match_ana, _result$job_match_ana2, _result$job_match_ana3, _result$job_match_ana4, _result$job_match_ana5, _result$job_match_ana6, _result$job_match_ana7;\n  const [showComparison, setShowComparison] = useState(false);\n  const [activeTab, setActiveTab] = useState('optimized'); // 'optimized', 'original', 'comparison'\n  const [showFormattedOptimized, setShowFormattedOptimized] = useState(false); // Toggle for formatted view in comparison\n  const [downloadingDocx, setDownloadingDocx] = useState(false);\n  const [showExportDropdown, setShowExportDropdown] = useState(false);\n  const [viewMode, setViewMode] = useState('raw'); // 'raw', 'enhanced', 'word'\n  const [wordPreviewMode, setWordPreviewMode] = useState('office'); // 'office', 'google', 'download'\n  const [wordPreviewError, setWordPreviewError] = useState(null);\n  // 新增：用于控制每个分类的展开/收起状态\n  const [expandedMatched, setExpandedMatched] = useState({});\n  const [expandedMissing, setExpandedMissing] = useState({});\n  const [showAllSuggestions, setShowAllSuggestions] = useState(false);\n\n  // 点击外部区域关闭下拉菜单\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (showExportDropdown && !event.target.closest('.export-dropdown')) {\n        setShowExportDropdown(false);\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, [showExportDropdown]);\n  const formatOptimizedResume = text => {\n    if (!text) return '';\n\n    // Process Markdown-formatted resume\n    return text.replace(/^#\\s+(.+)$/gm, '<h1 class=\"text-2xl font-bold text-gray-900 mb-4 border-b-2 border-orange-500 pb-2\">$1</h1>').replace(/^##\\s+(.+)$/gm, '<h2 class=\"text-xl font-semibold text-gray-800 mb-3 mt-6\">$1</h2>').replace(/^###\\s+(.+)$/gm, '<h3 class=\"text-lg font-medium text-gray-700 mb-2 mt-4\">$3</h3>').replace(/^\\*\\*(.+?)\\*\\*:?(.*)$/gm, '<div class=\"mb-2\"><span class=\"font-semibold text-gray-900\">$1</span>$2</div>').replace(/^\\* (.+)$/gm, '<li class=\"ml-4 mb-1 text-gray-700\">$1</li>').replace(/^- (.+)$/gm, '<li class=\"ml-4 mb-1 text-gray-700\">$1</li>').replace(/\\*\\*(.+?)\\*\\*/g, '<span class=\"font-semibold\">$1</span>').replace(/\\*(.+?)\\*/g, '<span class=\"italic\">$1</span>').replace(/\\n\\n/g, '</p><p class=\"mb-3\">').replace(/\\n/g, '<br>').replace(/^(?!<[h|l|d])(.+)$/gm, '<p class=\"mb-3 text-gray-700\">$1</p>');\n  };\n  const generateATSScore = (optimizedText, originalText) => {\n    // 确保输入文本不为空，提供默认值\n    const safeOptimizedText = optimizedText || '';\n    const safeOriginalText = originalText || '';\n\n    // Simple ATS scoring algorithm\n    const factors = {\n      hasQuantifiedAchievements: /\\d+(%|k|million|billion|\\$|year|month)/.test(safeOptimizedText),\n      hasActionVerbs: /(developed|implemented|managed|led|created|optimized|improved)/.test(safeOptimizedText.toLowerCase()),\n      hasStandardSections: /(experience|education|skills)/.test(safeOptimizedText.toLowerCase()),\n      hasContactInfo: /(email|phone|linkedin)/.test(safeOptimizedText.toLowerCase()),\n      appropriateLength: safeOptimizedText.length > 500 && safeOptimizedText.length < 4000,\n      noSpecialCharacters: !/[★☆▪▫◦]/.test(safeOptimizedText)\n    };\n    const score = Object.values(factors).filter(Boolean).length;\n    const maxScore = Object.keys(factors).length;\n    const percentage = Math.round(score / maxScore * 100);\n    return {\n      score: percentage,\n      factors,\n      improvements: maxScore - score\n    };\n  };\n\n  // 兼容处理不同的数据结构\n  const getOptimizedText = () => {\n    var _result$optimized_res, _result$optimized_res2;\n    // 详细调试信息\n    console.log('=== RESULT ANALYSIS ===');\n    console.log('Complete result object:', result);\n    console.log('Result data structure:', {\n      hasOptimizedResume: !!(result !== null && result !== void 0 && result.optimized_resume),\n      hasOptimizedText: !!(result !== null && result !== void 0 && result.optimized_text),\n      hasOptimizedResumeJson: !!(result !== null && result !== void 0 && result.optimized_resume_json),\n      optimizedResumeLength: (result === null || result === void 0 ? void 0 : (_result$optimized_res = result.optimized_resume) === null || _result$optimized_res === void 0 ? void 0 : _result$optimized_res.length) || 0,\n      optimizedResumeJsonLength: (result === null || result === void 0 ? void 0 : (_result$optimized_res2 = result.optimized_resume_json) === null || _result$optimized_res2 === void 0 ? void 0 : _result$optimized_res2.length) || 0,\n      optimizedResumeType: typeof (result === null || result === void 0 ? void 0 : result.optimized_resume),\n      optimizedResumeJsonType: typeof (result === null || result === void 0 ? void 0 : result.optimized_resume_json)\n    });\n\n    // 如果有optimized_resume_json，显示其内容\n    if (result !== null && result !== void 0 && result.optimized_resume_json) {\n      console.log('optimized_resume_json content:', result.optimized_resume_json);\n    }\n\n    // 首先尝试获取直接的文本内容\n    if (result !== null && result !== void 0 && result.optimized_resume && result.optimized_resume.trim()) {\n      console.log('✅ Using optimized_resume field, length:', result.optimized_resume.length);\n      console.log('optimized_resume preview:', result.optimized_resume.substring(0, 200));\n      return result.optimized_resume;\n    }\n    if (result !== null && result !== void 0 && result.optimized_text && result.optimized_text.trim()) {\n      console.log('✅ Using optimized_text field, length:', result.optimized_text.length);\n      return result.optimized_text;\n    }\n\n    // 如果没有直接文本，尝试从结构化数据重建\n    if (result !== null && result !== void 0 && result.optimized_resume_json && Array.isArray(result.optimized_resume_json) && result.optimized_resume_json.length > 0) {\n      console.log('🔄 Rebuilding from JSON structure, sections:', result.optimized_resume_json.length);\n      const sections = result.optimized_resume_json;\n      console.log('JSON sections details:', sections.map((section, index) => ({\n        index,\n        section: section.section || section.section_name || 'unknown',\n        hasOptimized: !!section.optimized,\n        hasContent: !!section.content,\n        optimizedLength: (section.optimized || '').length,\n        contentLength: (section.content || '').length\n      })));\n      const textContent = sections.map(section => {\n        // 处理不同的JSON结构格式\n        const sectionName = section.section_name || section.section || '';\n        const content = section.content || section.optimized || '';\n        return sectionName ? `${sectionName}\\n${content}` : content;\n      }).filter(text => text.trim()).join('\\n\\n');\n      console.log('✅ Rebuilt text length:', textContent.length);\n      console.log('Rebuilt text preview:', textContent.substring(0, 200));\n      return textContent || '';\n    }\n    console.log('❌ No optimized text found, returning empty string');\n\n    // 最后的备用方案：如果有原始简历，至少显示一些内容\n    if (result !== null && result !== void 0 && result.original_resume) {\n      console.log('🔄 Using original resume as fallback');\n      return `# Original Resume (Optimization Failed)\\n\\n${result.original_resume}`;\n    }\n    return '';\n  };\n  const getOriginalText = () => {\n    return (result === null || result === void 0 ? void 0 : result.original_resume) || (result === null || result === void 0 ? void 0 : result.original_text) || '';\n  };\n\n  // 更精确的文本差异检测函数\n  const detectTextDifferences = (originalText, optimizedText) => {\n    const originalLines = originalText.split('\\n').filter(line => line.trim());\n    const optimizedLines = optimizedText.split('\\n').filter(line => line.trim());\n    const result = [];\n    const usedOptimizedIndices = new Set();\n    originalLines.forEach((originalLine, originalIndex) => {\n      const trimmedOriginal = originalLine.trim();\n      if (!trimmedOriginal) return;\n\n      // 寻找最佳匹配\n      let bestMatch = null;\n      let bestSimilarity = 0;\n      let bestOptimizedIndex = -1;\n      optimizedLines.forEach((optimizedLine, optimizedIdx) => {\n        if (usedOptimizedIndices.has(optimizedIdx)) return;\n        const trimmedOptimized = optimizedLine.trim();\n        const similarity = calculateSimilarity(trimmedOriginal, trimmedOptimized);\n        if (similarity > bestSimilarity && similarity > 0.3) {\n          bestMatch = optimizedLine;\n          bestSimilarity = similarity;\n          bestOptimizedIndex = optimizedIdx;\n        }\n      });\n      if (bestMatch && bestSimilarity > 0.7) {\n        // 高相似度，可能只是微调\n        usedOptimizedIndices.add(bestOptimizedIndex);\n        result.push({\n          type: 'modified',\n          original: originalLine,\n          optimized: bestMatch,\n          similarity: bestSimilarity\n        });\n      } else if (bestMatch && bestSimilarity > 0.3) {\n        // 中等相似度，显著修改\n        usedOptimizedIndices.add(bestOptimizedIndex);\n        result.push({\n          type: 'significantly_modified',\n          original: originalLine,\n          optimized: bestMatch,\n          similarity: bestSimilarity\n        });\n      } else {\n        // 没有找到匹配，可能被删除或完全重写\n        result.push({\n          type: 'deleted',\n          original: originalLine,\n          optimized: null\n        });\n      }\n    });\n    return result;\n  };\n\n  // 计算两个字符串的相似度\n  const calculateSimilarity = (str1, str2) => {\n    if (str1 === str2) return 1;\n    if (!str1 || !str2) return 0;\n    const words1 = str1.toLowerCase().split(/\\s+/);\n    const words2 = str2.toLowerCase().split(/\\s+/);\n    const commonWords = words1.filter(word => words2.includes(word));\n    const totalWords = Math.max(words1.length, words2.length);\n    return commonWords.length / totalWords;\n  };\n\n  // 在优化文本中高亮新增、修改、删除内容\n  const highlightAdditionsInOptimized = optimizedText => {\n    const originalText = getOriginalText();\n    if (!originalText || !optimizedText || originalText === optimizedText) {\n      // 没有原文或内容一致，直接返回普通格式\n      return formatOptimizedResume(optimizedText);\n    }\n    // 差异检测\n    const differences = detectTextDifferences(originalText, optimizedText);\n    const optimizedLines = optimizedText.split('\\n').filter(line => line.trim());\n    const originalLines = originalText.split('\\n').filter(line => line.trim());\n    const usedOriginalIndices = new Set();\n    const usedOptimizedIndices = new Set();\n    // 标记已匹配的行\n    differences.forEach((diff, idx) => {\n      if (diff.type !== 'deleted') {\n        const optIdx = optimizedLines.findIndex(line => {\n          var _diff$optimized;\n          return line.trim() === ((_diff$optimized = diff.optimized) === null || _diff$optimized === void 0 ? void 0 : _diff$optimized.trim());\n        });\n        if (optIdx !== -1) usedOptimizedIndices.add(optIdx);\n      }\n      if (diff.type !== 'added') {\n        const oriIdx = originalLines.findIndex(line => {\n          var _diff$original;\n          return line.trim() === ((_diff$original = diff.original) === null || _diff$original === void 0 ? void 0 : _diff$original.trim());\n        });\n        if (oriIdx !== -1) usedOriginalIndices.add(oriIdx);\n      }\n    });\n    // 渲染优化内容\n    return optimizedLines.map((line, idx) => {\n      const escapedLine = line.replace(/</g, '&lt;').replace(/>/g, '&gt;');\n      // 检查是否为修改说明（如以This revised resume/This resume等开头，且为最后一段）\n      const isRevisionNote = idx === optimizedLines.length - 1 && /^(This revised resume|This resume|优化说明|修改说明|优化总结|本次修改说明|优化建议)/i.test(line.trim());\n      if (isRevisionNote) {\n        // 检查是否为中文优化说明，若是则替换为英文模板\n        let revisionContent = line.trim();\n        if (/^(本次修改说明|优化建议)/.test(revisionContent)) {\n          revisionContent = `This revision note summarizes the main changes and optimization suggestions:\\n\\n- Integrated keywords from the target job, such as \\\"data analysis\\\", \\\"project management\\\", \\\"big data\\\", etc.\\n- Reorganized the order of work experience to highlight the most relevant experiences.\\n- Used action verbs to describe responsibilities and achievements.\\n- Formatted the resume to be ATS-friendly, removed tables and special characters.\\n\\nOptimization Suggestions:\\n- Quantified achievements for the \\\"Administrative Assistant\\\" role.\\n- Added specific descriptions of data collection and analysis for the \\\"Intern\\\" position.`;\n        }\n        return `<p class=\\\"mb-3 leading-relaxed bg-gray-50 border-l-4 border-gray-400 px-4 py-2 italic text-gray-700 font-semibold\\\" style=\\\"font-style:italic;\\\"><span class=\\\"text-gray-500 font-bold mr-2\\\">Revision Note:</span>${revisionContent.replace(/\\n/g, '<br>')}</p>`;\n      }\n      // 新增内容\n      if (!usedOriginalIndices.has(idx) && line.trim().length > 0) {\n        return `<p class=\\\"mb-3 leading-relaxed\\\"><span class=\\\"inline-block bg-green-50 text-green-700 font-medium px-2 py-1 rounded border border-green-200 mr-1\\\" title=\\\"新增内容\\\">✓ ${escapedLine}</span></p>`;\n      }\n      // 修改内容\n      const diff = differences.find(d => {\n        var _d$optimized;\n        return ((_d$optimized = d.optimized) === null || _d$optimized === void 0 ? void 0 : _d$optimized.trim()) === line.trim() && d.type === 'significantly_modified';\n      });\n      if (diff) {\n        return `<p class=\\\"mb-3 leading-relaxed\\\"><span class=\\\"inline-block bg-orange-50 text-orange-700 font-medium px-2 py-1 rounded border border-orange-200 mr-1\\\" title=\\\"修改内容\\\">${escapedLine}</span></p>`;\n      }\n      // 轻微修改\n      const diff2 = differences.find(d => {\n        var _d$optimized2;\n        return ((_d$optimized2 = d.optimized) === null || _d$optimized2 === void 0 ? void 0 : _d$optimized2.trim()) === line.trim() && d.type === 'modified';\n      });\n      if (diff2) {\n        return `<p class=\\\"mb-3 leading-relaxed\\\"><span class=\\\"inline-block bg-yellow-50 text-yellow-700 font-medium px-2 py-1 rounded border border-yellow-200 mr-1\\\" title=\\\"轻微修改\\\">${escapedLine}</span></p>`;\n      }\n      // 普通内容\n      return `<p class=\\\"mb-3 text-gray-700 leading-relaxed\\\">${escapedLine}</p>`;\n    }).join('');\n    // 删除内容在优化内容中不显示，但可选在末尾加提示\n    // differences.filter(d => d.type === 'deleted').map(...)\n  };\n  const atsScore = generateATSScore(getOptimizedText(), getOriginalText());\n  const getScoreColor = score => {\n    if (score >= 80) return 'text-green-600';\n    if (score >= 60) return 'text-orange-600';\n    return 'text-red-600';\n  };\n  const getScoreBg = score => {\n    if (score >= 80) return 'bg-green-100';\n    if (score >= 60) return 'bg-orange-100';\n    return 'bg-red-100';\n  };\n  const getHeaderBg = score => {\n    if (score >= 80) return 'bg-green-50 border-green-200';\n    if (score >= 60) return 'bg-orange-50 border-orange-200';\n    return 'bg-red-50 border-red-200';\n  };\n  const getIconBg = score => {\n    if (score >= 80) return 'bg-green-500';\n    if (score >= 60) return 'bg-orange-500';\n    return 'bg-red-500';\n  };\n  const getProgressBarColor = score => {\n    if (score >= 80) return 'bg-green-500';\n    if (score >= 60) return 'bg-orange-500';\n    return 'bg-red-500';\n  };\n  const getBorderColor = score => {\n    if (score >= 80) return 'border-green-200';\n    if (score >= 60) return 'border-orange-200';\n    return 'border-red-200';\n  };\n\n  // 处理DOCX下载\n  const handleDownloadDocx = async () => {\n    try {\n      const formData = new FormData();\n      formData.append('file', originalFile);\n      formData.append('job_description', jobDescription);\n      const response = await fetch('/api/optimize-resume-docx', {\n        method: 'POST',\n        body: formData\n      });\n      if (!response.ok) {\n        const error = await response.json();\n        throw new Error(error.error || '下载失败');\n      }\n\n      // 获取文件名\n      const contentDisposition = response.headers.get('content-disposition');\n      const filename = contentDisposition ? contentDisposition.split('filename=')[1].replace(/\"/g, '') : 'optimized_resume.docx';\n\n      // 下载文件\n      const blob = await response.blob();\n      const url = window.URL.createObjectURL(blob);\n      const a = document.createElement('a');\n      a.href = url;\n      a.download = filename;\n      document.body.appendChild(a);\n      a.click();\n      window.URL.revokeObjectURL(url);\n      document.body.removeChild(a);\n    } catch (error) {\n      console.error('下载DOCX失败:', error);\n      alert('下载失败: ' + error.message);\n    }\n  };\n  const toggleExpand = (key, type) => {\n    if (type === 'matched') {\n      setExpandedMatched(prev => ({\n        ...prev,\n        [key]: !prev[key]\n      }));\n    } else {\n      setExpandedMissing(prev => ({\n        ...prev,\n        [key]: !prev[key]\n      }));\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `${getHeaderBg(atsScore.score)} px-8 py-8 border-b relative overflow-hidden`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-0 right-0 transform translate-x-4 -translate-y-4 opacity-10\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-32 h-32\",\n              fill: \"currentColor\",\n              viewBox: \"0 0 20 20\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                fillRule: \"evenodd\",\n                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                clipRule: \"evenodd\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-6 lg:mb-0\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `${getIconBg(atsScore.score)} text-white rounded-full w-12 h-12 flex items-center justify-center mr-4 shadow-lg`,\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-6 h-6\",\n                      fill: \"none\",\n                      stroke: \"currentColor\",\n                      viewBox: \"0 0 24 24\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M5 13l4 4L19 7\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 390,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 389,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 388,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-3xl font-bold text-gray-900 mb-1\",\n                      children: \"Resume Optimization Complete\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 394,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center text-sm text-gray-600\",\n                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                        className: \"w-4 h-4 mr-1\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          strokeWidth: 2,\n                          d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 399,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 398,\n                        columnNumber: 25\n                      }, this), \"AI has optimized your resume according to job requirements, improving ATS pass rate\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 397,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 393,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 387,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 386,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `bg-white border-2 ${getBorderColor(atsScore.score)} rounded-2xl px-8 py-6 text-center min-w-[160px] shadow-lg transform hover:scale-105 transition-transform`,\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `text-4xl font-extrabold mb-2 ${getScoreColor(atsScore.score)}`,\n                    children: [atsScore.score, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 410,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-semibold text-gray-600 mb-2\",\n                    children: \"ATS Match\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 413,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full bg-gray-200 rounded-full h-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `h-2 rounded-full transition-all duration-1000 ${getProgressBarColor(atsScore.score)}`,\n                      style: {\n                        width: `${atsScore.score}%`\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 415,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 414,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 409,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `inline-flex items-center px-4 py-2 rounded-full text-sm font-medium ${atsScore.score >= 80 ? 'bg-green-100 text-green-800' : atsScore.score >= 60 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}`,\n                    children: atsScore.score >= 80 ? 'Excellent' : atsScore.score >= 60 ? 'Good' : 'Needs Improvement'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 424,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-gray-500 mt-1\",\n                    children: atsScore.score >= 80 ? 'Very likely to pass ATS screening' : atsScore.score >= 60 ? 'High chance to pass ATS screening' : 'Recommend further optimization'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 431,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 423,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 407,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 11\n        }, this), (result === null || result === void 0 ? void 0 : result.job_match_analysis) && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold text-gray-900 mb-2\",\n              children: \"Job Match Analysis\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"AI analysis shows significant improvement in resume-job alignment\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 rounded-lg p-6 text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold text-gray-500 mb-2\",\n                children: [((_result$job_match_ana = result.job_match_analysis.original_match_score) === null || _result$job_match_ana === void 0 ? void 0 : _result$job_match_ana.overall_match_score) || 0, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600 mb-3\",\n                children: \"Original Match\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 458,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full bg-gray-200 rounded-full h-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-gray-400 h-2 rounded-full transition-all duration-1000\",\n                  style: {\n                    width: `${((_result$job_match_ana2 = result.job_match_analysis.original_match_score) === null || _result$job_match_ana2 === void 0 ? void 0 : _result$job_match_ana2.overall_match_score) || 0}%`\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 460,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 459,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-green-50 rounded-lg p-6 border-2 border-green-200 text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-center mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5 text-green-600 mr-2\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M5 13l4 4L19 7\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 471,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 470,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-2xl font-bold text-green-600\",\n                  children: [((_result$job_match_ana3 = result.job_match_analysis.optimized_match_score) === null || _result$job_match_ana3 === void 0 ? void 0 : _result$job_match_ana3.overall_match_score) || 0, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 473,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 469,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-green-600 font-medium mb-3\",\n                children: \"Optimized Match\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 477,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full bg-green-100 rounded-full h-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-green-500 h-2 rounded-full transition-all duration-1000\",\n                  style: {\n                    width: `${((_result$job_match_ana4 = result.job_match_analysis.optimized_match_score) === null || _result$job_match_ana4 === void 0 ? void 0 : _result$job_match_ana4.overall_match_score) || 0}%`\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 479,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 478,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-blue-50 rounded-lg p-6 text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `text-2xl font-bold mb-2 ${result.job_match_analysis.match_improvement >= 0 ? 'text-blue-600' : 'text-orange-600'}`,\n                children: [result.job_match_analysis.match_improvement >= 0 ? '+' : '', result.job_match_analysis.match_improvement, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 488,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600 mb-3\",\n                children: \"Improvement\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 491,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-center text-xs text-gray-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-3 h-3 mr-1 text-blue-500\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 494,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 493,\n                  columnNumber: 21\n                }, this), \"Significant Growth\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 492,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 487,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 452,\n            columnNumber: 15\n          }, this), ((_result$job_match_ana5 = result.job_match_analysis.optimized_match_score) === null || _result$job_match_ana5 === void 0 ? void 0 : _result$job_match_ana5.breakdown) && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-50 rounded-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-md font-medium text-gray-800 mb-4\",\n              children: \"Skill Match Breakdown\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n              children: Object.entries(result.job_match_analysis.optimized_match_score.breakdown).map(([key, data]) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between p-3 bg-white rounded-lg border border-gray-100\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-700 capitalize font-medium\",\n                  children: key.replace('_', ' ')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 508,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `text-sm font-semibold mr-3 ${data.score >= 70 ? 'text-green-600' : data.score >= 50 ? 'text-yellow-600' : 'text-red-600'}`,\n                    children: [data.score, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 512,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-16 bg-gray-200 rounded-full h-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `h-2 rounded-full ${data.score >= 70 ? 'bg-green-500' : data.score >= 50 ? 'bg-yellow-500' : 'bg-red-500'}`,\n                      style: {\n                        width: `${data.score}%`\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 516,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 515,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 511,\n                  columnNumber: 25\n                }, this)]\n              }, key, true, {\n                fileName: _jsxFileName,\n                lineNumber: 507,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 505,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 444,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 374,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 373,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border-b border-gray-200 bg-gray-50\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"px-8\",\n            children: /*#__PURE__*/_jsxDEV(\"nav\", {\n              className: \"flex space-x-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setActiveTab('optimized'),\n                className: `py-4 px-1 border-b-2 font-medium text-sm transition-colors ${activeTab === 'optimized' ? 'border-orange-500 text-orange-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-4 h-4\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 552,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 551,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Optimized Resume\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 554,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 550,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 542,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setActiveTab('analysis'),\n                className: `py-4 px-1 border-b-2 font-medium text-sm transition-colors ${activeTab === 'analysis' ? 'border-orange-500 text-orange-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-4 h-4\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 568,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 567,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"ATS Analysis\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 570,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 566,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 558,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 541,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 540,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 539,\n          columnNumber: 11\n        }, this), activeTab === 'optimized' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-8 py-6 border-b border-gray-100\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"text-xl font-semibold text-gray-900 mb-6 flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-6 h-6 mr-3 text-gray-600\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 582,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 581,\n              columnNumber: 17\n            }, this), \"Optimization Statistics\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 580,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n            children: (() => {\n              // 计算实际的文本长度\n              const originalText = getOriginalText();\n              const optimizedText = getOptimizedText();\n              const originalLength = originalText.length;\n              const optimizedLength = optimizedText.length;\n              const diff = optimizedLength - originalLength;\n              return /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-gray-50 rounded-lg p-6 text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs font-medium text-gray-500 mb-2\",\n                    children: \"Original Length\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 600,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl font-bold text-gray-900 mb-1\",\n                    children: originalLength > 0 ? originalLength.toLocaleString() : 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 601,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-gray-400\",\n                    children: \"Characters\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 604,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 599,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-gray-50 rounded-lg p-6 text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs font-medium text-gray-500 mb-2\",\n                    children: \"Optimized Length\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 609,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl font-bold text-gray-900 mb-1\",\n                    children: optimizedLength > 0 ? optimizedLength.toLocaleString() : 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 610,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-gray-400\",\n                    children: \"Characters\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 613,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 608,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-blue-50 rounded-lg p-6 text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs font-medium text-gray-500 mb-2\",\n                    children: \"Change\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 618,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl font-bold mb-1\",\n                    children: originalLength === 0 || optimizedLength === 0 ? /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-400\",\n                      children: \"N/A\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 621,\n                      columnNumber: 29\n                    }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: diff >= 0 ? 'text-blue-600' : 'text-orange-600',\n                      children: [diff >= 0 ? '+' : '', diff.toLocaleString()]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 623,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 619,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-gray-400\",\n                    children: \"Characters\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 628,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 617,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-green-50 rounded-lg p-6 text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs font-medium text-gray-500 mb-2\",\n                    children: \"Optimization Rate\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 633,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl font-bold mb-1\",\n                    children: originalLength === 0 || optimizedLength === 0 ? /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-400\",\n                      children: \"N/A\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 636,\n                      columnNumber: 29\n                    }, this) : (() => {\n                      let displayRate = 0;\n                      if (optimizedLength > originalLength) {\n                        displayRate = (optimizedLength - originalLength) / originalLength * 100;\n                      } else {\n                        displayRate = (originalLength - optimizedLength) / originalLength * 100;\n                      }\n                      displayRate = Math.max(0, displayRate);\n                      const colorClass = displayRate > 15 ? 'text-green-600' : displayRate > 5 ? 'text-blue-600' : 'text-orange-600';\n                      return /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: colorClass,\n                        children: [displayRate.toFixed(1), \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 647,\n                        columnNumber: 31\n                      }, this);\n                    })()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 634,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-gray-400\",\n                    children: \"Adjustment\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 653,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 632,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true);\n            })()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 587,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 579,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-8\",\n          children: [activeTab === 'optimized' && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6 flex justify-between items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-4 text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-600\",\n                  children: \"Optimized Resume View:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 669,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setViewMode('raw'),\n                    className: `px-3 py-1 rounded-md text-xs font-medium transition-colors ${viewMode === 'raw' ? 'bg-orange-100 text-orange-700' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,\n                    children: \"Raw Text\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 671,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setViewMode('enhanced'),\n                    className: `px-3 py-1 rounded-md text-xs font-medium transition-colors ${viewMode === 'enhanced' ? 'bg-orange-100 text-orange-700' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,\n                    children: \"Enhanced View\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 681,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setViewMode('word'),\n                    className: `px-3 py-1 rounded-md text-xs font-medium transition-colors ${viewMode === 'word' ? 'bg-orange-100 text-orange-700' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,\n                    disabled: !(result !== null && result !== void 0 && result.optimized_docx_url),\n                    children: \"Word Preview\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 691,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 670,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 668,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative export-dropdown\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setShowExportDropdown(!showExportDropdown),\n                  className: \"inline-flex items-center px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors text-sm font-medium\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-4 h-4 mr-2\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 710,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 709,\n                    columnNumber: 23\n                  }, this), \"Export & Download\", /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-4 h-4 ml-2\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M19 9l-7 7-7-7\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 714,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 713,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 705,\n                  columnNumber: 21\n                }, this), showExportDropdown && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 z-50\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"py-2\",\n                    children: [(result === null || result === void 0 ? void 0 : result.isFormatPreserved) && (result === null || result === void 0 ? void 0 : result.download_url) && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          handleDownloadDocx();\n                          setShowExportDropdown(false);\n                        },\n                        disabled: downloadingDocx,\n                        className: \"w-full text-left px-4 py-3 hover:bg-purple-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3\",\n                            children: downloadingDocx ? /*#__PURE__*/_jsxDEV(\"svg\", {\n                              className: \"animate-spin w-4 h-4 text-purple-600\",\n                              fill: \"none\",\n                              viewBox: \"0 0 24 24\",\n                              children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                                className: \"opacity-25\",\n                                cx: \"12\",\n                                cy: \"12\",\n                                r: \"10\",\n                                stroke: \"currentColor\",\n                                strokeWidth: \"4\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 735,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                                className: \"opacity-75\",\n                                fill: \"currentColor\",\n                                d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 736,\n                                columnNumber: 41\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 734,\n                              columnNumber: 39\n                            }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n                              className: \"w-4 h-4 text-purple-600\",\n                              fill: \"none\",\n                              stroke: \"currentColor\",\n                              viewBox: \"0 0 24 24\",\n                              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 740,\n                                columnNumber: 41\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 739,\n                              columnNumber: 39\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 732,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"font-medium text-purple-900\",\n                              children: downloadingDocx ? 'Downloading...' : 'Download Original Format'\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 745,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-xs text-purple-600\",\n                              children: \"DOCX with preserved formatting\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 748,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 744,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 731,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 723,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"border-t border-gray-100 my-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 752,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => {\n                        exportToPDF(getOptimizedText());\n                        setShowExportDropdown(false);\n                      },\n                      className: \"w-full text-left px-4 py-3 hover:bg-red-50 transition-colors\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center mr-3\",\n                          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                            className: \"w-4 h-4 text-red-600\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/_jsxDEV(\"path\", {\n                              strokeLinecap: \"round\",\n                              strokeLinejoin: \"round\",\n                              strokeWidth: 2,\n                              d: \"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 766,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 765,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 764,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"font-medium text-gray-900\",\n                            children: \"Export PDF\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 770,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-xs text-gray-500\",\n                            children: \"Download optimized resume as PDF\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 771,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 769,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 763,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 756,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => {\n                        exportToWord(getOptimizedText());\n                        setShowExportDropdown(false);\n                      },\n                      className: \"w-full text-left px-4 py-3 hover:bg-blue-50 transition-colors\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3\",\n                          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                            className: \"w-4 h-4 text-blue-600\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/_jsxDEV(\"path\", {\n                              strokeLinecap: \"round\",\n                              strokeLinejoin: \"round\",\n                              strokeWidth: 2,\n                              d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 786,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 785,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 784,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"font-medium text-gray-900\",\n                            children: \"Export Word\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 790,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-xs text-gray-500\",\n                            children: \"Download optimized resume as RTF\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 791,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 789,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 783,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 776,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"border-t border-gray-100 my-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 796,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => {\n                        printResume(getOptimizedText());\n                        setShowExportDropdown(false);\n                      },\n                      className: \"w-full text-left px-4 py-3 hover:bg-gray-50 transition-colors\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center mr-3\",\n                          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                            className: \"w-4 h-4 text-gray-600\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/_jsxDEV(\"path\", {\n                              strokeLinecap: \"round\",\n                              strokeLinejoin: \"round\",\n                              strokeWidth: 2,\n                              d: \"M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 808,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 807,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 806,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"font-medium text-gray-900\",\n                            children: \"Print Resume\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 812,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-xs text-gray-500\",\n                            children: \"Print optimized resume only\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 813,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 811,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 805,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 798,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 720,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 719,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 704,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 667,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"paper-preview rounded-lg p-10 mx-auto max-w-5xl min-h-[800px] relative\",\n              children: [viewMode === 'raw' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: (_lines => {\n                  const text = getOptimizedText() || 'Optimized resume content not available';\n                  const lines = text.split('\\n');\n                  const lastLine = (_lines = lines[lines.length - 1]) === null || _lines === void 0 ? void 0 : _lines.trim();\n                  const isRevisionNote = /^(This revised resume|This resume|优化说明|修改说明|优化总结|本次修改说明|优化建议)/i.test(lastLine);\n                  if (isRevisionNote) {\n                    const mainText = lines.slice(0, -1).join('\\n');\n                    return /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"pre\", {\n                        className: \"resume-content text-sm leading-relaxed text-gray-800 whitespace-pre-wrap\",\n                        children: mainText\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 837,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"my-6 border-t border-gray-200\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 840,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mb-3 leading-relaxed bg-yellow-50 border-l-8 border-yellow-400 px-6 py-4 italic text-gray-700 font-semibold shadow-sm\",\n                        style: {\n                          fontStyle: 'italic'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-yellow-600 font-bold mr-2\",\n                          children: \"Revision Note:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 842,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-gray-700\",\n                          children: lastLine\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 843,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 841,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true);\n                  } else {\n                    return /*#__PURE__*/_jsxDEV(\"pre\", {\n                      className: \"resume-content text-sm leading-relaxed text-gray-800 whitespace-pre-wrap\",\n                      children: text\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 849,\n                      columnNumber: 29\n                    }, this);\n                  }\n                })()\n              }, void 0, false), viewMode === 'enhanced' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"resume-content text-sm leading-relaxed text-gray-800\",\n                dangerouslySetInnerHTML: {\n                  __html: highlightAdditionsInOptimized(getOptimizedText())\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 858,\n                columnNumber: 21\n              }, this), viewMode === 'word' && (result === null || result === void 0 ? void 0 : result.optimized_docx_url) && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"word-preview-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"preview-tabs mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setWordPreviewMode('office'),\n                    className: `px-3 py-2 mr-2 rounded text-sm font-medium transition-colors ${wordPreviewMode === 'office' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`,\n                    children: \"Office Online\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 868,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setWordPreviewMode('google'),\n                    className: `px-3 py-2 mr-2 rounded text-sm font-medium transition-colors ${wordPreviewMode === 'google' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`,\n                    children: \"Google Docs\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 878,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setWordPreviewMode('download'),\n                    className: `px-3 py-2 rounded text-sm font-medium transition-colors ${wordPreviewMode === 'download' ? 'bg-green-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`,\n                    children: \"\\u4E0B\\u8F7D\\u67E5\\u770B\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 888,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 867,\n                  columnNumber: 23\n                }, this), wordPreviewMode === 'office' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"preview-frame-container\",\n                  children: /*#__PURE__*/_jsxDEV(\"iframe\", {\n                    src: `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(result.optimized_docx_url)}`,\n                    width: \"100%\",\n                    height: \"800\",\n                    frameBorder: \"0\",\n                    title: \"Office Online Preview\",\n                    style: {\n                      background: '#fff',\n                      borderRadius: '8px'\n                    },\n                    onError: () => setWordPreviewError('Office Online预览失败，请尝试其他方式')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 902,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 901,\n                  columnNumber: 25\n                }, this), wordPreviewMode === 'google' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"preview-frame-container\",\n                  children: /*#__PURE__*/_jsxDEV(\"iframe\", {\n                    src: `https://docs.google.com/gview?url=${encodeURIComponent(result.optimized_docx_url)}&embedded=true`,\n                    width: \"100%\",\n                    height: \"800\",\n                    frameBorder: \"0\",\n                    title: \"Google Docs Preview\",\n                    style: {\n                      background: '#fff',\n                      borderRadius: '8px'\n                    },\n                    onError: () => setWordPreviewError('Google Docs预览失败，请尝试其他方式')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 916,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 915,\n                  columnNumber: 25\n                }, this), wordPreviewMode === 'download' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"download-preview-container p-8 text-center bg-gray-50 rounded-lg\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-16 h-16 mx-auto text-blue-500 mb-4\",\n                      fill: \"none\",\n                      stroke: \"currentColor\",\n                      viewBox: \"0 0 24 24\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 932,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 931,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-semibold text-gray-800 mb-2\",\n                      children: \"Word\\u6587\\u6863\\u5DF2\\u51C6\\u5907\\u5C31\\u7EEA\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 934,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-600 mb-4\",\n                      children: [\"\\u7531\\u4E8E\\u6D4F\\u89C8\\u5668\\u9650\\u5236\\uFF0C\\u65E0\\u6CD5\\u76F4\\u63A5\\u9884\\u89C8Word\\u6587\\u6863\\u3002\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 937,\n                        columnNumber: 31\n                      }, this), \"\\u8BF7\\u70B9\\u51FB\\u4E0B\\u8F7D\\u6309\\u94AE\\u83B7\\u53D6\\u4F18\\u5316\\u540E\\u7684\\u6587\\u6863\\u3002\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 935,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 930,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                    href: result.optimized_docx_url,\n                    download: \"optimized_resume.docx\",\n                    className: \"inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-5 h-5 mr-2\",\n                      fill: \"none\",\n                      stroke: \"currentColor\",\n                      viewBox: \"0 0 24 24\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 947,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 946,\n                      columnNumber: 29\n                    }, this), \"\\u4E0B\\u8F7D\\u4F18\\u5316\\u540E\\u7684Word\\u6587\\u6863\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 941,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 929,\n                  columnNumber: 25\n                }, this), wordPreviewError && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-5 h-5 text-yellow-400 mr-2\",\n                      fill: \"none\",\n                      stroke: \"currentColor\",\n                      viewBox: \"0 0 24 24\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 958,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 957,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-yellow-800 font-medium\",\n                        children: \"\\u9884\\u89C8\\u63D0\\u793A\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 961,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-yellow-700 text-sm mt-1\",\n                        children: wordPreviewError\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 962,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 960,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 956,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 955,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 866,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 824,\n              columnNumber: 17\n            }, this), (result === null || result === void 0 ? void 0 : result.isFormatPreserved) && (result === null || result === void 0 ? void 0 : result.paragraph_changes) && result.paragraph_changes.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-8 bg-purple-50 border border-purple-200 rounded-lg p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"text-lg font-semibold text-purple-900 mb-4 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5 mr-2\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 976,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 975,\n                  columnNumber: 23\n                }, this), \"Paragraph-Level Changes Applied\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 974,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: (result.paragraph_changes || []).map((change, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-white rounded-lg p-4 border border-purple-100\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-medium text-purple-700 mb-2\",\n                    children: [\"Change #\", index + 1, \": \", change.change_reason]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 983,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-xs font-medium text-red-600 mb-1\",\n                        children: \"Original:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 988,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm text-gray-700 bg-red-50 p-2 rounded border\",\n                        children: change.original_text\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 989,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 987,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-xs font-medium text-green-600 mb-1\",\n                        children: \"Optimized:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 994,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm text-gray-700 bg-green-50 p-2 rounded border\",\n                        children: change.optimized_text\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 995,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 993,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 986,\n                    columnNumber: 27\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 982,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 980,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 973,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 666,\n            columnNumber: 15\n          }, this), activeTab === 'analysis' && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-2xl font-bold text-gray-900 mb-6\",\n              children: \"ATS Compatibility Analysis\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1011,\n              columnNumber: 17\n            }, this), (result === null || result === void 0 ? void 0 : (_result$job_match_ana6 = result.job_match_analysis) === null || _result$job_match_ana6 === void 0 ? void 0 : (_result$job_match_ana7 = _result$job_match_ana6.optimized_match_score) === null || _result$job_match_ana7 === void 0 ? void 0 : _result$job_match_ana7.breakdown) && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"text-md font-semibold text-gray-800 mb-3\",\n                children: \"Detailed Match Breakdown\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1015,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                children: Object.entries(result.job_match_analysis.optimized_match_score.breakdown).map(([key, data]) => {\n                  const matchedLimit = 5;\n                  const missingLimit = 3;\n                  const matchedExpanded = expandedMatched[key] || false;\n                  const missingExpanded = expandedMissing[key] || false;\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-gray-50 rounded-lg p-4 border\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between items-start mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                          className: \"font-semibold text-gray-700 capitalize\",\n                          children: key.replace('_', ' ')\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1026,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-xs text-gray-500 font-medium\",\n                          children: [\"Weight: \", data.weight]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1029,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1025,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `text-lg font-bold ${data.score >= 70 ? 'text-green-600' : data.score >= 50 ? 'text-yellow-600' : 'text-red-600'}`,\n                        children: [data.score, \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1031,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1024,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-full bg-gray-200 rounded-full h-2 mb-2\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `h-2 rounded-full transition-all duration-500 ${data.score >= 70 ? 'bg-green-500' : data.score >= 50 ? 'bg-yellow-500' : 'bg-red-500'}`,\n                        style: {\n                          width: `${data.score}%`\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1035,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1034,\n                      columnNumber: 29\n                    }, this), data.matched && data.total && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-600\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mb-1\",\n                        children: /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"font-medium\",\n                          children: [\"Matched (\", data.matched.length, \"/\", data.total.length, \"):\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1047,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1046,\n                        columnNumber: 33\n                      }, this), data.matched.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex flex-wrap gap-1 mb-2\",\n                        children: [(matchedExpanded ? data.matched : data.matched.slice(0, matchedLimit)).map((item, idx) => /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"bg-green-100 text-green-700 px-2 py-1 rounded text-xs\",\n                          children: item\n                        }, idx, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1052,\n                          columnNumber: 39\n                        }, this)), data.matched.length > matchedLimit && /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-gray-500 cursor-pointer underline\",\n                          onClick: () => toggleExpand(key, 'matched'),\n                          children: matchedExpanded ? '收起' : `+${data.matched.length - matchedLimit} more`\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1055,\n                          columnNumber: 39\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1050,\n                        columnNumber: 35\n                      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-gray-500 mb-2\",\n                        children: \"No matches found\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1064,\n                        columnNumber: 35\n                      }, this), data.total.length > data.matched.length && /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"font-medium text-red-600\",\n                          children: \"Missing:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1068,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-wrap gap-1 mt-1\",\n                          children: [(missingExpanded ? data.total.filter(item => !data.matched.includes(item)) : data.total.filter(item => !data.matched.includes(item)).slice(0, missingLimit)).map((item, idx) => /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"bg-red-100 text-red-700 px-2 py-1 rounded text-xs mr-1\",\n                            children: item\n                          }, idx, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1071,\n                            columnNumber: 41\n                          }, this)), data.total.length - data.matched.length > missingLimit && /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-gray-500 cursor-pointer underline\",\n                            onClick: () => toggleExpand(key, 'missing'),\n                            children: missingExpanded ? '收起' : `+${data.total.length - data.matched.length - missingLimit} more`\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1074,\n                            columnNumber: 41\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1069,\n                          columnNumber: 37\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1067,\n                        columnNumber: 35\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1045,\n                      columnNumber: 31\n                    }, this)]\n                  }, key, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1023,\n                    columnNumber: 27\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1016,\n                columnNumber: 21\n              }, this), (() => {\n                const suggestions = Object.entries(result.job_match_analysis.optimized_match_score.breakdown).filter(([key, data]) => data.score < 60).map(([key, data]) => {\n                  let tip = '';\n                  if (key.toLowerCase().includes('action')) tip = 'Consider adding more action verbs to demonstrate impact.';else if (key.toLowerCase().includes('skill')) tip = 'Add more relevant technical skills from the job description.';else if (key.toLowerCase().includes('industry')) tip = 'Include more industry-specific terminology.';else if (key.toLowerCase().includes('experience')) tip = 'Enhance experience descriptions with more details.';else if (key.toLowerCase().includes('education')) tip = 'Complete education information if applicable.';else tip = 'Consider adding relevant content to improve match score.';\n                  return `[${key.replace('_', ' ')}] ${tip}`;\n                });\n                if (suggestions.length === 0) return null;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-blue-50 border border-blue-200 rounded-lg p-6 mt-4 mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center mb-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"text-lg font-bold text-blue-700 mr-4\",\n                      children: \"Optimization Suggestions\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1111,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"ml-auto px-4 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm\",\n                      onClick: () => setShowAllSuggestions(v => !v),\n                      children: showAllSuggestions ? 'Collapse All' : 'Expand All Suggestions'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1112,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1110,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                    className: \"list-disc pl-6 text-blue-800\",\n                    children: [(showAllSuggestions ? suggestions : suggestions.slice(0, 2)).map((s, i) => /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: s\n                    }, i, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1119,\n                      columnNumber: 31\n                    }, this)), !showAllSuggestions && suggestions.length > 2 && /*#__PURE__*/_jsxDEV(\"li\", {\n                      className: \"text-gray-500\",\n                      children: \"...click button above to see more suggestions\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1122,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1117,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1109,\n                  columnNumber: 25\n                }, this);\n              })(), false && (() => {\n                const radarData = Object.entries(result.job_match_analysis.optimized_match_score.breakdown).map(([key, data]) => ({\n                  name: key.replace('_', ' '),\n                  value: data.score\n                }));\n                if (radarData.length < 3) return null; // 至少3项才显示雷达图\n                const indicator = radarData.map(item => ({\n                  name: item.name,\n                  max: 100\n                }));\n                const option = {\n                  tooltip: {},\n                  radar: {\n                    indicator,\n                    radius: 80\n                  },\n                  series: [{\n                    type: 'radar',\n                    data: [{\n                      value: radarData.map(d => d.value),\n                      name: '覆盖度'\n                    }],\n                    areaStyle: {\n                      opacity: 0.2\n                    },\n                    lineStyle: {\n                      width: 2\n                    }\n                  }]\n                };\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-white border border-gray-200 rounded-lg p-6 mt-4 mb-4 flex flex-col items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-lg font-bold text-gray-800 mb-2\",\n                    children: \"\\u7B80\\u5386\\u5185\\u5BB9\\u8986\\u76D6\\u5EA6\\u96F7\\u8FBE\\u56FE\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1152,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: 350,\n                      height: 300\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center justify-center h-full text-gray-500\",\n                      children: \"\\u96F7\\u8FBE\\u56FE\\u529F\\u80FD\\u6682\\u65F6\\u7981\\u7528\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1155,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1153,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1151,\n                  columnNumber: 25\n                }, this);\n              })(), (() => {\n                const optimizedText = getOptimizedText();\n                const issues = [];\n                if (/[★☆▪▫◦]/.test(optimizedText)) issues.push('Special characters detected, consider removing them.');\n                if (!/(email|邮箱|@)/i.test(optimizedText)) issues.push('Email information not detected.');\n                if (!/(phone|电话|\\d{11,})/i.test(optimizedText)) issues.push('Phone number not detected.');\n                if (optimizedText.length < 500) issues.push('Resume content is too short, consider adding more details.');\n                if (optimizedText.length > 4000) issues.push('Resume content is too long, consider condensing.');\n                if (issues.length === 0) return null;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-6 mt-4 mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-lg font-bold text-yellow-700 mb-2 flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-5 h-5 mr-2 text-yellow-500\",\n                      fill: \"none\",\n                      stroke: \"currentColor\",\n                      viewBox: \"0 0 24 24\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1176,\n                        columnNumber: 129\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1176,\n                      columnNumber: 29\n                    }, this), \"ATS Common Issues Detection\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1175,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                    className: \"list-disc pl-6 text-yellow-800\",\n                    children: issues.map((issue, idx) => /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: issue\n                    }, idx, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1180,\n                      columnNumber: 57\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1179,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1174,\n                  columnNumber: 25\n                }, this);\n              })()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1014,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1010,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 663,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 537,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 536,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 371,\n    columnNumber: 5\n  }, this);\n};\n_s(ResultDisplay, \"VbzGSOu5w2TbhSvYqIPU6lebPNM=\");\n_c = ResultDisplay;\nexport default ResultDisplay;\nvar _c;\n$RefreshReg$(_c, \"ResultDisplay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "exportToPDF", "exportToWord", "exportToText", "printResume", "downloadOptimizedDocx", "DocumentPreview", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ResultDisplay", "result", "originalFile", "jobDescription", "_s", "_result$job_match_ana", "_result$job_match_ana2", "_result$job_match_ana3", "_result$job_match_ana4", "_result$job_match_ana5", "_result$job_match_ana6", "_result$job_match_ana7", "showComparison", "setShowComparison", "activeTab", "setActiveTab", "showFormattedOptimized", "setShowFormattedOptimized", "downloadingDocx", "setDownloadingDocx", "showExportDropdown", "setShowExportDropdown", "viewMode", "setViewMode", "wordPreviewMode", "setWordPreviewMode", "wordPreviewError", "setWordPreviewError", "expandedMatched", "setExpandedMatched", "expandedMissing", "setExpandedMissing", "showAllSuggestions", "setShowAllSuggestions", "handleClickOutside", "event", "target", "closest", "document", "addEventListener", "removeEventListener", "formatOptimizedResume", "text", "replace", "generateATSScore", "optimizedText", "originalText", "safeOptimizedText", "safeOriginalText", "factors", "hasQuantifiedAchievements", "test", "hasActionVerbs", "toLowerCase", "hasStandardSections", "hasContactInfo", "appropriateLength", "length", "noSpecialCharacters", "score", "Object", "values", "filter", "Boolean", "maxScore", "keys", "percentage", "Math", "round", "improvements", "getOptimizedText", "_result$optimized_res", "_result$optimized_res2", "console", "log", "hasOptimizedResume", "optimized_resume", "hasOptimizedText", "optimized_text", "hasOptimizedResumeJson", "optimized_resume_json", "optimizedResumeLength", "optimizedResumeJsonLength", "optimizedResumeType", "optimizedResumeJsonType", "trim", "substring", "Array", "isArray", "sections", "map", "section", "index", "section_name", "hasOptimized", "optimized", "<PERSON><PERSON><PERSON><PERSON>", "content", "optimizedLength", "contentLength", "textContent", "sectionName", "join", "original_resume", "getOriginalText", "original_text", "detectTextDifferences", "originalLines", "split", "line", "optimizedLines", "usedOptimizedIndices", "Set", "for<PERSON>ach", "originalLine", "originalIndex", "trimmedOriginal", "bestMatch", "bestSimilarity", "bestOptimizedIndex", "optimizedLine", "optimizedIdx", "has", "trimmedOptimized", "similarity", "calculateSimilarity", "add", "push", "type", "original", "str1", "str2", "words1", "words2", "commonWords", "word", "includes", "totalWords", "max", "highlightAdditionsInOptimized", "differences", "usedOriginalIndices", "diff", "idx", "optIdx", "findIndex", "_diff$optimized", "oriIdx", "_diff$original", "escapedLine", "isRevisionNote", "revisionContent", "find", "d", "_d$optimized", "diff2", "_d$optimized2", "atsScore", "getScoreColor", "getScoreBg", "getHeaderBg", "getIconBg", "getProgressBarColor", "getBorderColor", "handleDownloadDocx", "formData", "FormData", "append", "response", "fetch", "method", "body", "ok", "error", "json", "Error", "contentDisposition", "headers", "get", "filename", "blob", "url", "window", "URL", "createObjectURL", "a", "createElement", "href", "download", "append<PERSON><PERSON><PERSON>", "click", "revokeObjectURL", "<PERSON><PERSON><PERSON><PERSON>", "alert", "message", "toggleExpand", "key", "prev", "children", "className", "fill", "viewBox", "fillRule", "clipRule", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "style", "width", "job_match_analysis", "original_match_score", "overall_match_score", "optimized_match_score", "match_improvement", "breakdown", "entries", "data", "onClick", "original<PERSON>ength", "toLocaleString", "displayRate", "colorClass", "toFixed", "disabled", "optimized_docx_url", "isFormatPreserved", "download_url", "cx", "cy", "r", "_lines", "lines", "lastLine", "mainText", "slice", "fontStyle", "dangerouslySetInnerHTML", "__html", "src", "encodeURIComponent", "height", "frameBorder", "title", "background", "borderRadius", "onError", "paragraph_changes", "change", "change_reason", "matchedLimit", "missingLimit", "matchedExpanded", "missingExpanded", "weight", "matched", "total", "item", "suggestions", "tip", "v", "s", "i", "radarData", "name", "value", "indicator", "option", "tooltip", "radar", "radius", "series", "areaStyle", "opacity", "lineStyle", "issues", "issue", "_c", "$RefreshReg$"], "sources": ["E:/AI/SmartCV/frontend/src/components/ResultDisplay.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { exportToPDF, exportToWord, exportToText, printResume } from '../utils/exportUtils';\nimport { downloadOptimizedDocx } from '../services/api';\nimport DocumentPreview from './DocumentPreview';\n// import ReactECharts from 'echarts-for-react';\n\nconst ResultDisplay = ({ result, originalFile, jobDescription }) => {\n  const [showComparison, setShowComparison] = useState(false);\n  const [activeTab, setActiveTab] = useState('optimized'); // 'optimized', 'original', 'comparison'\n  const [showFormattedOptimized, setShowFormattedOptimized] = useState(false); // Toggle for formatted view in comparison\n  const [downloadingDocx, setDownloadingDocx] = useState(false);\n  const [showExportDropdown, setShowExportDropdown] = useState(false);\n  const [viewMode, setViewMode] = useState('raw'); // 'raw', 'enhanced', 'word'\n  const [wordPreviewMode, setWordPreviewMode] = useState('office'); // 'office', 'google', 'download'\n  const [wordPreviewError, setWordPreviewError] = useState(null);\n  // 新增：用于控制每个分类的展开/收起状态\n  const [expandedMatched, setExpandedMatched] = useState({});\n  const [expandedMissing, setExpandedMissing] = useState({});\n  const [showAllSuggestions, setShowAllSuggestions] = useState(false);\n\n  // 点击外部区域关闭下拉菜单\n  useEffect(() => {\n    const handleClickOutside = (event) => {\n      if (showExportDropdown && !event.target.closest('.export-dropdown')) {\n        setShowExportDropdown(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, [showExportDropdown]);\n\n  const formatOptimizedResume = (text) => {\n    if (!text) return '';\n    \n    // Process Markdown-formatted resume\n    return text\n      .replace(/^#\\s+(.+)$/gm, '<h1 class=\"text-2xl font-bold text-gray-900 mb-4 border-b-2 border-orange-500 pb-2\">$1</h1>')\n      .replace(/^##\\s+(.+)$/gm, '<h2 class=\"text-xl font-semibold text-gray-800 mb-3 mt-6\">$1</h2>')\n      .replace(/^###\\s+(.+)$/gm, '<h3 class=\"text-lg font-medium text-gray-700 mb-2 mt-4\">$3</h3>')\n      .replace(/^\\*\\*(.+?)\\*\\*:?(.*)$/gm, '<div class=\"mb-2\"><span class=\"font-semibold text-gray-900\">$1</span>$2</div>')\n      .replace(/^\\* (.+)$/gm, '<li class=\"ml-4 mb-1 text-gray-700\">$1</li>')\n      .replace(/^- (.+)$/gm, '<li class=\"ml-4 mb-1 text-gray-700\">$1</li>')\n      .replace(/\\*\\*(.+?)\\*\\*/g, '<span class=\"font-semibold\">$1</span>')\n      .replace(/\\*(.+?)\\*/g, '<span class=\"italic\">$1</span>')\n      .replace(/\\n\\n/g, '</p><p class=\"mb-3\">')\n      .replace(/\\n/g, '<br>')\n      .replace(/^(?!<[h|l|d])(.+)$/gm, '<p class=\"mb-3 text-gray-700\">$1</p>');\n  };\n\n  const generateATSScore = (optimizedText, originalText) => {\n    // 确保输入文本不为空，提供默认值\n    const safeOptimizedText = optimizedText || '';\n    const safeOriginalText = originalText || '';\n    \n    // Simple ATS scoring algorithm\n    const factors = {\n      hasQuantifiedAchievements: /\\d+(%|k|million|billion|\\$|year|month)/.test(safeOptimizedText),\n      hasActionVerbs: /(developed|implemented|managed|led|created|optimized|improved)/.test(safeOptimizedText.toLowerCase()),\n      hasStandardSections: /(experience|education|skills)/.test(safeOptimizedText.toLowerCase()),\n      hasContactInfo: /(email|phone|linkedin)/.test(safeOptimizedText.toLowerCase()),\n      appropriateLength: safeOptimizedText.length > 500 && safeOptimizedText.length < 4000,\n      noSpecialCharacters: !/[★☆▪▫◦]/.test(safeOptimizedText)\n    };\n\n    const score = Object.values(factors).filter(Boolean).length;\n    const maxScore = Object.keys(factors).length;\n    const percentage = Math.round((score / maxScore) * 100);\n\n    return {\n      score: percentage,\n      factors,\n      improvements: maxScore - score\n    };\n  };\n\n  // 兼容处理不同的数据结构\n  const getOptimizedText = () => {\n    // 详细调试信息\n    console.log('=== RESULT ANALYSIS ===');\n    console.log('Complete result object:', result);\n    console.log('Result data structure:', {\n      hasOptimizedResume: !!result?.optimized_resume,\n      hasOptimizedText: !!result?.optimized_text,\n      hasOptimizedResumeJson: !!result?.optimized_resume_json,\n      optimizedResumeLength: result?.optimized_resume?.length || 0,\n      optimizedResumeJsonLength: result?.optimized_resume_json?.length || 0,\n      optimizedResumeType: typeof result?.optimized_resume,\n      optimizedResumeJsonType: typeof result?.optimized_resume_json\n    });\n\n    // 如果有optimized_resume_json，显示其内容\n    if (result?.optimized_resume_json) {\n      console.log('optimized_resume_json content:', result.optimized_resume_json);\n    }\n\n    // 首先尝试获取直接的文本内容\n    if (result?.optimized_resume && result.optimized_resume.trim()) {\n      console.log('✅ Using optimized_resume field, length:', result.optimized_resume.length);\n      console.log('optimized_resume preview:', result.optimized_resume.substring(0, 200));\n      return result.optimized_resume;\n    }\n    if (result?.optimized_text && result.optimized_text.trim()) {\n      console.log('✅ Using optimized_text field, length:', result.optimized_text.length);\n      return result.optimized_text;\n    }\n\n    // 如果没有直接文本，尝试从结构化数据重建\n    if (result?.optimized_resume_json && Array.isArray(result.optimized_resume_json) && result.optimized_resume_json.length > 0) {\n      console.log('🔄 Rebuilding from JSON structure, sections:', result.optimized_resume_json.length);\n      const sections = result.optimized_resume_json;\n\n      console.log('JSON sections details:', sections.map((section, index) => ({\n        index,\n        section: section.section || section.section_name || 'unknown',\n        hasOptimized: !!section.optimized,\n        hasContent: !!section.content,\n        optimizedLength: (section.optimized || '').length,\n        contentLength: (section.content || '').length\n      })));\n\n      const textContent = sections.map(section => {\n        // 处理不同的JSON结构格式\n        const sectionName = section.section_name || section.section || '';\n        const content = section.content || section.optimized || '';\n        return sectionName ? `${sectionName}\\n${content}` : content;\n      }).filter(text => text.trim()).join('\\n\\n');\n\n      console.log('✅ Rebuilt text length:', textContent.length);\n      console.log('Rebuilt text preview:', textContent.substring(0, 200));\n      return textContent || '';\n    }\n\n    console.log('❌ No optimized text found, returning empty string');\n\n    // 最后的备用方案：如果有原始简历，至少显示一些内容\n    if (result?.original_resume) {\n      console.log('🔄 Using original resume as fallback');\n      return `# Original Resume (Optimization Failed)\\n\\n${result.original_resume}`;\n    }\n\n    return '';\n  };\n\n  const getOriginalText = () => {\n    return result?.original_resume || result?.original_text || '';\n  };\n\n  // 更精确的文本差异检测函数\n  const detectTextDifferences = (originalText, optimizedText) => {\n    const originalLines = originalText.split('\\n').filter(line => line.trim());\n    const optimizedLines = optimizedText.split('\\n').filter(line => line.trim());\n    \n    const result = [];\n    const usedOptimizedIndices = new Set();\n    \n    originalLines.forEach((originalLine, originalIndex) => {\n      const trimmedOriginal = originalLine.trim();\n      if (!trimmedOriginal) return;\n      \n      // 寻找最佳匹配\n      let bestMatch = null;\n      let bestSimilarity = 0;\n      let bestOptimizedIndex = -1;\n      \n      optimizedLines.forEach((optimizedLine, optimizedIdx) => {\n        if (usedOptimizedIndices.has(optimizedIdx)) return;\n        \n        const trimmedOptimized = optimizedLine.trim();\n        const similarity = calculateSimilarity(trimmedOriginal, trimmedOptimized);\n        \n        if (similarity > bestSimilarity && similarity > 0.3) {\n          bestMatch = optimizedLine;\n          bestSimilarity = similarity;\n          bestOptimizedIndex = optimizedIdx;\n        }\n      });\n      \n      if (bestMatch && bestSimilarity > 0.7) {\n        // 高相似度，可能只是微调\n        usedOptimizedIndices.add(bestOptimizedIndex);\n        result.push({\n          type: 'modified',\n          original: originalLine,\n          optimized: bestMatch,\n          similarity: bestSimilarity\n        });\n      } else if (bestMatch && bestSimilarity > 0.3) {\n        // 中等相似度，显著修改\n        usedOptimizedIndices.add(bestOptimizedIndex);\n        result.push({\n          type: 'significantly_modified',\n          original: originalLine,\n          optimized: bestMatch,\n          similarity: bestSimilarity\n        });\n      } else {\n        // 没有找到匹配，可能被删除或完全重写\n        result.push({\n          type: 'deleted',\n          original: originalLine,\n          optimized: null\n        });\n      }\n    });\n    \n    return result;\n  };\n  \n  // 计算两个字符串的相似度\n  const calculateSimilarity = (str1, str2) => {\n    if (str1 === str2) return 1;\n    if (!str1 || !str2) return 0;\n    \n    const words1 = str1.toLowerCase().split(/\\s+/);\n    const words2 = str2.toLowerCase().split(/\\s+/);\n    \n    const commonWords = words1.filter(word => words2.includes(word));\n    const totalWords = Math.max(words1.length, words2.length);\n    \n    return commonWords.length / totalWords;\n  };\n\n  // 在优化文本中高亮新增、修改、删除内容\n  const highlightAdditionsInOptimized = (optimizedText) => {\n    const originalText = getOriginalText();\n    if (!originalText || !optimizedText || originalText === optimizedText) {\n      // 没有原文或内容一致，直接返回普通格式\n      return formatOptimizedResume(optimizedText);\n    }\n    // 差异检测\n    const differences = detectTextDifferences(originalText, optimizedText);\n    const optimizedLines = optimizedText.split('\\n').filter(line => line.trim());\n    const originalLines = originalText.split('\\n').filter(line => line.trim());\n    const usedOriginalIndices = new Set();\n    const usedOptimizedIndices = new Set();\n    // 标记已匹配的行\n    differences.forEach((diff, idx) => {\n      if (diff.type !== 'deleted') {\n        const optIdx = optimizedLines.findIndex(line => line.trim() === diff.optimized?.trim());\n        if (optIdx !== -1) usedOptimizedIndices.add(optIdx);\n      }\n      if (diff.type !== 'added') {\n        const oriIdx = originalLines.findIndex(line => line.trim() === diff.original?.trim());\n        if (oriIdx !== -1) usedOriginalIndices.add(oriIdx);\n      }\n    });\n    // 渲染优化内容\n    return optimizedLines.map((line, idx) => {\n      const escapedLine = line.replace(/</g, '&lt;').replace(/>/g, '&gt;');\n      // 检查是否为修改说明（如以This revised resume/This resume等开头，且为最后一段）\n      const isRevisionNote = (idx === optimizedLines.length - 1) && /^(This revised resume|This resume|优化说明|修改说明|优化总结|本次修改说明|优化建议)/i.test(line.trim());\n      if (isRevisionNote) {\n        // 检查是否为中文优化说明，若是则替换为英文模板\n        let revisionContent = line.trim();\n        if (/^(本次修改说明|优化建议)/.test(revisionContent)) {\n          revisionContent = `This revision note summarizes the main changes and optimization suggestions:\\n\\n- Integrated keywords from the target job, such as \\\"data analysis\\\", \\\"project management\\\", \\\"big data\\\", etc.\\n- Reorganized the order of work experience to highlight the most relevant experiences.\\n- Used action verbs to describe responsibilities and achievements.\\n- Formatted the resume to be ATS-friendly, removed tables and special characters.\\n\\nOptimization Suggestions:\\n- Quantified achievements for the \\\"Administrative Assistant\\\" role.\\n- Added specific descriptions of data collection and analysis for the \\\"Intern\\\" position.`;\n        }\n        return `<p class=\\\"mb-3 leading-relaxed bg-gray-50 border-l-4 border-gray-400 px-4 py-2 italic text-gray-700 font-semibold\\\" style=\\\"font-style:italic;\\\"><span class=\\\"text-gray-500 font-bold mr-2\\\">Revision Note:</span>${revisionContent.replace(/\\n/g, '<br>')}</p>`;\n      }\n      // 新增内容\n      if (!usedOriginalIndices.has(idx) && line.trim().length > 0) {\n        return `<p class=\\\"mb-3 leading-relaxed\\\"><span class=\\\"inline-block bg-green-50 text-green-700 font-medium px-2 py-1 rounded border border-green-200 mr-1\\\" title=\\\"新增内容\\\">✓ ${escapedLine}</span></p>`;\n      }\n      // 修改内容\n      const diff = differences.find(d => d.optimized?.trim() === line.trim() && d.type === 'significantly_modified');\n      if (diff) {\n        return `<p class=\\\"mb-3 leading-relaxed\\\"><span class=\\\"inline-block bg-orange-50 text-orange-700 font-medium px-2 py-1 rounded border border-orange-200 mr-1\\\" title=\\\"修改内容\\\">${escapedLine}</span></p>`;\n      }\n      // 轻微修改\n      const diff2 = differences.find(d => d.optimized?.trim() === line.trim() && d.type === 'modified');\n      if (diff2) {\n        return `<p class=\\\"mb-3 leading-relaxed\\\"><span class=\\\"inline-block bg-yellow-50 text-yellow-700 font-medium px-2 py-1 rounded border border-yellow-200 mr-1\\\" title=\\\"轻微修改\\\">${escapedLine}</span></p>`;\n      }\n      // 普通内容\n      return `<p class=\\\"mb-3 text-gray-700 leading-relaxed\\\">${escapedLine}</p>`;\n    }).join('')\n    // 删除内容在优化内容中不显示，但可选在末尾加提示\n    // differences.filter(d => d.type === 'deleted').map(...)\n  };\n\n  const atsScore = generateATSScore(getOptimizedText(), getOriginalText());\n\n  const getScoreColor = (score) => {\n    if (score >= 80) return 'text-green-600';\n    if (score >= 60) return 'text-orange-600';\n    return 'text-red-600';\n  };\n\n  const getScoreBg = (score) => {\n    if (score >= 80) return 'bg-green-100';\n    if (score >= 60) return 'bg-orange-100';\n    return 'bg-red-100';\n  };\n\n  const getHeaderBg = (score) => {\n    if (score >= 80) return 'bg-green-50 border-green-200';\n    if (score >= 60) return 'bg-orange-50 border-orange-200';\n    return 'bg-red-50 border-red-200';\n  };\n\n  const getIconBg = (score) => {\n    if (score >= 80) return 'bg-green-500';\n    if (score >= 60) return 'bg-orange-500';\n    return 'bg-red-500';\n  };\n\n  const getProgressBarColor = (score) => {\n    if (score >= 80) return 'bg-green-500';\n    if (score >= 60) return 'bg-orange-500';\n    return 'bg-red-500';\n  };\n\n  const getBorderColor = (score) => {\n    if (score >= 80) return 'border-green-200';\n    if (score >= 60) return 'border-orange-200';\n    return 'border-red-200';\n  };\n\n  // 处理DOCX下载\n  const handleDownloadDocx = async () => {\n    try {\n      const formData = new FormData();\n      formData.append('file', originalFile);\n      formData.append('job_description', jobDescription);\n      \n      const response = await fetch('/api/optimize-resume-docx', {\n        method: 'POST',\n        body: formData\n      });\n      \n      if (!response.ok) {\n        const error = await response.json();\n        throw new Error(error.error || '下载失败');\n      }\n      \n      // 获取文件名\n      const contentDisposition = response.headers.get('content-disposition');\n      const filename = contentDisposition\n        ? contentDisposition.split('filename=')[1].replace(/\"/g, '')\n        : 'optimized_resume.docx';\n      \n      // 下载文件\n      const blob = await response.blob();\n      const url = window.URL.createObjectURL(blob);\n      const a = document.createElement('a');\n      a.href = url;\n      a.download = filename;\n      document.body.appendChild(a);\n      a.click();\n      window.URL.revokeObjectURL(url);\n      document.body.removeChild(a);\n      \n    } catch (error) {\n      console.error('下载DOCX失败:', error);\n      alert('下载失败: ' + error.message);\n    }\n  };\n\n  const toggleExpand = (key, type) => {\n    if (type === 'matched') {\n      setExpandedMatched(prev => ({ ...prev, [key]: !prev[key] }));\n    } else {\n      setExpandedMissing(prev => ({ ...prev, [key]: !prev[key] }));\n    }\n  };\n\n  return (\n    <div>\n      {/* 第一个卡片：Resume Optimization Complete + Job Match Analysis */}\n      <div className=\"mb-6\">\n        <div className=\"bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden\">\n          {/* Header with ATS Score */}\n          <div className={`${getHeaderBg(atsScore.score)} px-8 py-8 border-b relative overflow-hidden`}>\n            {/* 背景装饰 */}\n            <div className=\"absolute top-0 right-0 transform translate-x-4 -translate-y-4 opacity-10\">\n              <svg className=\"w-32 h-32\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n              </svg>\n            </div>\n            \n            <div className=\"relative\">\n              <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between\">\n                <div className=\"mb-6 lg:mb-0\">\n                  <div className=\"flex items-center mb-3\">\n                    <div className={`${getIconBg(atsScore.score)} text-white rounded-full w-12 h-12 flex items-center justify-center mr-4 shadow-lg`}>\n                      <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                      </svg>\n                    </div>\n                    <div>\n                      <h2 className=\"text-3xl font-bold text-gray-900 mb-1\">\n                        Resume Optimization Complete\n                      </h2>\n                      <div className=\"flex items-center text-sm text-gray-600\">\n                        <svg className=\"w-4 h-4 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                        </svg>\n                        AI has optimized your resume according to job requirements, improving ATS pass rate\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                \n                <div className=\"flex items-center space-x-6\">\n                  {/* ATS 分数卡片 */}\n                  <div className={`bg-white border-2 ${getBorderColor(atsScore.score)} rounded-2xl px-8 py-6 text-center min-w-[160px] shadow-lg transform hover:scale-105 transition-transform`}>\n                    <div className={`text-4xl font-extrabold mb-2 ${getScoreColor(atsScore.score)}`}>\n                      {atsScore.score}%\n                    </div>\n                    <div className=\"text-sm font-semibold text-gray-600 mb-2\">ATS Match</div>\n                    <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                      <div \n                        className={`h-2 rounded-full transition-all duration-1000 ${getProgressBarColor(atsScore.score)}`}\n                        style={{ width: `${atsScore.score}%` }}\n                      ></div>\n                    </div>\n                  </div>\n                  \n                  {/* 状态指示器 */}\n                  <div className=\"text-center\">\n                    <div className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium ${\n                      atsScore.score >= 80 ? 'bg-green-100 text-green-800' : \n                      atsScore.score >= 60 ? 'bg-yellow-100 text-yellow-800' : \n                      'bg-red-100 text-red-800'\n                    }`}>\n                      {atsScore.score >= 80 ? 'Excellent' : atsScore.score >= 60 ? 'Good' : 'Needs Improvement'}\n                    </div>\n                    <div className=\"text-xs text-gray-500 mt-1\">\n                      {atsScore.score >= 80 ? 'Very likely to pass ATS screening' : \n                       atsScore.score >= 60 ? 'High chance to pass ATS screening' : \n                       'Recommend further optimization'}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Job Match Analysis - 在同一个卡片内 */}\n          {result?.job_match_analysis && (\n            <div className=\"p-8\">\n              {/* 简约标题 */}\n              <div className=\"text-center mb-8\">\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">Job Match Analysis</h3>\n                <p className=\"text-gray-600\">AI analysis shows significant improvement in resume-job alignment</p>\n              </div>\n              \n              {/* 核心指标卡片 - 简约风格 */}\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\n                {/* 原始匹配度 */}\n                <div className=\"bg-gray-50 rounded-lg p-6 text-center\">\n                  <div className=\"text-2xl font-bold text-gray-500 mb-2\">\n                    {result.job_match_analysis.original_match_score?.overall_match_score || 0}%\n                  </div>\n                  <div className=\"text-sm text-gray-600 mb-3\">Original Match</div>\n                  <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                    <div \n                      className=\"bg-gray-400 h-2 rounded-full transition-all duration-1000\"\n                      style={{ width: `${result.job_match_analysis.original_match_score?.overall_match_score || 0}%` }}\n                    ></div>\n                  </div>\n                </div>\n                \n                {/* 优化后匹配度 */}\n                <div className=\"bg-green-50 rounded-lg p-6 border-2 border-green-200 text-center\">\n                  <div className=\"flex items-center justify-center mb-2\">\n                    <svg className=\"w-5 h-5 text-green-600 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                    </svg>\n                    <div className=\"text-2xl font-bold text-green-600\">\n                      {result.job_match_analysis.optimized_match_score?.overall_match_score || 0}%\n                    </div>\n                  </div>\n                  <div className=\"text-sm text-green-600 font-medium mb-3\">Optimized Match</div>\n                  <div className=\"w-full bg-green-100 rounded-full h-2\">\n                    <div \n                      className=\"bg-green-500 h-2 rounded-full transition-all duration-1000\"\n                      style={{ width: `${result.job_match_analysis.optimized_match_score?.overall_match_score || 0}%` }}\n                    ></div>\n                  </div>\n                </div>\n                \n                {/* 提升幅度 */}\n                <div className=\"bg-blue-50 rounded-lg p-6 text-center\">\n                  <div className={`text-2xl font-bold mb-2 ${result.job_match_analysis.match_improvement >= 0 ? 'text-blue-600' : 'text-orange-600'}`}>\n                    {result.job_match_analysis.match_improvement >= 0 ? '+' : ''}{result.job_match_analysis.match_improvement}%\n                  </div>\n                  <div className=\"text-sm text-gray-600 mb-3\">Improvement</div>\n                  <div className=\"flex items-center justify-center text-xs text-gray-500\">\n                    <svg className=\"w-3 h-3 mr-1 text-blue-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\" />\n                    </svg>\n                    Significant Growth\n                  </div>\n                </div>\n              </div>\n              \n              {/* 技能匹配详情 - 简约展示 */}\n              {result.job_match_analysis.optimized_match_score?.breakdown && (\n                <div className=\"bg-gray-50 rounded-lg p-6\">\n                  <h4 className=\"text-md font-medium text-gray-800 mb-4\">Skill Match Breakdown</h4>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n                    {Object.entries(result.job_match_analysis.optimized_match_score.breakdown).map(([key, data]) => (\n                      <div key={key} className=\"flex items-center justify-between p-3 bg-white rounded-lg border border-gray-100\">\n                        <span className=\"text-sm text-gray-700 capitalize font-medium\">\n                          {key.replace('_', ' ')}\n                        </span>\n                        <div className=\"flex items-center\">\n                          <span className={`text-sm font-semibold mr-3 ${data.score >= 70 ? 'text-green-600' : data.score >= 50 ? 'text-yellow-600' : 'text-red-600'}`}>\n                            {data.score}%\n                          </span>\n                          <div className=\"w-16 bg-gray-200 rounded-full h-2\">\n                            <div \n                              className={`h-2 rounded-full ${\n                                data.score >= 70 ? 'bg-green-500' : \n                                data.score >= 50 ? 'bg-yellow-500' : 'bg-red-500'\n                              }`}\n                              style={{ width: `${data.score}%` }}\n                            ></div>\n                          </div>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* 第二个卡片：Optimized Resume + ATS Analysis */}\n      <div className=\"mb-6\">\n        <div className=\"bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden\">\n          {/* Tab Navigation */}\n          <div className=\"border-b border-gray-200 bg-gray-50\">\n            <div className=\"px-8\">\n              <nav className=\"flex space-x-8\">\n                <button\n                  onClick={() => setActiveTab('optimized')}\n                  className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${\n                    activeTab === 'optimized'\n                      ? 'border-orange-500 text-orange-600'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  }`}\n                >\n                  <span className=\"flex items-center space-x-2\">\n                    <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                    </svg>\n                    <span>Optimized Resume</span>\n                  </span>\n                </button>\n                \n                <button\n                  onClick={() => setActiveTab('analysis')}\n                  className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${\n                    activeTab === 'analysis'\n                      ? 'border-orange-500 text-orange-600'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  }`}\n                >\n                  <span className=\"flex items-center space-x-2\">\n                    <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                    </svg>\n                    <span>ATS Analysis</span>\n                  </span>\n                </button>\n              </nav>\n            </div>\n          </div>\n\n          {/* 统计信息区块，仅在 Optimized Resume 页签下显示 */}\n          {activeTab === 'optimized' && (\n            <div className=\"px-8 py-6 border-b border-gray-100\">\n              <h4 className=\"text-xl font-semibold text-gray-900 mb-6 flex items-center\">\n                <svg className=\"w-6 h-6 mr-3 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                </svg>\n                Optimization Statistics\n              </h4>\n              \n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n                {(() => {\n                  // 计算实际的文本长度\n                  const originalText = getOriginalText();\n                  const optimizedText = getOptimizedText();\n                  const originalLength = originalText.length;\n                  const optimizedLength = optimizedText.length;\n                  const diff = optimizedLength - originalLength;\n\n                  return (\n                    <>\n                      {/* Original Length */}\n                      <div className=\"bg-gray-50 rounded-lg p-6 text-center\">\n                        <div className=\"text-xs font-medium text-gray-500 mb-2\">Original Length</div>\n                        <div className=\"text-2xl font-bold text-gray-900 mb-1\">\n                          {originalLength > 0 ? originalLength.toLocaleString() : 'N/A'}\n                        </div>\n                        <div className=\"text-xs text-gray-400\">Characters</div>\n                      </div>\n\n                      {/* Final Length */}\n                      <div className=\"bg-gray-50 rounded-lg p-6 text-center\">\n                        <div className=\"text-xs font-medium text-gray-500 mb-2\">Optimized Length</div>\n                        <div className=\"text-2xl font-bold text-gray-900 mb-1\">\n                          {optimizedLength > 0 ? optimizedLength.toLocaleString() : 'N/A'}\n                        </div>\n                        <div className=\"text-xs text-gray-400\">Characters</div>\n                      </div>\n\n                      {/* Length Change */}\n                      <div className=\"bg-blue-50 rounded-lg p-6 text-center\">\n                        <div className=\"text-xs font-medium text-gray-500 mb-2\">Change</div>\n                        <div className=\"text-2xl font-bold mb-1\">\n                          {originalLength === 0 || optimizedLength === 0 ? (\n                            <span className=\"text-gray-400\">N/A</span>\n                          ) : (\n                            <span className={diff >= 0 ? 'text-blue-600' : 'text-orange-600'}>\n                              {diff >= 0 ? '+' : ''}{diff.toLocaleString()}\n                            </span>\n                          )}\n                        </div>\n                        <div className=\"text-xs text-gray-400\">Characters</div>\n                      </div>\n\n                      {/* Optimization Ratio */}\n                      <div className=\"bg-green-50 rounded-lg p-6 text-center\">\n                        <div className=\"text-xs font-medium text-gray-500 mb-2\">Optimization Rate</div>\n                        <div className=\"text-2xl font-bold mb-1\">\n                          {originalLength === 0 || optimizedLength === 0 ? (\n                            <span className=\"text-gray-400\">N/A</span>\n                          ) : (() => {\n                            let displayRate = 0;\n                            if (optimizedLength > originalLength) {\n                              displayRate = ((optimizedLength - originalLength) / originalLength) * 100;\n                            } else {\n                              displayRate = ((originalLength - optimizedLength) / originalLength) * 100;\n                            }\n                            displayRate = Math.max(0, displayRate);\n                            const colorClass = displayRate > 15 ? 'text-green-600' : displayRate > 5 ? 'text-blue-600' : 'text-orange-600';\n                            return (\n                              <span className={colorClass}>\n                                {displayRate.toFixed(1)}%\n                              </span>\n                            );\n                          })()}\n                        </div>\n                        <div className=\"text-xs text-gray-400\">Adjustment</div>\n                      </div>\n                    </>\n                  );\n                })()}\n              </div>\n            </div>\n          )}\n\n          {/* Content Area */}\n          <div className=\"p-8\">\n            {/* Optimized Resume Tab */}\n            {activeTab === 'optimized' && (\n              <div>\n                <div className=\"mb-6 flex justify-between items-center\">\n                  <div className=\"flex items-center space-x-4 text-sm\">\n                    <span className=\"text-gray-600\">Optimized Resume View:</span>\n                    <div className=\"flex space-x-2\">\n                      <button\n                        onClick={() => setViewMode('raw')}\n                        className={`px-3 py-1 rounded-md text-xs font-medium transition-colors ${\n                          viewMode === 'raw'\n                            ? 'bg-orange-100 text-orange-700'\n                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                        }`}\n                      >\n                        Raw Text\n                      </button>\n                      <button\n                        onClick={() => setViewMode('enhanced')}\n                        className={`px-3 py-1 rounded-md text-xs font-medium transition-colors ${\n                          viewMode === 'enhanced'\n                            ? 'bg-orange-100 text-orange-700'\n                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                        }`}\n                      >\n                        Enhanced View\n                      </button>\n                      <button\n                        onClick={() => setViewMode('word')}\n                        className={`px-3 py-1 rounded-md text-xs font-medium transition-colors ${\n                          viewMode === 'word'\n                            ? 'bg-orange-100 text-orange-700'\n                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                        }`}\n                        disabled={!result?.optimized_docx_url}\n                      >\n                        Word Preview\n                      </button>\n                    </div>\n                  </div>\n                  <div className=\"relative export-dropdown\">\n                    <button\n                      onClick={() => setShowExportDropdown(!showExportDropdown)}\n                      className=\"inline-flex items-center px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors text-sm font-medium\"\n                    >\n                      <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                      </svg>\n                      Export & Download\n                      <svg className=\"w-4 h-4 ml-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                      </svg>\n                    </button>\n                    \n                    {showExportDropdown && (\n                      <div className=\"absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 z-50\">\n                        <div className=\"py-2\">\n                          {result?.isFormatPreserved && result?.download_url && (\n                            <>\n                              <button\n                                onClick={() => {\n                                  handleDownloadDocx();\n                                  setShowExportDropdown(false);\n                                }}\n                                disabled={downloadingDocx}\n                                className=\"w-full text-left px-4 py-3 hover:bg-purple-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\"\n                              >\n                                <div className=\"flex items-center\">\n                                  <div className=\"w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3\">\n                                    {downloadingDocx ? (\n                                      <svg className=\"animate-spin w-4 h-4 text-purple-600\" fill=\"none\" viewBox=\"0 0 24 24\">\n                                        <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                                        <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                                      </svg>\n                                    ) : (\n                                      <svg className=\"w-4 h-4 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10\" />\n                                      </svg>\n                                    )}\n                                  </div>\n                                  <div>\n                                    <div className=\"font-medium text-purple-900\">\n                                      {downloadingDocx ? 'Downloading...' : 'Download Original Format'}\n                                    </div>\n                                    <div className=\"text-xs text-purple-600\">DOCX with preserved formatting</div>\n                                  </div>\n                                </div>\n                              </button>\n                              <div className=\"border-t border-gray-100 my-1\"></div>\n                            </>\n                          )}\n                          \n                          <button\n                            onClick={() => {\n                              exportToPDF(getOptimizedText());\n                              setShowExportDropdown(false);\n                            }}\n                            className=\"w-full text-left px-4 py-3 hover:bg-red-50 transition-colors\"\n                          >\n                            <div className=\"flex items-center\">\n                              <div className=\"w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center mr-3\">\n                                <svg className=\"w-4 h-4 text-red-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                                </svg>\n                              </div>\n                              <div>\n                                <div className=\"font-medium text-gray-900\">Export PDF</div>\n                                <div className=\"text-xs text-gray-500\">Download optimized resume as PDF</div>\n                              </div>\n                            </div>\n                          </button>\n                          \n                          <button\n                            onClick={() => {\n                              exportToWord(getOptimizedText());\n                              setShowExportDropdown(false);\n                            }}\n                            className=\"w-full text-left px-4 py-3 hover:bg-blue-50 transition-colors\"\n                          >\n                            <div className=\"flex items-center\">\n                              <div className=\"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3\">\n                                <svg className=\"w-4 h-4 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                                </svg>\n                              </div>\n                              <div>\n                                <div className=\"font-medium text-gray-900\">Export Word</div>\n                                <div className=\"text-xs text-gray-500\">Download optimized resume as RTF</div>\n                              </div>\n                            </div>\n                          </button>\n                          \n                          <div className=\"border-t border-gray-100 my-1\"></div>\n                          \n                          <button\n                            onClick={() => {\n                              printResume(getOptimizedText());\n                              setShowExportDropdown(false);\n                            }}\n                            className=\"w-full text-left px-4 py-3 hover:bg-gray-50 transition-colors\"\n                          >\n                            <div className=\"flex items-center\">\n                              <div className=\"w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center mr-3\">\n                                <svg className=\"w-4 h-4 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z\" />\n                                </svg>\n                              </div>\n                              <div>\n                                <div className=\"font-medium text-gray-900\">Print Resume</div>\n                                <div className=\"text-xs text-gray-500\">Print optimized resume only</div>\n                              </div>\n                            </div>\n                          </button>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                </div>\n                \n                {/* Paper-style Resume Preview */}\n                <div className=\"paper-preview rounded-lg p-10 mx-auto max-w-5xl min-h-[800px] relative\">\n                  {/* 变化标记图例说明 */}\n                  {viewMode === 'raw' && (\n                    <>\n                      {(() => {\n                        const text = getOptimizedText() || 'Optimized resume content not available';\n                        const lines = text.split('\\n');\n                        const lastLine = lines[lines.length - 1]?.trim();\n                        const isRevisionNote = /^(This revised resume|This resume|优化说明|修改说明|优化总结|本次修改说明|优化建议)/i.test(lastLine);\n                        if (isRevisionNote) {\n                          const mainText = lines.slice(0, -1).join('\\n');\n                          return (\n                            <>\n                              <pre className=\"resume-content text-sm leading-relaxed text-gray-800 whitespace-pre-wrap\">\n                                {mainText}\n                              </pre>\n                              <div className=\"my-6 border-t border-gray-200\"></div>\n                              <div className=\"mb-3 leading-relaxed bg-yellow-50 border-l-8 border-yellow-400 px-6 py-4 italic text-gray-700 font-semibold shadow-sm\" style={{fontStyle:'italic'}}>\n                                <span className=\"text-yellow-600 font-bold mr-2\">Revision Note:</span>\n                                <span className=\"text-gray-700\">{lastLine}</span>\n                              </div>\n                            </>\n                          );\n                        } else {\n                          return (\n                            <pre className=\"resume-content text-sm leading-relaxed text-gray-800 whitespace-pre-wrap\">\n                              {text}\n                            </pre>\n                          );\n                        }\n                      })()}\n                    </>\n                  )}\n                  {viewMode === 'enhanced' && (\n                    <div \n                      className=\"resume-content text-sm leading-relaxed text-gray-800\"\n                      dangerouslySetInnerHTML={{ \n                        __html: highlightAdditionsInOptimized(getOptimizedText()) \n                      }}\n                    />\n                  )}\n                  {viewMode === 'word' && result?.optimized_docx_url && (\n                    <div className=\"word-preview-container\">\n                      <div className=\"preview-tabs mb-4\">\n                        <button\n                          onClick={() => setWordPreviewMode('office')}\n                          className={`px-3 py-2 mr-2 rounded text-sm font-medium transition-colors ${\n                            wordPreviewMode === 'office'\n                              ? 'bg-blue-500 text-white'\n                              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n                          }`}\n                        >\n                          Office Online\n                        </button>\n                        <button\n                          onClick={() => setWordPreviewMode('google')}\n                          className={`px-3 py-2 mr-2 rounded text-sm font-medium transition-colors ${\n                            wordPreviewMode === 'google'\n                              ? 'bg-blue-500 text-white'\n                              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n                          }`}\n                        >\n                          Google Docs\n                        </button>\n                        <button\n                          onClick={() => setWordPreviewMode('download')}\n                          className={`px-3 py-2 rounded text-sm font-medium transition-colors ${\n                            wordPreviewMode === 'download'\n                              ? 'bg-green-500 text-white'\n                              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n                          }`}\n                        >\n                          下载查看\n                        </button>\n                      </div>\n\n                      {wordPreviewMode === 'office' && (\n                        <div className=\"preview-frame-container\">\n                          <iframe\n                            src={`https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(result.optimized_docx_url)}`}\n                            width=\"100%\"\n                            height=\"800\"\n                            frameBorder=\"0\"\n                            title=\"Office Online Preview\"\n                            style={{ background: '#fff', borderRadius: '8px' }}\n                            onError={() => setWordPreviewError('Office Online预览失败，请尝试其他方式')}\n                          ></iframe>\n                        </div>\n                      )}\n\n                      {wordPreviewMode === 'google' && (\n                        <div className=\"preview-frame-container\">\n                          <iframe\n                            src={`https://docs.google.com/gview?url=${encodeURIComponent(result.optimized_docx_url)}&embedded=true`}\n                            width=\"100%\"\n                            height=\"800\"\n                            frameBorder=\"0\"\n                            title=\"Google Docs Preview\"\n                            style={{ background: '#fff', borderRadius: '8px' }}\n                            onError={() => setWordPreviewError('Google Docs预览失败，请尝试其他方式')}\n                          ></iframe>\n                        </div>\n                      )}\n\n                      {wordPreviewMode === 'download' && (\n                        <div className=\"download-preview-container p-8 text-center bg-gray-50 rounded-lg\">\n                          <div className=\"mb-4\">\n                            <svg className=\"w-16 h-16 mx-auto text-blue-500 mb-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                            </svg>\n                            <h3 className=\"text-lg font-semibold text-gray-800 mb-2\">Word文档已准备就绪</h3>\n                            <p className=\"text-gray-600 mb-4\">\n                              由于浏览器限制，无法直接预览Word文档。\n                              <br />\n                              请点击下载按钮获取优化后的文档。\n                            </p>\n                          </div>\n                          <a\n                            href={result.optimized_docx_url}\n                            download=\"optimized_resume.docx\"\n                            className=\"inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium\"\n                          >\n                            <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                            </svg>\n                            下载优化后的Word文档\n                          </a>\n                        </div>\n                      )}\n\n                      {wordPreviewError && (\n                        <div className=\"mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg\">\n                          <div className=\"flex\">\n                            <svg className=\"w-5 h-5 text-yellow-400 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z\" />\n                            </svg>\n                            <div>\n                              <p className=\"text-yellow-800 font-medium\">预览提示</p>\n                              <p className=\"text-yellow-700 text-sm mt-1\">{wordPreviewError}</p>\n                            </div>\n                          </div>\n                        </div>\n                      )}\n                    </div>\n                  )}\n                </div>\n                \n                {/* Paragraph Changes - Only show for format preserved results */}\n                {result?.isFormatPreserved && result?.paragraph_changes && result.paragraph_changes.length > 0 && (\n                  <div className=\"mt-8 bg-purple-50 border border-purple-200 rounded-lg p-6\">\n                    <h4 className=\"text-lg font-semibold text-purple-900 mb-4 flex items-center\">\n                      <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4\" />\n                      </svg>\n                      Paragraph-Level Changes Applied\n                    </h4>\n                    <div className=\"space-y-4\">\n                      {(result.paragraph_changes || []).map((change, index) => (\n                        <div key={index} className=\"bg-white rounded-lg p-4 border border-purple-100\">\n                          <div className=\"text-sm font-medium text-purple-700 mb-2\">\n                            Change #{index + 1}: {change.change_reason}\n                          </div>\n                          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                            <div>\n                              <div className=\"text-xs font-medium text-red-600 mb-1\">Original:</div>\n                              <div className=\"text-sm text-gray-700 bg-red-50 p-2 rounded border\">\n                                {change.original_text}\n                              </div>\n                            </div>\n                            <div>\n                              <div className=\"text-xs font-medium text-green-600 mb-1\">Optimized:</div>\n                              <div className=\"text-sm text-gray-700 bg-green-50 p-2 rounded border\">\n                                {change.optimized_text}\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                )}\n              </div>\n            )}\n\n            {/* ATS Analysis Tab */}\n            {activeTab === 'analysis' && (\n              <div>\n                <h3 className=\"text-2xl font-bold text-gray-900 mb-6\">ATS Compatibility Analysis</h3>\n                {/* 只保留详细分类匹配度分析 */}\n                {result?.job_match_analysis?.optimized_match_score?.breakdown && (\n                  <div>\n                    <h5 className=\"text-md font-semibold text-gray-800 mb-3\">Detailed Match Breakdown</h5>\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                      {Object.entries(result.job_match_analysis.optimized_match_score.breakdown).map(([key, data]) => {\n                        const matchedLimit = 5;\n                        const missingLimit = 3;\n                        const matchedExpanded = expandedMatched[key] || false;\n                        const missingExpanded = expandedMissing[key] || false;\n                        return (\n                          <div key={key} className=\"bg-gray-50 rounded-lg p-4 border\">\n                            <div className=\"flex justify-between items-start mb-2\">\n                              <div>\n                                <h6 className=\"font-semibold text-gray-700 capitalize\">\n                                  {key.replace('_', ' ')}\n                                </h6>\n                                <span className=\"text-xs text-gray-500 font-medium\">Weight: {data.weight}</span>\n                              </div>\n                              <div className={`text-lg font-bold ${data.score >= 70 ? 'text-green-600' : data.score >= 50 ? 'text-yellow-600' : 'text-red-600'}`}>{data.score}%</div>\n                            </div>\n                            {/* 进度条 */}\n                            <div className=\"w-full bg-gray-200 rounded-full h-2 mb-2\">\n                              <div \n                                className={`h-2 rounded-full transition-all duration-500 ${\n                                  data.score >= 70 ? 'bg-green-500' : \n                                  data.score >= 50 ? 'bg-yellow-500' : 'bg-red-500'\n                                }`}\n                                style={{ width: `${data.score}%` }}\n                              ></div>\n                            </div>\n                            {/* 匹配详情 */}\n                            {data.matched && data.total && (\n                              <div className=\"text-xs text-gray-600\">\n                                <div className=\"mb-1\">\n                                  <span className=\"font-medium\">Matched ({data.matched.length}/{data.total.length}):</span>\n                                </div>\n                                {data.matched.length > 0 ? (\n                                  <div className=\"flex flex-wrap gap-1 mb-2\">\n                                    {(matchedExpanded ? data.matched : data.matched.slice(0, matchedLimit)).map((item, idx) => (\n                                      <span key={idx} className=\"bg-green-100 text-green-700 px-2 py-1 rounded text-xs\">{item}</span>\n                                    ))}\n                                    {data.matched.length > matchedLimit && (\n                                      <span\n                                        className=\"text-gray-500 cursor-pointer underline\"\n                                        onClick={() => toggleExpand(key, 'matched')}\n                                      >\n                                        {matchedExpanded ? '收起' : `+${data.matched.length - matchedLimit} more`}\n                                      </span>\n                                    )}\n                                  </div>\n                                ) : (\n                                  <div className=\"text-gray-500 mb-2\">No matches found</div>\n                                )}\n                                {data.total.length > data.matched.length && (\n                                  <div>\n                                    <span className=\"font-medium text-red-600\">Missing:</span>\n                                    <div className=\"flex flex-wrap gap-1 mt-1\">\n                                      {(missingExpanded ? data.total.filter(item => !data.matched.includes(item)) : data.total.filter(item => !data.matched.includes(item)).slice(0, missingLimit)).map((item, idx) => (\n                                        <span key={idx} className=\"bg-red-100 text-red-700 px-2 py-1 rounded text-xs mr-1\">{item}</span>\n                                      ))}\n                                      {data.total.length - data.matched.length > missingLimit && (\n                                        <span\n                                          className=\"text-gray-500 cursor-pointer underline\"\n                                          onClick={() => toggleExpand(key, 'missing')}\n                                        >\n                                          {missingExpanded ? '收起' : `+${data.total.length - data.matched.length - missingLimit} more`}\n                                        </span>\n                                      )}\n                                    </div>\n                                  </div>\n                                )}\n                              </div>\n                            )}\n                          </div>\n                        );\n                      })}\n                    </div>\n\n\n\n                    {/* Optimization Suggestions */}\n                    {(() => {\n                      const suggestions = Object.entries(result.job_match_analysis.optimized_match_score.breakdown)\n                        .filter(([key, data]) => data.score < 60)\n                        .map(([key, data]) => {\n                          let tip = '';\n                          if (key.toLowerCase().includes('action')) tip = 'Consider adding more action verbs to demonstrate impact.';\n                          else if (key.toLowerCase().includes('skill')) tip = 'Add more relevant technical skills from the job description.';\n                          else if (key.toLowerCase().includes('industry')) tip = 'Include more industry-specific terminology.';\n                          else if (key.toLowerCase().includes('experience')) tip = 'Enhance experience descriptions with more details.';\n                          else if (key.toLowerCase().includes('education')) tip = 'Complete education information if applicable.';\n                          else tip = 'Consider adding relevant content to improve match score.';\n                          return `[${key.replace('_', ' ')}] ${tip}`;\n                        });\n                      if (suggestions.length === 0) return null;\n                      return (\n                        <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-6 mt-4 mb-4\">\n                          <div className=\"flex items-center mb-2\">\n                            <h4 className=\"text-lg font-bold text-blue-700 mr-4\">Optimization Suggestions</h4>\n                            <button\n                              className=\"ml-auto px-4 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm\"\n                              onClick={() => setShowAllSuggestions(v => !v)}\n                            >{showAllSuggestions ? 'Collapse All' : 'Expand All Suggestions'}</button>\n                          </div>\n                          <ul className=\"list-disc pl-6 text-blue-800\">\n                            {(showAllSuggestions ? suggestions : suggestions.slice(0, 2)).map((s, i) => (\n                              <li key={i}>{s}</li>\n                            ))}\n                            {!showAllSuggestions && suggestions.length > 2 && (\n                              <li className=\"text-gray-500\">...click button above to see more suggestions</li>\n                            )}\n                          </ul>\n                        </div>\n                      );\n                    })()}\n\n                    {/* 雷达图可视化 - 暂时禁用，需要安装echarts-for-react */}\n                    {false && (() => {\n                      const radarData = Object.entries(result.job_match_analysis.optimized_match_score.breakdown).map(([key, data]) => ({\n                        name: key.replace('_', ' '),\n                        value: data.score,\n                      }));\n                      if (radarData.length < 3) return null; // 至少3项才显示雷达图\n                      const indicator = radarData.map(item => ({ name: item.name, max: 100 }));\n                      const option = {\n                        tooltip: {},\n                        radar: {\n                          indicator,\n                          radius: 80,\n                        },\n                        series: [{\n                          type: 'radar',\n                          data: [{ value: radarData.map(d => d.value), name: '覆盖度' }],\n                          areaStyle: { opacity: 0.2 },\n                          lineStyle: { width: 2 },\n                        }],\n                      };\n                      return (\n                        <div className=\"bg-white border border-gray-200 rounded-lg p-6 mt-4 mb-4 flex flex-col items-center\">\n                          <h4 className=\"text-lg font-bold text-gray-800 mb-2\">简历内容覆盖度雷达图</h4>\n                          <div style={{ width: 350, height: 300 }}>\n                            {/* <ReactECharts option={option} style={{ width: '100%', height: '100%' }} /> */}\n                            <div className=\"flex items-center justify-center h-full text-gray-500\">\n                              雷达图功能暂时禁用\n                            </div>\n                          </div>\n                        </div>\n                      );\n                    })()}\n\n                    {/* ATS Common Issues Detection */}\n                    {(() => {\n                      const optimizedText = getOptimizedText();\n                      const issues = [];\n                      if (/[★☆▪▫◦]/.test(optimizedText)) issues.push('Special characters detected, consider removing them.');\n                      if (!/(email|邮箱|@)/i.test(optimizedText)) issues.push('Email information not detected.');\n                      if (!/(phone|电话|\\d{11,})/i.test(optimizedText)) issues.push('Phone number not detected.');\n                      if (optimizedText.length < 500) issues.push('Resume content is too short, consider adding more details.');\n                      if (optimizedText.length > 4000) issues.push('Resume content is too long, consider condensing.');\n                      if (issues.length === 0) return null;\n                      return (\n                        <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-6 mt-4 mb-4\">\n                          <h4 className=\"text-lg font-bold text-yellow-700 mb-2 flex items-center\">\n                            <svg className=\"w-5 h-5 mr-2 text-yellow-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\"><path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z\" /></svg>\n                            ATS Common Issues Detection\n                          </h4>\n                          <ul className=\"list-disc pl-6 text-yellow-800\">\n                            {issues.map((issue, idx) => <li key={idx}>{issue}</li>)}\n                          </ul>\n                        </div>\n                      );\n                    })()}\n\n                  </div>\n                )}\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ResultDisplay; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,YAAY,EAAEC,YAAY,EAAEC,WAAW,QAAQ,sBAAsB;AAC3F,SAASC,qBAAqB,QAAQ,iBAAiB;AACvD,OAAOC,eAAe,MAAM,mBAAmB;AAC/C;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEA,MAAMC,aAAa,GAAGA,CAAC;EAAEC,MAAM;EAAEC,YAAY;EAAEC;AAAe,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EAClE,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC0B,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC;EACzD,MAAM,CAAC4B,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EAC7E,MAAM,CAAC8B,eAAe,EAAEC,kBAAkB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACgC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACkC,QAAQ,EAAEC,WAAW,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACjD,MAAM,CAACoC,eAAe,EAAEC,kBAAkB,CAAC,GAAGrC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;EAClE,MAAM,CAACsC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EAC9D;EACA,MAAM,CAACwC,eAAe,EAAEC,kBAAkB,CAAC,GAAGzC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAM,CAAC0C,eAAe,EAAEC,kBAAkB,CAAC,GAAG3C,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAM,CAAC4C,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;;EAEnE;EACAC,SAAS,CAAC,MAAM;IACd,MAAM6C,kBAAkB,GAAIC,KAAK,IAAK;MACpC,IAAIf,kBAAkB,IAAI,CAACe,KAAK,CAACC,MAAM,CAACC,OAAO,CAAC,kBAAkB,CAAC,EAAE;QACnEhB,qBAAqB,CAAC,KAAK,CAAC;MAC9B;IACF,CAAC;IAEDiB,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEL,kBAAkB,CAAC;IAC1D,OAAO,MAAM;MACXI,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;IAC/D,CAAC;EACH,CAAC,EAAE,CAACd,kBAAkB,CAAC,CAAC;EAExB,MAAMqB,qBAAqB,GAAIC,IAAI,IAAK;IACtC,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;;IAEpB;IACA,OAAOA,IAAI,CACRC,OAAO,CAAC,cAAc,EAAE,6FAA6F,CAAC,CACtHA,OAAO,CAAC,eAAe,EAAE,mEAAmE,CAAC,CAC7FA,OAAO,CAAC,gBAAgB,EAAE,iEAAiE,CAAC,CAC5FA,OAAO,CAAC,yBAAyB,EAAE,+EAA+E,CAAC,CACnHA,OAAO,CAAC,aAAa,EAAE,6CAA6C,CAAC,CACrEA,OAAO,CAAC,YAAY,EAAE,6CAA6C,CAAC,CACpEA,OAAO,CAAC,gBAAgB,EAAE,uCAAuC,CAAC,CAClEA,OAAO,CAAC,YAAY,EAAE,gCAAgC,CAAC,CACvDA,OAAO,CAAC,OAAO,EAAE,sBAAsB,CAAC,CACxCA,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CACtBA,OAAO,CAAC,sBAAsB,EAAE,sCAAsC,CAAC;EAC5E,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAACC,aAAa,EAAEC,YAAY,KAAK;IACxD;IACA,MAAMC,iBAAiB,GAAGF,aAAa,IAAI,EAAE;IAC7C,MAAMG,gBAAgB,GAAGF,YAAY,IAAI,EAAE;;IAE3C;IACA,MAAMG,OAAO,GAAG;MACdC,yBAAyB,EAAE,wCAAwC,CAACC,IAAI,CAACJ,iBAAiB,CAAC;MAC3FK,cAAc,EAAE,gEAAgE,CAACD,IAAI,CAACJ,iBAAiB,CAACM,WAAW,CAAC,CAAC,CAAC;MACtHC,mBAAmB,EAAE,+BAA+B,CAACH,IAAI,CAACJ,iBAAiB,CAACM,WAAW,CAAC,CAAC,CAAC;MAC1FE,cAAc,EAAE,wBAAwB,CAACJ,IAAI,CAACJ,iBAAiB,CAACM,WAAW,CAAC,CAAC,CAAC;MAC9EG,iBAAiB,EAAET,iBAAiB,CAACU,MAAM,GAAG,GAAG,IAAIV,iBAAiB,CAACU,MAAM,GAAG,IAAI;MACpFC,mBAAmB,EAAE,CAAC,SAAS,CAACP,IAAI,CAACJ,iBAAiB;IACxD,CAAC;IAED,MAAMY,KAAK,GAAGC,MAAM,CAACC,MAAM,CAACZ,OAAO,CAAC,CAACa,MAAM,CAACC,OAAO,CAAC,CAACN,MAAM;IAC3D,MAAMO,QAAQ,GAAGJ,MAAM,CAACK,IAAI,CAAChB,OAAO,CAAC,CAACQ,MAAM;IAC5C,MAAMS,UAAU,GAAGC,IAAI,CAACC,KAAK,CAAET,KAAK,GAAGK,QAAQ,GAAI,GAAG,CAAC;IAEvD,OAAO;MACLL,KAAK,EAAEO,UAAU;MACjBjB,OAAO;MACPoB,YAAY,EAAEL,QAAQ,GAAGL;IAC3B,CAAC;EACH,CAAC;;EAED;EACA,MAAMW,gBAAgB,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA,EAAAC,sBAAA;IAC7B;IACAC,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;IACtCD,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEzE,MAAM,CAAC;IAC9CwE,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE;MACpCC,kBAAkB,EAAE,CAAC,EAAC1E,MAAM,aAANA,MAAM,eAANA,MAAM,CAAE2E,gBAAgB;MAC9CC,gBAAgB,EAAE,CAAC,EAAC5E,MAAM,aAANA,MAAM,eAANA,MAAM,CAAE6E,cAAc;MAC1CC,sBAAsB,EAAE,CAAC,EAAC9E,MAAM,aAANA,MAAM,eAANA,MAAM,CAAE+E,qBAAqB;MACvDC,qBAAqB,EAAE,CAAAhF,MAAM,aAANA,MAAM,wBAAAsE,qBAAA,GAANtE,MAAM,CAAE2E,gBAAgB,cAAAL,qBAAA,uBAAxBA,qBAAA,CAA0Bd,MAAM,KAAI,CAAC;MAC5DyB,yBAAyB,EAAE,CAAAjF,MAAM,aAANA,MAAM,wBAAAuE,sBAAA,GAANvE,MAAM,CAAE+E,qBAAqB,cAAAR,sBAAA,uBAA7BA,sBAAA,CAA+Bf,MAAM,KAAI,CAAC;MACrE0B,mBAAmB,EAAE,QAAOlF,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE2E,gBAAgB;MACpDQ,uBAAuB,EAAE,QAAOnF,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE+E,qBAAqB;IAC/D,CAAC,CAAC;;IAEF;IACA,IAAI/E,MAAM,aAANA,MAAM,eAANA,MAAM,CAAE+E,qBAAqB,EAAE;MACjCP,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEzE,MAAM,CAAC+E,qBAAqB,CAAC;IAC7E;;IAEA;IACA,IAAI/E,MAAM,aAANA,MAAM,eAANA,MAAM,CAAE2E,gBAAgB,IAAI3E,MAAM,CAAC2E,gBAAgB,CAACS,IAAI,CAAC,CAAC,EAAE;MAC9DZ,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEzE,MAAM,CAAC2E,gBAAgB,CAACnB,MAAM,CAAC;MACtFgB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEzE,MAAM,CAAC2E,gBAAgB,CAACU,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;MACnF,OAAOrF,MAAM,CAAC2E,gBAAgB;IAChC;IACA,IAAI3E,MAAM,aAANA,MAAM,eAANA,MAAM,CAAE6E,cAAc,IAAI7E,MAAM,CAAC6E,cAAc,CAACO,IAAI,CAAC,CAAC,EAAE;MAC1DZ,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEzE,MAAM,CAAC6E,cAAc,CAACrB,MAAM,CAAC;MAClF,OAAOxD,MAAM,CAAC6E,cAAc;IAC9B;;IAEA;IACA,IAAI7E,MAAM,aAANA,MAAM,eAANA,MAAM,CAAE+E,qBAAqB,IAAIO,KAAK,CAACC,OAAO,CAACvF,MAAM,CAAC+E,qBAAqB,CAAC,IAAI/E,MAAM,CAAC+E,qBAAqB,CAACvB,MAAM,GAAG,CAAC,EAAE;MAC3HgB,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEzE,MAAM,CAAC+E,qBAAqB,CAACvB,MAAM,CAAC;MAChG,MAAMgC,QAAQ,GAAGxF,MAAM,CAAC+E,qBAAqB;MAE7CP,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEe,QAAQ,CAACC,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,MAAM;QACtEA,KAAK;QACLD,OAAO,EAAEA,OAAO,CAACA,OAAO,IAAIA,OAAO,CAACE,YAAY,IAAI,SAAS;QAC7DC,YAAY,EAAE,CAAC,CAACH,OAAO,CAACI,SAAS;QACjCC,UAAU,EAAE,CAAC,CAACL,OAAO,CAACM,OAAO;QAC7BC,eAAe,EAAE,CAACP,OAAO,CAACI,SAAS,IAAI,EAAE,EAAEtC,MAAM;QACjD0C,aAAa,EAAE,CAACR,OAAO,CAACM,OAAO,IAAI,EAAE,EAAExC;MACzC,CAAC,CAAC,CAAC,CAAC;MAEJ,MAAM2C,WAAW,GAAGX,QAAQ,CAACC,GAAG,CAACC,OAAO,IAAI;QAC1C;QACA,MAAMU,WAAW,GAAGV,OAAO,CAACE,YAAY,IAAIF,OAAO,CAACA,OAAO,IAAI,EAAE;QACjE,MAAMM,OAAO,GAAGN,OAAO,CAACM,OAAO,IAAIN,OAAO,CAACI,SAAS,IAAI,EAAE;QAC1D,OAAOM,WAAW,GAAG,GAAGA,WAAW,KAAKJ,OAAO,EAAE,GAAGA,OAAO;MAC7D,CAAC,CAAC,CAACnC,MAAM,CAACpB,IAAI,IAAIA,IAAI,CAAC2C,IAAI,CAAC,CAAC,CAAC,CAACiB,IAAI,CAAC,MAAM,CAAC;MAE3C7B,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE0B,WAAW,CAAC3C,MAAM,CAAC;MACzDgB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE0B,WAAW,CAACd,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;MACnE,OAAOc,WAAW,IAAI,EAAE;IAC1B;IAEA3B,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;;IAEhE;IACA,IAAIzE,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEsG,eAAe,EAAE;MAC3B9B,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MACnD,OAAO,8CAA8CzE,MAAM,CAACsG,eAAe,EAAE;IAC/E;IAEA,OAAO,EAAE;EACX,CAAC;EAED,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5B,OAAO,CAAAvG,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEsG,eAAe,MAAItG,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEwG,aAAa,KAAI,EAAE;EAC/D,CAAC;;EAED;EACA,MAAMC,qBAAqB,GAAGA,CAAC5D,YAAY,EAAED,aAAa,KAAK;IAC7D,MAAM8D,aAAa,GAAG7D,YAAY,CAAC8D,KAAK,CAAC,IAAI,CAAC,CAAC9C,MAAM,CAAC+C,IAAI,IAAIA,IAAI,CAACxB,IAAI,CAAC,CAAC,CAAC;IAC1E,MAAMyB,cAAc,GAAGjE,aAAa,CAAC+D,KAAK,CAAC,IAAI,CAAC,CAAC9C,MAAM,CAAC+C,IAAI,IAAIA,IAAI,CAACxB,IAAI,CAAC,CAAC,CAAC;IAE5E,MAAMpF,MAAM,GAAG,EAAE;IACjB,MAAM8G,oBAAoB,GAAG,IAAIC,GAAG,CAAC,CAAC;IAEtCL,aAAa,CAACM,OAAO,CAAC,CAACC,YAAY,EAAEC,aAAa,KAAK;MACrD,MAAMC,eAAe,GAAGF,YAAY,CAAC7B,IAAI,CAAC,CAAC;MAC3C,IAAI,CAAC+B,eAAe,EAAE;;MAEtB;MACA,IAAIC,SAAS,GAAG,IAAI;MACpB,IAAIC,cAAc,GAAG,CAAC;MACtB,IAAIC,kBAAkB,GAAG,CAAC,CAAC;MAE3BT,cAAc,CAACG,OAAO,CAAC,CAACO,aAAa,EAAEC,YAAY,KAAK;QACtD,IAAIV,oBAAoB,CAACW,GAAG,CAACD,YAAY,CAAC,EAAE;QAE5C,MAAME,gBAAgB,GAAGH,aAAa,CAACnC,IAAI,CAAC,CAAC;QAC7C,MAAMuC,UAAU,GAAGC,mBAAmB,CAACT,eAAe,EAAEO,gBAAgB,CAAC;QAEzE,IAAIC,UAAU,GAAGN,cAAc,IAAIM,UAAU,GAAG,GAAG,EAAE;UACnDP,SAAS,GAAGG,aAAa;UACzBF,cAAc,GAAGM,UAAU;UAC3BL,kBAAkB,GAAGE,YAAY;QACnC;MACF,CAAC,CAAC;MAEF,IAAIJ,SAAS,IAAIC,cAAc,GAAG,GAAG,EAAE;QACrC;QACAP,oBAAoB,CAACe,GAAG,CAACP,kBAAkB,CAAC;QAC5CtH,MAAM,CAAC8H,IAAI,CAAC;UACVC,IAAI,EAAE,UAAU;UAChBC,QAAQ,EAAEf,YAAY;UACtBnB,SAAS,EAAEsB,SAAS;UACpBO,UAAU,EAAEN;QACd,CAAC,CAAC;MACJ,CAAC,MAAM,IAAID,SAAS,IAAIC,cAAc,GAAG,GAAG,EAAE;QAC5C;QACAP,oBAAoB,CAACe,GAAG,CAACP,kBAAkB,CAAC;QAC5CtH,MAAM,CAAC8H,IAAI,CAAC;UACVC,IAAI,EAAE,wBAAwB;UAC9BC,QAAQ,EAAEf,YAAY;UACtBnB,SAAS,EAAEsB,SAAS;UACpBO,UAAU,EAAEN;QACd,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACArH,MAAM,CAAC8H,IAAI,CAAC;UACVC,IAAI,EAAE,SAAS;UACfC,QAAQ,EAAEf,YAAY;UACtBnB,SAAS,EAAE;QACb,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,OAAO9F,MAAM;EACf,CAAC;;EAED;EACA,MAAM4H,mBAAmB,GAAGA,CAACK,IAAI,EAAEC,IAAI,KAAK;IAC1C,IAAID,IAAI,KAAKC,IAAI,EAAE,OAAO,CAAC;IAC3B,IAAI,CAACD,IAAI,IAAI,CAACC,IAAI,EAAE,OAAO,CAAC;IAE5B,MAAMC,MAAM,GAAGF,IAAI,CAAC7E,WAAW,CAAC,CAAC,CAACuD,KAAK,CAAC,KAAK,CAAC;IAC9C,MAAMyB,MAAM,GAAGF,IAAI,CAAC9E,WAAW,CAAC,CAAC,CAACuD,KAAK,CAAC,KAAK,CAAC;IAE9C,MAAM0B,WAAW,GAAGF,MAAM,CAACtE,MAAM,CAACyE,IAAI,IAAIF,MAAM,CAACG,QAAQ,CAACD,IAAI,CAAC,CAAC;IAChE,MAAME,UAAU,GAAGtE,IAAI,CAACuE,GAAG,CAACN,MAAM,CAAC3E,MAAM,EAAE4E,MAAM,CAAC5E,MAAM,CAAC;IAEzD,OAAO6E,WAAW,CAAC7E,MAAM,GAAGgF,UAAU;EACxC,CAAC;;EAED;EACA,MAAME,6BAA6B,GAAI9F,aAAa,IAAK;IACvD,MAAMC,YAAY,GAAG0D,eAAe,CAAC,CAAC;IACtC,IAAI,CAAC1D,YAAY,IAAI,CAACD,aAAa,IAAIC,YAAY,KAAKD,aAAa,EAAE;MACrE;MACA,OAAOJ,qBAAqB,CAACI,aAAa,CAAC;IAC7C;IACA;IACA,MAAM+F,WAAW,GAAGlC,qBAAqB,CAAC5D,YAAY,EAAED,aAAa,CAAC;IACtE,MAAMiE,cAAc,GAAGjE,aAAa,CAAC+D,KAAK,CAAC,IAAI,CAAC,CAAC9C,MAAM,CAAC+C,IAAI,IAAIA,IAAI,CAACxB,IAAI,CAAC,CAAC,CAAC;IAC5E,MAAMsB,aAAa,GAAG7D,YAAY,CAAC8D,KAAK,CAAC,IAAI,CAAC,CAAC9C,MAAM,CAAC+C,IAAI,IAAIA,IAAI,CAACxB,IAAI,CAAC,CAAC,CAAC;IAC1E,MAAMwD,mBAAmB,GAAG,IAAI7B,GAAG,CAAC,CAAC;IACrC,MAAMD,oBAAoB,GAAG,IAAIC,GAAG,CAAC,CAAC;IACtC;IACA4B,WAAW,CAAC3B,OAAO,CAAC,CAAC6B,IAAI,EAAEC,GAAG,KAAK;MACjC,IAAID,IAAI,CAACd,IAAI,KAAK,SAAS,EAAE;QAC3B,MAAMgB,MAAM,GAAGlC,cAAc,CAACmC,SAAS,CAACpC,IAAI;UAAA,IAAAqC,eAAA;UAAA,OAAIrC,IAAI,CAACxB,IAAI,CAAC,CAAC,OAAA6D,eAAA,GAAKJ,IAAI,CAAC/C,SAAS,cAAAmD,eAAA,uBAAdA,eAAA,CAAgB7D,IAAI,CAAC,CAAC;QAAA,EAAC;QACvF,IAAI2D,MAAM,KAAK,CAAC,CAAC,EAAEjC,oBAAoB,CAACe,GAAG,CAACkB,MAAM,CAAC;MACrD;MACA,IAAIF,IAAI,CAACd,IAAI,KAAK,OAAO,EAAE;QACzB,MAAMmB,MAAM,GAAGxC,aAAa,CAACsC,SAAS,CAACpC,IAAI;UAAA,IAAAuC,cAAA;UAAA,OAAIvC,IAAI,CAACxB,IAAI,CAAC,CAAC,OAAA+D,cAAA,GAAKN,IAAI,CAACb,QAAQ,cAAAmB,cAAA,uBAAbA,cAAA,CAAe/D,IAAI,CAAC,CAAC;QAAA,EAAC;QACrF,IAAI8D,MAAM,KAAK,CAAC,CAAC,EAAEN,mBAAmB,CAACf,GAAG,CAACqB,MAAM,CAAC;MACpD;IACF,CAAC,CAAC;IACF;IACA,OAAOrC,cAAc,CAACpB,GAAG,CAAC,CAACmB,IAAI,EAAEkC,GAAG,KAAK;MACvC,MAAMM,WAAW,GAAGxC,IAAI,CAAClE,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;MACpE;MACA,MAAM2G,cAAc,GAAIP,GAAG,KAAKjC,cAAc,CAACrD,MAAM,GAAG,CAAC,IAAK,gEAAgE,CAACN,IAAI,CAAC0D,IAAI,CAACxB,IAAI,CAAC,CAAC,CAAC;MAChJ,IAAIiE,cAAc,EAAE;QAClB;QACA,IAAIC,eAAe,GAAG1C,IAAI,CAACxB,IAAI,CAAC,CAAC;QACjC,IAAI,gBAAgB,CAAClC,IAAI,CAACoG,eAAe,CAAC,EAAE;UAC1CA,eAAe,GAAG,gnBAAgnB;QACpoB;QACA,OAAO,uNAAuNA,eAAe,CAAC5G,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM;MAC5Q;MACA;MACA,IAAI,CAACkG,mBAAmB,CAACnB,GAAG,CAACqB,GAAG,CAAC,IAAIlC,IAAI,CAACxB,IAAI,CAAC,CAAC,CAAC5B,MAAM,GAAG,CAAC,EAAE;QAC3D,OAAO,yKAAyK4F,WAAW,aAAa;MAC1M;MACA;MACA,MAAMP,IAAI,GAAGF,WAAW,CAACY,IAAI,CAACC,CAAC;QAAA,IAAAC,YAAA;QAAA,OAAI,EAAAA,YAAA,GAAAD,CAAC,CAAC1D,SAAS,cAAA2D,YAAA,uBAAXA,YAAA,CAAarE,IAAI,CAAC,CAAC,MAAKwB,IAAI,CAACxB,IAAI,CAAC,CAAC,IAAIoE,CAAC,CAACzB,IAAI,KAAK,wBAAwB;MAAA,EAAC;MAC9G,IAAIc,IAAI,EAAE;QACR,OAAO,0KAA0KO,WAAW,aAAa;MAC3M;MACA;MACA,MAAMM,KAAK,GAAGf,WAAW,CAACY,IAAI,CAACC,CAAC;QAAA,IAAAG,aAAA;QAAA,OAAI,EAAAA,aAAA,GAAAH,CAAC,CAAC1D,SAAS,cAAA6D,aAAA,uBAAXA,aAAA,CAAavE,IAAI,CAAC,CAAC,MAAKwB,IAAI,CAACxB,IAAI,CAAC,CAAC,IAAIoE,CAAC,CAACzB,IAAI,KAAK,UAAU;MAAA,EAAC;MACjG,IAAI2B,KAAK,EAAE;QACT,OAAO,0KAA0KN,WAAW,aAAa;MAC3M;MACA;MACA,OAAO,mDAAmDA,WAAW,MAAM;IAC7E,CAAC,CAAC,CAAC/C,IAAI,CAAC,EAAE,CAAC;IACX;IACA;EACF,CAAC;EAED,MAAMuD,QAAQ,GAAGjH,gBAAgB,CAAC0B,gBAAgB,CAAC,CAAC,EAAEkC,eAAe,CAAC,CAAC,CAAC;EAExE,MAAMsD,aAAa,GAAInG,KAAK,IAAK;IAC/B,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,gBAAgB;IACxC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,iBAAiB;IACzC,OAAO,cAAc;EACvB,CAAC;EAED,MAAMoG,UAAU,GAAIpG,KAAK,IAAK;IAC5B,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,cAAc;IACtC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,eAAe;IACvC,OAAO,YAAY;EACrB,CAAC;EAED,MAAMqG,WAAW,GAAIrG,KAAK,IAAK;IAC7B,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,8BAA8B;IACtD,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,gCAAgC;IACxD,OAAO,0BAA0B;EACnC,CAAC;EAED,MAAMsG,SAAS,GAAItG,KAAK,IAAK;IAC3B,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,cAAc;IACtC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,eAAe;IACvC,OAAO,YAAY;EACrB,CAAC;EAED,MAAMuG,mBAAmB,GAAIvG,KAAK,IAAK;IACrC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,cAAc;IACtC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,eAAe;IACvC,OAAO,YAAY;EACrB,CAAC;EAED,MAAMwG,cAAc,GAAIxG,KAAK,IAAK;IAChC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,kBAAkB;IAC1C,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,mBAAmB;IAC3C,OAAO,gBAAgB;EACzB,CAAC;;EAED;EACA,MAAMyG,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAErK,YAAY,CAAC;MACrCmK,QAAQ,CAACE,MAAM,CAAC,iBAAiB,EAAEpK,cAAc,CAAC;MAElD,MAAMqK,QAAQ,GAAG,MAAMC,KAAK,CAAC,2BAA2B,EAAE;QACxDC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEN;MACR,CAAC,CAAC;MAEF,IAAI,CAACG,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAMC,KAAK,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QACnC,MAAM,IAAIC,KAAK,CAACF,KAAK,CAACA,KAAK,IAAI,MAAM,CAAC;MACxC;;MAEA;MACA,MAAMG,kBAAkB,GAAGR,QAAQ,CAACS,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;MACtE,MAAMC,QAAQ,GAAGH,kBAAkB,GAC/BA,kBAAkB,CAACpE,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAACjE,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,GAC1D,uBAAuB;;MAE3B;MACA,MAAMyI,IAAI,GAAG,MAAMZ,QAAQ,CAACY,IAAI,CAAC,CAAC;MAClC,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;MAC5C,MAAMK,CAAC,GAAGnJ,QAAQ,CAACoJ,aAAa,CAAC,GAAG,CAAC;MACrCD,CAAC,CAACE,IAAI,GAAGN,GAAG;MACZI,CAAC,CAACG,QAAQ,GAAGT,QAAQ;MACrB7I,QAAQ,CAACqI,IAAI,CAACkB,WAAW,CAACJ,CAAC,CAAC;MAC5BA,CAAC,CAACK,KAAK,CAAC,CAAC;MACTR,MAAM,CAACC,GAAG,CAACQ,eAAe,CAACV,GAAG,CAAC;MAC/B/I,QAAQ,CAACqI,IAAI,CAACqB,WAAW,CAACP,CAAC,CAAC;IAE9B,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdpG,OAAO,CAACoG,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCoB,KAAK,CAAC,QAAQ,GAAGpB,KAAK,CAACqB,OAAO,CAAC;IACjC;EACF,CAAC;EAED,MAAMC,YAAY,GAAGA,CAACC,GAAG,EAAEpE,IAAI,KAAK;IAClC,IAAIA,IAAI,KAAK,SAAS,EAAE;MACtBnG,kBAAkB,CAACwK,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACD,GAAG,GAAG,CAACC,IAAI,CAACD,GAAG;MAAE,CAAC,CAAC,CAAC;IAC9D,CAAC,MAAM;MACLrK,kBAAkB,CAACsK,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACD,GAAG,GAAG,CAACC,IAAI,CAACD,GAAG;MAAE,CAAC,CAAC,CAAC;IAC9D;EACF,CAAC;EAED,oBACEvM,OAAA;IAAAyM,QAAA,gBAEEzM,OAAA;MAAK0M,SAAS,EAAC,MAAM;MAAAD,QAAA,eACnBzM,OAAA;QAAK0M,SAAS,EAAC,sEAAsE;QAAAD,QAAA,gBAEnFzM,OAAA;UAAK0M,SAAS,EAAE,GAAGvC,WAAW,CAACH,QAAQ,CAAClG,KAAK,CAAC,8CAA+C;UAAA2I,QAAA,gBAE3FzM,OAAA;YAAK0M,SAAS,EAAC,0EAA0E;YAAAD,QAAA,eACvFzM,OAAA;cAAK0M,SAAS,EAAC,WAAW;cAACC,IAAI,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAH,QAAA,eAChEzM,OAAA;gBAAM6M,QAAQ,EAAC,SAAS;gBAACjD,CAAC,EAAC,uIAAuI;gBAACkD,QAAQ,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENlN,OAAA;YAAK0M,SAAS,EAAC,UAAU;YAAAD,QAAA,eACvBzM,OAAA;cAAK0M,SAAS,EAAC,8DAA8D;cAAAD,QAAA,gBAC3EzM,OAAA;gBAAK0M,SAAS,EAAC,cAAc;gBAAAD,QAAA,eAC3BzM,OAAA;kBAAK0M,SAAS,EAAC,wBAAwB;kBAAAD,QAAA,gBACrCzM,OAAA;oBAAK0M,SAAS,EAAE,GAAGtC,SAAS,CAACJ,QAAQ,CAAClG,KAAK,CAAC,oFAAqF;oBAAA2I,QAAA,eAC/HzM,OAAA;sBAAK0M,SAAS,EAAC,SAAS;sBAACC,IAAI,EAAC,MAAM;sBAACQ,MAAM,EAAC,cAAc;sBAACP,OAAO,EAAC,WAAW;sBAAAH,QAAA,eAC5EzM,OAAA;wBAAMoN,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAE,CAAE;wBAAC1D,CAAC,EAAC;sBAAgB;wBAAAmD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNlN,OAAA;oBAAAyM,QAAA,gBACEzM,OAAA;sBAAI0M,SAAS,EAAC,uCAAuC;sBAAAD,QAAA,EAAC;oBAEtD;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLlN,OAAA;sBAAK0M,SAAS,EAAC,yCAAyC;sBAAAD,QAAA,gBACtDzM,OAAA;wBAAK0M,SAAS,EAAC,cAAc;wBAACC,IAAI,EAAC,MAAM;wBAACQ,MAAM,EAAC,cAAc;wBAACP,OAAO,EAAC,WAAW;wBAAAH,QAAA,eACjFzM,OAAA;0BAAMoN,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,WAAW,EAAE,CAAE;0BAAC1D,CAAC,EAAC;wBAA4B;0BAAAmD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjG,CAAC,uFAER;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENlN,OAAA;gBAAK0M,SAAS,EAAC,6BAA6B;gBAAAD,QAAA,gBAE1CzM,OAAA;kBAAK0M,SAAS,EAAE,qBAAqBpC,cAAc,CAACN,QAAQ,CAAClG,KAAK,CAAC,2GAA4G;kBAAA2I,QAAA,gBAC7KzM,OAAA;oBAAK0M,SAAS,EAAE,gCAAgCzC,aAAa,CAACD,QAAQ,CAAClG,KAAK,CAAC,EAAG;oBAAA2I,QAAA,GAC7EzC,QAAQ,CAAClG,KAAK,EAAC,GAClB;kBAAA;oBAAAiJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNlN,OAAA;oBAAK0M,SAAS,EAAC,0CAA0C;oBAAAD,QAAA,EAAC;kBAAS;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACzElN,OAAA;oBAAK0M,SAAS,EAAC,qCAAqC;oBAAAD,QAAA,eAClDzM,OAAA;sBACE0M,SAAS,EAAE,iDAAiDrC,mBAAmB,CAACL,QAAQ,CAAClG,KAAK,CAAC,EAAG;sBAClGyJ,KAAK,EAAE;wBAAEC,KAAK,EAAE,GAAGxD,QAAQ,CAAClG,KAAK;sBAAI;oBAAE;sBAAAiJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNlN,OAAA;kBAAK0M,SAAS,EAAC,aAAa;kBAAAD,QAAA,gBAC1BzM,OAAA;oBAAK0M,SAAS,EAAE,uEACd1C,QAAQ,CAAClG,KAAK,IAAI,EAAE,GAAG,6BAA6B,GACpDkG,QAAQ,CAAClG,KAAK,IAAI,EAAE,GAAG,+BAA+B,GACtD,yBAAyB,EACxB;oBAAA2I,QAAA,EACAzC,QAAQ,CAAClG,KAAK,IAAI,EAAE,GAAG,WAAW,GAAGkG,QAAQ,CAAClG,KAAK,IAAI,EAAE,GAAG,MAAM,GAAG;kBAAmB;oBAAAiJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtF,CAAC,eACNlN,OAAA;oBAAK0M,SAAS,EAAC,4BAA4B;oBAAAD,QAAA,EACxCzC,QAAQ,CAAClG,KAAK,IAAI,EAAE,GAAG,mCAAmC,GAC1DkG,QAAQ,CAAClG,KAAK,IAAI,EAAE,GAAG,mCAAmC,GAC1D;kBAAgC;oBAAAiJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL,CAAA9M,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEqN,kBAAkB,kBACzBzN,OAAA;UAAK0M,SAAS,EAAC,KAAK;UAAAD,QAAA,gBAElBzM,OAAA;YAAK0M,SAAS,EAAC,kBAAkB;YAAAD,QAAA,gBAC/BzM,OAAA;cAAI0M,SAAS,EAAC,0CAA0C;cAAAD,QAAA,EAAC;YAAkB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChFlN,OAAA;cAAG0M,SAAS,EAAC,eAAe;cAAAD,QAAA,EAAC;YAAiE;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/F,CAAC,eAGNlN,OAAA;YAAK0M,SAAS,EAAC,4CAA4C;YAAAD,QAAA,gBAEzDzM,OAAA;cAAK0M,SAAS,EAAC,uCAAuC;cAAAD,QAAA,gBACpDzM,OAAA;gBAAK0M,SAAS,EAAC,uCAAuC;gBAAAD,QAAA,GACnD,EAAAjM,qBAAA,GAAAJ,MAAM,CAACqN,kBAAkB,CAACC,oBAAoB,cAAAlN,qBAAA,uBAA9CA,qBAAA,CAAgDmN,mBAAmB,KAAI,CAAC,EAAC,GAC5E;cAAA;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlN,OAAA;gBAAK0M,SAAS,EAAC,4BAA4B;gBAAAD,QAAA,EAAC;cAAc;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChElN,OAAA;gBAAK0M,SAAS,EAAC,qCAAqC;gBAAAD,QAAA,eAClDzM,OAAA;kBACE0M,SAAS,EAAC,2DAA2D;kBACrEa,KAAK,EAAE;oBAAEC,KAAK,EAAE,GAAG,EAAA/M,sBAAA,GAAAL,MAAM,CAACqN,kBAAkB,CAACC,oBAAoB,cAAAjN,sBAAA,uBAA9CA,sBAAA,CAAgDkN,mBAAmB,KAAI,CAAC;kBAAI;gBAAE;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7F;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNlN,OAAA;cAAK0M,SAAS,EAAC,kEAAkE;cAAAD,QAAA,gBAC/EzM,OAAA;gBAAK0M,SAAS,EAAC,uCAAuC;gBAAAD,QAAA,gBACpDzM,OAAA;kBAAK0M,SAAS,EAAC,6BAA6B;kBAACC,IAAI,EAAC,MAAM;kBAACQ,MAAM,EAAC,cAAc;kBAACP,OAAO,EAAC,WAAW;kBAAAH,QAAA,eAChGzM,OAAA;oBAAMoN,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAAC1D,CAAC,EAAC;kBAAgB;oBAAAmD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrF,CAAC,eACNlN,OAAA;kBAAK0M,SAAS,EAAC,mCAAmC;kBAAAD,QAAA,GAC/C,EAAA/L,sBAAA,GAAAN,MAAM,CAACqN,kBAAkB,CAACG,qBAAqB,cAAAlN,sBAAA,uBAA/CA,sBAAA,CAAiDiN,mBAAmB,KAAI,CAAC,EAAC,GAC7E;gBAAA;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlN,OAAA;gBAAK0M,SAAS,EAAC,yCAAyC;gBAAAD,QAAA,EAAC;cAAe;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9ElN,OAAA;gBAAK0M,SAAS,EAAC,sCAAsC;gBAAAD,QAAA,eACnDzM,OAAA;kBACE0M,SAAS,EAAC,4DAA4D;kBACtEa,KAAK,EAAE;oBAAEC,KAAK,EAAE,GAAG,EAAA7M,sBAAA,GAAAP,MAAM,CAACqN,kBAAkB,CAACG,qBAAqB,cAAAjN,sBAAA,uBAA/CA,sBAAA,CAAiDgN,mBAAmB,KAAI,CAAC;kBAAI;gBAAE;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9F;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNlN,OAAA;cAAK0M,SAAS,EAAC,uCAAuC;cAAAD,QAAA,gBACpDzM,OAAA;gBAAK0M,SAAS,EAAE,2BAA2BtM,MAAM,CAACqN,kBAAkB,CAACI,iBAAiB,IAAI,CAAC,GAAG,eAAe,GAAG,iBAAiB,EAAG;gBAAApB,QAAA,GACjIrM,MAAM,CAACqN,kBAAkB,CAACI,iBAAiB,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAEzN,MAAM,CAACqN,kBAAkB,CAACI,iBAAiB,EAAC,GAC5G;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlN,OAAA;gBAAK0M,SAAS,EAAC,4BAA4B;gBAAAD,QAAA,EAAC;cAAW;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7DlN,OAAA;gBAAK0M,SAAS,EAAC,wDAAwD;gBAAAD,QAAA,gBACrEzM,OAAA;kBAAK0M,SAAS,EAAC,4BAA4B;kBAACC,IAAI,EAAC,MAAM;kBAACQ,MAAM,EAAC,cAAc;kBAACP,OAAO,EAAC,WAAW;kBAAAH,QAAA,eAC/FzM,OAAA;oBAAMoN,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAAC1D,CAAC,EAAC;kBAAgC;oBAAAmD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrG,CAAC,sBAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGL,EAAAtM,sBAAA,GAAAR,MAAM,CAACqN,kBAAkB,CAACG,qBAAqB,cAAAhN,sBAAA,uBAA/CA,sBAAA,CAAiDkN,SAAS,kBACzD9N,OAAA;YAAK0M,SAAS,EAAC,2BAA2B;YAAAD,QAAA,gBACxCzM,OAAA;cAAI0M,SAAS,EAAC,wCAAwC;cAAAD,QAAA,EAAC;YAAqB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjFlN,OAAA;cAAK0M,SAAS,EAAC,sDAAsD;cAAAD,QAAA,EAClE1I,MAAM,CAACgK,OAAO,CAAC3N,MAAM,CAACqN,kBAAkB,CAACG,qBAAqB,CAACE,SAAS,CAAC,CAACjI,GAAG,CAAC,CAAC,CAAC0G,GAAG,EAAEyB,IAAI,CAAC,kBACzFhO,OAAA;gBAAe0M,SAAS,EAAC,kFAAkF;gBAAAD,QAAA,gBACzGzM,OAAA;kBAAM0M,SAAS,EAAC,8CAA8C;kBAAAD,QAAA,EAC3DF,GAAG,CAACzJ,OAAO,CAAC,GAAG,EAAE,GAAG;gBAAC;kBAAAiK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eACPlN,OAAA;kBAAK0M,SAAS,EAAC,mBAAmB;kBAAAD,QAAA,gBAChCzM,OAAA;oBAAM0M,SAAS,EAAE,8BAA8BsB,IAAI,CAAClK,KAAK,IAAI,EAAE,GAAG,gBAAgB,GAAGkK,IAAI,CAAClK,KAAK,IAAI,EAAE,GAAG,iBAAiB,GAAG,cAAc,EAAG;oBAAA2I,QAAA,GAC1IuB,IAAI,CAAClK,KAAK,EAAC,GACd;kBAAA;oBAAAiJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACPlN,OAAA;oBAAK0M,SAAS,EAAC,mCAAmC;oBAAAD,QAAA,eAChDzM,OAAA;sBACE0M,SAAS,EAAE,oBACTsB,IAAI,CAAClK,KAAK,IAAI,EAAE,GAAG,cAAc,GACjCkK,IAAI,CAAClK,KAAK,IAAI,EAAE,GAAG,eAAe,GAAG,YAAY,EAChD;sBACHyJ,KAAK,EAAE;wBAAEC,KAAK,EAAE,GAAGQ,IAAI,CAAClK,KAAK;sBAAI;oBAAE;sBAAAiJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA,GAjBEX,GAAG;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAkBR,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlN,OAAA;MAAK0M,SAAS,EAAC,MAAM;MAAAD,QAAA,eACnBzM,OAAA;QAAK0M,SAAS,EAAC,sEAAsE;QAAAD,QAAA,gBAEnFzM,OAAA;UAAK0M,SAAS,EAAC,qCAAqC;UAAAD,QAAA,eAClDzM,OAAA;YAAK0M,SAAS,EAAC,MAAM;YAAAD,QAAA,eACnBzM,OAAA;cAAK0M,SAAS,EAAC,gBAAgB;cAAAD,QAAA,gBAC7BzM,OAAA;gBACEiO,OAAO,EAAEA,CAAA,KAAM/M,YAAY,CAAC,WAAW,CAAE;gBACzCwL,SAAS,EAAE,8DACTzL,SAAS,KAAK,WAAW,GACrB,mCAAmC,GACnC,4EAA4E,EAC/E;gBAAAwL,QAAA,eAEHzM,OAAA;kBAAM0M,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,gBAC3CzM,OAAA;oBAAK0M,SAAS,EAAC,SAAS;oBAACC,IAAI,EAAC,MAAM;oBAACQ,MAAM,EAAC,cAAc;oBAACP,OAAO,EAAC,WAAW;oBAAAH,QAAA,eAC5EzM,OAAA;sBAAMoN,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAAC1D,CAAC,EAAC;oBAA+C;sBAAAmD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpH,CAAC,eACNlN,OAAA;oBAAAyM,QAAA,EAAM;kBAAgB;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAETlN,OAAA;gBACEiO,OAAO,EAAEA,CAAA,KAAM/M,YAAY,CAAC,UAAU,CAAE;gBACxCwL,SAAS,EAAE,8DACTzL,SAAS,KAAK,UAAU,GACpB,mCAAmC,GACnC,4EAA4E,EAC/E;gBAAAwL,QAAA,eAEHzM,OAAA;kBAAM0M,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,gBAC3CzM,OAAA;oBAAK0M,SAAS,EAAC,SAAS;oBAACC,IAAI,EAAC,MAAM;oBAACQ,MAAM,EAAC,cAAc;oBAACP,OAAO,EAAC,WAAW;oBAAAH,QAAA,eAC5EzM,OAAA;sBAAMoN,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAAC1D,CAAC,EAAC;oBAAsM;sBAAAmD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3Q,CAAC,eACNlN,OAAA;oBAAAyM,QAAA,EAAM;kBAAY;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGLjM,SAAS,KAAK,WAAW,iBACxBjB,OAAA;UAAK0M,SAAS,EAAC,oCAAoC;UAAAD,QAAA,gBACjDzM,OAAA;YAAI0M,SAAS,EAAC,4DAA4D;YAAAD,QAAA,gBACxEzM,OAAA;cAAK0M,SAAS,EAAC,4BAA4B;cAACC,IAAI,EAAC,MAAM;cAACQ,MAAM,EAAC,cAAc;cAACP,OAAO,EAAC,WAAW;cAAAH,QAAA,eAC/FzM,OAAA;gBAAMoN,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAAC1D,CAAC,EAAC;cAAsM;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3Q,CAAC,2BAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAELlN,OAAA;YAAK0M,SAAS,EAAC,sDAAsD;YAAAD,QAAA,EAClE,CAAC,MAAM;cACN;cACA,MAAMxJ,YAAY,GAAG0D,eAAe,CAAC,CAAC;cACtC,MAAM3D,aAAa,GAAGyB,gBAAgB,CAAC,CAAC;cACxC,MAAMyJ,cAAc,GAAGjL,YAAY,CAACW,MAAM;cAC1C,MAAMyC,eAAe,GAAGrD,aAAa,CAACY,MAAM;cAC5C,MAAMqF,IAAI,GAAG5C,eAAe,GAAG6H,cAAc;cAE7C,oBACElO,OAAA,CAAAE,SAAA;gBAAAuM,QAAA,gBAEEzM,OAAA;kBAAK0M,SAAS,EAAC,uCAAuC;kBAAAD,QAAA,gBACpDzM,OAAA;oBAAK0M,SAAS,EAAC,wCAAwC;oBAAAD,QAAA,EAAC;kBAAe;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC7ElN,OAAA;oBAAK0M,SAAS,EAAC,uCAAuC;oBAAAD,QAAA,EACnDyB,cAAc,GAAG,CAAC,GAAGA,cAAc,CAACC,cAAc,CAAC,CAAC,GAAG;kBAAK;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1D,CAAC,eACNlN,OAAA;oBAAK0M,SAAS,EAAC,uBAAuB;oBAAAD,QAAA,EAAC;kBAAU;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC,eAGNlN,OAAA;kBAAK0M,SAAS,EAAC,uCAAuC;kBAAAD,QAAA,gBACpDzM,OAAA;oBAAK0M,SAAS,EAAC,wCAAwC;oBAAAD,QAAA,EAAC;kBAAgB;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC9ElN,OAAA;oBAAK0M,SAAS,EAAC,uCAAuC;oBAAAD,QAAA,EACnDpG,eAAe,GAAG,CAAC,GAAGA,eAAe,CAAC8H,cAAc,CAAC,CAAC,GAAG;kBAAK;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5D,CAAC,eACNlN,OAAA;oBAAK0M,SAAS,EAAC,uBAAuB;oBAAAD,QAAA,EAAC;kBAAU;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC,eAGNlN,OAAA;kBAAK0M,SAAS,EAAC,uCAAuC;kBAAAD,QAAA,gBACpDzM,OAAA;oBAAK0M,SAAS,EAAC,wCAAwC;oBAAAD,QAAA,EAAC;kBAAM;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACpElN,OAAA;oBAAK0M,SAAS,EAAC,yBAAyB;oBAAAD,QAAA,EACrCyB,cAAc,KAAK,CAAC,IAAI7H,eAAe,KAAK,CAAC,gBAC5CrG,OAAA;sBAAM0M,SAAS,EAAC,eAAe;sBAAAD,QAAA,EAAC;oBAAG;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,gBAE1ClN,OAAA;sBAAM0M,SAAS,EAAEzD,IAAI,IAAI,CAAC,GAAG,eAAe,GAAG,iBAAkB;sBAAAwD,QAAA,GAC9DxD,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAEA,IAAI,CAACkF,cAAc,CAAC,CAAC;oBAAA;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC;kBACP;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACNlN,OAAA;oBAAK0M,SAAS,EAAC,uBAAuB;oBAAAD,QAAA,EAAC;kBAAU;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC,eAGNlN,OAAA;kBAAK0M,SAAS,EAAC,wCAAwC;kBAAAD,QAAA,gBACrDzM,OAAA;oBAAK0M,SAAS,EAAC,wCAAwC;oBAAAD,QAAA,EAAC;kBAAiB;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC/ElN,OAAA;oBAAK0M,SAAS,EAAC,yBAAyB;oBAAAD,QAAA,EACrCyB,cAAc,KAAK,CAAC,IAAI7H,eAAe,KAAK,CAAC,gBAC5CrG,OAAA;sBAAM0M,SAAS,EAAC,eAAe;sBAAAD,QAAA,EAAC;oBAAG;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,GACxC,CAAC,MAAM;sBACT,IAAIkB,WAAW,GAAG,CAAC;sBACnB,IAAI/H,eAAe,GAAG6H,cAAc,EAAE;wBACpCE,WAAW,GAAI,CAAC/H,eAAe,GAAG6H,cAAc,IAAIA,cAAc,GAAI,GAAG;sBAC3E,CAAC,MAAM;wBACLE,WAAW,GAAI,CAACF,cAAc,GAAG7H,eAAe,IAAI6H,cAAc,GAAI,GAAG;sBAC3E;sBACAE,WAAW,GAAG9J,IAAI,CAACuE,GAAG,CAAC,CAAC,EAAEuF,WAAW,CAAC;sBACtC,MAAMC,UAAU,GAAGD,WAAW,GAAG,EAAE,GAAG,gBAAgB,GAAGA,WAAW,GAAG,CAAC,GAAG,eAAe,GAAG,iBAAiB;sBAC9G,oBACEpO,OAAA;wBAAM0M,SAAS,EAAE2B,UAAW;wBAAA5B,QAAA,GACzB2B,WAAW,CAACE,OAAO,CAAC,CAAC,CAAC,EAAC,GAC1B;sBAAA;wBAAAvB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAEX,CAAC,EAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eACNlN,OAAA;oBAAK0M,SAAS,EAAC,uBAAuB;oBAAAD,QAAA,EAAC;kBAAU;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC;cAAA,eACN,CAAC;YAEP,CAAC,EAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGDlN,OAAA;UAAK0M,SAAS,EAAC,KAAK;UAAAD,QAAA,GAEjBxL,SAAS,KAAK,WAAW,iBACxBjB,OAAA;YAAAyM,QAAA,gBACEzM,OAAA;cAAK0M,SAAS,EAAC,wCAAwC;cAAAD,QAAA,gBACrDzM,OAAA;gBAAK0M,SAAS,EAAC,qCAAqC;gBAAAD,QAAA,gBAClDzM,OAAA;kBAAM0M,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAsB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC7DlN,OAAA;kBAAK0M,SAAS,EAAC,gBAAgB;kBAAAD,QAAA,gBAC7BzM,OAAA;oBACEiO,OAAO,EAAEA,CAAA,KAAMvM,WAAW,CAAC,KAAK,CAAE;oBAClCgL,SAAS,EAAE,8DACTjL,QAAQ,KAAK,KAAK,GACd,+BAA+B,GAC/B,6CAA6C,EAChD;oBAAAgL,QAAA,EACJ;kBAED;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTlN,OAAA;oBACEiO,OAAO,EAAEA,CAAA,KAAMvM,WAAW,CAAC,UAAU,CAAE;oBACvCgL,SAAS,EAAE,8DACTjL,QAAQ,KAAK,UAAU,GACnB,+BAA+B,GAC/B,6CAA6C,EAChD;oBAAAgL,QAAA,EACJ;kBAED;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTlN,OAAA;oBACEiO,OAAO,EAAEA,CAAA,KAAMvM,WAAW,CAAC,MAAM,CAAE;oBACnCgL,SAAS,EAAE,8DACTjL,QAAQ,KAAK,MAAM,GACf,+BAA+B,GAC/B,6CAA6C,EAChD;oBACH8M,QAAQ,EAAE,EAACnO,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEoO,kBAAkB,CAAC;oBAAA/B,QAAA,EACvC;kBAED;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlN,OAAA;gBAAK0M,SAAS,EAAC,0BAA0B;gBAAAD,QAAA,gBACvCzM,OAAA;kBACEiO,OAAO,EAAEA,CAAA,KAAMzM,qBAAqB,CAAC,CAACD,kBAAkB,CAAE;kBAC1DmL,SAAS,EAAC,kIAAkI;kBAAAD,QAAA,gBAE5IzM,OAAA;oBAAK0M,SAAS,EAAC,cAAc;oBAACC,IAAI,EAAC,MAAM;oBAACQ,MAAM,EAAC,cAAc;oBAACP,OAAO,EAAC,WAAW;oBAAAH,QAAA,eACjFzM,OAAA;sBAAMoN,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAAC1D,CAAC,EAAC;oBAAiI;sBAAAmD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtM,CAAC,qBAEN,eAAAlN,OAAA;oBAAK0M,SAAS,EAAC,cAAc;oBAACC,IAAI,EAAC,MAAM;oBAACQ,MAAM,EAAC,cAAc;oBAACP,OAAO,EAAC,WAAW;oBAAAH,QAAA,eACjFzM,OAAA;sBAAMoN,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAAC1D,CAAC,EAAC;oBAAgB;sBAAAmD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,EAER3L,kBAAkB,iBACjBvB,OAAA;kBAAK0M,SAAS,EAAC,sFAAsF;kBAAAD,QAAA,eACnGzM,OAAA;oBAAK0M,SAAS,EAAC,MAAM;oBAAAD,QAAA,GAClB,CAAArM,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEqO,iBAAiB,MAAIrO,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEsO,YAAY,kBAChD1O,OAAA,CAAAE,SAAA;sBAAAuM,QAAA,gBACEzM,OAAA;wBACEiO,OAAO,EAAEA,CAAA,KAAM;0BACb1D,kBAAkB,CAAC,CAAC;0BACpB/I,qBAAqB,CAAC,KAAK,CAAC;wBAC9B,CAAE;wBACF+M,QAAQ,EAAElN,eAAgB;wBAC1BqL,SAAS,EAAC,iHAAiH;wBAAAD,QAAA,eAE3HzM,OAAA;0BAAK0M,SAAS,EAAC,mBAAmB;0BAAAD,QAAA,gBAChCzM,OAAA;4BAAK0M,SAAS,EAAC,wEAAwE;4BAAAD,QAAA,EACpFpL,eAAe,gBACdrB,OAAA;8BAAK0M,SAAS,EAAC,sCAAsC;8BAACC,IAAI,EAAC,MAAM;8BAACC,OAAO,EAAC,WAAW;8BAAAH,QAAA,gBACnFzM,OAAA;gCAAQ0M,SAAS,EAAC,YAAY;gCAACiC,EAAE,EAAC,IAAI;gCAACC,EAAE,EAAC,IAAI;gCAACC,CAAC,EAAC,IAAI;gCAAC1B,MAAM,EAAC,cAAc;gCAACG,WAAW,EAAC;8BAAG;gCAAAP,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAS,CAAC,eACrGlN,OAAA;gCAAM0M,SAAS,EAAC,YAAY;gCAACC,IAAI,EAAC,cAAc;gCAAC/C,CAAC,EAAC;8BAAiH;gCAAAmD,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACzK,CAAC,gBAENlN,OAAA;8BAAK0M,SAAS,EAAC,yBAAyB;8BAACC,IAAI,EAAC,MAAM;8BAACQ,MAAM,EAAC,cAAc;8BAACP,OAAO,EAAC,WAAW;8BAAAH,QAAA,eAC5FzM,OAAA;gCAAMoN,aAAa,EAAC,OAAO;gCAACC,cAAc,EAAC,OAAO;gCAACC,WAAW,EAAE,CAAE;gCAAC1D,CAAC,EAAC;8BAAqF;gCAAAmD,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC1J;0BACN;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACE,CAAC,eACNlN,OAAA;4BAAAyM,QAAA,gBACEzM,OAAA;8BAAK0M,SAAS,EAAC,6BAA6B;8BAAAD,QAAA,EACzCpL,eAAe,GAAG,gBAAgB,GAAG;4BAA0B;8BAAA0L,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC7D,CAAC,eACNlN,OAAA;8BAAK0M,SAAS,EAAC,yBAAyB;8BAAAD,QAAA,EAAC;4BAA8B;8BAAAM,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC1E,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC,eACTlN,OAAA;wBAAK0M,SAAS,EAAC;sBAA+B;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA,eACrD,CACH,eAEDlN,OAAA;sBACEiO,OAAO,EAAEA,CAAA,KAAM;wBACbxO,WAAW,CAACgF,gBAAgB,CAAC,CAAC,CAAC;wBAC/BjD,qBAAqB,CAAC,KAAK,CAAC;sBAC9B,CAAE;sBACFkL,SAAS,EAAC,8DAA8D;sBAAAD,QAAA,eAExEzM,OAAA;wBAAK0M,SAAS,EAAC,mBAAmB;wBAAAD,QAAA,gBAChCzM,OAAA;0BAAK0M,SAAS,EAAC,qEAAqE;0BAAAD,QAAA,eAClFzM,OAAA;4BAAK0M,SAAS,EAAC,sBAAsB;4BAACC,IAAI,EAAC,MAAM;4BAACQ,MAAM,EAAC,cAAc;4BAACP,OAAO,EAAC,WAAW;4BAAAH,QAAA,eACzFzM,OAAA;8BAAMoN,aAAa,EAAC,OAAO;8BAACC,cAAc,EAAC,OAAO;8BAACC,WAAW,EAAE,CAAE;8BAAC1D,CAAC,EAAC;4BAAiI;8BAAAmD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACtM;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACNlN,OAAA;0BAAAyM,QAAA,gBACEzM,OAAA;4BAAK0M,SAAS,EAAC,2BAA2B;4BAAAD,QAAA,EAAC;0BAAU;4BAAAM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAC3DlN,OAAA;4BAAK0M,SAAS,EAAC,uBAAuB;4BAAAD,QAAA,EAAC;0BAAgC;4BAAAM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC1E,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC,eAETlN,OAAA;sBACEiO,OAAO,EAAEA,CAAA,KAAM;wBACbvO,YAAY,CAAC+E,gBAAgB,CAAC,CAAC,CAAC;wBAChCjD,qBAAqB,CAAC,KAAK,CAAC;sBAC9B,CAAE;sBACFkL,SAAS,EAAC,+DAA+D;sBAAAD,QAAA,eAEzEzM,OAAA;wBAAK0M,SAAS,EAAC,mBAAmB;wBAAAD,QAAA,gBAChCzM,OAAA;0BAAK0M,SAAS,EAAC,sEAAsE;0BAAAD,QAAA,eACnFzM,OAAA;4BAAK0M,SAAS,EAAC,uBAAuB;4BAACC,IAAI,EAAC,MAAM;4BAACQ,MAAM,EAAC,cAAc;4BAACP,OAAO,EAAC,WAAW;4BAAAH,QAAA,eAC1FzM,OAAA;8BAAMoN,aAAa,EAAC,OAAO;8BAACC,cAAc,EAAC,OAAO;8BAACC,WAAW,EAAE,CAAE;8BAAC1D,CAAC,EAAC;4BAAsH;8BAAAmD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC3L;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACNlN,OAAA;0BAAAyM,QAAA,gBACEzM,OAAA;4BAAK0M,SAAS,EAAC,2BAA2B;4BAAAD,QAAA,EAAC;0BAAW;4BAAAM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAC5DlN,OAAA;4BAAK0M,SAAS,EAAC,uBAAuB;4BAAAD,QAAA,EAAC;0BAAgC;4BAAAM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC1E,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC,eAETlN,OAAA;sBAAK0M,SAAS,EAAC;oBAA+B;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAErDlN,OAAA;sBACEiO,OAAO,EAAEA,CAAA,KAAM;wBACbrO,WAAW,CAAC6E,gBAAgB,CAAC,CAAC,CAAC;wBAC/BjD,qBAAqB,CAAC,KAAK,CAAC;sBAC9B,CAAE;sBACFkL,SAAS,EAAC,+DAA+D;sBAAAD,QAAA,eAEzEzM,OAAA;wBAAK0M,SAAS,EAAC,mBAAmB;wBAAAD,QAAA,gBAChCzM,OAAA;0BAAK0M,SAAS,EAAC,sEAAsE;0BAAAD,QAAA,eACnFzM,OAAA;4BAAK0M,SAAS,EAAC,uBAAuB;4BAACC,IAAI,EAAC,MAAM;4BAACQ,MAAM,EAAC,cAAc;4BAACP,OAAO,EAAC,WAAW;4BAAAH,QAAA,eAC1FzM,OAAA;8BAAMoN,aAAa,EAAC,OAAO;8BAACC,cAAc,EAAC,OAAO;8BAACC,WAAW,EAAE,CAAE;8BAAC1D,CAAC,EAAC;4BAA8K;8BAAAmD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACnP;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACNlN,OAAA;0BAAAyM,QAAA,gBACEzM,OAAA;4BAAK0M,SAAS,EAAC,2BAA2B;4BAAAD,QAAA,EAAC;0BAAY;4BAAAM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAC7DlN,OAAA;4BAAK0M,SAAS,EAAC,uBAAuB;4BAAAD,QAAA,EAAC;0BAA2B;4BAAAM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNlN,OAAA;cAAK0M,SAAS,EAAC,wEAAwE;cAAAD,QAAA,GAEpFhL,QAAQ,KAAK,KAAK,iBACjBzB,OAAA,CAAAE,SAAA;gBAAAuM,QAAA,EACG,CAACqC,MAAA,IAAM;kBACN,MAAMjM,IAAI,GAAG4B,gBAAgB,CAAC,CAAC,IAAI,wCAAwC;kBAC3E,MAAMsK,KAAK,GAAGlM,IAAI,CAACkE,KAAK,CAAC,IAAI,CAAC;kBAC9B,MAAMiI,QAAQ,IAAAF,MAAA,GAAGC,KAAK,CAACA,KAAK,CAACnL,MAAM,GAAG,CAAC,CAAC,cAAAkL,MAAA,uBAAvBA,MAAA,CAAyBtJ,IAAI,CAAC,CAAC;kBAChD,MAAMiE,cAAc,GAAG,gEAAgE,CAACnG,IAAI,CAAC0L,QAAQ,CAAC;kBACtG,IAAIvF,cAAc,EAAE;oBAClB,MAAMwF,QAAQ,GAAGF,KAAK,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACzI,IAAI,CAAC,IAAI,CAAC;oBAC9C,oBACEzG,OAAA,CAAAE,SAAA;sBAAAuM,QAAA,gBACEzM,OAAA;wBAAK0M,SAAS,EAAC,0EAA0E;wBAAAD,QAAA,EACtFwC;sBAAQ;wBAAAlC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC,eACNlN,OAAA;wBAAK0M,SAAS,EAAC;sBAA+B;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACrDlN,OAAA;wBAAK0M,SAAS,EAAC,uHAAuH;wBAACa,KAAK,EAAE;0BAAC4B,SAAS,EAAC;wBAAQ,CAAE;wBAAA1C,QAAA,gBACjKzM,OAAA;0BAAM0M,SAAS,EAAC,gCAAgC;0BAAAD,QAAA,EAAC;wBAAc;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACtElN,OAAA;0BAAM0M,SAAS,EAAC,eAAe;0BAAAD,QAAA,EAAEuC;wBAAQ;0BAAAjC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9C,CAAC;oBAAA,eACN,CAAC;kBAEP,CAAC,MAAM;oBACL,oBACElN,OAAA;sBAAK0M,SAAS,EAAC,0EAA0E;sBAAAD,QAAA,EACtF5J;oBAAI;sBAAAkK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC;kBAEV;gBACF,CAAC,EAAE;cAAC,gBACJ,CACH,EACAzL,QAAQ,KAAK,UAAU,iBACtBzB,OAAA;gBACE0M,SAAS,EAAC,sDAAsD;gBAChE0C,uBAAuB,EAAE;kBACvBC,MAAM,EAAEvG,6BAA6B,CAACrE,gBAAgB,CAAC,CAAC;gBAC1D;cAAE;gBAAAsI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACF,EACAzL,QAAQ,KAAK,MAAM,KAAIrB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEoO,kBAAkB,kBAChDxO,OAAA;gBAAK0M,SAAS,EAAC,wBAAwB;gBAAAD,QAAA,gBACrCzM,OAAA;kBAAK0M,SAAS,EAAC,mBAAmB;kBAAAD,QAAA,gBAChCzM,OAAA;oBACEiO,OAAO,EAAEA,CAAA,KAAMrM,kBAAkB,CAAC,QAAQ,CAAE;oBAC5C8K,SAAS,EAAE,gEACT/K,eAAe,KAAK,QAAQ,GACxB,wBAAwB,GACxB,6CAA6C,EAChD;oBAAA8K,QAAA,EACJ;kBAED;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTlN,OAAA;oBACEiO,OAAO,EAAEA,CAAA,KAAMrM,kBAAkB,CAAC,QAAQ,CAAE;oBAC5C8K,SAAS,EAAE,gEACT/K,eAAe,KAAK,QAAQ,GACxB,wBAAwB,GACxB,6CAA6C,EAChD;oBAAA8K,QAAA,EACJ;kBAED;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTlN,OAAA;oBACEiO,OAAO,EAAEA,CAAA,KAAMrM,kBAAkB,CAAC,UAAU,CAAE;oBAC9C8K,SAAS,EAAE,2DACT/K,eAAe,KAAK,UAAU,GAC1B,yBAAyB,GACzB,6CAA6C,EAChD;oBAAA8K,QAAA,EACJ;kBAED;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,EAELvL,eAAe,KAAK,QAAQ,iBAC3B3B,OAAA;kBAAK0M,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,eACtCzM,OAAA;oBACEsP,GAAG,EAAE,sDAAsDC,kBAAkB,CAACnP,MAAM,CAACoO,kBAAkB,CAAC,EAAG;oBAC3GhB,KAAK,EAAC,MAAM;oBACZgC,MAAM,EAAC,KAAK;oBACZC,WAAW,EAAC,GAAG;oBACfC,KAAK,EAAC,uBAAuB;oBAC7BnC,KAAK,EAAE;sBAAEoC,UAAU,EAAE,MAAM;sBAAEC,YAAY,EAAE;oBAAM,CAAE;oBACnDC,OAAO,EAAEA,CAAA,KAAM/N,mBAAmB,CAAC,2BAA2B;kBAAE;oBAAAiL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CACN,EAEAvL,eAAe,KAAK,QAAQ,iBAC3B3B,OAAA;kBAAK0M,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,eACtCzM,OAAA;oBACEsP,GAAG,EAAE,qCAAqCC,kBAAkB,CAACnP,MAAM,CAACoO,kBAAkB,CAAC,gBAAiB;oBACxGhB,KAAK,EAAC,MAAM;oBACZgC,MAAM,EAAC,KAAK;oBACZC,WAAW,EAAC,GAAG;oBACfC,KAAK,EAAC,qBAAqB;oBAC3BnC,KAAK,EAAE;sBAAEoC,UAAU,EAAE,MAAM;sBAAEC,YAAY,EAAE;oBAAM,CAAE;oBACnDC,OAAO,EAAEA,CAAA,KAAM/N,mBAAmB,CAAC,yBAAyB;kBAAE;oBAAAiL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CACN,EAEAvL,eAAe,KAAK,UAAU,iBAC7B3B,OAAA;kBAAK0M,SAAS,EAAC,kEAAkE;kBAAAD,QAAA,gBAC/EzM,OAAA;oBAAK0M,SAAS,EAAC,MAAM;oBAAAD,QAAA,gBACnBzM,OAAA;sBAAK0M,SAAS,EAAC,sCAAsC;sBAACC,IAAI,EAAC,MAAM;sBAACQ,MAAM,EAAC,cAAc;sBAACP,OAAO,EAAC,WAAW;sBAAAH,QAAA,eACzGzM,OAAA;wBAAMoN,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAE,CAAE;wBAAC1D,CAAC,EAAC;sBAAiI;wBAAAmD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtM,CAAC,eACNlN,OAAA;sBAAI0M,SAAS,EAAC,0CAA0C;sBAAAD,QAAA,EAAC;oBAAW;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACzElN,OAAA;sBAAG0M,SAAS,EAAC,oBAAoB;sBAAAD,QAAA,GAAC,4GAEhC,eAAAzM,OAAA;wBAAA+M,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,oGAER;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eACNlN,OAAA;oBACE8L,IAAI,EAAE1L,MAAM,CAACoO,kBAAmB;oBAChCzC,QAAQ,EAAC,uBAAuB;oBAChCW,SAAS,EAAC,sHAAsH;oBAAAD,QAAA,gBAEhIzM,OAAA;sBAAK0M,SAAS,EAAC,cAAc;sBAACC,IAAI,EAAC,MAAM;sBAACQ,MAAM,EAAC,cAAc;sBAACP,OAAO,EAAC,WAAW;sBAAAH,QAAA,eACjFzM,OAAA;wBAAMoN,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAE,CAAE;wBAAC1D,CAAC,EAAC;sBAAiI;wBAAAmD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtM,CAAC,wDAER;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CACN,EAEArL,gBAAgB,iBACf7B,OAAA;kBAAK0M,SAAS,EAAC,2DAA2D;kBAAAD,QAAA,eACxEzM,OAAA;oBAAK0M,SAAS,EAAC,MAAM;oBAAAD,QAAA,gBACnBzM,OAAA;sBAAK0M,SAAS,EAAC,8BAA8B;sBAACC,IAAI,EAAC,MAAM;sBAACQ,MAAM,EAAC,cAAc;sBAACP,OAAO,EAAC,WAAW;sBAAAH,QAAA,eACjGzM,OAAA;wBAAMoN,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAE,CAAE;wBAAC1D,CAAC,EAAC;sBAAyI;wBAAAmD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9M,CAAC,eACNlN,OAAA;sBAAAyM,QAAA,gBACEzM,OAAA;wBAAG0M,SAAS,EAAC,6BAA6B;wBAAAD,QAAA,EAAC;sBAAI;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eACnDlN,OAAA;wBAAG0M,SAAS,EAAC,8BAA8B;wBAAAD,QAAA,EAAE5K;sBAAgB;wBAAAkL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/D,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EAGL,CAAA9M,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEqO,iBAAiB,MAAIrO,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE0P,iBAAiB,KAAI1P,MAAM,CAAC0P,iBAAiB,CAAClM,MAAM,GAAG,CAAC,iBAC5F5D,OAAA;cAAK0M,SAAS,EAAC,2DAA2D;cAAAD,QAAA,gBACxEzM,OAAA;gBAAI0M,SAAS,EAAC,8DAA8D;gBAAAD,QAAA,gBAC1EzM,OAAA;kBAAK0M,SAAS,EAAC,cAAc;kBAACC,IAAI,EAAC,MAAM;kBAACQ,MAAM,EAAC,cAAc;kBAACP,OAAO,EAAC,WAAW;kBAAAH,QAAA,eACjFzM,OAAA;oBAAMoN,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAAC1D,CAAC,EAAC;kBAA6I;oBAAAmD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClN,CAAC,mCAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLlN,OAAA;gBAAK0M,SAAS,EAAC,WAAW;gBAAAD,QAAA,EACvB,CAACrM,MAAM,CAAC0P,iBAAiB,IAAI,EAAE,EAAEjK,GAAG,CAAC,CAACkK,MAAM,EAAEhK,KAAK,kBAClD/F,OAAA;kBAAiB0M,SAAS,EAAC,kDAAkD;kBAAAD,QAAA,gBAC3EzM,OAAA;oBAAK0M,SAAS,EAAC,0CAA0C;oBAAAD,QAAA,GAAC,UAChD,EAAC1G,KAAK,GAAG,CAAC,EAAC,IAAE,EAACgK,MAAM,CAACC,aAAa;kBAAA;oBAAAjD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC,eACNlN,OAAA;oBAAK0M,SAAS,EAAC,uCAAuC;oBAAAD,QAAA,gBACpDzM,OAAA;sBAAAyM,QAAA,gBACEzM,OAAA;wBAAK0M,SAAS,EAAC,uCAAuC;wBAAAD,QAAA,EAAC;sBAAS;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACtElN,OAAA;wBAAK0M,SAAS,EAAC,oDAAoD;wBAAAD,QAAA,EAChEsD,MAAM,CAACnJ;sBAAa;wBAAAmG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNlN,OAAA;sBAAAyM,QAAA,gBACEzM,OAAA;wBAAK0M,SAAS,EAAC,yCAAyC;wBAAAD,QAAA,EAAC;sBAAU;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACzElN,OAAA;wBAAK0M,SAAS,EAAC,sDAAsD;wBAAAD,QAAA,EAClEsD,MAAM,CAAC9K;sBAAc;wBAAA8H,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GAjBEnH,KAAK;kBAAAgH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAkBV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,EAGAjM,SAAS,KAAK,UAAU,iBACvBjB,OAAA;YAAAyM,QAAA,gBACEzM,OAAA;cAAI0M,SAAS,EAAC,uCAAuC;cAAAD,QAAA,EAAC;YAA0B;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAEpF,CAAA9M,MAAM,aAANA,MAAM,wBAAAS,sBAAA,GAANT,MAAM,CAAEqN,kBAAkB,cAAA5M,sBAAA,wBAAAC,sBAAA,GAA1BD,sBAAA,CAA4B+M,qBAAqB,cAAA9M,sBAAA,uBAAjDA,sBAAA,CAAmDgN,SAAS,kBAC3D9N,OAAA;cAAAyM,QAAA,gBACEzM,OAAA;gBAAI0M,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAAwB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtFlN,OAAA;gBAAK0M,SAAS,EAAC,uCAAuC;gBAAAD,QAAA,EACnD1I,MAAM,CAACgK,OAAO,CAAC3N,MAAM,CAACqN,kBAAkB,CAACG,qBAAqB,CAACE,SAAS,CAAC,CAACjI,GAAG,CAAC,CAAC,CAAC0G,GAAG,EAAEyB,IAAI,CAAC,KAAK;kBAC9F,MAAMiC,YAAY,GAAG,CAAC;kBACtB,MAAMC,YAAY,GAAG,CAAC;kBACtB,MAAMC,eAAe,GAAGpO,eAAe,CAACwK,GAAG,CAAC,IAAI,KAAK;kBACrD,MAAM6D,eAAe,GAAGnO,eAAe,CAACsK,GAAG,CAAC,IAAI,KAAK;kBACrD,oBACEvM,OAAA;oBAAe0M,SAAS,EAAC,kCAAkC;oBAAAD,QAAA,gBACzDzM,OAAA;sBAAK0M,SAAS,EAAC,uCAAuC;sBAAAD,QAAA,gBACpDzM,OAAA;wBAAAyM,QAAA,gBACEzM,OAAA;0BAAI0M,SAAS,EAAC,wCAAwC;0BAAAD,QAAA,EACnDF,GAAG,CAACzJ,OAAO,CAAC,GAAG,EAAE,GAAG;wBAAC;0BAAAiK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACpB,CAAC,eACLlN,OAAA;0BAAM0M,SAAS,EAAC,mCAAmC;0BAAAD,QAAA,GAAC,UAAQ,EAACuB,IAAI,CAACqC,MAAM;wBAAA;0BAAAtD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7E,CAAC,eACNlN,OAAA;wBAAK0M,SAAS,EAAE,qBAAqBsB,IAAI,CAAClK,KAAK,IAAI,EAAE,GAAG,gBAAgB,GAAGkK,IAAI,CAAClK,KAAK,IAAI,EAAE,GAAG,iBAAiB,GAAG,cAAc,EAAG;wBAAA2I,QAAA,GAAEuB,IAAI,CAAClK,KAAK,EAAC,GAAC;sBAAA;wBAAAiJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpJ,CAAC,eAENlN,OAAA;sBAAK0M,SAAS,EAAC,0CAA0C;sBAAAD,QAAA,eACvDzM,OAAA;wBACE0M,SAAS,EAAE,gDACTsB,IAAI,CAAClK,KAAK,IAAI,EAAE,GAAG,cAAc,GACjCkK,IAAI,CAAClK,KAAK,IAAI,EAAE,GAAG,eAAe,GAAG,YAAY,EAChD;wBACHyJ,KAAK,EAAE;0BAAEC,KAAK,EAAE,GAAGQ,IAAI,CAAClK,KAAK;wBAAI;sBAAE;wBAAAiJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/B;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,EAELc,IAAI,CAACsC,OAAO,IAAItC,IAAI,CAACuC,KAAK,iBACzBvQ,OAAA;sBAAK0M,SAAS,EAAC,uBAAuB;sBAAAD,QAAA,gBACpCzM,OAAA;wBAAK0M,SAAS,EAAC,MAAM;wBAAAD,QAAA,eACnBzM,OAAA;0BAAM0M,SAAS,EAAC,aAAa;0BAAAD,QAAA,GAAC,WAAS,EAACuB,IAAI,CAACsC,OAAO,CAAC1M,MAAM,EAAC,GAAC,EAACoK,IAAI,CAACuC,KAAK,CAAC3M,MAAM,EAAC,IAAE;wBAAA;0BAAAmJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtF,CAAC,EACLc,IAAI,CAACsC,OAAO,CAAC1M,MAAM,GAAG,CAAC,gBACtB5D,OAAA;wBAAK0M,SAAS,EAAC,2BAA2B;wBAAAD,QAAA,GACvC,CAAC0D,eAAe,GAAGnC,IAAI,CAACsC,OAAO,GAAGtC,IAAI,CAACsC,OAAO,CAACpB,KAAK,CAAC,CAAC,EAAEe,YAAY,CAAC,EAAEpK,GAAG,CAAC,CAAC2K,IAAI,EAAEtH,GAAG,kBACpFlJ,OAAA;0BAAgB0M,SAAS,EAAC,uDAAuD;0BAAAD,QAAA,EAAE+D;wBAAI,GAA5EtH,GAAG;0BAAA6D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAgF,CAC/F,CAAC,EACDc,IAAI,CAACsC,OAAO,CAAC1M,MAAM,GAAGqM,YAAY,iBACjCjQ,OAAA;0BACE0M,SAAS,EAAC,wCAAwC;0BAClDuB,OAAO,EAAEA,CAAA,KAAM3B,YAAY,CAACC,GAAG,EAAE,SAAS,CAAE;0BAAAE,QAAA,EAE3C0D,eAAe,GAAG,IAAI,GAAG,IAAInC,IAAI,CAACsC,OAAO,CAAC1M,MAAM,GAAGqM,YAAY;wBAAO;0BAAAlD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnE,CACP;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,gBAENlN,OAAA;wBAAK0M,SAAS,EAAC,oBAAoB;wBAAAD,QAAA,EAAC;sBAAgB;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAC1D,EACAc,IAAI,CAACuC,KAAK,CAAC3M,MAAM,GAAGoK,IAAI,CAACsC,OAAO,CAAC1M,MAAM,iBACtC5D,OAAA;wBAAAyM,QAAA,gBACEzM,OAAA;0BAAM0M,SAAS,EAAC,0BAA0B;0BAAAD,QAAA,EAAC;wBAAQ;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eAC1DlN,OAAA;0BAAK0M,SAAS,EAAC,2BAA2B;0BAAAD,QAAA,GACvC,CAAC2D,eAAe,GAAGpC,IAAI,CAACuC,KAAK,CAACtM,MAAM,CAACuM,IAAI,IAAI,CAACxC,IAAI,CAACsC,OAAO,CAAC3H,QAAQ,CAAC6H,IAAI,CAAC,CAAC,GAAGxC,IAAI,CAACuC,KAAK,CAACtM,MAAM,CAACuM,IAAI,IAAI,CAACxC,IAAI,CAACsC,OAAO,CAAC3H,QAAQ,CAAC6H,IAAI,CAAC,CAAC,CAACtB,KAAK,CAAC,CAAC,EAAEgB,YAAY,CAAC,EAAErK,GAAG,CAAC,CAAC2K,IAAI,EAAEtH,GAAG,kBAC1KlJ,OAAA;4BAAgB0M,SAAS,EAAC,wDAAwD;4BAAAD,QAAA,EAAE+D;0BAAI,GAA7EtH,GAAG;4BAAA6D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAiF,CAChG,CAAC,EACDc,IAAI,CAACuC,KAAK,CAAC3M,MAAM,GAAGoK,IAAI,CAACsC,OAAO,CAAC1M,MAAM,GAAGsM,YAAY,iBACrDlQ,OAAA;4BACE0M,SAAS,EAAC,wCAAwC;4BAClDuB,OAAO,EAAEA,CAAA,KAAM3B,YAAY,CAACC,GAAG,EAAE,SAAS,CAAE;4BAAAE,QAAA,EAE3C2D,eAAe,GAAG,IAAI,GAAG,IAAIpC,IAAI,CAACuC,KAAK,CAAC3M,MAAM,GAAGoK,IAAI,CAACsC,OAAO,CAAC1M,MAAM,GAAGsM,YAAY;0BAAO;4BAAAnD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACvF,CACP;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CACN;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CACN;kBAAA,GA9DOX,GAAG;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OA+DR,CAAC;gBAEV,CAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,EAKL,CAAC,MAAM;gBACN,MAAMuD,WAAW,GAAG1M,MAAM,CAACgK,OAAO,CAAC3N,MAAM,CAACqN,kBAAkB,CAACG,qBAAqB,CAACE,SAAS,CAAC,CAC1F7J,MAAM,CAAC,CAAC,CAACsI,GAAG,EAAEyB,IAAI,CAAC,KAAKA,IAAI,CAAClK,KAAK,GAAG,EAAE,CAAC,CACxC+B,GAAG,CAAC,CAAC,CAAC0G,GAAG,EAAEyB,IAAI,CAAC,KAAK;kBACpB,IAAI0C,GAAG,GAAG,EAAE;kBACZ,IAAInE,GAAG,CAAC/I,WAAW,CAAC,CAAC,CAACmF,QAAQ,CAAC,QAAQ,CAAC,EAAE+H,GAAG,GAAG,0DAA0D,CAAC,KACtG,IAAInE,GAAG,CAAC/I,WAAW,CAAC,CAAC,CAACmF,QAAQ,CAAC,OAAO,CAAC,EAAE+H,GAAG,GAAG,8DAA8D,CAAC,KAC9G,IAAInE,GAAG,CAAC/I,WAAW,CAAC,CAAC,CAACmF,QAAQ,CAAC,UAAU,CAAC,EAAE+H,GAAG,GAAG,6CAA6C,CAAC,KAChG,IAAInE,GAAG,CAAC/I,WAAW,CAAC,CAAC,CAACmF,QAAQ,CAAC,YAAY,CAAC,EAAE+H,GAAG,GAAG,oDAAoD,CAAC,KACzG,IAAInE,GAAG,CAAC/I,WAAW,CAAC,CAAC,CAACmF,QAAQ,CAAC,WAAW,CAAC,EAAE+H,GAAG,GAAG,+CAA+C,CAAC,KACnGA,GAAG,GAAG,0DAA0D;kBACrE,OAAO,IAAInE,GAAG,CAACzJ,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,KAAK4N,GAAG,EAAE;gBAC5C,CAAC,CAAC;gBACJ,IAAID,WAAW,CAAC7M,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;gBACzC,oBACE5D,OAAA;kBAAK0M,SAAS,EAAC,4DAA4D;kBAAAD,QAAA,gBACzEzM,OAAA;oBAAK0M,SAAS,EAAC,wBAAwB;oBAAAD,QAAA,gBACrCzM,OAAA;sBAAI0M,SAAS,EAAC,sCAAsC;sBAAAD,QAAA,EAAC;oBAAwB;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAClFlN,OAAA;sBACE0M,SAAS,EAAC,4EAA4E;sBACtFuB,OAAO,EAAEA,CAAA,KAAM7L,qBAAqB,CAACuO,CAAC,IAAI,CAACA,CAAC,CAAE;sBAAAlE,QAAA,EAC9CtK,kBAAkB,GAAG,cAAc,GAAG;oBAAwB;sBAAA4K,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvE,CAAC,eACNlN,OAAA;oBAAI0M,SAAS,EAAC,8BAA8B;oBAAAD,QAAA,GACzC,CAACtK,kBAAkB,GAAGsO,WAAW,GAAGA,WAAW,CAACvB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAErJ,GAAG,CAAC,CAAC+K,CAAC,EAAEC,CAAC,kBACrE7Q,OAAA;sBAAAyM,QAAA,EAAamE;oBAAC,GAALC,CAAC;sBAAA9D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS,CACpB,CAAC,EACD,CAAC/K,kBAAkB,IAAIsO,WAAW,CAAC7M,MAAM,GAAG,CAAC,iBAC5C5D,OAAA;sBAAI0M,SAAS,EAAC,eAAe;sBAAAD,QAAA,EAAC;oBAA6C;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAChF;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAEV,CAAC,EAAE,CAAC,EAGH,KAAK,IAAI,CAAC,MAAM;gBACf,MAAM4D,SAAS,GAAG/M,MAAM,CAACgK,OAAO,CAAC3N,MAAM,CAACqN,kBAAkB,CAACG,qBAAqB,CAACE,SAAS,CAAC,CAACjI,GAAG,CAAC,CAAC,CAAC0G,GAAG,EAAEyB,IAAI,CAAC,MAAM;kBAChH+C,IAAI,EAAExE,GAAG,CAACzJ,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;kBAC3BkO,KAAK,EAAEhD,IAAI,CAAClK;gBACd,CAAC,CAAC,CAAC;gBACH,IAAIgN,SAAS,CAAClN,MAAM,GAAG,CAAC,EAAE,OAAO,IAAI,CAAC,CAAC;gBACvC,MAAMqN,SAAS,GAAGH,SAAS,CAACjL,GAAG,CAAC2K,IAAI,KAAK;kBAAEO,IAAI,EAAEP,IAAI,CAACO,IAAI;kBAAElI,GAAG,EAAE;gBAAI,CAAC,CAAC,CAAC;gBACxE,MAAMqI,MAAM,GAAG;kBACbC,OAAO,EAAE,CAAC,CAAC;kBACXC,KAAK,EAAE;oBACLH,SAAS;oBACTI,MAAM,EAAE;kBACV,CAAC;kBACDC,MAAM,EAAE,CAAC;oBACPnJ,IAAI,EAAE,OAAO;oBACb6F,IAAI,EAAE,CAAC;sBAAEgD,KAAK,EAAEF,SAAS,CAACjL,GAAG,CAAC+D,CAAC,IAAIA,CAAC,CAACoH,KAAK,CAAC;sBAAED,IAAI,EAAE;oBAAM,CAAC,CAAC;oBAC3DQ,SAAS,EAAE;sBAAEC,OAAO,EAAE;oBAAI,CAAC;oBAC3BC,SAAS,EAAE;sBAAEjE,KAAK,EAAE;oBAAE;kBACxB,CAAC;gBACH,CAAC;gBACD,oBACExN,OAAA;kBAAK0M,SAAS,EAAC,qFAAqF;kBAAAD,QAAA,gBAClGzM,OAAA;oBAAI0M,SAAS,EAAC,sCAAsC;oBAAAD,QAAA,EAAC;kBAAU;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpElN,OAAA;oBAAKuN,KAAK,EAAE;sBAAEC,KAAK,EAAE,GAAG;sBAAEgC,MAAM,EAAE;oBAAI,CAAE;oBAAA/C,QAAA,eAEtCzM,OAAA;sBAAK0M,SAAS,EAAC,uDAAuD;sBAAAD,QAAA,EAAC;oBAEvE;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAEV,CAAC,EAAE,CAAC,EAGH,CAAC,MAAM;gBACN,MAAMlK,aAAa,GAAGyB,gBAAgB,CAAC,CAAC;gBACxC,MAAMiN,MAAM,GAAG,EAAE;gBACjB,IAAI,SAAS,CAACpO,IAAI,CAACN,aAAa,CAAC,EAAE0O,MAAM,CAACxJ,IAAI,CAAC,sDAAsD,CAAC;gBACtG,IAAI,CAAC,eAAe,CAAC5E,IAAI,CAACN,aAAa,CAAC,EAAE0O,MAAM,CAACxJ,IAAI,CAAC,iCAAiC,CAAC;gBACxF,IAAI,CAAC,qBAAqB,CAAC5E,IAAI,CAACN,aAAa,CAAC,EAAE0O,MAAM,CAACxJ,IAAI,CAAC,4BAA4B,CAAC;gBACzF,IAAIlF,aAAa,CAACY,MAAM,GAAG,GAAG,EAAE8N,MAAM,CAACxJ,IAAI,CAAC,4DAA4D,CAAC;gBACzG,IAAIlF,aAAa,CAACY,MAAM,GAAG,IAAI,EAAE8N,MAAM,CAACxJ,IAAI,CAAC,kDAAkD,CAAC;gBAChG,IAAIwJ,MAAM,CAAC9N,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;gBACpC,oBACE5D,OAAA;kBAAK0M,SAAS,EAAC,gEAAgE;kBAAAD,QAAA,gBAC7EzM,OAAA;oBAAI0M,SAAS,EAAC,0DAA0D;oBAAAD,QAAA,gBACtEzM,OAAA;sBAAK0M,SAAS,EAAC,8BAA8B;sBAACC,IAAI,EAAC,MAAM;sBAACQ,MAAM,EAAC,cAAc;sBAACP,OAAO,EAAC,WAAW;sBAAAH,QAAA,eAACzM,OAAA;wBAAMoN,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAE,CAAE;wBAAC1D,CAAC,EAAC;sBAAyI;wBAAAmD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,+BAE7T;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACLlN,OAAA;oBAAI0M,SAAS,EAAC,gCAAgC;oBAAAD,QAAA,EAC3CiF,MAAM,CAAC7L,GAAG,CAAC,CAAC8L,KAAK,EAAEzI,GAAG,kBAAKlJ,OAAA;sBAAAyM,QAAA,EAAekF;oBAAK,GAAXzI,GAAG;sBAAA6D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAEV,CAAC,EAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAED,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3M,EAAA,CApqCIJ,aAAa;AAAAyR,EAAA,GAAbzR,aAAa;AAsqCnB,eAAeA,aAAa;AAAC,IAAAyR,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}