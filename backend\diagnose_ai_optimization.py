#!/usr/bin/env python3
"""
AI优化诊断脚本 - 专门用于诊断AI优化过程中的问题
"""
import os
import sys
import json
import logging
from dotenv import load_dotenv
from openai import AzureOpenAI

# 加载环境变量
load_dotenv()

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_azure_openai_connection():
    """测试Azure OpenAI连接"""
    try:
        logger.info("🔧 测试Azure OpenAI连接...")
        
        # 检查环境变量
        api_key = os.getenv("AZURE_OPENAI_API_KEY")
        endpoint = os.getenv("AZURE_OPENAI_ENDPOINT")
        deployment = os.getenv("AZURE_OPENAI_GPT4_DEPLOYMENT")
        
        logger.info(f"API Key: {'✅ 已设置' if api_key else '❌ 未设置'}")
        logger.info(f"Endpoint: {endpoint if endpoint else '❌ 未设置'}")
        logger.info(f"Deployment: {deployment if deployment else '❌ 未设置'}")
        
        if not all([api_key, endpoint, deployment]):
            logger.error("❌ Azure OpenAI配置不完整")
            return False
        
        # 初始化客户端
        client = AzureOpenAI(
            api_key=api_key,
            api_version="2024-02-15-preview",
            azure_endpoint=endpoint
        )
        
        # 测试简单调用
        response = client.chat.completions.create(
            model=deployment,
            messages=[
                {"role": "user", "content": "Hello, this is a test. Please respond with 'Connection successful'."}
            ],
            max_tokens=50
        )
        
        result = response.choices[0].message.content.strip()
        logger.info(f"✅ 连接测试成功，响应: {result}")
        return True, client
        
    except Exception as e:
        logger.error(f"❌ Azure OpenAI连接失败: {str(e)}")
        return False, None

def test_jd_analysis(client, job_description):
    """测试JD解析功能"""
    try:
        logger.info("🔍 测试JD解析功能...")
        
        prompt = f"""
请分析以下职位描述，提取：
1. 职位名称（title）
2. 核心硬技能（core_skills）
3. 所需软技能（soft_skills）
4. 关键词及其重要性（keywords，格式为{{"关键词": 权重}}，权重为1-5，综合出现频率和语义相关性）

请严格以如下JSON格式输出：
{{
  "title": "",
  "core_skills": [],
  "soft_skills": [],
  "keywords": {{}}
}}

职位描述：
{job_description}
"""
        
        response = client.chat.completions.create(
            model=os.getenv("AZURE_OPENAI_GPT4_DEPLOYMENT"),
            messages=[
                {"role": "system", "content": "你是一个专业的JD解析专家，擅长结构化提取职位描述关键信息。"},
                {"role": "user", "content": prompt}
            ],
            temperature=0.2,
            max_tokens=800
        )
        
        result_text = response.choices[0].message.content.strip()
        logger.info(f"📋 JD解析原始响应: {result_text}")
        
        # 尝试解析JSON
        try:
            jd_json = json.loads(result_text)
            logger.info(f"✅ JD解析成功: {json.dumps(jd_json, ensure_ascii=False, indent=2)}")
            return True, jd_json
        except json.JSONDecodeError as e:
            logger.error(f"❌ JD解析JSON格式错误: {str(e)}")
            return False, {}
            
    except Exception as e:
        logger.error(f"❌ JD解析失败: {str(e)}")
        return False, {}

def test_resume_optimization(client, resume_text, job_description, jd_json):
    """测试简历优化功能"""
    try:
        logger.info("🤖 测试简历优化功能...")
        
        # 构建优化提示词
        system_prompt = "你是一个专为HR系统优化简历的语言专家，所有内容必须基于原文，不得虚构。输出JSON。"
        
        user_prompt = (
            "你是一个专为HR系统优化简历的语言专家。请根据以下简历和职位描述，优化简历内容以提升通过ATS系统的概率，但**严禁编造虚假内容**，必须基于原内容合理扩展或重写。\n\n"
            f"简历原文：\n{resume_text}\n\n"
            f"职位描述：\n{job_description}\n\n"
            f"JD结构化信息：\n{json.dumps(jd_json, ensure_ascii=False)}\n\n"
            "请输出优化建议内容，保持原有段落结构，每段注明所依据的JD关键词（用jd_keywords字段标注），并以**严格JSON数组**格式返回，示例：\n"
            "[\n"
            "  {\"section\": \"Experience\", \"original\": \"...\", \"optimized\": \"...\", \"jd_keywords\": [\"Agile\", \"Scrum\"]},\n"
            "  {\"section\": \"Education\", \"original\": \"...\", \"optimized\": \"...\", \"jd_keywords\": [\"Bachelor\"]}\n"
            "]\n"
            "不要输出任何多余内容。"
        )
        
        logger.info(f"📝 优化提示词长度: {len(user_prompt)} 字符")
        
        response = client.chat.completions.create(
            model=os.getenv("AZURE_OPENAI_GPT4_DEPLOYMENT"),
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            max_tokens=4000,
            temperature=0.3,
            top_p=0.8
        )
        
        result_text = response.choices[0].message.content.strip()
        logger.info(f"🤖 AI优化原始响应长度: {len(result_text)} 字符")
        logger.info(f"📄 AI优化完整响应: {result_text}")
        
        # 尝试解析JSON
        import re
        try:
            # 先用正则提取以 [ 开头的JSON数组
            match = re.search(r'\[.*\]', result_text, re.DOTALL)
            if match:
                json_text = match.group()
                logger.info(f"🔍 提取的JSON文本: {json_text}")
                try:
                    optimized_json = json.loads(json_text)
                    logger.info(f"✅ JSON解析成功，包含 {len(optimized_json)} 个项目")
                    logger.info(f"📊 优化结果: {json.dumps(optimized_json, ensure_ascii=False, indent=2)}")
                    return True, optimized_json
                except Exception as json_error:
                    logger.error(f"❌ JSON解析失败: {str(json_error)}")
                    return False, []
            else:
                logger.warning("⚠️ 未找到JSON数组格式")
                return False, []
                
        except Exception as e:
            logger.error(f"❌ 优化内容解析失败: {str(e)}")
            return False, []
            
    except Exception as e:
        logger.error(f"❌ 简历优化失败: {str(e)}")
        return False, []

def main():
    """主测试函数"""
    logger.info("🚀 开始AI优化诊断...")
    
    # 测试数据
    test_resume = """
John Smith
Software Engineer

Experience:
- Worked as a software developer for 3 years
- Developed web applications using Python and JavaScript
- Collaborated with team members on various projects

Education:
- Bachelor's degree in Computer Science
- University of Technology

Skills:
- Python, JavaScript, HTML, CSS
- Database management
- Problem solving
"""
    
    test_jd = """
We are looking for a Senior Python Developer to join our team.

Requirements:
- 5+ years of experience in Python development
- Experience with Django or Flask frameworks
- Knowledge of React.js for frontend development
- Experience with PostgreSQL database
- Strong problem-solving skills
- Excellent communication abilities
- Bachelor's degree in Computer Science or related field

Responsibilities:
- Develop and maintain web applications
- Work with cross-functional teams
- Implement new features and optimize existing code
- Participate in code reviews and technical discussions
"""
    
    # 1. 测试Azure OpenAI连接
    success, client = test_azure_openai_connection()
    if not success:
        logger.error("❌ 无法连接到Azure OpenAI，终止测试")
        return
    
    # 2. 测试JD解析
    success, jd_json = test_jd_analysis(client, test_jd)
    if not success:
        logger.error("❌ JD解析失败，终止测试")
        return
    
    # 3. 测试简历优化
    success, optimized_json = test_resume_optimization(client, test_resume, test_jd, jd_json)
    if not success:
        logger.error("❌ 简历优化失败")
        return
    
    logger.info("🎉 所有测试通过！AI优化功能正常工作")

if __name__ == "__main__":
    main()
