{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport WeakMap from 'zrender/lib/core/WeakMap.js';\nimport LRU from 'zrender/lib/core/LRU.js';\nimport { defaults, map, isArray, isString, isNumber } from 'zrender/lib/core/util.js';\nimport { getLeastCommonMultiple } from './number.js';\nimport { createSymbol } from './symbol.js';\nimport { brushSingle } from 'zrender/lib/canvas/graphic.js';\nimport { platformApi } from 'zrender/lib/core/platform.js';\nvar decalMap = new WeakMap();\nvar decalCache = new LRU(100);\nvar decalKeys = ['symbol', 'symbolSize', 'symbolKeepAspect', 'color', 'backgroundColor', 'dashArrayX', 'dashArrayY', 'maxTileWidth', 'maxTileHeight'];\n/**\r\n * Create or update pattern image from decal options\r\n *\r\n * @param {InnerDecalObject | 'none'} decalObject decal options, 'none' if no decal\r\n * @return {Pattern} pattern with generated image, null if no decal\r\n */\nexport function createOrUpdatePatternFromDecal(decalObject, api) {\n  if (decalObject === 'none') {\n    return null;\n  }\n  var dpr = api.getDevicePixelRatio();\n  var zr = api.getZr();\n  var isSVG = zr.painter.type === 'svg';\n  if (decalObject.dirty) {\n    decalMap[\"delete\"](decalObject);\n  }\n  var oldPattern = decalMap.get(decalObject);\n  if (oldPattern) {\n    return oldPattern;\n  }\n  var decalOpt = defaults(decalObject, {\n    symbol: 'rect',\n    symbolSize: 1,\n    symbolKeepAspect: true,\n    color: 'rgba(0, 0, 0, 0.2)',\n    backgroundColor: null,\n    dashArrayX: 5,\n    dashArrayY: 5,\n    rotation: 0,\n    maxTileWidth: 512,\n    maxTileHeight: 512\n  });\n  if (decalOpt.backgroundColor === 'none') {\n    decalOpt.backgroundColor = null;\n  }\n  var pattern = {\n    repeat: 'repeat'\n  };\n  setPatternnSource(pattern);\n  pattern.rotation = decalOpt.rotation;\n  pattern.scaleX = pattern.scaleY = isSVG ? 1 : 1 / dpr;\n  decalMap.set(decalObject, pattern);\n  decalObject.dirty = false;\n  return pattern;\n  function setPatternnSource(pattern) {\n    var keys = [dpr];\n    var isValidKey = true;\n    for (var i = 0; i < decalKeys.length; ++i) {\n      var value = decalOpt[decalKeys[i]];\n      if (value != null && !isArray(value) && !isString(value) && !isNumber(value) && typeof value !== 'boolean') {\n        isValidKey = false;\n        break;\n      }\n      keys.push(value);\n    }\n    var cacheKey;\n    if (isValidKey) {\n      cacheKey = keys.join(',') + (isSVG ? '-svg' : '');\n      var cache = decalCache.get(cacheKey);\n      if (cache) {\n        isSVG ? pattern.svgElement = cache : pattern.image = cache;\n      }\n    }\n    var dashArrayX = normalizeDashArrayX(decalOpt.dashArrayX);\n    var dashArrayY = normalizeDashArrayY(decalOpt.dashArrayY);\n    var symbolArray = normalizeSymbolArray(decalOpt.symbol);\n    var lineBlockLengthsX = getLineBlockLengthX(dashArrayX);\n    var lineBlockLengthY = getLineBlockLengthY(dashArrayY);\n    var canvas = !isSVG && platformApi.createCanvas();\n    var svgRoot = isSVG && {\n      tag: 'g',\n      attrs: {},\n      key: 'dcl',\n      children: []\n    };\n    var pSize = getPatternSize();\n    var ctx;\n    if (canvas) {\n      canvas.width = pSize.width * dpr;\n      canvas.height = pSize.height * dpr;\n      ctx = canvas.getContext('2d');\n    }\n    brushDecal();\n    if (isValidKey) {\n      decalCache.put(cacheKey, canvas || svgRoot);\n    }\n    pattern.image = canvas;\n    pattern.svgElement = svgRoot;\n    pattern.svgWidth = pSize.width;\n    pattern.svgHeight = pSize.height;\n    /**\r\n     * Get minimum length that can make a repeatable pattern.\r\n     *\r\n     * @return {Object} pattern width and height\r\n     */\n    function getPatternSize() {\n      /**\r\n       * For example, if dash is [[3, 2], [2, 1]] for X, it looks like\r\n       * |---  ---  ---  ---  --- ...\r\n       * |-- -- -- -- -- -- -- -- ...\r\n       * |---  ---  ---  ---  --- ...\r\n       * |-- -- -- -- -- -- -- -- ...\r\n       * So the minimum length of X is 15,\r\n       * which is the least common multiple of `3 + 2` and `2 + 1`\r\n       * |---  ---  ---  |---  --- ...\r\n       * |-- -- -- -- -- |-- -- -- ...\r\n       */\n      var width = 1;\n      for (var i = 0, xlen = lineBlockLengthsX.length; i < xlen; ++i) {\n        width = getLeastCommonMultiple(width, lineBlockLengthsX[i]);\n      }\n      var symbolRepeats = 1;\n      for (var i = 0, xlen = symbolArray.length; i < xlen; ++i) {\n        symbolRepeats = getLeastCommonMultiple(symbolRepeats, symbolArray[i].length);\n      }\n      width *= symbolRepeats;\n      var height = lineBlockLengthY * lineBlockLengthsX.length * symbolArray.length;\n      if (process.env.NODE_ENV !== 'production') {\n        var warn = function (attrName) {\n          /* eslint-disable-next-line */\n          console.warn(\"Calculated decal size is greater than \" + attrName + \" due to decal option settings so \" + attrName + \" is used for the decal size. Please consider changing the decal option to make a smaller decal or set \" + attrName + \" to be larger to avoid incontinuity.\");\n        };\n        if (width > decalOpt.maxTileWidth) {\n          warn('maxTileWidth');\n        }\n        if (height > decalOpt.maxTileHeight) {\n          warn('maxTileHeight');\n        }\n      }\n      return {\n        width: Math.max(1, Math.min(width, decalOpt.maxTileWidth)),\n        height: Math.max(1, Math.min(height, decalOpt.maxTileHeight))\n      };\n    }\n    function brushDecal() {\n      if (ctx) {\n        ctx.clearRect(0, 0, canvas.width, canvas.height);\n        if (decalOpt.backgroundColor) {\n          ctx.fillStyle = decalOpt.backgroundColor;\n          ctx.fillRect(0, 0, canvas.width, canvas.height);\n        }\n      }\n      var ySum = 0;\n      for (var i = 0; i < dashArrayY.length; ++i) {\n        ySum += dashArrayY[i];\n      }\n      if (ySum <= 0) {\n        // dashArrayY is 0, draw nothing\n        return;\n      }\n      var y = -lineBlockLengthY;\n      var yId = 0;\n      var yIdTotal = 0;\n      var xId0 = 0;\n      while (y < pSize.height) {\n        if (yId % 2 === 0) {\n          var symbolYId = yIdTotal / 2 % symbolArray.length;\n          var x = 0;\n          var xId1 = 0;\n          var xId1Total = 0;\n          while (x < pSize.width * 2) {\n            var xSum = 0;\n            for (var i = 0; i < dashArrayX[xId0].length; ++i) {\n              xSum += dashArrayX[xId0][i];\n            }\n            if (xSum <= 0) {\n              // Skip empty line\n              break;\n            }\n            // E.g., [15, 5, 20, 5] draws only for 15 and 20\n            if (xId1 % 2 === 0) {\n              var size = (1 - decalOpt.symbolSize) * 0.5;\n              var left = x + dashArrayX[xId0][xId1] * size;\n              var top_1 = y + dashArrayY[yId] * size;\n              var width = dashArrayX[xId0][xId1] * decalOpt.symbolSize;\n              var height = dashArrayY[yId] * decalOpt.symbolSize;\n              var symbolXId = xId1Total / 2 % symbolArray[symbolYId].length;\n              brushSymbol(left, top_1, width, height, symbolArray[symbolYId][symbolXId]);\n            }\n            x += dashArrayX[xId0][xId1];\n            ++xId1Total;\n            ++xId1;\n            if (xId1 === dashArrayX[xId0].length) {\n              xId1 = 0;\n            }\n          }\n          ++xId0;\n          if (xId0 === dashArrayX.length) {\n            xId0 = 0;\n          }\n        }\n        y += dashArrayY[yId];\n        ++yIdTotal;\n        ++yId;\n        if (yId === dashArrayY.length) {\n          yId = 0;\n        }\n      }\n      function brushSymbol(x, y, width, height, symbolType) {\n        var scale = isSVG ? 1 : dpr;\n        var symbol = createSymbol(symbolType, x * scale, y * scale, width * scale, height * scale, decalOpt.color, decalOpt.symbolKeepAspect);\n        if (isSVG) {\n          var symbolVNode = zr.painter.renderOneToVNode(symbol);\n          if (symbolVNode) {\n            svgRoot.children.push(symbolVNode);\n          }\n        } else {\n          // Paint to canvas for all other renderers.\n          brushSingle(ctx, symbol);\n        }\n      }\n    }\n  }\n}\n/**\r\n * Convert symbol array into normalized array\r\n *\r\n * @param {string | (string | string[])[]} symbol symbol input\r\n * @return {string[][]} normolized symbol array\r\n */\nfunction normalizeSymbolArray(symbol) {\n  if (!symbol || symbol.length === 0) {\n    return [['rect']];\n  }\n  if (isString(symbol)) {\n    return [[symbol]];\n  }\n  var isAllString = true;\n  for (var i = 0; i < symbol.length; ++i) {\n    if (!isString(symbol[i])) {\n      isAllString = false;\n      break;\n    }\n  }\n  if (isAllString) {\n    return normalizeSymbolArray([symbol]);\n  }\n  var result = [];\n  for (var i = 0; i < symbol.length; ++i) {\n    if (isString(symbol[i])) {\n      result.push([symbol[i]]);\n    } else {\n      result.push(symbol[i]);\n    }\n  }\n  return result;\n}\n/**\r\n * Convert dash input into dashArray\r\n *\r\n * @param {DecalDashArrayX} dash dash input\r\n * @return {number[][]} normolized dash array\r\n */\nfunction normalizeDashArrayX(dash) {\n  if (!dash || dash.length === 0) {\n    return [[0, 0]];\n  }\n  if (isNumber(dash)) {\n    var dashValue = Math.ceil(dash);\n    return [[dashValue, dashValue]];\n  }\n  /**\r\n   * [20, 5] should be normalized into [[20, 5]],\r\n   * while [20, [5, 10]] should be normalized into [[20, 20], [5, 10]]\r\n   */\n  var isAllNumber = true;\n  for (var i = 0; i < dash.length; ++i) {\n    if (!isNumber(dash[i])) {\n      isAllNumber = false;\n      break;\n    }\n  }\n  if (isAllNumber) {\n    return normalizeDashArrayX([dash]);\n  }\n  var result = [];\n  for (var i = 0; i < dash.length; ++i) {\n    if (isNumber(dash[i])) {\n      var dashValue = Math.ceil(dash[i]);\n      result.push([dashValue, dashValue]);\n    } else {\n      var dashValue = map(dash[i], function (n) {\n        return Math.ceil(n);\n      });\n      if (dashValue.length % 2 === 1) {\n        // [4, 2, 1] means |----  -    -- |----  -    -- |\n        // so normalize it to be [4, 2, 1, 4, 2, 1]\n        result.push(dashValue.concat(dashValue));\n      } else {\n        result.push(dashValue);\n      }\n    }\n  }\n  return result;\n}\n/**\r\n * Convert dash input into dashArray\r\n *\r\n * @param {DecalDashArrayY} dash dash input\r\n * @return {number[]} normolized dash array\r\n */\nfunction normalizeDashArrayY(dash) {\n  if (!dash || typeof dash === 'object' && dash.length === 0) {\n    return [0, 0];\n  }\n  if (isNumber(dash)) {\n    var dashValue_1 = Math.ceil(dash);\n    return [dashValue_1, dashValue_1];\n  }\n  var dashValue = map(dash, function (n) {\n    return Math.ceil(n);\n  });\n  return dash.length % 2 ? dashValue.concat(dashValue) : dashValue;\n}\n/**\r\n * Get block length of each line. A block is the length of dash line and space.\r\n * For example, a line with [4, 1] has a dash line of 4 and a space of 1 after\r\n * that, so the block length of this line is 5.\r\n *\r\n * @param {number[][]} dash dash array of X or Y\r\n * @return {number[]} block length of each line\r\n */\nfunction getLineBlockLengthX(dash) {\n  return map(dash, function (line) {\n    return getLineBlockLengthY(line);\n  });\n}\nfunction getLineBlockLengthY(dash) {\n  var blockLength = 0;\n  for (var i = 0; i < dash.length; ++i) {\n    blockLength += dash[i];\n  }\n  if (dash.length % 2 === 1) {\n    // [4, 2, 1] means |----  -    -- |----  -    -- |\n    // So total length is (4 + 2 + 1) * 2\n    return blockLength * 2;\n  }\n  return blockLength;\n}", "map": {"version": 3, "names": ["WeakMap", "LRU", "defaults", "map", "isArray", "isString", "isNumber", "getLeastCommonMultiple", "createSymbol", "brushSingle", "platformApi", "decalMap", "decal<PERSON>ache", "decal<PERSON>eys", "createOrUpdatePatternFromDecal", "decalObject", "api", "dpr", "getDevicePixelRatio", "zr", "getZr", "isSVG", "painter", "type", "dirty", "oldPattern", "get", "decalOpt", "symbol", "symbolSize", "symbolKeepAspect", "color", "backgroundColor", "dashArrayX", "dashArrayY", "rotation", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "maxTileHeight", "pattern", "repeat", "setPatternnSource", "scaleX", "scaleY", "set", "keys", "is<PERSON><PERSON><PERSON><PERSON><PERSON>", "i", "length", "value", "push", "cache<PERSON>ey", "join", "cache", "svgElement", "image", "normalizeDashArrayX", "normalizeDashArrayY", "symbolArray", "normalizeSymbolArray", "lineBlockLengthsX", "getLineBlockLengthX", "lineBlockLengthY", "getLineBlockLengthY", "canvas", "createCanvas", "svgRoot", "tag", "attrs", "key", "children", "pSize", "getPatternSize", "ctx", "width", "height", "getContext", "brushDecal", "put", "svgWidth", "svgHeight", "xlen", "symbolRepeats", "process", "env", "NODE_ENV", "warn", "attrName", "console", "Math", "max", "min", "clearRect", "fillStyle", "fillRect", "ySum", "y", "yId", "yIdTotal", "xId0", "symbolYId", "x", "xId1", "xId1Total", "xSum", "size", "left", "top_1", "symbolXId", "brushSymbol", "symbolType", "scale", "symbolVNode", "renderOneToVNode", "isAllString", "result", "dash", "dashValue", "ceil", "isAllNumber", "n", "concat", "dashValue_1", "line", "blockLength"], "sources": ["E:/AI/SmartCV/node_modules/echarts/lib/util/decal.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport WeakMap from 'zrender/lib/core/WeakMap.js';\nimport LRU from 'zrender/lib/core/LRU.js';\nimport { defaults, map, isArray, isString, isNumber } from 'zrender/lib/core/util.js';\nimport { getLeastCommonMultiple } from './number.js';\nimport { createSymbol } from './symbol.js';\nimport { brushSingle } from 'zrender/lib/canvas/graphic.js';\nimport { platformApi } from 'zrender/lib/core/platform.js';\nvar decalMap = new WeakMap();\nvar decalCache = new LRU(100);\nvar decalKeys = ['symbol', 'symbolSize', 'symbolKeepAspect', 'color', 'backgroundColor', 'dashArrayX', 'dashArrayY', 'maxTileWidth', 'maxTileHeight'];\n/**\r\n * Create or update pattern image from decal options\r\n *\r\n * @param {InnerDecalObject | 'none'} decalObject decal options, 'none' if no decal\r\n * @return {Pattern} pattern with generated image, null if no decal\r\n */\nexport function createOrUpdatePatternFromDecal(decalObject, api) {\n  if (decalObject === 'none') {\n    return null;\n  }\n  var dpr = api.getDevicePixelRatio();\n  var zr = api.getZr();\n  var isSVG = zr.painter.type === 'svg';\n  if (decalObject.dirty) {\n    decalMap[\"delete\"](decalObject);\n  }\n  var oldPattern = decalMap.get(decalObject);\n  if (oldPattern) {\n    return oldPattern;\n  }\n  var decalOpt = defaults(decalObject, {\n    symbol: 'rect',\n    symbolSize: 1,\n    symbolKeepAspect: true,\n    color: 'rgba(0, 0, 0, 0.2)',\n    backgroundColor: null,\n    dashArrayX: 5,\n    dashArrayY: 5,\n    rotation: 0,\n    maxTileWidth: 512,\n    maxTileHeight: 512\n  });\n  if (decalOpt.backgroundColor === 'none') {\n    decalOpt.backgroundColor = null;\n  }\n  var pattern = {\n    repeat: 'repeat'\n  };\n  setPatternnSource(pattern);\n  pattern.rotation = decalOpt.rotation;\n  pattern.scaleX = pattern.scaleY = isSVG ? 1 : 1 / dpr;\n  decalMap.set(decalObject, pattern);\n  decalObject.dirty = false;\n  return pattern;\n  function setPatternnSource(pattern) {\n    var keys = [dpr];\n    var isValidKey = true;\n    for (var i = 0; i < decalKeys.length; ++i) {\n      var value = decalOpt[decalKeys[i]];\n      if (value != null && !isArray(value) && !isString(value) && !isNumber(value) && typeof value !== 'boolean') {\n        isValidKey = false;\n        break;\n      }\n      keys.push(value);\n    }\n    var cacheKey;\n    if (isValidKey) {\n      cacheKey = keys.join(',') + (isSVG ? '-svg' : '');\n      var cache = decalCache.get(cacheKey);\n      if (cache) {\n        isSVG ? pattern.svgElement = cache : pattern.image = cache;\n      }\n    }\n    var dashArrayX = normalizeDashArrayX(decalOpt.dashArrayX);\n    var dashArrayY = normalizeDashArrayY(decalOpt.dashArrayY);\n    var symbolArray = normalizeSymbolArray(decalOpt.symbol);\n    var lineBlockLengthsX = getLineBlockLengthX(dashArrayX);\n    var lineBlockLengthY = getLineBlockLengthY(dashArrayY);\n    var canvas = !isSVG && platformApi.createCanvas();\n    var svgRoot = isSVG && {\n      tag: 'g',\n      attrs: {},\n      key: 'dcl',\n      children: []\n    };\n    var pSize = getPatternSize();\n    var ctx;\n    if (canvas) {\n      canvas.width = pSize.width * dpr;\n      canvas.height = pSize.height * dpr;\n      ctx = canvas.getContext('2d');\n    }\n    brushDecal();\n    if (isValidKey) {\n      decalCache.put(cacheKey, canvas || svgRoot);\n    }\n    pattern.image = canvas;\n    pattern.svgElement = svgRoot;\n    pattern.svgWidth = pSize.width;\n    pattern.svgHeight = pSize.height;\n    /**\r\n     * Get minimum length that can make a repeatable pattern.\r\n     *\r\n     * @return {Object} pattern width and height\r\n     */\n    function getPatternSize() {\n      /**\r\n       * For example, if dash is [[3, 2], [2, 1]] for X, it looks like\r\n       * |---  ---  ---  ---  --- ...\r\n       * |-- -- -- -- -- -- -- -- ...\r\n       * |---  ---  ---  ---  --- ...\r\n       * |-- -- -- -- -- -- -- -- ...\r\n       * So the minimum length of X is 15,\r\n       * which is the least common multiple of `3 + 2` and `2 + 1`\r\n       * |---  ---  ---  |---  --- ...\r\n       * |-- -- -- -- -- |-- -- -- ...\r\n       */\n      var width = 1;\n      for (var i = 0, xlen = lineBlockLengthsX.length; i < xlen; ++i) {\n        width = getLeastCommonMultiple(width, lineBlockLengthsX[i]);\n      }\n      var symbolRepeats = 1;\n      for (var i = 0, xlen = symbolArray.length; i < xlen; ++i) {\n        symbolRepeats = getLeastCommonMultiple(symbolRepeats, symbolArray[i].length);\n      }\n      width *= symbolRepeats;\n      var height = lineBlockLengthY * lineBlockLengthsX.length * symbolArray.length;\n      if (process.env.NODE_ENV !== 'production') {\n        var warn = function (attrName) {\n          /* eslint-disable-next-line */\n          console.warn(\"Calculated decal size is greater than \" + attrName + \" due to decal option settings so \" + attrName + \" is used for the decal size. Please consider changing the decal option to make a smaller decal or set \" + attrName + \" to be larger to avoid incontinuity.\");\n        };\n        if (width > decalOpt.maxTileWidth) {\n          warn('maxTileWidth');\n        }\n        if (height > decalOpt.maxTileHeight) {\n          warn('maxTileHeight');\n        }\n      }\n      return {\n        width: Math.max(1, Math.min(width, decalOpt.maxTileWidth)),\n        height: Math.max(1, Math.min(height, decalOpt.maxTileHeight))\n      };\n    }\n    function brushDecal() {\n      if (ctx) {\n        ctx.clearRect(0, 0, canvas.width, canvas.height);\n        if (decalOpt.backgroundColor) {\n          ctx.fillStyle = decalOpt.backgroundColor;\n          ctx.fillRect(0, 0, canvas.width, canvas.height);\n        }\n      }\n      var ySum = 0;\n      for (var i = 0; i < dashArrayY.length; ++i) {\n        ySum += dashArrayY[i];\n      }\n      if (ySum <= 0) {\n        // dashArrayY is 0, draw nothing\n        return;\n      }\n      var y = -lineBlockLengthY;\n      var yId = 0;\n      var yIdTotal = 0;\n      var xId0 = 0;\n      while (y < pSize.height) {\n        if (yId % 2 === 0) {\n          var symbolYId = yIdTotal / 2 % symbolArray.length;\n          var x = 0;\n          var xId1 = 0;\n          var xId1Total = 0;\n          while (x < pSize.width * 2) {\n            var xSum = 0;\n            for (var i = 0; i < dashArrayX[xId0].length; ++i) {\n              xSum += dashArrayX[xId0][i];\n            }\n            if (xSum <= 0) {\n              // Skip empty line\n              break;\n            }\n            // E.g., [15, 5, 20, 5] draws only for 15 and 20\n            if (xId1 % 2 === 0) {\n              var size = (1 - decalOpt.symbolSize) * 0.5;\n              var left = x + dashArrayX[xId0][xId1] * size;\n              var top_1 = y + dashArrayY[yId] * size;\n              var width = dashArrayX[xId0][xId1] * decalOpt.symbolSize;\n              var height = dashArrayY[yId] * decalOpt.symbolSize;\n              var symbolXId = xId1Total / 2 % symbolArray[symbolYId].length;\n              brushSymbol(left, top_1, width, height, symbolArray[symbolYId][symbolXId]);\n            }\n            x += dashArrayX[xId0][xId1];\n            ++xId1Total;\n            ++xId1;\n            if (xId1 === dashArrayX[xId0].length) {\n              xId1 = 0;\n            }\n          }\n          ++xId0;\n          if (xId0 === dashArrayX.length) {\n            xId0 = 0;\n          }\n        }\n        y += dashArrayY[yId];\n        ++yIdTotal;\n        ++yId;\n        if (yId === dashArrayY.length) {\n          yId = 0;\n        }\n      }\n      function brushSymbol(x, y, width, height, symbolType) {\n        var scale = isSVG ? 1 : dpr;\n        var symbol = createSymbol(symbolType, x * scale, y * scale, width * scale, height * scale, decalOpt.color, decalOpt.symbolKeepAspect);\n        if (isSVG) {\n          var symbolVNode = zr.painter.renderOneToVNode(symbol);\n          if (symbolVNode) {\n            svgRoot.children.push(symbolVNode);\n          }\n        } else {\n          // Paint to canvas for all other renderers.\n          brushSingle(ctx, symbol);\n        }\n      }\n    }\n  }\n}\n/**\r\n * Convert symbol array into normalized array\r\n *\r\n * @param {string | (string | string[])[]} symbol symbol input\r\n * @return {string[][]} normolized symbol array\r\n */\nfunction normalizeSymbolArray(symbol) {\n  if (!symbol || symbol.length === 0) {\n    return [['rect']];\n  }\n  if (isString(symbol)) {\n    return [[symbol]];\n  }\n  var isAllString = true;\n  for (var i = 0; i < symbol.length; ++i) {\n    if (!isString(symbol[i])) {\n      isAllString = false;\n      break;\n    }\n  }\n  if (isAllString) {\n    return normalizeSymbolArray([symbol]);\n  }\n  var result = [];\n  for (var i = 0; i < symbol.length; ++i) {\n    if (isString(symbol[i])) {\n      result.push([symbol[i]]);\n    } else {\n      result.push(symbol[i]);\n    }\n  }\n  return result;\n}\n/**\r\n * Convert dash input into dashArray\r\n *\r\n * @param {DecalDashArrayX} dash dash input\r\n * @return {number[][]} normolized dash array\r\n */\nfunction normalizeDashArrayX(dash) {\n  if (!dash || dash.length === 0) {\n    return [[0, 0]];\n  }\n  if (isNumber(dash)) {\n    var dashValue = Math.ceil(dash);\n    return [[dashValue, dashValue]];\n  }\n  /**\r\n   * [20, 5] should be normalized into [[20, 5]],\r\n   * while [20, [5, 10]] should be normalized into [[20, 20], [5, 10]]\r\n   */\n  var isAllNumber = true;\n  for (var i = 0; i < dash.length; ++i) {\n    if (!isNumber(dash[i])) {\n      isAllNumber = false;\n      break;\n    }\n  }\n  if (isAllNumber) {\n    return normalizeDashArrayX([dash]);\n  }\n  var result = [];\n  for (var i = 0; i < dash.length; ++i) {\n    if (isNumber(dash[i])) {\n      var dashValue = Math.ceil(dash[i]);\n      result.push([dashValue, dashValue]);\n    } else {\n      var dashValue = map(dash[i], function (n) {\n        return Math.ceil(n);\n      });\n      if (dashValue.length % 2 === 1) {\n        // [4, 2, 1] means |----  -    -- |----  -    -- |\n        // so normalize it to be [4, 2, 1, 4, 2, 1]\n        result.push(dashValue.concat(dashValue));\n      } else {\n        result.push(dashValue);\n      }\n    }\n  }\n  return result;\n}\n/**\r\n * Convert dash input into dashArray\r\n *\r\n * @param {DecalDashArrayY} dash dash input\r\n * @return {number[]} normolized dash array\r\n */\nfunction normalizeDashArrayY(dash) {\n  if (!dash || typeof dash === 'object' && dash.length === 0) {\n    return [0, 0];\n  }\n  if (isNumber(dash)) {\n    var dashValue_1 = Math.ceil(dash);\n    return [dashValue_1, dashValue_1];\n  }\n  var dashValue = map(dash, function (n) {\n    return Math.ceil(n);\n  });\n  return dash.length % 2 ? dashValue.concat(dashValue) : dashValue;\n}\n/**\r\n * Get block length of each line. A block is the length of dash line and space.\r\n * For example, a line with [4, 1] has a dash line of 4 and a space of 1 after\r\n * that, so the block length of this line is 5.\r\n *\r\n * @param {number[][]} dash dash array of X or Y\r\n * @return {number[]} block length of each line\r\n */\nfunction getLineBlockLengthX(dash) {\n  return map(dash, function (line) {\n    return getLineBlockLengthY(line);\n  });\n}\nfunction getLineBlockLengthY(dash) {\n  var blockLength = 0;\n  for (var i = 0; i < dash.length; ++i) {\n    blockLength += dash[i];\n  }\n  if (dash.length % 2 === 1) {\n    // [4, 2, 1] means |----  -    -- |----  -    -- |\n    // So total length is (4 + 2 + 1) * 2\n    return blockLength * 2;\n  }\n  return blockLength;\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,OAAO,MAAM,6BAA6B;AACjD,OAAOC,GAAG,MAAM,yBAAyB;AACzC,SAASC,QAAQ,EAAEC,GAAG,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,0BAA0B;AACrF,SAASC,sBAAsB,QAAQ,aAAa;AACpD,SAASC,YAAY,QAAQ,aAAa;AAC1C,SAASC,WAAW,QAAQ,+BAA+B;AAC3D,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,IAAIC,QAAQ,GAAG,IAAIX,OAAO,CAAC,CAAC;AAC5B,IAAIY,UAAU,GAAG,IAAIX,GAAG,CAAC,GAAG,CAAC;AAC7B,IAAIY,SAAS,GAAG,CAAC,QAAQ,EAAE,YAAY,EAAE,kBAAkB,EAAE,OAAO,EAAE,iBAAiB,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,eAAe,CAAC;AACrJ;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,8BAA8BA,CAACC,WAAW,EAAEC,GAAG,EAAE;EAC/D,IAAID,WAAW,KAAK,MAAM,EAAE;IAC1B,OAAO,IAAI;EACb;EACA,IAAIE,GAAG,GAAGD,GAAG,CAACE,mBAAmB,CAAC,CAAC;EACnC,IAAIC,EAAE,GAAGH,GAAG,CAACI,KAAK,CAAC,CAAC;EACpB,IAAIC,KAAK,GAAGF,EAAE,CAACG,OAAO,CAACC,IAAI,KAAK,KAAK;EACrC,IAAIR,WAAW,CAACS,KAAK,EAAE;IACrBb,QAAQ,CAAC,QAAQ,CAAC,CAACI,WAAW,CAAC;EACjC;EACA,IAAIU,UAAU,GAAGd,QAAQ,CAACe,GAAG,CAACX,WAAW,CAAC;EAC1C,IAAIU,UAAU,EAAE;IACd,OAAOA,UAAU;EACnB;EACA,IAAIE,QAAQ,GAAGzB,QAAQ,CAACa,WAAW,EAAE;IACnCa,MAAM,EAAE,MAAM;IACdC,UAAU,EAAE,CAAC;IACbC,gBAAgB,EAAE,IAAI;IACtBC,KAAK,EAAE,oBAAoB;IAC3BC,eAAe,EAAE,IAAI;IACrBC,UAAU,EAAE,CAAC;IACbC,UAAU,EAAE,CAAC;IACbC,QAAQ,EAAE,CAAC;IACXC,YAAY,EAAE,GAAG;IACjBC,aAAa,EAAE;EACjB,CAAC,CAAC;EACF,IAAIV,QAAQ,CAACK,eAAe,KAAK,MAAM,EAAE;IACvCL,QAAQ,CAACK,eAAe,GAAG,IAAI;EACjC;EACA,IAAIM,OAAO,GAAG;IACZC,MAAM,EAAE;EACV,CAAC;EACDC,iBAAiB,CAACF,OAAO,CAAC;EAC1BA,OAAO,CAACH,QAAQ,GAAGR,QAAQ,CAACQ,QAAQ;EACpCG,OAAO,CAACG,MAAM,GAAGH,OAAO,CAACI,MAAM,GAAGrB,KAAK,GAAG,CAAC,GAAG,CAAC,GAAGJ,GAAG;EACrDN,QAAQ,CAACgC,GAAG,CAAC5B,WAAW,EAAEuB,OAAO,CAAC;EAClCvB,WAAW,CAACS,KAAK,GAAG,KAAK;EACzB,OAAOc,OAAO;EACd,SAASE,iBAAiBA,CAACF,OAAO,EAAE;IAClC,IAAIM,IAAI,GAAG,CAAC3B,GAAG,CAAC;IAChB,IAAI4B,UAAU,GAAG,IAAI;IACrB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjC,SAAS,CAACkC,MAAM,EAAE,EAAED,CAAC,EAAE;MACzC,IAAIE,KAAK,GAAGrB,QAAQ,CAACd,SAAS,CAACiC,CAAC,CAAC,CAAC;MAClC,IAAIE,KAAK,IAAI,IAAI,IAAI,CAAC5C,OAAO,CAAC4C,KAAK,CAAC,IAAI,CAAC3C,QAAQ,CAAC2C,KAAK,CAAC,IAAI,CAAC1C,QAAQ,CAAC0C,KAAK,CAAC,IAAI,OAAOA,KAAK,KAAK,SAAS,EAAE;QAC1GH,UAAU,GAAG,KAAK;QAClB;MACF;MACAD,IAAI,CAACK,IAAI,CAACD,KAAK,CAAC;IAClB;IACA,IAAIE,QAAQ;IACZ,IAAIL,UAAU,EAAE;MACdK,QAAQ,GAAGN,IAAI,CAACO,IAAI,CAAC,GAAG,CAAC,IAAI9B,KAAK,GAAG,MAAM,GAAG,EAAE,CAAC;MACjD,IAAI+B,KAAK,GAAGxC,UAAU,CAACc,GAAG,CAACwB,QAAQ,CAAC;MACpC,IAAIE,KAAK,EAAE;QACT/B,KAAK,GAAGiB,OAAO,CAACe,UAAU,GAAGD,KAAK,GAAGd,OAAO,CAACgB,KAAK,GAAGF,KAAK;MAC5D;IACF;IACA,IAAInB,UAAU,GAAGsB,mBAAmB,CAAC5B,QAAQ,CAACM,UAAU,CAAC;IACzD,IAAIC,UAAU,GAAGsB,mBAAmB,CAAC7B,QAAQ,CAACO,UAAU,CAAC;IACzD,IAAIuB,WAAW,GAAGC,oBAAoB,CAAC/B,QAAQ,CAACC,MAAM,CAAC;IACvD,IAAI+B,iBAAiB,GAAGC,mBAAmB,CAAC3B,UAAU,CAAC;IACvD,IAAI4B,gBAAgB,GAAGC,mBAAmB,CAAC5B,UAAU,CAAC;IACtD,IAAI6B,MAAM,GAAG,CAAC1C,KAAK,IAAIX,WAAW,CAACsD,YAAY,CAAC,CAAC;IACjD,IAAIC,OAAO,GAAG5C,KAAK,IAAI;MACrB6C,GAAG,EAAE,GAAG;MACRC,KAAK,EAAE,CAAC,CAAC;MACTC,GAAG,EAAE,KAAK;MACVC,QAAQ,EAAE;IACZ,CAAC;IACD,IAAIC,KAAK,GAAGC,cAAc,CAAC,CAAC;IAC5B,IAAIC,GAAG;IACP,IAAIT,MAAM,EAAE;MACVA,MAAM,CAACU,KAAK,GAAGH,KAAK,CAACG,KAAK,GAAGxD,GAAG;MAChC8C,MAAM,CAACW,MAAM,GAAGJ,KAAK,CAACI,MAAM,GAAGzD,GAAG;MAClCuD,GAAG,GAAGT,MAAM,CAACY,UAAU,CAAC,IAAI,CAAC;IAC/B;IACAC,UAAU,CAAC,CAAC;IACZ,IAAI/B,UAAU,EAAE;MACdjC,UAAU,CAACiE,GAAG,CAAC3B,QAAQ,EAAEa,MAAM,IAAIE,OAAO,CAAC;IAC7C;IACA3B,OAAO,CAACgB,KAAK,GAAGS,MAAM;IACtBzB,OAAO,CAACe,UAAU,GAAGY,OAAO;IAC5B3B,OAAO,CAACwC,QAAQ,GAAGR,KAAK,CAACG,KAAK;IAC9BnC,OAAO,CAACyC,SAAS,GAAGT,KAAK,CAACI,MAAM;IAChC;AACJ;AACA;AACA;AACA;IACI,SAASH,cAAcA,CAAA,EAAG;MACxB;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACM,IAAIE,KAAK,GAAG,CAAC;MACb,KAAK,IAAI3B,CAAC,GAAG,CAAC,EAAEkC,IAAI,GAAGrB,iBAAiB,CAACZ,MAAM,EAAED,CAAC,GAAGkC,IAAI,EAAE,EAAElC,CAAC,EAAE;QAC9D2B,KAAK,GAAGlE,sBAAsB,CAACkE,KAAK,EAAEd,iBAAiB,CAACb,CAAC,CAAC,CAAC;MAC7D;MACA,IAAImC,aAAa,GAAG,CAAC;MACrB,KAAK,IAAInC,CAAC,GAAG,CAAC,EAAEkC,IAAI,GAAGvB,WAAW,CAACV,MAAM,EAAED,CAAC,GAAGkC,IAAI,EAAE,EAAElC,CAAC,EAAE;QACxDmC,aAAa,GAAG1E,sBAAsB,CAAC0E,aAAa,EAAExB,WAAW,CAACX,CAAC,CAAC,CAACC,MAAM,CAAC;MAC9E;MACA0B,KAAK,IAAIQ,aAAa;MACtB,IAAIP,MAAM,GAAGb,gBAAgB,GAAGF,iBAAiB,CAACZ,MAAM,GAAGU,WAAW,CAACV,MAAM;MAC7E,IAAImC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,IAAIC,IAAI,GAAG,SAAAA,CAAUC,QAAQ,EAAE;UAC7B;UACAC,OAAO,CAACF,IAAI,CAAC,wCAAwC,GAAGC,QAAQ,GAAG,mCAAmC,GAAGA,QAAQ,GAAG,wGAAwG,GAAGA,QAAQ,GAAG,sCAAsC,CAAC;QACnR,CAAC;QACD,IAAIb,KAAK,GAAG9C,QAAQ,CAACS,YAAY,EAAE;UACjCiD,IAAI,CAAC,cAAc,CAAC;QACtB;QACA,IAAIX,MAAM,GAAG/C,QAAQ,CAACU,aAAa,EAAE;UACnCgD,IAAI,CAAC,eAAe,CAAC;QACvB;MACF;MACA,OAAO;QACLZ,KAAK,EAAEe,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAACjB,KAAK,EAAE9C,QAAQ,CAACS,YAAY,CAAC,CAAC;QAC1DsC,MAAM,EAAEc,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAChB,MAAM,EAAE/C,QAAQ,CAACU,aAAa,CAAC;MAC9D,CAAC;IACH;IACA,SAASuC,UAAUA,CAAA,EAAG;MACpB,IAAIJ,GAAG,EAAE;QACPA,GAAG,CAACmB,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE5B,MAAM,CAACU,KAAK,EAAEV,MAAM,CAACW,MAAM,CAAC;QAChD,IAAI/C,QAAQ,CAACK,eAAe,EAAE;UAC5BwC,GAAG,CAACoB,SAAS,GAAGjE,QAAQ,CAACK,eAAe;UACxCwC,GAAG,CAACqB,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE9B,MAAM,CAACU,KAAK,EAAEV,MAAM,CAACW,MAAM,CAAC;QACjD;MACF;MACA,IAAIoB,IAAI,GAAG,CAAC;MACZ,KAAK,IAAIhD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,UAAU,CAACa,MAAM,EAAE,EAAED,CAAC,EAAE;QAC1CgD,IAAI,IAAI5D,UAAU,CAACY,CAAC,CAAC;MACvB;MACA,IAAIgD,IAAI,IAAI,CAAC,EAAE;QACb;QACA;MACF;MACA,IAAIC,CAAC,GAAG,CAAClC,gBAAgB;MACzB,IAAImC,GAAG,GAAG,CAAC;MACX,IAAIC,QAAQ,GAAG,CAAC;MAChB,IAAIC,IAAI,GAAG,CAAC;MACZ,OAAOH,CAAC,GAAGzB,KAAK,CAACI,MAAM,EAAE;QACvB,IAAIsB,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE;UACjB,IAAIG,SAAS,GAAGF,QAAQ,GAAG,CAAC,GAAGxC,WAAW,CAACV,MAAM;UACjD,IAAIqD,CAAC,GAAG,CAAC;UACT,IAAIC,IAAI,GAAG,CAAC;UACZ,IAAIC,SAAS,GAAG,CAAC;UACjB,OAAOF,CAAC,GAAG9B,KAAK,CAACG,KAAK,GAAG,CAAC,EAAE;YAC1B,IAAI8B,IAAI,GAAG,CAAC;YACZ,KAAK,IAAIzD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,UAAU,CAACiE,IAAI,CAAC,CAACnD,MAAM,EAAE,EAAED,CAAC,EAAE;cAChDyD,IAAI,IAAItE,UAAU,CAACiE,IAAI,CAAC,CAACpD,CAAC,CAAC;YAC7B;YACA,IAAIyD,IAAI,IAAI,CAAC,EAAE;cACb;cACA;YACF;YACA;YACA,IAAIF,IAAI,GAAG,CAAC,KAAK,CAAC,EAAE;cAClB,IAAIG,IAAI,GAAG,CAAC,CAAC,GAAG7E,QAAQ,CAACE,UAAU,IAAI,GAAG;cAC1C,IAAI4E,IAAI,GAAGL,CAAC,GAAGnE,UAAU,CAACiE,IAAI,CAAC,CAACG,IAAI,CAAC,GAAGG,IAAI;cAC5C,IAAIE,KAAK,GAAGX,CAAC,GAAG7D,UAAU,CAAC8D,GAAG,CAAC,GAAGQ,IAAI;cACtC,IAAI/B,KAAK,GAAGxC,UAAU,CAACiE,IAAI,CAAC,CAACG,IAAI,CAAC,GAAG1E,QAAQ,CAACE,UAAU;cACxD,IAAI6C,MAAM,GAAGxC,UAAU,CAAC8D,GAAG,CAAC,GAAGrE,QAAQ,CAACE,UAAU;cAClD,IAAI8E,SAAS,GAAGL,SAAS,GAAG,CAAC,GAAG7C,WAAW,CAAC0C,SAAS,CAAC,CAACpD,MAAM;cAC7D6D,WAAW,CAACH,IAAI,EAAEC,KAAK,EAAEjC,KAAK,EAAEC,MAAM,EAAEjB,WAAW,CAAC0C,SAAS,CAAC,CAACQ,SAAS,CAAC,CAAC;YAC5E;YACAP,CAAC,IAAInE,UAAU,CAACiE,IAAI,CAAC,CAACG,IAAI,CAAC;YAC3B,EAAEC,SAAS;YACX,EAAED,IAAI;YACN,IAAIA,IAAI,KAAKpE,UAAU,CAACiE,IAAI,CAAC,CAACnD,MAAM,EAAE;cACpCsD,IAAI,GAAG,CAAC;YACV;UACF;UACA,EAAEH,IAAI;UACN,IAAIA,IAAI,KAAKjE,UAAU,CAACc,MAAM,EAAE;YAC9BmD,IAAI,GAAG,CAAC;UACV;QACF;QACAH,CAAC,IAAI7D,UAAU,CAAC8D,GAAG,CAAC;QACpB,EAAEC,QAAQ;QACV,EAAED,GAAG;QACL,IAAIA,GAAG,KAAK9D,UAAU,CAACa,MAAM,EAAE;UAC7BiD,GAAG,GAAG,CAAC;QACT;MACF;MACA,SAASY,WAAWA,CAACR,CAAC,EAAEL,CAAC,EAAEtB,KAAK,EAAEC,MAAM,EAAEmC,UAAU,EAAE;QACpD,IAAIC,KAAK,GAAGzF,KAAK,GAAG,CAAC,GAAGJ,GAAG;QAC3B,IAAIW,MAAM,GAAGpB,YAAY,CAACqG,UAAU,EAAET,CAAC,GAAGU,KAAK,EAAEf,CAAC,GAAGe,KAAK,EAAErC,KAAK,GAAGqC,KAAK,EAAEpC,MAAM,GAAGoC,KAAK,EAAEnF,QAAQ,CAACI,KAAK,EAAEJ,QAAQ,CAACG,gBAAgB,CAAC;QACrI,IAAIT,KAAK,EAAE;UACT,IAAI0F,WAAW,GAAG5F,EAAE,CAACG,OAAO,CAAC0F,gBAAgB,CAACpF,MAAM,CAAC;UACrD,IAAImF,WAAW,EAAE;YACf9C,OAAO,CAACI,QAAQ,CAACpB,IAAI,CAAC8D,WAAW,CAAC;UACpC;QACF,CAAC,MAAM;UACL;UACAtG,WAAW,CAAC+D,GAAG,EAAE5C,MAAM,CAAC;QAC1B;MACF;IACF;EACF;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS8B,oBAAoBA,CAAC9B,MAAM,EAAE;EACpC,IAAI,CAACA,MAAM,IAAIA,MAAM,CAACmB,MAAM,KAAK,CAAC,EAAE;IAClC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC;EACnB;EACA,IAAI1C,QAAQ,CAACuB,MAAM,CAAC,EAAE;IACpB,OAAO,CAAC,CAACA,MAAM,CAAC,CAAC;EACnB;EACA,IAAIqF,WAAW,GAAG,IAAI;EACtB,KAAK,IAAInE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlB,MAAM,CAACmB,MAAM,EAAE,EAAED,CAAC,EAAE;IACtC,IAAI,CAACzC,QAAQ,CAACuB,MAAM,CAACkB,CAAC,CAAC,CAAC,EAAE;MACxBmE,WAAW,GAAG,KAAK;MACnB;IACF;EACF;EACA,IAAIA,WAAW,EAAE;IACf,OAAOvD,oBAAoB,CAAC,CAAC9B,MAAM,CAAC,CAAC;EACvC;EACA,IAAIsF,MAAM,GAAG,EAAE;EACf,KAAK,IAAIpE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlB,MAAM,CAACmB,MAAM,EAAE,EAAED,CAAC,EAAE;IACtC,IAAIzC,QAAQ,CAACuB,MAAM,CAACkB,CAAC,CAAC,CAAC,EAAE;MACvBoE,MAAM,CAACjE,IAAI,CAAC,CAACrB,MAAM,CAACkB,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,MAAM;MACLoE,MAAM,CAACjE,IAAI,CAACrB,MAAM,CAACkB,CAAC,CAAC,CAAC;IACxB;EACF;EACA,OAAOoE,MAAM;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS3D,mBAAmBA,CAAC4D,IAAI,EAAE;EACjC,IAAI,CAACA,IAAI,IAAIA,IAAI,CAACpE,MAAM,KAAK,CAAC,EAAE;IAC9B,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACjB;EACA,IAAIzC,QAAQ,CAAC6G,IAAI,CAAC,EAAE;IAClB,IAAIC,SAAS,GAAG5B,IAAI,CAAC6B,IAAI,CAACF,IAAI,CAAC;IAC/B,OAAO,CAAC,CAACC,SAAS,EAAEA,SAAS,CAAC,CAAC;EACjC;EACA;AACF;AACA;AACA;EACE,IAAIE,WAAW,GAAG,IAAI;EACtB,KAAK,IAAIxE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqE,IAAI,CAACpE,MAAM,EAAE,EAAED,CAAC,EAAE;IACpC,IAAI,CAACxC,QAAQ,CAAC6G,IAAI,CAACrE,CAAC,CAAC,CAAC,EAAE;MACtBwE,WAAW,GAAG,KAAK;MACnB;IACF;EACF;EACA,IAAIA,WAAW,EAAE;IACf,OAAO/D,mBAAmB,CAAC,CAAC4D,IAAI,CAAC,CAAC;EACpC;EACA,IAAID,MAAM,GAAG,EAAE;EACf,KAAK,IAAIpE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqE,IAAI,CAACpE,MAAM,EAAE,EAAED,CAAC,EAAE;IACpC,IAAIxC,QAAQ,CAAC6G,IAAI,CAACrE,CAAC,CAAC,CAAC,EAAE;MACrB,IAAIsE,SAAS,GAAG5B,IAAI,CAAC6B,IAAI,CAACF,IAAI,CAACrE,CAAC,CAAC,CAAC;MAClCoE,MAAM,CAACjE,IAAI,CAAC,CAACmE,SAAS,EAAEA,SAAS,CAAC,CAAC;IACrC,CAAC,MAAM;MACL,IAAIA,SAAS,GAAGjH,GAAG,CAACgH,IAAI,CAACrE,CAAC,CAAC,EAAE,UAAUyE,CAAC,EAAE;QACxC,OAAO/B,IAAI,CAAC6B,IAAI,CAACE,CAAC,CAAC;MACrB,CAAC,CAAC;MACF,IAAIH,SAAS,CAACrE,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;QAC9B;QACA;QACAmE,MAAM,CAACjE,IAAI,CAACmE,SAAS,CAACI,MAAM,CAACJ,SAAS,CAAC,CAAC;MAC1C,CAAC,MAAM;QACLF,MAAM,CAACjE,IAAI,CAACmE,SAAS,CAAC;MACxB;IACF;EACF;EACA,OAAOF,MAAM;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS1D,mBAAmBA,CAAC2D,IAAI,EAAE;EACjC,IAAI,CAACA,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAACpE,MAAM,KAAK,CAAC,EAAE;IAC1D,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;EACf;EACA,IAAIzC,QAAQ,CAAC6G,IAAI,CAAC,EAAE;IAClB,IAAIM,WAAW,GAAGjC,IAAI,CAAC6B,IAAI,CAACF,IAAI,CAAC;IACjC,OAAO,CAACM,WAAW,EAAEA,WAAW,CAAC;EACnC;EACA,IAAIL,SAAS,GAAGjH,GAAG,CAACgH,IAAI,EAAE,UAAUI,CAAC,EAAE;IACrC,OAAO/B,IAAI,CAAC6B,IAAI,CAACE,CAAC,CAAC;EACrB,CAAC,CAAC;EACF,OAAOJ,IAAI,CAACpE,MAAM,GAAG,CAAC,GAAGqE,SAAS,CAACI,MAAM,CAACJ,SAAS,CAAC,GAAGA,SAAS;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASxD,mBAAmBA,CAACuD,IAAI,EAAE;EACjC,OAAOhH,GAAG,CAACgH,IAAI,EAAE,UAAUO,IAAI,EAAE;IAC/B,OAAO5D,mBAAmB,CAAC4D,IAAI,CAAC;EAClC,CAAC,CAAC;AACJ;AACA,SAAS5D,mBAAmBA,CAACqD,IAAI,EAAE;EACjC,IAAIQ,WAAW,GAAG,CAAC;EACnB,KAAK,IAAI7E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqE,IAAI,CAACpE,MAAM,EAAE,EAAED,CAAC,EAAE;IACpC6E,WAAW,IAAIR,IAAI,CAACrE,CAAC,CAAC;EACxB;EACA,IAAIqE,IAAI,CAACpE,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;IACzB;IACA;IACA,OAAO4E,WAAW,GAAG,CAAC;EACxB;EACA,OAAOA,WAAW;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}