{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport Path from 'zrender/lib/graphic/Path.js';\nvar PointerShape = /** @class */function () {\n  function PointerShape() {\n    this.angle = 0;\n    this.width = 10;\n    this.r = 10;\n    this.x = 0;\n    this.y = 0;\n  }\n  return PointerShape;\n}();\nvar PointerPath = /** @class */function (_super) {\n  __extends(PointerPath, _super);\n  function PointerPath(opts) {\n    var _this = _super.call(this, opts) || this;\n    _this.type = 'pointer';\n    return _this;\n  }\n  PointerPath.prototype.getDefaultShape = function () {\n    return new PointerShape();\n  };\n  PointerPath.prototype.buildPath = function (ctx, shape) {\n    var mathCos = Math.cos;\n    var mathSin = Math.sin;\n    var r = shape.r;\n    var width = shape.width;\n    var angle = shape.angle;\n    var x = shape.x - mathCos(angle) * width * (width >= r / 3 ? 1 : 2);\n    var y = shape.y - mathSin(angle) * width * (width >= r / 3 ? 1 : 2);\n    angle = shape.angle - Math.PI / 2;\n    ctx.moveTo(x, y);\n    ctx.lineTo(shape.x + mathCos(angle) * width, shape.y + mathSin(angle) * width);\n    ctx.lineTo(shape.x + mathCos(shape.angle) * r, shape.y + mathSin(shape.angle) * r);\n    ctx.lineTo(shape.x - mathCos(angle) * width, shape.y - mathSin(angle) * width);\n    ctx.lineTo(x, y);\n  };\n  return PointerPath;\n}(Path);\nexport default PointerPath;", "map": {"version": 3, "names": ["__extends", "Path", "<PERSON>er<PERSON><PERSON><PERSON>", "angle", "width", "r", "x", "y", "<PERSON><PERSON><PERSON><PERSON>", "_super", "opts", "_this", "call", "type", "prototype", "getDefaultShape", "buildPath", "ctx", "shape", "mathCos", "Math", "cos", "mathSin", "sin", "PI", "moveTo", "lineTo"], "sources": ["E:/AI/SmartCV/node_modules/echarts/lib/chart/gauge/PointerPath.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport Path from 'zrender/lib/graphic/Path.js';\nvar PointerShape = /** @class */function () {\n  function PointerShape() {\n    this.angle = 0;\n    this.width = 10;\n    this.r = 10;\n    this.x = 0;\n    this.y = 0;\n  }\n  return PointerShape;\n}();\nvar PointerPath = /** @class */function (_super) {\n  __extends(PointerPath, _super);\n  function PointerPath(opts) {\n    var _this = _super.call(this, opts) || this;\n    _this.type = 'pointer';\n    return _this;\n  }\n  PointerPath.prototype.getDefaultShape = function () {\n    return new PointerShape();\n  };\n  PointerPath.prototype.buildPath = function (ctx, shape) {\n    var mathCos = Math.cos;\n    var mathSin = Math.sin;\n    var r = shape.r;\n    var width = shape.width;\n    var angle = shape.angle;\n    var x = shape.x - mathCos(angle) * width * (width >= r / 3 ? 1 : 2);\n    var y = shape.y - mathSin(angle) * width * (width >= r / 3 ? 1 : 2);\n    angle = shape.angle - Math.PI / 2;\n    ctx.moveTo(x, y);\n    ctx.lineTo(shape.x + mathCos(angle) * width, shape.y + mathSin(angle) * width);\n    ctx.lineTo(shape.x + mathCos(shape.angle) * r, shape.y + mathSin(shape.angle) * r);\n    ctx.lineTo(shape.x - mathCos(angle) * width, shape.y - mathSin(angle) * width);\n    ctx.lineTo(x, y);\n  };\n  return PointerPath;\n}(Path);\nexport default PointerPath;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,IAAI,MAAM,6BAA6B;AAC9C,IAAIC,YAAY,GAAG,aAAa,YAAY;EAC1C,SAASA,YAAYA,CAAA,EAAG;IACtB,IAAI,CAACC,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,CAAC,GAAG,EAAE;IACX,IAAI,CAACC,CAAC,GAAG,CAAC;IACV,IAAI,CAACC,CAAC,GAAG,CAAC;EACZ;EACA,OAAOL,YAAY;AACrB,CAAC,CAAC,CAAC;AACH,IAAIM,WAAW,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC/CT,SAAS,CAACQ,WAAW,EAAEC,MAAM,CAAC;EAC9B,SAASD,WAAWA,CAACE,IAAI,EAAE;IACzB,IAAIC,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,EAAEF,IAAI,CAAC,IAAI,IAAI;IAC3CC,KAAK,CAACE,IAAI,GAAG,SAAS;IACtB,OAAOF,KAAK;EACd;EACAH,WAAW,CAACM,SAAS,CAACC,eAAe,GAAG,YAAY;IAClD,OAAO,IAAIb,YAAY,CAAC,CAAC;EAC3B,CAAC;EACDM,WAAW,CAACM,SAAS,CAACE,SAAS,GAAG,UAAUC,GAAG,EAAEC,KAAK,EAAE;IACtD,IAAIC,OAAO,GAAGC,IAAI,CAACC,GAAG;IACtB,IAAIC,OAAO,GAAGF,IAAI,CAACG,GAAG;IACtB,IAAIlB,CAAC,GAAGa,KAAK,CAACb,CAAC;IACf,IAAID,KAAK,GAAGc,KAAK,CAACd,KAAK;IACvB,IAAID,KAAK,GAAGe,KAAK,CAACf,KAAK;IACvB,IAAIG,CAAC,GAAGY,KAAK,CAACZ,CAAC,GAAGa,OAAO,CAAChB,KAAK,CAAC,GAAGC,KAAK,IAAIA,KAAK,IAAIC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACnE,IAAIE,CAAC,GAAGW,KAAK,CAACX,CAAC,GAAGe,OAAO,CAACnB,KAAK,CAAC,GAAGC,KAAK,IAAIA,KAAK,IAAIC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACnEF,KAAK,GAAGe,KAAK,CAACf,KAAK,GAAGiB,IAAI,CAACI,EAAE,GAAG,CAAC;IACjCP,GAAG,CAACQ,MAAM,CAACnB,CAAC,EAAEC,CAAC,CAAC;IAChBU,GAAG,CAACS,MAAM,CAACR,KAAK,CAACZ,CAAC,GAAGa,OAAO,CAAChB,KAAK,CAAC,GAAGC,KAAK,EAAEc,KAAK,CAACX,CAAC,GAAGe,OAAO,CAACnB,KAAK,CAAC,GAAGC,KAAK,CAAC;IAC9Ea,GAAG,CAACS,MAAM,CAACR,KAAK,CAACZ,CAAC,GAAGa,OAAO,CAACD,KAAK,CAACf,KAAK,CAAC,GAAGE,CAAC,EAAEa,KAAK,CAACX,CAAC,GAAGe,OAAO,CAACJ,KAAK,CAACf,KAAK,CAAC,GAAGE,CAAC,CAAC;IAClFY,GAAG,CAACS,MAAM,CAACR,KAAK,CAACZ,CAAC,GAAGa,OAAO,CAAChB,KAAK,CAAC,GAAGC,KAAK,EAAEc,KAAK,CAACX,CAAC,GAAGe,OAAO,CAACnB,KAAK,CAAC,GAAGC,KAAK,CAAC;IAC9Ea,GAAG,CAACS,MAAM,CAACpB,CAAC,EAAEC,CAAC,CAAC;EAClB,CAAC;EACD,OAAOC,WAAW;AACpB,CAAC,CAACP,IAAI,CAAC;AACP,eAAeO,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}