{"ast": null, "code": "var Point = function () {\n  function Point(x, y) {\n    this.x = x || 0;\n    this.y = y || 0;\n  }\n  Point.prototype.copy = function (other) {\n    this.x = other.x;\n    this.y = other.y;\n    return this;\n  };\n  Point.prototype.clone = function () {\n    return new Point(this.x, this.y);\n  };\n  Point.prototype.set = function (x, y) {\n    this.x = x;\n    this.y = y;\n    return this;\n  };\n  Point.prototype.equal = function (other) {\n    return other.x === this.x && other.y === this.y;\n  };\n  Point.prototype.add = function (other) {\n    this.x += other.x;\n    this.y += other.y;\n    return this;\n  };\n  Point.prototype.scale = function (scalar) {\n    this.x *= scalar;\n    this.y *= scalar;\n  };\n  Point.prototype.scaleAndAdd = function (other, scalar) {\n    this.x += other.x * scalar;\n    this.y += other.y * scalar;\n  };\n  Point.prototype.sub = function (other) {\n    this.x -= other.x;\n    this.y -= other.y;\n    return this;\n  };\n  Point.prototype.dot = function (other) {\n    return this.x * other.x + this.y * other.y;\n  };\n  Point.prototype.len = function () {\n    return Math.sqrt(this.x * this.x + this.y * this.y);\n  };\n  Point.prototype.lenSquare = function () {\n    return this.x * this.x + this.y * this.y;\n  };\n  Point.prototype.normalize = function () {\n    var len = this.len();\n    this.x /= len;\n    this.y /= len;\n    return this;\n  };\n  Point.prototype.distance = function (other) {\n    var dx = this.x - other.x;\n    var dy = this.y - other.y;\n    return Math.sqrt(dx * dx + dy * dy);\n  };\n  Point.prototype.distanceSquare = function (other) {\n    var dx = this.x - other.x;\n    var dy = this.y - other.y;\n    return dx * dx + dy * dy;\n  };\n  Point.prototype.negate = function () {\n    this.x = -this.x;\n    this.y = -this.y;\n    return this;\n  };\n  Point.prototype.transform = function (m) {\n    if (!m) {\n      return;\n    }\n    var x = this.x;\n    var y = this.y;\n    this.x = m[0] * x + m[2] * y + m[4];\n    this.y = m[1] * x + m[3] * y + m[5];\n    return this;\n  };\n  Point.prototype.toArray = function (out) {\n    out[0] = this.x;\n    out[1] = this.y;\n    return out;\n  };\n  Point.prototype.fromArray = function (input) {\n    this.x = input[0];\n    this.y = input[1];\n  };\n  Point.set = function (p, x, y) {\n    p.x = x;\n    p.y = y;\n  };\n  Point.copy = function (p, p2) {\n    p.x = p2.x;\n    p.y = p2.y;\n  };\n  Point.len = function (p) {\n    return Math.sqrt(p.x * p.x + p.y * p.y);\n  };\n  Point.lenSquare = function (p) {\n    return p.x * p.x + p.y * p.y;\n  };\n  Point.dot = function (p0, p1) {\n    return p0.x * p1.x + p0.y * p1.y;\n  };\n  Point.add = function (out, p0, p1) {\n    out.x = p0.x + p1.x;\n    out.y = p0.y + p1.y;\n  };\n  Point.sub = function (out, p0, p1) {\n    out.x = p0.x - p1.x;\n    out.y = p0.y - p1.y;\n  };\n  Point.scale = function (out, p0, scalar) {\n    out.x = p0.x * scalar;\n    out.y = p0.y * scalar;\n  };\n  Point.scaleAndAdd = function (out, p0, p1, scalar) {\n    out.x = p0.x + p1.x * scalar;\n    out.y = p0.y + p1.y * scalar;\n  };\n  Point.lerp = function (out, p0, p1, t) {\n    var onet = 1 - t;\n    out.x = onet * p0.x + t * p1.x;\n    out.y = onet * p0.y + t * p1.y;\n  };\n  return Point;\n}();\nexport default Point;", "map": {"version": 3, "names": ["Point", "x", "y", "prototype", "copy", "other", "clone", "set", "equal", "add", "scale", "scalar", "scaleAndAdd", "sub", "dot", "len", "Math", "sqrt", "lenSquare", "normalize", "distance", "dx", "dy", "distanceSquare", "negate", "transform", "m", "toArray", "out", "fromArray", "input", "p", "p2", "p0", "p1", "lerp", "t", "onet"], "sources": ["E:/AI/SmartCV/node_modules/zrender/lib/core/Point.js"], "sourcesContent": ["var Point = (function () {\n    function Point(x, y) {\n        this.x = x || 0;\n        this.y = y || 0;\n    }\n    Point.prototype.copy = function (other) {\n        this.x = other.x;\n        this.y = other.y;\n        return this;\n    };\n    Point.prototype.clone = function () {\n        return new Point(this.x, this.y);\n    };\n    Point.prototype.set = function (x, y) {\n        this.x = x;\n        this.y = y;\n        return this;\n    };\n    Point.prototype.equal = function (other) {\n        return other.x === this.x && other.y === this.y;\n    };\n    Point.prototype.add = function (other) {\n        this.x += other.x;\n        this.y += other.y;\n        return this;\n    };\n    Point.prototype.scale = function (scalar) {\n        this.x *= scalar;\n        this.y *= scalar;\n    };\n    Point.prototype.scaleAndAdd = function (other, scalar) {\n        this.x += other.x * scalar;\n        this.y += other.y * scalar;\n    };\n    Point.prototype.sub = function (other) {\n        this.x -= other.x;\n        this.y -= other.y;\n        return this;\n    };\n    Point.prototype.dot = function (other) {\n        return this.x * other.x + this.y * other.y;\n    };\n    Point.prototype.len = function () {\n        return Math.sqrt(this.x * this.x + this.y * this.y);\n    };\n    Point.prototype.lenSquare = function () {\n        return this.x * this.x + this.y * this.y;\n    };\n    Point.prototype.normalize = function () {\n        var len = this.len();\n        this.x /= len;\n        this.y /= len;\n        return this;\n    };\n    Point.prototype.distance = function (other) {\n        var dx = this.x - other.x;\n        var dy = this.y - other.y;\n        return Math.sqrt(dx * dx + dy * dy);\n    };\n    Point.prototype.distanceSquare = function (other) {\n        var dx = this.x - other.x;\n        var dy = this.y - other.y;\n        return dx * dx + dy * dy;\n    };\n    Point.prototype.negate = function () {\n        this.x = -this.x;\n        this.y = -this.y;\n        return this;\n    };\n    Point.prototype.transform = function (m) {\n        if (!m) {\n            return;\n        }\n        var x = this.x;\n        var y = this.y;\n        this.x = m[0] * x + m[2] * y + m[4];\n        this.y = m[1] * x + m[3] * y + m[5];\n        return this;\n    };\n    Point.prototype.toArray = function (out) {\n        out[0] = this.x;\n        out[1] = this.y;\n        return out;\n    };\n    Point.prototype.fromArray = function (input) {\n        this.x = input[0];\n        this.y = input[1];\n    };\n    Point.set = function (p, x, y) {\n        p.x = x;\n        p.y = y;\n    };\n    Point.copy = function (p, p2) {\n        p.x = p2.x;\n        p.y = p2.y;\n    };\n    Point.len = function (p) {\n        return Math.sqrt(p.x * p.x + p.y * p.y);\n    };\n    Point.lenSquare = function (p) {\n        return p.x * p.x + p.y * p.y;\n    };\n    Point.dot = function (p0, p1) {\n        return p0.x * p1.x + p0.y * p1.y;\n    };\n    Point.add = function (out, p0, p1) {\n        out.x = p0.x + p1.x;\n        out.y = p0.y + p1.y;\n    };\n    Point.sub = function (out, p0, p1) {\n        out.x = p0.x - p1.x;\n        out.y = p0.y - p1.y;\n    };\n    Point.scale = function (out, p0, scalar) {\n        out.x = p0.x * scalar;\n        out.y = p0.y * scalar;\n    };\n    Point.scaleAndAdd = function (out, p0, p1, scalar) {\n        out.x = p0.x + p1.x * scalar;\n        out.y = p0.y + p1.y * scalar;\n    };\n    Point.lerp = function (out, p0, p1, t) {\n        var onet = 1 - t;\n        out.x = onet * p0.x + t * p1.x;\n        out.y = onet * p0.y + t * p1.y;\n    };\n    return Point;\n}());\nexport default Point;\n"], "mappings": "AAAA,IAAIA,KAAK,GAAI,YAAY;EACrB,SAASA,KAAKA,CAACC,CAAC,EAAEC,CAAC,EAAE;IACjB,IAAI,CAACD,CAAC,GAAGA,CAAC,IAAI,CAAC;IACf,IAAI,CAACC,CAAC,GAAGA,CAAC,IAAI,CAAC;EACnB;EACAF,KAAK,CAACG,SAAS,CAACC,IAAI,GAAG,UAAUC,KAAK,EAAE;IACpC,IAAI,CAACJ,CAAC,GAAGI,KAAK,CAACJ,CAAC;IAChB,IAAI,CAACC,CAAC,GAAGG,KAAK,CAACH,CAAC;IAChB,OAAO,IAAI;EACf,CAAC;EACDF,KAAK,CAACG,SAAS,CAACG,KAAK,GAAG,YAAY;IAChC,OAAO,IAAIN,KAAK,CAAC,IAAI,CAACC,CAAC,EAAE,IAAI,CAACC,CAAC,CAAC;EACpC,CAAC;EACDF,KAAK,CAACG,SAAS,CAACI,GAAG,GAAG,UAAUN,CAAC,EAAEC,CAAC,EAAE;IAClC,IAAI,CAACD,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,CAAC,GAAGA,CAAC;IACV,OAAO,IAAI;EACf,CAAC;EACDF,KAAK,CAACG,SAAS,CAACK,KAAK,GAAG,UAAUH,KAAK,EAAE;IACrC,OAAOA,KAAK,CAACJ,CAAC,KAAK,IAAI,CAACA,CAAC,IAAII,KAAK,CAACH,CAAC,KAAK,IAAI,CAACA,CAAC;EACnD,CAAC;EACDF,KAAK,CAACG,SAAS,CAACM,GAAG,GAAG,UAAUJ,KAAK,EAAE;IACnC,IAAI,CAACJ,CAAC,IAAII,KAAK,CAACJ,CAAC;IACjB,IAAI,CAACC,CAAC,IAAIG,KAAK,CAACH,CAAC;IACjB,OAAO,IAAI;EACf,CAAC;EACDF,KAAK,CAACG,SAAS,CAACO,KAAK,GAAG,UAAUC,MAAM,EAAE;IACtC,IAAI,CAACV,CAAC,IAAIU,MAAM;IAChB,IAAI,CAACT,CAAC,IAAIS,MAAM;EACpB,CAAC;EACDX,KAAK,CAACG,SAAS,CAACS,WAAW,GAAG,UAAUP,KAAK,EAAEM,MAAM,EAAE;IACnD,IAAI,CAACV,CAAC,IAAII,KAAK,CAACJ,CAAC,GAAGU,MAAM;IAC1B,IAAI,CAACT,CAAC,IAAIG,KAAK,CAACH,CAAC,GAAGS,MAAM;EAC9B,CAAC;EACDX,KAAK,CAACG,SAAS,CAACU,GAAG,GAAG,UAAUR,KAAK,EAAE;IACnC,IAAI,CAACJ,CAAC,IAAII,KAAK,CAACJ,CAAC;IACjB,IAAI,CAACC,CAAC,IAAIG,KAAK,CAACH,CAAC;IACjB,OAAO,IAAI;EACf,CAAC;EACDF,KAAK,CAACG,SAAS,CAACW,GAAG,GAAG,UAAUT,KAAK,EAAE;IACnC,OAAO,IAAI,CAACJ,CAAC,GAAGI,KAAK,CAACJ,CAAC,GAAG,IAAI,CAACC,CAAC,GAAGG,KAAK,CAACH,CAAC;EAC9C,CAAC;EACDF,KAAK,CAACG,SAAS,CAACY,GAAG,GAAG,YAAY;IAC9B,OAAOC,IAAI,CAACC,IAAI,CAAC,IAAI,CAAChB,CAAC,GAAG,IAAI,CAACA,CAAC,GAAG,IAAI,CAACC,CAAC,GAAG,IAAI,CAACA,CAAC,CAAC;EACvD,CAAC;EACDF,KAAK,CAACG,SAAS,CAACe,SAAS,GAAG,YAAY;IACpC,OAAO,IAAI,CAACjB,CAAC,GAAG,IAAI,CAACA,CAAC,GAAG,IAAI,CAACC,CAAC,GAAG,IAAI,CAACA,CAAC;EAC5C,CAAC;EACDF,KAAK,CAACG,SAAS,CAACgB,SAAS,GAAG,YAAY;IACpC,IAAIJ,GAAG,GAAG,IAAI,CAACA,GAAG,CAAC,CAAC;IACpB,IAAI,CAACd,CAAC,IAAIc,GAAG;IACb,IAAI,CAACb,CAAC,IAAIa,GAAG;IACb,OAAO,IAAI;EACf,CAAC;EACDf,KAAK,CAACG,SAAS,CAACiB,QAAQ,GAAG,UAAUf,KAAK,EAAE;IACxC,IAAIgB,EAAE,GAAG,IAAI,CAACpB,CAAC,GAAGI,KAAK,CAACJ,CAAC;IACzB,IAAIqB,EAAE,GAAG,IAAI,CAACpB,CAAC,GAAGG,KAAK,CAACH,CAAC;IACzB,OAAOc,IAAI,CAACC,IAAI,CAACI,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC;EACvC,CAAC;EACDtB,KAAK,CAACG,SAAS,CAACoB,cAAc,GAAG,UAAUlB,KAAK,EAAE;IAC9C,IAAIgB,EAAE,GAAG,IAAI,CAACpB,CAAC,GAAGI,KAAK,CAACJ,CAAC;IACzB,IAAIqB,EAAE,GAAG,IAAI,CAACpB,CAAC,GAAGG,KAAK,CAACH,CAAC;IACzB,OAAOmB,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE;EAC5B,CAAC;EACDtB,KAAK,CAACG,SAAS,CAACqB,MAAM,GAAG,YAAY;IACjC,IAAI,CAACvB,CAAC,GAAG,CAAC,IAAI,CAACA,CAAC;IAChB,IAAI,CAACC,CAAC,GAAG,CAAC,IAAI,CAACA,CAAC;IAChB,OAAO,IAAI;EACf,CAAC;EACDF,KAAK,CAACG,SAAS,CAACsB,SAAS,GAAG,UAAUC,CAAC,EAAE;IACrC,IAAI,CAACA,CAAC,EAAE;MACJ;IACJ;IACA,IAAIzB,CAAC,GAAG,IAAI,CAACA,CAAC;IACd,IAAIC,CAAC,GAAG,IAAI,CAACA,CAAC;IACd,IAAI,CAACD,CAAC,GAAGyB,CAAC,CAAC,CAAC,CAAC,GAAGzB,CAAC,GAAGyB,CAAC,CAAC,CAAC,CAAC,GAAGxB,CAAC,GAAGwB,CAAC,CAAC,CAAC,CAAC;IACnC,IAAI,CAACxB,CAAC,GAAGwB,CAAC,CAAC,CAAC,CAAC,GAAGzB,CAAC,GAAGyB,CAAC,CAAC,CAAC,CAAC,GAAGxB,CAAC,GAAGwB,CAAC,CAAC,CAAC,CAAC;IACnC,OAAO,IAAI;EACf,CAAC;EACD1B,KAAK,CAACG,SAAS,CAACwB,OAAO,GAAG,UAAUC,GAAG,EAAE;IACrCA,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC3B,CAAC;IACf2B,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC1B,CAAC;IACf,OAAO0B,GAAG;EACd,CAAC;EACD5B,KAAK,CAACG,SAAS,CAAC0B,SAAS,GAAG,UAAUC,KAAK,EAAE;IACzC,IAAI,CAAC7B,CAAC,GAAG6B,KAAK,CAAC,CAAC,CAAC;IACjB,IAAI,CAAC5B,CAAC,GAAG4B,KAAK,CAAC,CAAC,CAAC;EACrB,CAAC;EACD9B,KAAK,CAACO,GAAG,GAAG,UAAUwB,CAAC,EAAE9B,CAAC,EAAEC,CAAC,EAAE;IAC3B6B,CAAC,CAAC9B,CAAC,GAAGA,CAAC;IACP8B,CAAC,CAAC7B,CAAC,GAAGA,CAAC;EACX,CAAC;EACDF,KAAK,CAACI,IAAI,GAAG,UAAU2B,CAAC,EAAEC,EAAE,EAAE;IAC1BD,CAAC,CAAC9B,CAAC,GAAG+B,EAAE,CAAC/B,CAAC;IACV8B,CAAC,CAAC7B,CAAC,GAAG8B,EAAE,CAAC9B,CAAC;EACd,CAAC;EACDF,KAAK,CAACe,GAAG,GAAG,UAAUgB,CAAC,EAAE;IACrB,OAAOf,IAAI,CAACC,IAAI,CAACc,CAAC,CAAC9B,CAAC,GAAG8B,CAAC,CAAC9B,CAAC,GAAG8B,CAAC,CAAC7B,CAAC,GAAG6B,CAAC,CAAC7B,CAAC,CAAC;EAC3C,CAAC;EACDF,KAAK,CAACkB,SAAS,GAAG,UAAUa,CAAC,EAAE;IAC3B,OAAOA,CAAC,CAAC9B,CAAC,GAAG8B,CAAC,CAAC9B,CAAC,GAAG8B,CAAC,CAAC7B,CAAC,GAAG6B,CAAC,CAAC7B,CAAC;EAChC,CAAC;EACDF,KAAK,CAACc,GAAG,GAAG,UAAUmB,EAAE,EAAEC,EAAE,EAAE;IAC1B,OAAOD,EAAE,CAAChC,CAAC,GAAGiC,EAAE,CAACjC,CAAC,GAAGgC,EAAE,CAAC/B,CAAC,GAAGgC,EAAE,CAAChC,CAAC;EACpC,CAAC;EACDF,KAAK,CAACS,GAAG,GAAG,UAAUmB,GAAG,EAAEK,EAAE,EAAEC,EAAE,EAAE;IAC/BN,GAAG,CAAC3B,CAAC,GAAGgC,EAAE,CAAChC,CAAC,GAAGiC,EAAE,CAACjC,CAAC;IACnB2B,GAAG,CAAC1B,CAAC,GAAG+B,EAAE,CAAC/B,CAAC,GAAGgC,EAAE,CAAChC,CAAC;EACvB,CAAC;EACDF,KAAK,CAACa,GAAG,GAAG,UAAUe,GAAG,EAAEK,EAAE,EAAEC,EAAE,EAAE;IAC/BN,GAAG,CAAC3B,CAAC,GAAGgC,EAAE,CAAChC,CAAC,GAAGiC,EAAE,CAACjC,CAAC;IACnB2B,GAAG,CAAC1B,CAAC,GAAG+B,EAAE,CAAC/B,CAAC,GAAGgC,EAAE,CAAChC,CAAC;EACvB,CAAC;EACDF,KAAK,CAACU,KAAK,GAAG,UAAUkB,GAAG,EAAEK,EAAE,EAAEtB,MAAM,EAAE;IACrCiB,GAAG,CAAC3B,CAAC,GAAGgC,EAAE,CAAChC,CAAC,GAAGU,MAAM;IACrBiB,GAAG,CAAC1B,CAAC,GAAG+B,EAAE,CAAC/B,CAAC,GAAGS,MAAM;EACzB,CAAC;EACDX,KAAK,CAACY,WAAW,GAAG,UAAUgB,GAAG,EAAEK,EAAE,EAAEC,EAAE,EAAEvB,MAAM,EAAE;IAC/CiB,GAAG,CAAC3B,CAAC,GAAGgC,EAAE,CAAChC,CAAC,GAAGiC,EAAE,CAACjC,CAAC,GAAGU,MAAM;IAC5BiB,GAAG,CAAC1B,CAAC,GAAG+B,EAAE,CAAC/B,CAAC,GAAGgC,EAAE,CAAChC,CAAC,GAAGS,MAAM;EAChC,CAAC;EACDX,KAAK,CAACmC,IAAI,GAAG,UAAUP,GAAG,EAAEK,EAAE,EAAEC,EAAE,EAAEE,CAAC,EAAE;IACnC,IAAIC,IAAI,GAAG,CAAC,GAAGD,CAAC;IAChBR,GAAG,CAAC3B,CAAC,GAAGoC,IAAI,GAAGJ,EAAE,CAAChC,CAAC,GAAGmC,CAAC,GAAGF,EAAE,CAACjC,CAAC;IAC9B2B,GAAG,CAAC1B,CAAC,GAAGmC,IAAI,GAAGJ,EAAE,CAAC/B,CAAC,GAAGkC,CAAC,GAAGF,EAAE,CAAChC,CAAC;EAClC,CAAC;EACD,OAAOF,KAAK;AAChB,CAAC,CAAC,CAAE;AACJ,eAAeA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}