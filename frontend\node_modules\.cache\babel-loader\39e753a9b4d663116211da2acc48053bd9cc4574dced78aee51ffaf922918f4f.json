{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport visualDefault from '../../visual/visualDefault.js';\nimport VisualMapping from '../../visual/VisualMapping.js';\nimport * as visualSolution from '../../visual/visualSolution.js';\nimport * as modelUtil from '../../util/model.js';\nimport * as numberUtil from '../../util/number.js';\nimport ComponentModel from '../../model/Component.js';\nvar mapVisual = VisualMapping.mapVisual;\nvar eachVisual = VisualMapping.eachVisual;\nvar isArray = zrUtil.isArray;\nvar each = zrUtil.each;\nvar asc = numberUtil.asc;\nvar linearMap = numberUtil.linearMap;\nvar VisualMapModel = /** @class */function (_super) {\n  __extends(VisualMapModel, _super);\n  function VisualMapModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = VisualMapModel.type;\n    _this.stateList = ['inRange', 'outOfRange'];\n    _this.replacableOptionKeys = ['inRange', 'outOfRange', 'target', 'controller', 'color'];\n    _this.layoutMode = {\n      type: 'box',\n      ignoreSize: true\n    };\n    /**\r\n     * [lowerBound, upperBound]\r\n     */\n    _this.dataBound = [-Infinity, Infinity];\n    _this.targetVisuals = {};\n    _this.controllerVisuals = {};\n    return _this;\n  }\n  VisualMapModel.prototype.init = function (option, parentModel, ecModel) {\n    this.mergeDefaultAndTheme(option, ecModel);\n  };\n  /**\r\n   * @protected\r\n   */\n  VisualMapModel.prototype.optionUpdated = function (newOption, isInit) {\n    var thisOption = this.option;\n    !isInit && visualSolution.replaceVisualOption(thisOption, newOption, this.replacableOptionKeys);\n    this.textStyleModel = this.getModel('textStyle');\n    this.resetItemSize();\n    this.completeVisualOption();\n  };\n  /**\r\n   * @protected\r\n   */\n  VisualMapModel.prototype.resetVisual = function (supplementVisualOption) {\n    var stateList = this.stateList;\n    supplementVisualOption = zrUtil.bind(supplementVisualOption, this);\n    this.controllerVisuals = visualSolution.createVisualMappings(this.option.controller, stateList, supplementVisualOption);\n    this.targetVisuals = visualSolution.createVisualMappings(this.option.target, stateList, supplementVisualOption);\n  };\n  /**\r\n   * @public\r\n   */\n  VisualMapModel.prototype.getItemSymbol = function () {\n    return null;\n  };\n  /**\r\n   * @protected\r\n   * @return {Array.<number>} An array of series indices.\r\n   */\n  VisualMapModel.prototype.getTargetSeriesIndices = function () {\n    var optionSeriesIndex = this.option.seriesIndex;\n    var seriesIndices = [];\n    if (optionSeriesIndex == null || optionSeriesIndex === 'all') {\n      this.ecModel.eachSeries(function (seriesModel, index) {\n        seriesIndices.push(index);\n      });\n    } else {\n      seriesIndices = modelUtil.normalizeToArray(optionSeriesIndex);\n    }\n    return seriesIndices;\n  };\n  /**\r\n   * @public\r\n   */\n  VisualMapModel.prototype.eachTargetSeries = function (callback, context) {\n    zrUtil.each(this.getTargetSeriesIndices(), function (seriesIndex) {\n      var seriesModel = this.ecModel.getSeriesByIndex(seriesIndex);\n      if (seriesModel) {\n        callback.call(context, seriesModel);\n      }\n    }, this);\n  };\n  /**\r\n   * @pubilc\r\n   */\n  VisualMapModel.prototype.isTargetSeries = function (seriesModel) {\n    var is = false;\n    this.eachTargetSeries(function (model) {\n      model === seriesModel && (is = true);\n    });\n    return is;\n  };\n  /**\r\n   * @example\r\n   * this.formatValueText(someVal); // format single numeric value to text.\r\n   * this.formatValueText(someVal, true); // format single category value to text.\r\n   * this.formatValueText([min, max]); // format numeric min-max to text.\r\n   * this.formatValueText([this.dataBound[0], max]); // using data lower bound.\r\n   * this.formatValueText([min, this.dataBound[1]]); // using data upper bound.\r\n   *\r\n   * @param value Real value, or this.dataBound[0 or 1].\r\n   * @param isCategory Only available when value is number.\r\n   * @param edgeSymbols Open-close symbol when value is interval.\r\n   * @protected\r\n   */\n  VisualMapModel.prototype.formatValueText = function (value, isCategory, edgeSymbols) {\n    var option = this.option;\n    var precision = option.precision;\n    var dataBound = this.dataBound;\n    var formatter = option.formatter;\n    var isMinMax;\n    edgeSymbols = edgeSymbols || ['<', '>'];\n    if (zrUtil.isArray(value)) {\n      value = value.slice();\n      isMinMax = true;\n    }\n    var textValue = isCategory ? value // Value is string when isCategory\n    : isMinMax ? [toFixed(value[0]), toFixed(value[1])] : toFixed(value);\n    if (zrUtil.isString(formatter)) {\n      return formatter.replace('{value}', isMinMax ? textValue[0] : textValue).replace('{value2}', isMinMax ? textValue[1] : textValue);\n    } else if (zrUtil.isFunction(formatter)) {\n      return isMinMax ? formatter(value[0], value[1]) : formatter(value);\n    }\n    if (isMinMax) {\n      if (value[0] === dataBound[0]) {\n        return edgeSymbols[0] + ' ' + textValue[1];\n      } else if (value[1] === dataBound[1]) {\n        return edgeSymbols[1] + ' ' + textValue[0];\n      } else {\n        return textValue[0] + ' - ' + textValue[1];\n      }\n    } else {\n      // Format single value (includes category case).\n      return textValue;\n    }\n    function toFixed(val) {\n      return val === dataBound[0] ? 'min' : val === dataBound[1] ? 'max' : (+val).toFixed(Math.min(precision, 20));\n    }\n  };\n  /**\r\n   * @protected\r\n   */\n  VisualMapModel.prototype.resetExtent = function () {\n    var thisOption = this.option;\n    // Can not calculate data extent by data here.\n    // Because series and data may be modified in processing stage.\n    // So we do not support the feature \"auto min/max\".\n    var extent = asc([thisOption.min, thisOption.max]);\n    this._dataExtent = extent;\n  };\n  /**\r\n   * PENDING:\r\n   * delete this method if no outer usage.\r\n   *\r\n   * Return  Concrete dimension. If null/undefined is returned, no dimension is used.\r\n   */\n  // getDataDimension(data: SeriesData) {\n  //     const optDim = this.option.dimension;\n  //     if (optDim != null) {\n  //         return data.getDimension(optDim);\n  //     }\n  //     const dimNames = data.dimensions;\n  //     for (let i = dimNames.length - 1; i >= 0; i--) {\n  //         const dimName = dimNames[i];\n  //         const dimInfo = data.getDimensionInfo(dimName);\n  //         if (!dimInfo.isCalculationCoord) {\n  //             return dimName;\n  //         }\n  //     }\n  // }\n  VisualMapModel.prototype.getDataDimensionIndex = function (data) {\n    var optDim = this.option.dimension;\n    if (optDim != null) {\n      return data.getDimensionIndex(optDim);\n    }\n    var dimNames = data.dimensions;\n    for (var i = dimNames.length - 1; i >= 0; i--) {\n      var dimName = dimNames[i];\n      var dimInfo = data.getDimensionInfo(dimName);\n      if (!dimInfo.isCalculationCoord) {\n        return dimInfo.storeDimIndex;\n      }\n    }\n  };\n  VisualMapModel.prototype.getExtent = function () {\n    return this._dataExtent.slice();\n  };\n  VisualMapModel.prototype.completeVisualOption = function () {\n    var ecModel = this.ecModel;\n    var thisOption = this.option;\n    var base = {\n      inRange: thisOption.inRange,\n      outOfRange: thisOption.outOfRange\n    };\n    var target = thisOption.target || (thisOption.target = {});\n    var controller = thisOption.controller || (thisOption.controller = {});\n    zrUtil.merge(target, base); // Do not override\n    zrUtil.merge(controller, base); // Do not override\n    var isCategory = this.isCategory();\n    completeSingle.call(this, target);\n    completeSingle.call(this, controller);\n    completeInactive.call(this, target, 'inRange', 'outOfRange');\n    // completeInactive.call(this, target, 'outOfRange', 'inRange');\n    completeController.call(this, controller);\n    function completeSingle(base) {\n      // Compatible with ec2 dataRange.color.\n      // The mapping order of dataRange.color is: [high value, ..., low value]\n      // whereas inRange.color and outOfRange.color is [low value, ..., high value]\n      // Notice: ec2 has no inverse.\n      if (isArray(thisOption.color)\n      // If there has been inRange: {symbol: ...}, adding color is a mistake.\n      // So adding color only when no inRange defined.\n      && !base.inRange) {\n        base.inRange = {\n          color: thisOption.color.slice().reverse()\n        };\n      }\n      // Compatible with previous logic, always give a default color, otherwise\n      // simple config with no inRange and outOfRange will not work.\n      // Originally we use visualMap.color as the default color, but setOption at\n      // the second time the default color will be erased. So we change to use\n      // constant DEFAULT_COLOR.\n      // If user do not want the default color, set inRange: {color: null}.\n      base.inRange = base.inRange || {\n        color: ecModel.get('gradientColor')\n      };\n    }\n    function completeInactive(base, stateExist, stateAbsent) {\n      var optExist = base[stateExist];\n      var optAbsent = base[stateAbsent];\n      if (optExist && !optAbsent) {\n        optAbsent = base[stateAbsent] = {};\n        each(optExist, function (visualData, visualType) {\n          if (!VisualMapping.isValidType(visualType)) {\n            return;\n          }\n          var defa = visualDefault.get(visualType, 'inactive', isCategory);\n          if (defa != null) {\n            optAbsent[visualType] = defa;\n            // Compatibable with ec2:\n            // Only inactive color to rgba(0,0,0,0) can not\n            // make label transparent, so use opacity also.\n            if (visualType === 'color' && !optAbsent.hasOwnProperty('opacity') && !optAbsent.hasOwnProperty('colorAlpha')) {\n              optAbsent.opacity = [0, 0];\n            }\n          }\n        });\n      }\n    }\n    function completeController(controller) {\n      var symbolExists = (controller.inRange || {}).symbol || (controller.outOfRange || {}).symbol;\n      var symbolSizeExists = (controller.inRange || {}).symbolSize || (controller.outOfRange || {}).symbolSize;\n      var inactiveColor = this.get('inactiveColor');\n      var itemSymbol = this.getItemSymbol();\n      var defaultSymbol = itemSymbol || 'roundRect';\n      each(this.stateList, function (state) {\n        var itemSize = this.itemSize;\n        var visuals = controller[state];\n        // Set inactive color for controller if no other color\n        // attr (like colorAlpha) specified.\n        if (!visuals) {\n          visuals = controller[state] = {\n            color: isCategory ? inactiveColor : [inactiveColor]\n          };\n        }\n        // Consistent symbol and symbolSize if not specified.\n        if (visuals.symbol == null) {\n          visuals.symbol = symbolExists && zrUtil.clone(symbolExists) || (isCategory ? defaultSymbol : [defaultSymbol]);\n        }\n        if (visuals.symbolSize == null) {\n          visuals.symbolSize = symbolSizeExists && zrUtil.clone(symbolSizeExists) || (isCategory ? itemSize[0] : [itemSize[0], itemSize[0]]);\n        }\n        // Filter none\n        visuals.symbol = mapVisual(visuals.symbol, function (symbol) {\n          return symbol === 'none' ? defaultSymbol : symbol;\n        });\n        // Normalize symbolSize\n        var symbolSize = visuals.symbolSize;\n        if (symbolSize != null) {\n          var max_1 = -Infinity;\n          // symbolSize can be object when categories defined.\n          eachVisual(symbolSize, function (value) {\n            value > max_1 && (max_1 = value);\n          });\n          visuals.symbolSize = mapVisual(symbolSize, function (value) {\n            return linearMap(value, [0, max_1], [0, itemSize[0]], true);\n          });\n        }\n      }, this);\n    }\n  };\n  VisualMapModel.prototype.resetItemSize = function () {\n    this.itemSize = [parseFloat(this.get('itemWidth')), parseFloat(this.get('itemHeight'))];\n  };\n  VisualMapModel.prototype.isCategory = function () {\n    return !!this.option.categories;\n  };\n  /**\r\n   * @public\r\n   * @abstract\r\n   */\n  VisualMapModel.prototype.setSelected = function (selected) {};\n  VisualMapModel.prototype.getSelected = function () {\n    return null;\n  };\n  /**\r\n   * @public\r\n   * @abstract\r\n   */\n  VisualMapModel.prototype.getValueState = function (value) {\n    return null;\n  };\n  /**\r\n   * FIXME\r\n   * Do not publish to thirt-part-dev temporarily\r\n   * util the interface is stable. (Should it return\r\n   * a function but not visual meta?)\r\n   *\r\n   * @pubilc\r\n   * @abstract\r\n   * @param getColorVisual\r\n   *        params: value, valueState\r\n   *        return: color\r\n   * @return {Object} visualMeta\r\n   *        should includes {stops, outerColors}\r\n   *        outerColor means [colorBeyondMinValue, colorBeyondMaxValue]\r\n   */\n  VisualMapModel.prototype.getVisualMeta = function (getColorVisual) {\n    return null;\n  };\n  VisualMapModel.type = 'visualMap';\n  VisualMapModel.dependencies = ['series'];\n  VisualMapModel.defaultOption = {\n    show: true,\n    // zlevel: 0,\n    z: 4,\n    seriesIndex: 'all',\n    min: 0,\n    max: 200,\n    left: 0,\n    right: null,\n    top: null,\n    bottom: 0,\n    itemWidth: null,\n    itemHeight: null,\n    inverse: false,\n    orient: 'vertical',\n    backgroundColor: 'rgba(0,0,0,0)',\n    borderColor: '#ccc',\n    contentColor: '#5793f3',\n    inactiveColor: '#aaa',\n    borderWidth: 0,\n    padding: 5,\n    // 接受数组分别设定上右下左边距，同css\n    textGap: 10,\n    precision: 0,\n    textStyle: {\n      color: '#333' // 值域文字颜色\n    }\n  };\n  return VisualMapModel;\n}(ComponentModel);\nexport default VisualMapModel;", "map": {"version": 3, "names": ["__extends", "zrUtil", "visualDefault", "VisualMapping", "visualSolution", "modelUtil", "numberUtil", "ComponentModel", "mapVisual", "eachVisual", "isArray", "each", "asc", "linearMap", "VisualMapModel", "_super", "_this", "apply", "arguments", "type", "stateList", "replacableOptionKeys", "layoutMode", "ignoreSize", "dataBound", "Infinity", "targetVisuals", "controllerVisuals", "prototype", "init", "option", "parentModel", "ecModel", "mergeDefaultAndTheme", "optionUpdated", "newOption", "isInit", "thisOption", "replaceVisualOption", "textStyleModel", "getModel", "resetItemSize", "completeVisualOption", "resetVisual", "supplementVisualOption", "bind", "createVisualMappings", "controller", "target", "getItemSymbol", "getTargetSeriesIndices", "optionSeriesIndex", "seriesIndex", "seriesIndices", "eachSeries", "seriesModel", "index", "push", "normalizeToArray", "eachTargetSeries", "callback", "context", "getSeriesByIndex", "call", "isTargetSeries", "is", "model", "formatValueText", "value", "isCategory", "edgeSymbols", "precision", "formatter", "isMinMax", "slice", "textValue", "toFixed", "isString", "replace", "isFunction", "val", "Math", "min", "resetExtent", "extent", "max", "_dataExtent", "getDataDimensionIndex", "data", "optDim", "dimension", "getDimensionIndex", "dimNames", "dimensions", "i", "length", "dimName", "dimInfo", "getDimensionInfo", "isCalculationCoord", "storeDimIndex", "getExtent", "base", "inRange", "outOfRange", "merge", "completeSingle", "completeInactive", "completeController", "color", "reverse", "get", "stateExist", "stateAbsent", "optExist", "optAbsent", "visualData", "visualType", "isValidType", "defa", "hasOwnProperty", "opacity", "symbolExists", "symbol", "symbolSizeExists", "symbolSize", "inactiveColor", "itemSymbol", "defaultSymbol", "state", "itemSize", "visuals", "clone", "max_1", "parseFloat", "categories", "setSelected", "selected", "getSelected", "getValueState", "getVisualMeta", "getColorVisual", "dependencies", "defaultOption", "show", "z", "left", "right", "top", "bottom", "itemWidth", "itemHeight", "inverse", "orient", "backgroundColor", "borderColor", "contentColor", "borderWidth", "padding", "textGap", "textStyle"], "sources": ["E:/AI/SmartCV/node_modules/echarts/lib/component/visualMap/VisualMapModel.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport visualDefault from '../../visual/visualDefault.js';\nimport VisualMapping from '../../visual/VisualMapping.js';\nimport * as visualSolution from '../../visual/visualSolution.js';\nimport * as modelUtil from '../../util/model.js';\nimport * as numberUtil from '../../util/number.js';\nimport ComponentModel from '../../model/Component.js';\nvar mapVisual = VisualMapping.mapVisual;\nvar eachVisual = VisualMapping.eachVisual;\nvar isArray = zrUtil.isArray;\nvar each = zrUtil.each;\nvar asc = numberUtil.asc;\nvar linearMap = numberUtil.linearMap;\nvar VisualMapModel = /** @class */function (_super) {\n  __extends(VisualMapModel, _super);\n  function VisualMapModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = VisualMapModel.type;\n    _this.stateList = ['inRange', 'outOfRange'];\n    _this.replacableOptionKeys = ['inRange', 'outOfRange', 'target', 'controller', 'color'];\n    _this.layoutMode = {\n      type: 'box',\n      ignoreSize: true\n    };\n    /**\r\n     * [lowerBound, upperBound]\r\n     */\n    _this.dataBound = [-Infinity, Infinity];\n    _this.targetVisuals = {};\n    _this.controllerVisuals = {};\n    return _this;\n  }\n  VisualMapModel.prototype.init = function (option, parentModel, ecModel) {\n    this.mergeDefaultAndTheme(option, ecModel);\n  };\n  /**\r\n   * @protected\r\n   */\n  VisualMapModel.prototype.optionUpdated = function (newOption, isInit) {\n    var thisOption = this.option;\n    !isInit && visualSolution.replaceVisualOption(thisOption, newOption, this.replacableOptionKeys);\n    this.textStyleModel = this.getModel('textStyle');\n    this.resetItemSize();\n    this.completeVisualOption();\n  };\n  /**\r\n   * @protected\r\n   */\n  VisualMapModel.prototype.resetVisual = function (supplementVisualOption) {\n    var stateList = this.stateList;\n    supplementVisualOption = zrUtil.bind(supplementVisualOption, this);\n    this.controllerVisuals = visualSolution.createVisualMappings(this.option.controller, stateList, supplementVisualOption);\n    this.targetVisuals = visualSolution.createVisualMappings(this.option.target, stateList, supplementVisualOption);\n  };\n  /**\r\n   * @public\r\n   */\n  VisualMapModel.prototype.getItemSymbol = function () {\n    return null;\n  };\n  /**\r\n   * @protected\r\n   * @return {Array.<number>} An array of series indices.\r\n   */\n  VisualMapModel.prototype.getTargetSeriesIndices = function () {\n    var optionSeriesIndex = this.option.seriesIndex;\n    var seriesIndices = [];\n    if (optionSeriesIndex == null || optionSeriesIndex === 'all') {\n      this.ecModel.eachSeries(function (seriesModel, index) {\n        seriesIndices.push(index);\n      });\n    } else {\n      seriesIndices = modelUtil.normalizeToArray(optionSeriesIndex);\n    }\n    return seriesIndices;\n  };\n  /**\r\n   * @public\r\n   */\n  VisualMapModel.prototype.eachTargetSeries = function (callback, context) {\n    zrUtil.each(this.getTargetSeriesIndices(), function (seriesIndex) {\n      var seriesModel = this.ecModel.getSeriesByIndex(seriesIndex);\n      if (seriesModel) {\n        callback.call(context, seriesModel);\n      }\n    }, this);\n  };\n  /**\r\n   * @pubilc\r\n   */\n  VisualMapModel.prototype.isTargetSeries = function (seriesModel) {\n    var is = false;\n    this.eachTargetSeries(function (model) {\n      model === seriesModel && (is = true);\n    });\n    return is;\n  };\n  /**\r\n   * @example\r\n   * this.formatValueText(someVal); // format single numeric value to text.\r\n   * this.formatValueText(someVal, true); // format single category value to text.\r\n   * this.formatValueText([min, max]); // format numeric min-max to text.\r\n   * this.formatValueText([this.dataBound[0], max]); // using data lower bound.\r\n   * this.formatValueText([min, this.dataBound[1]]); // using data upper bound.\r\n   *\r\n   * @param value Real value, or this.dataBound[0 or 1].\r\n   * @param isCategory Only available when value is number.\r\n   * @param edgeSymbols Open-close symbol when value is interval.\r\n   * @protected\r\n   */\n  VisualMapModel.prototype.formatValueText = function (value, isCategory, edgeSymbols) {\n    var option = this.option;\n    var precision = option.precision;\n    var dataBound = this.dataBound;\n    var formatter = option.formatter;\n    var isMinMax;\n    edgeSymbols = edgeSymbols || ['<', '>'];\n    if (zrUtil.isArray(value)) {\n      value = value.slice();\n      isMinMax = true;\n    }\n    var textValue = isCategory ? value // Value is string when isCategory\n    : isMinMax ? [toFixed(value[0]), toFixed(value[1])] : toFixed(value);\n    if (zrUtil.isString(formatter)) {\n      return formatter.replace('{value}', isMinMax ? textValue[0] : textValue).replace('{value2}', isMinMax ? textValue[1] : textValue);\n    } else if (zrUtil.isFunction(formatter)) {\n      return isMinMax ? formatter(value[0], value[1]) : formatter(value);\n    }\n    if (isMinMax) {\n      if (value[0] === dataBound[0]) {\n        return edgeSymbols[0] + ' ' + textValue[1];\n      } else if (value[1] === dataBound[1]) {\n        return edgeSymbols[1] + ' ' + textValue[0];\n      } else {\n        return textValue[0] + ' - ' + textValue[1];\n      }\n    } else {\n      // Format single value (includes category case).\n      return textValue;\n    }\n    function toFixed(val) {\n      return val === dataBound[0] ? 'min' : val === dataBound[1] ? 'max' : (+val).toFixed(Math.min(precision, 20));\n    }\n  };\n  /**\r\n   * @protected\r\n   */\n  VisualMapModel.prototype.resetExtent = function () {\n    var thisOption = this.option;\n    // Can not calculate data extent by data here.\n    // Because series and data may be modified in processing stage.\n    // So we do not support the feature \"auto min/max\".\n    var extent = asc([thisOption.min, thisOption.max]);\n    this._dataExtent = extent;\n  };\n  /**\r\n   * PENDING:\r\n   * delete this method if no outer usage.\r\n   *\r\n   * Return  Concrete dimension. If null/undefined is returned, no dimension is used.\r\n   */\n  // getDataDimension(data: SeriesData) {\n  //     const optDim = this.option.dimension;\n  //     if (optDim != null) {\n  //         return data.getDimension(optDim);\n  //     }\n  //     const dimNames = data.dimensions;\n  //     for (let i = dimNames.length - 1; i >= 0; i--) {\n  //         const dimName = dimNames[i];\n  //         const dimInfo = data.getDimensionInfo(dimName);\n  //         if (!dimInfo.isCalculationCoord) {\n  //             return dimName;\n  //         }\n  //     }\n  // }\n  VisualMapModel.prototype.getDataDimensionIndex = function (data) {\n    var optDim = this.option.dimension;\n    if (optDim != null) {\n      return data.getDimensionIndex(optDim);\n    }\n    var dimNames = data.dimensions;\n    for (var i = dimNames.length - 1; i >= 0; i--) {\n      var dimName = dimNames[i];\n      var dimInfo = data.getDimensionInfo(dimName);\n      if (!dimInfo.isCalculationCoord) {\n        return dimInfo.storeDimIndex;\n      }\n    }\n  };\n  VisualMapModel.prototype.getExtent = function () {\n    return this._dataExtent.slice();\n  };\n  VisualMapModel.prototype.completeVisualOption = function () {\n    var ecModel = this.ecModel;\n    var thisOption = this.option;\n    var base = {\n      inRange: thisOption.inRange,\n      outOfRange: thisOption.outOfRange\n    };\n    var target = thisOption.target || (thisOption.target = {});\n    var controller = thisOption.controller || (thisOption.controller = {});\n    zrUtil.merge(target, base); // Do not override\n    zrUtil.merge(controller, base); // Do not override\n    var isCategory = this.isCategory();\n    completeSingle.call(this, target);\n    completeSingle.call(this, controller);\n    completeInactive.call(this, target, 'inRange', 'outOfRange');\n    // completeInactive.call(this, target, 'outOfRange', 'inRange');\n    completeController.call(this, controller);\n    function completeSingle(base) {\n      // Compatible with ec2 dataRange.color.\n      // The mapping order of dataRange.color is: [high value, ..., low value]\n      // whereas inRange.color and outOfRange.color is [low value, ..., high value]\n      // Notice: ec2 has no inverse.\n      if (isArray(thisOption.color)\n      // If there has been inRange: {symbol: ...}, adding color is a mistake.\n      // So adding color only when no inRange defined.\n      && !base.inRange) {\n        base.inRange = {\n          color: thisOption.color.slice().reverse()\n        };\n      }\n      // Compatible with previous logic, always give a default color, otherwise\n      // simple config with no inRange and outOfRange will not work.\n      // Originally we use visualMap.color as the default color, but setOption at\n      // the second time the default color will be erased. So we change to use\n      // constant DEFAULT_COLOR.\n      // If user do not want the default color, set inRange: {color: null}.\n      base.inRange = base.inRange || {\n        color: ecModel.get('gradientColor')\n      };\n    }\n    function completeInactive(base, stateExist, stateAbsent) {\n      var optExist = base[stateExist];\n      var optAbsent = base[stateAbsent];\n      if (optExist && !optAbsent) {\n        optAbsent = base[stateAbsent] = {};\n        each(optExist, function (visualData, visualType) {\n          if (!VisualMapping.isValidType(visualType)) {\n            return;\n          }\n          var defa = visualDefault.get(visualType, 'inactive', isCategory);\n          if (defa != null) {\n            optAbsent[visualType] = defa;\n            // Compatibable with ec2:\n            // Only inactive color to rgba(0,0,0,0) can not\n            // make label transparent, so use opacity also.\n            if (visualType === 'color' && !optAbsent.hasOwnProperty('opacity') && !optAbsent.hasOwnProperty('colorAlpha')) {\n              optAbsent.opacity = [0, 0];\n            }\n          }\n        });\n      }\n    }\n    function completeController(controller) {\n      var symbolExists = (controller.inRange || {}).symbol || (controller.outOfRange || {}).symbol;\n      var symbolSizeExists = (controller.inRange || {}).symbolSize || (controller.outOfRange || {}).symbolSize;\n      var inactiveColor = this.get('inactiveColor');\n      var itemSymbol = this.getItemSymbol();\n      var defaultSymbol = itemSymbol || 'roundRect';\n      each(this.stateList, function (state) {\n        var itemSize = this.itemSize;\n        var visuals = controller[state];\n        // Set inactive color for controller if no other color\n        // attr (like colorAlpha) specified.\n        if (!visuals) {\n          visuals = controller[state] = {\n            color: isCategory ? inactiveColor : [inactiveColor]\n          };\n        }\n        // Consistent symbol and symbolSize if not specified.\n        if (visuals.symbol == null) {\n          visuals.symbol = symbolExists && zrUtil.clone(symbolExists) || (isCategory ? defaultSymbol : [defaultSymbol]);\n        }\n        if (visuals.symbolSize == null) {\n          visuals.symbolSize = symbolSizeExists && zrUtil.clone(symbolSizeExists) || (isCategory ? itemSize[0] : [itemSize[0], itemSize[0]]);\n        }\n        // Filter none\n        visuals.symbol = mapVisual(visuals.symbol, function (symbol) {\n          return symbol === 'none' ? defaultSymbol : symbol;\n        });\n        // Normalize symbolSize\n        var symbolSize = visuals.symbolSize;\n        if (symbolSize != null) {\n          var max_1 = -Infinity;\n          // symbolSize can be object when categories defined.\n          eachVisual(symbolSize, function (value) {\n            value > max_1 && (max_1 = value);\n          });\n          visuals.symbolSize = mapVisual(symbolSize, function (value) {\n            return linearMap(value, [0, max_1], [0, itemSize[0]], true);\n          });\n        }\n      }, this);\n    }\n  };\n  VisualMapModel.prototype.resetItemSize = function () {\n    this.itemSize = [parseFloat(this.get('itemWidth')), parseFloat(this.get('itemHeight'))];\n  };\n  VisualMapModel.prototype.isCategory = function () {\n    return !!this.option.categories;\n  };\n  /**\r\n   * @public\r\n   * @abstract\r\n   */\n  VisualMapModel.prototype.setSelected = function (selected) {};\n  VisualMapModel.prototype.getSelected = function () {\n    return null;\n  };\n  /**\r\n   * @public\r\n   * @abstract\r\n   */\n  VisualMapModel.prototype.getValueState = function (value) {\n    return null;\n  };\n  /**\r\n   * FIXME\r\n   * Do not publish to thirt-part-dev temporarily\r\n   * util the interface is stable. (Should it return\r\n   * a function but not visual meta?)\r\n   *\r\n   * @pubilc\r\n   * @abstract\r\n   * @param getColorVisual\r\n   *        params: value, valueState\r\n   *        return: color\r\n   * @return {Object} visualMeta\r\n   *        should includes {stops, outerColors}\r\n   *        outerColor means [colorBeyondMinValue, colorBeyondMaxValue]\r\n   */\n  VisualMapModel.prototype.getVisualMeta = function (getColorVisual) {\n    return null;\n  };\n  VisualMapModel.type = 'visualMap';\n  VisualMapModel.dependencies = ['series'];\n  VisualMapModel.defaultOption = {\n    show: true,\n    // zlevel: 0,\n    z: 4,\n    seriesIndex: 'all',\n    min: 0,\n    max: 200,\n    left: 0,\n    right: null,\n    top: null,\n    bottom: 0,\n    itemWidth: null,\n    itemHeight: null,\n    inverse: false,\n    orient: 'vertical',\n    backgroundColor: 'rgba(0,0,0,0)',\n    borderColor: '#ccc',\n    contentColor: '#5793f3',\n    inactiveColor: '#aaa',\n    borderWidth: 0,\n    padding: 5,\n    // 接受数组分别设定上右下左边距，同css\n    textGap: 10,\n    precision: 0,\n    textStyle: {\n      color: '#333' // 值域文字颜色\n    }\n  };\n  return VisualMapModel;\n}(ComponentModel);\nexport default VisualMapModel;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAO,KAAKC,cAAc,MAAM,gCAAgC;AAChE,OAAO,KAAKC,SAAS,MAAM,qBAAqB;AAChD,OAAO,KAAKC,UAAU,MAAM,sBAAsB;AAClD,OAAOC,cAAc,MAAM,0BAA0B;AACrD,IAAIC,SAAS,GAAGL,aAAa,CAACK,SAAS;AACvC,IAAIC,UAAU,GAAGN,aAAa,CAACM,UAAU;AACzC,IAAIC,OAAO,GAAGT,MAAM,CAACS,OAAO;AAC5B,IAAIC,IAAI,GAAGV,MAAM,CAACU,IAAI;AACtB,IAAIC,GAAG,GAAGN,UAAU,CAACM,GAAG;AACxB,IAAIC,SAAS,GAAGP,UAAU,CAACO,SAAS;AACpC,IAAIC,cAAc,GAAG,aAAa,UAAUC,MAAM,EAAE;EAClDf,SAAS,CAACc,cAAc,EAAEC,MAAM,CAAC;EACjC,SAASD,cAAcA,CAAA,EAAG;IACxB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,cAAc,CAACK,IAAI;IAChCH,KAAK,CAACI,SAAS,GAAG,CAAC,SAAS,EAAE,YAAY,CAAC;IAC3CJ,KAAK,CAACK,oBAAoB,GAAG,CAAC,SAAS,EAAE,YAAY,EAAE,QAAQ,EAAE,YAAY,EAAE,OAAO,CAAC;IACvFL,KAAK,CAACM,UAAU,GAAG;MACjBH,IAAI,EAAE,KAAK;MACXI,UAAU,EAAE;IACd,CAAC;IACD;AACJ;AACA;IACIP,KAAK,CAACQ,SAAS,GAAG,CAAC,CAACC,QAAQ,EAAEA,QAAQ,CAAC;IACvCT,KAAK,CAACU,aAAa,GAAG,CAAC,CAAC;IACxBV,KAAK,CAACW,iBAAiB,GAAG,CAAC,CAAC;IAC5B,OAAOX,KAAK;EACd;EACAF,cAAc,CAACc,SAAS,CAACC,IAAI,GAAG,UAAUC,MAAM,EAAEC,WAAW,EAAEC,OAAO,EAAE;IACtE,IAAI,CAACC,oBAAoB,CAACH,MAAM,EAAEE,OAAO,CAAC;EAC5C,CAAC;EACD;AACF;AACA;EACElB,cAAc,CAACc,SAAS,CAACM,aAAa,GAAG,UAAUC,SAAS,EAAEC,MAAM,EAAE;IACpE,IAAIC,UAAU,GAAG,IAAI,CAACP,MAAM;IAC5B,CAACM,MAAM,IAAIhC,cAAc,CAACkC,mBAAmB,CAACD,UAAU,EAAEF,SAAS,EAAE,IAAI,CAACd,oBAAoB,CAAC;IAC/F,IAAI,CAACkB,cAAc,GAAG,IAAI,CAACC,QAAQ,CAAC,WAAW,CAAC;IAChD,IAAI,CAACC,aAAa,CAAC,CAAC;IACpB,IAAI,CAACC,oBAAoB,CAAC,CAAC;EAC7B,CAAC;EACD;AACF;AACA;EACE5B,cAAc,CAACc,SAAS,CAACe,WAAW,GAAG,UAAUC,sBAAsB,EAAE;IACvE,IAAIxB,SAAS,GAAG,IAAI,CAACA,SAAS;IAC9BwB,sBAAsB,GAAG3C,MAAM,CAAC4C,IAAI,CAACD,sBAAsB,EAAE,IAAI,CAAC;IAClE,IAAI,CAACjB,iBAAiB,GAAGvB,cAAc,CAAC0C,oBAAoB,CAAC,IAAI,CAAChB,MAAM,CAACiB,UAAU,EAAE3B,SAAS,EAAEwB,sBAAsB,CAAC;IACvH,IAAI,CAAClB,aAAa,GAAGtB,cAAc,CAAC0C,oBAAoB,CAAC,IAAI,CAAChB,MAAM,CAACkB,MAAM,EAAE5B,SAAS,EAAEwB,sBAAsB,CAAC;EACjH,CAAC;EACD;AACF;AACA;EACE9B,cAAc,CAACc,SAAS,CAACqB,aAAa,GAAG,YAAY;IACnD,OAAO,IAAI;EACb,CAAC;EACD;AACF;AACA;AACA;EACEnC,cAAc,CAACc,SAAS,CAACsB,sBAAsB,GAAG,YAAY;IAC5D,IAAIC,iBAAiB,GAAG,IAAI,CAACrB,MAAM,CAACsB,WAAW;IAC/C,IAAIC,aAAa,GAAG,EAAE;IACtB,IAAIF,iBAAiB,IAAI,IAAI,IAAIA,iBAAiB,KAAK,KAAK,EAAE;MAC5D,IAAI,CAACnB,OAAO,CAACsB,UAAU,CAAC,UAAUC,WAAW,EAAEC,KAAK,EAAE;QACpDH,aAAa,CAACI,IAAI,CAACD,KAAK,CAAC;MAC3B,CAAC,CAAC;IACJ,CAAC,MAAM;MACLH,aAAa,GAAGhD,SAAS,CAACqD,gBAAgB,CAACP,iBAAiB,CAAC;IAC/D;IACA,OAAOE,aAAa;EACtB,CAAC;EACD;AACF;AACA;EACEvC,cAAc,CAACc,SAAS,CAAC+B,gBAAgB,GAAG,UAAUC,QAAQ,EAAEC,OAAO,EAAE;IACvE5D,MAAM,CAACU,IAAI,CAAC,IAAI,CAACuC,sBAAsB,CAAC,CAAC,EAAE,UAAUE,WAAW,EAAE;MAChE,IAAIG,WAAW,GAAG,IAAI,CAACvB,OAAO,CAAC8B,gBAAgB,CAACV,WAAW,CAAC;MAC5D,IAAIG,WAAW,EAAE;QACfK,QAAQ,CAACG,IAAI,CAACF,OAAO,EAAEN,WAAW,CAAC;MACrC;IACF,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EACD;AACF;AACA;EACEzC,cAAc,CAACc,SAAS,CAACoC,cAAc,GAAG,UAAUT,WAAW,EAAE;IAC/D,IAAIU,EAAE,GAAG,KAAK;IACd,IAAI,CAACN,gBAAgB,CAAC,UAAUO,KAAK,EAAE;MACrCA,KAAK,KAAKX,WAAW,KAAKU,EAAE,GAAG,IAAI,CAAC;IACtC,CAAC,CAAC;IACF,OAAOA,EAAE;EACX,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEnD,cAAc,CAACc,SAAS,CAACuC,eAAe,GAAG,UAAUC,KAAK,EAAEC,UAAU,EAAEC,WAAW,EAAE;IACnF,IAAIxC,MAAM,GAAG,IAAI,CAACA,MAAM;IACxB,IAAIyC,SAAS,GAAGzC,MAAM,CAACyC,SAAS;IAChC,IAAI/C,SAAS,GAAG,IAAI,CAACA,SAAS;IAC9B,IAAIgD,SAAS,GAAG1C,MAAM,CAAC0C,SAAS;IAChC,IAAIC,QAAQ;IACZH,WAAW,GAAGA,WAAW,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC;IACvC,IAAIrE,MAAM,CAACS,OAAO,CAAC0D,KAAK,CAAC,EAAE;MACzBA,KAAK,GAAGA,KAAK,CAACM,KAAK,CAAC,CAAC;MACrBD,QAAQ,GAAG,IAAI;IACjB;IACA,IAAIE,SAAS,GAAGN,UAAU,GAAGD,KAAK,CAAC;IAAA,EACjCK,QAAQ,GAAG,CAACG,OAAO,CAACR,KAAK,CAAC,CAAC,CAAC,CAAC,EAAEQ,OAAO,CAACR,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGQ,OAAO,CAACR,KAAK,CAAC;IACpE,IAAInE,MAAM,CAAC4E,QAAQ,CAACL,SAAS,CAAC,EAAE;MAC9B,OAAOA,SAAS,CAACM,OAAO,CAAC,SAAS,EAAEL,QAAQ,GAAGE,SAAS,CAAC,CAAC,CAAC,GAAGA,SAAS,CAAC,CAACG,OAAO,CAAC,UAAU,EAAEL,QAAQ,GAAGE,SAAS,CAAC,CAAC,CAAC,GAAGA,SAAS,CAAC;IACnI,CAAC,MAAM,IAAI1E,MAAM,CAAC8E,UAAU,CAACP,SAAS,CAAC,EAAE;MACvC,OAAOC,QAAQ,GAAGD,SAAS,CAACJ,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGI,SAAS,CAACJ,KAAK,CAAC;IACpE;IACA,IAAIK,QAAQ,EAAE;MACZ,IAAIL,KAAK,CAAC,CAAC,CAAC,KAAK5C,SAAS,CAAC,CAAC,CAAC,EAAE;QAC7B,OAAO8C,WAAW,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGK,SAAS,CAAC,CAAC,CAAC;MAC5C,CAAC,MAAM,IAAIP,KAAK,CAAC,CAAC,CAAC,KAAK5C,SAAS,CAAC,CAAC,CAAC,EAAE;QACpC,OAAO8C,WAAW,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGK,SAAS,CAAC,CAAC,CAAC;MAC5C,CAAC,MAAM;QACL,OAAOA,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,GAAGA,SAAS,CAAC,CAAC,CAAC;MAC5C;IACF,CAAC,MAAM;MACL;MACA,OAAOA,SAAS;IAClB;IACA,SAASC,OAAOA,CAACI,GAAG,EAAE;MACpB,OAAOA,GAAG,KAAKxD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,GAAGwD,GAAG,KAAKxD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAACwD,GAAG,EAAEJ,OAAO,CAACK,IAAI,CAACC,GAAG,CAACX,SAAS,EAAE,EAAE,CAAC,CAAC;IAC9G;EACF,CAAC;EACD;AACF;AACA;EACEzD,cAAc,CAACc,SAAS,CAACuD,WAAW,GAAG,YAAY;IACjD,IAAI9C,UAAU,GAAG,IAAI,CAACP,MAAM;IAC5B;IACA;IACA;IACA,IAAIsD,MAAM,GAAGxE,GAAG,CAAC,CAACyB,UAAU,CAAC6C,GAAG,EAAE7C,UAAU,CAACgD,GAAG,CAAC,CAAC;IAClD,IAAI,CAACC,WAAW,GAAGF,MAAM;EAC3B,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAtE,cAAc,CAACc,SAAS,CAAC2D,qBAAqB,GAAG,UAAUC,IAAI,EAAE;IAC/D,IAAIC,MAAM,GAAG,IAAI,CAAC3D,MAAM,CAAC4D,SAAS;IAClC,IAAID,MAAM,IAAI,IAAI,EAAE;MAClB,OAAOD,IAAI,CAACG,iBAAiB,CAACF,MAAM,CAAC;IACvC;IACA,IAAIG,QAAQ,GAAGJ,IAAI,CAACK,UAAU;IAC9B,KAAK,IAAIC,CAAC,GAAGF,QAAQ,CAACG,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC7C,IAAIE,OAAO,GAAGJ,QAAQ,CAACE,CAAC,CAAC;MACzB,IAAIG,OAAO,GAAGT,IAAI,CAACU,gBAAgB,CAACF,OAAO,CAAC;MAC5C,IAAI,CAACC,OAAO,CAACE,kBAAkB,EAAE;QAC/B,OAAOF,OAAO,CAACG,aAAa;MAC9B;IACF;EACF,CAAC;EACDtF,cAAc,CAACc,SAAS,CAACyE,SAAS,GAAG,YAAY;IAC/C,OAAO,IAAI,CAACf,WAAW,CAACZ,KAAK,CAAC,CAAC;EACjC,CAAC;EACD5D,cAAc,CAACc,SAAS,CAACc,oBAAoB,GAAG,YAAY;IAC1D,IAAIV,OAAO,GAAG,IAAI,CAACA,OAAO;IAC1B,IAAIK,UAAU,GAAG,IAAI,CAACP,MAAM;IAC5B,IAAIwE,IAAI,GAAG;MACTC,OAAO,EAAElE,UAAU,CAACkE,OAAO;MAC3BC,UAAU,EAAEnE,UAAU,CAACmE;IACzB,CAAC;IACD,IAAIxD,MAAM,GAAGX,UAAU,CAACW,MAAM,KAAKX,UAAU,CAACW,MAAM,GAAG,CAAC,CAAC,CAAC;IAC1D,IAAID,UAAU,GAAGV,UAAU,CAACU,UAAU,KAAKV,UAAU,CAACU,UAAU,GAAG,CAAC,CAAC,CAAC;IACtE9C,MAAM,CAACwG,KAAK,CAACzD,MAAM,EAAEsD,IAAI,CAAC,CAAC,CAAC;IAC5BrG,MAAM,CAACwG,KAAK,CAAC1D,UAAU,EAAEuD,IAAI,CAAC,CAAC,CAAC;IAChC,IAAIjC,UAAU,GAAG,IAAI,CAACA,UAAU,CAAC,CAAC;IAClCqC,cAAc,CAAC3C,IAAI,CAAC,IAAI,EAAEf,MAAM,CAAC;IACjC0D,cAAc,CAAC3C,IAAI,CAAC,IAAI,EAAEhB,UAAU,CAAC;IACrC4D,gBAAgB,CAAC5C,IAAI,CAAC,IAAI,EAAEf,MAAM,EAAE,SAAS,EAAE,YAAY,CAAC;IAC5D;IACA4D,kBAAkB,CAAC7C,IAAI,CAAC,IAAI,EAAEhB,UAAU,CAAC;IACzC,SAAS2D,cAAcA,CAACJ,IAAI,EAAE;MAC5B;MACA;MACA;MACA;MACA,IAAI5F,OAAO,CAAC2B,UAAU,CAACwE,KAAK;MAC5B;MACA;MAAA,GACG,CAACP,IAAI,CAACC,OAAO,EAAE;QAChBD,IAAI,CAACC,OAAO,GAAG;UACbM,KAAK,EAAExE,UAAU,CAACwE,KAAK,CAACnC,KAAK,CAAC,CAAC,CAACoC,OAAO,CAAC;QAC1C,CAAC;MACH;MACA;MACA;MACA;MACA;MACA;MACA;MACAR,IAAI,CAACC,OAAO,GAAGD,IAAI,CAACC,OAAO,IAAI;QAC7BM,KAAK,EAAE7E,OAAO,CAAC+E,GAAG,CAAC,eAAe;MACpC,CAAC;IACH;IACA,SAASJ,gBAAgBA,CAACL,IAAI,EAAEU,UAAU,EAAEC,WAAW,EAAE;MACvD,IAAIC,QAAQ,GAAGZ,IAAI,CAACU,UAAU,CAAC;MAC/B,IAAIG,SAAS,GAAGb,IAAI,CAACW,WAAW,CAAC;MACjC,IAAIC,QAAQ,IAAI,CAACC,SAAS,EAAE;QAC1BA,SAAS,GAAGb,IAAI,CAACW,WAAW,CAAC,GAAG,CAAC,CAAC;QAClCtG,IAAI,CAACuG,QAAQ,EAAE,UAAUE,UAAU,EAAEC,UAAU,EAAE;UAC/C,IAAI,CAAClH,aAAa,CAACmH,WAAW,CAACD,UAAU,CAAC,EAAE;YAC1C;UACF;UACA,IAAIE,IAAI,GAAGrH,aAAa,CAAC6G,GAAG,CAACM,UAAU,EAAE,UAAU,EAAEhD,UAAU,CAAC;UAChE,IAAIkD,IAAI,IAAI,IAAI,EAAE;YAChBJ,SAAS,CAACE,UAAU,CAAC,GAAGE,IAAI;YAC5B;YACA;YACA;YACA,IAAIF,UAAU,KAAK,OAAO,IAAI,CAACF,SAAS,CAACK,cAAc,CAAC,SAAS,CAAC,IAAI,CAACL,SAAS,CAACK,cAAc,CAAC,YAAY,CAAC,EAAE;cAC7GL,SAAS,CAACM,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;YAC5B;UACF;QACF,CAAC,CAAC;MACJ;IACF;IACA,SAASb,kBAAkBA,CAAC7D,UAAU,EAAE;MACtC,IAAI2E,YAAY,GAAG,CAAC3E,UAAU,CAACwD,OAAO,IAAI,CAAC,CAAC,EAAEoB,MAAM,IAAI,CAAC5E,UAAU,CAACyD,UAAU,IAAI,CAAC,CAAC,EAAEmB,MAAM;MAC5F,IAAIC,gBAAgB,GAAG,CAAC7E,UAAU,CAACwD,OAAO,IAAI,CAAC,CAAC,EAAEsB,UAAU,IAAI,CAAC9E,UAAU,CAACyD,UAAU,IAAI,CAAC,CAAC,EAAEqB,UAAU;MACxG,IAAIC,aAAa,GAAG,IAAI,CAACf,GAAG,CAAC,eAAe,CAAC;MAC7C,IAAIgB,UAAU,GAAG,IAAI,CAAC9E,aAAa,CAAC,CAAC;MACrC,IAAI+E,aAAa,GAAGD,UAAU,IAAI,WAAW;MAC7CpH,IAAI,CAAC,IAAI,CAACS,SAAS,EAAE,UAAU6G,KAAK,EAAE;QACpC,IAAIC,QAAQ,GAAG,IAAI,CAACA,QAAQ;QAC5B,IAAIC,OAAO,GAAGpF,UAAU,CAACkF,KAAK,CAAC;QAC/B;QACA;QACA,IAAI,CAACE,OAAO,EAAE;UACZA,OAAO,GAAGpF,UAAU,CAACkF,KAAK,CAAC,GAAG;YAC5BpB,KAAK,EAAExC,UAAU,GAAGyD,aAAa,GAAG,CAACA,aAAa;UACpD,CAAC;QACH;QACA;QACA,IAAIK,OAAO,CAACR,MAAM,IAAI,IAAI,EAAE;UAC1BQ,OAAO,CAACR,MAAM,GAAGD,YAAY,IAAIzH,MAAM,CAACmI,KAAK,CAACV,YAAY,CAAC,KAAKrD,UAAU,GAAG2D,aAAa,GAAG,CAACA,aAAa,CAAC,CAAC;QAC/G;QACA,IAAIG,OAAO,CAACN,UAAU,IAAI,IAAI,EAAE;UAC9BM,OAAO,CAACN,UAAU,GAAGD,gBAAgB,IAAI3H,MAAM,CAACmI,KAAK,CAACR,gBAAgB,CAAC,KAAKvD,UAAU,GAAG6D,QAAQ,CAAC,CAAC,CAAC,GAAG,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;QACpI;QACA;QACAC,OAAO,CAACR,MAAM,GAAGnH,SAAS,CAAC2H,OAAO,CAACR,MAAM,EAAE,UAAUA,MAAM,EAAE;UAC3D,OAAOA,MAAM,KAAK,MAAM,GAAGK,aAAa,GAAGL,MAAM;QACnD,CAAC,CAAC;QACF;QACA,IAAIE,UAAU,GAAGM,OAAO,CAACN,UAAU;QACnC,IAAIA,UAAU,IAAI,IAAI,EAAE;UACtB,IAAIQ,KAAK,GAAG,CAAC5G,QAAQ;UACrB;UACAhB,UAAU,CAACoH,UAAU,EAAE,UAAUzD,KAAK,EAAE;YACtCA,KAAK,GAAGiE,KAAK,KAAKA,KAAK,GAAGjE,KAAK,CAAC;UAClC,CAAC,CAAC;UACF+D,OAAO,CAACN,UAAU,GAAGrH,SAAS,CAACqH,UAAU,EAAE,UAAUzD,KAAK,EAAE;YAC1D,OAAOvD,SAAS,CAACuD,KAAK,EAAE,CAAC,CAAC,EAAEiE,KAAK,CAAC,EAAE,CAAC,CAAC,EAAEH,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;UAC7D,CAAC,CAAC;QACJ;MACF,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC;EACDpH,cAAc,CAACc,SAAS,CAACa,aAAa,GAAG,YAAY;IACnD,IAAI,CAACyF,QAAQ,GAAG,CAACI,UAAU,CAAC,IAAI,CAACvB,GAAG,CAAC,WAAW,CAAC,CAAC,EAAEuB,UAAU,CAAC,IAAI,CAACvB,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC;EACzF,CAAC;EACDjG,cAAc,CAACc,SAAS,CAACyC,UAAU,GAAG,YAAY;IAChD,OAAO,CAAC,CAAC,IAAI,CAACvC,MAAM,CAACyG,UAAU;EACjC,CAAC;EACD;AACF;AACA;AACA;EACEzH,cAAc,CAACc,SAAS,CAAC4G,WAAW,GAAG,UAAUC,QAAQ,EAAE,CAAC,CAAC;EAC7D3H,cAAc,CAACc,SAAS,CAAC8G,WAAW,GAAG,YAAY;IACjD,OAAO,IAAI;EACb,CAAC;EACD;AACF;AACA;AACA;EACE5H,cAAc,CAACc,SAAS,CAAC+G,aAAa,GAAG,UAAUvE,KAAK,EAAE;IACxD,OAAO,IAAI;EACb,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEtD,cAAc,CAACc,SAAS,CAACgH,aAAa,GAAG,UAAUC,cAAc,EAAE;IACjE,OAAO,IAAI;EACb,CAAC;EACD/H,cAAc,CAACK,IAAI,GAAG,WAAW;EACjCL,cAAc,CAACgI,YAAY,GAAG,CAAC,QAAQ,CAAC;EACxChI,cAAc,CAACiI,aAAa,GAAG;IAC7BC,IAAI,EAAE,IAAI;IACV;IACAC,CAAC,EAAE,CAAC;IACJ7F,WAAW,EAAE,KAAK;IAClB8B,GAAG,EAAE,CAAC;IACNG,GAAG,EAAE,GAAG;IACR6D,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,IAAI;IACTC,MAAM,EAAE,CAAC;IACTC,SAAS,EAAE,IAAI;IACfC,UAAU,EAAE,IAAI;IAChBC,OAAO,EAAE,KAAK;IACdC,MAAM,EAAE,UAAU;IAClBC,eAAe,EAAE,eAAe;IAChCC,WAAW,EAAE,MAAM;IACnBC,YAAY,EAAE,SAAS;IACvB9B,aAAa,EAAE,MAAM;IACrB+B,WAAW,EAAE,CAAC;IACdC,OAAO,EAAE,CAAC;IACV;IACAC,OAAO,EAAE,EAAE;IACXxF,SAAS,EAAE,CAAC;IACZyF,SAAS,EAAE;MACTnD,KAAK,EAAE,MAAM,CAAC;IAChB;EACF,CAAC;EACD,OAAO/F,cAAc;AACvB,CAAC,CAACP,cAAc,CAAC;AACjB,eAAeO,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}