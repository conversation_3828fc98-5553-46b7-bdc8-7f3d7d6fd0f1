"""
DOCX格式保持的简历优化模块
保持原始文档的格式和样式，仅替换内容
"""

import os
import re
import tempfile
import logging
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
import openai
import json
from typing import Dict, List, Tuple
from openai import OpenAI

logger = logging.getLogger(__name__)

class DocxOptimizer:
    def __init__(self, api_key: str):
        """初始化优化器"""
        self.client = OpenAI(api_key=api_key)
        
    def _extract_all_text_elements(self, doc: Document) -> List[Dict]:
        """提取文档中所有文本元素及其位置信息"""
        elements = []
        
        # 1. 提取正文段落
        for i, para in enumerate(doc.paragraphs):
            if para.text.strip():
                elements.append({
                    'type': 'paragraph',
                    'index': i,
                    'text': para.text,
                    'style': para.style.name if para.style else None,
                    'runs': [{'text': run.text, 'bold': run.bold, 'italic': run.italic} for run in para.runs]
                })
        
        # 2. 提取表格内容
        for table_idx, table in enumerate(doc.tables):
            for row_idx, row in enumerate(table.rows):
                for cell_idx, cell in enumerate(row.cells):
                    for para_idx, para in enumerate(cell.paragraphs):
                        if para.text.strip():
                            elements.append({
                                'type': 'table_cell',
                                'table_index': table_idx,
                                'row_index': row_idx,
                                'cell_index': cell_idx,
                                'para_index': para_idx,
                                'text': para.text,
                                'style': para.style.name if para.style else None,
                                'runs': [{'text': run.text, 'bold': run.bold, 'italic': run.italic} for run in para.runs]
                            })
        
        # 3. 提取页眉
        for section in doc.sections:
            for header in section.header.paragraphs:
                if header.text.strip():
                    elements.append({
                        'type': 'header',
                        'text': header.text,
                        'style': header.style.name if header.style else None,
                        'runs': [{'text': run.text, 'bold': run.bold, 'italic': run.italic} for run in header.runs]
                    })
        
        # 4. 提取页脚
        for section in doc.sections:
            for footer in section.footer.paragraphs:
                if footer.text.strip():
                    elements.append({
                        'type': 'footer',
                        'text': footer.text,
                        'style': footer.style.name if footer.style else None,
                        'runs': [{'text': run.text, 'bold': run.bold, 'italic': run.italic} for run in footer.runs]
                    })
        
        return elements

    def _build_optimization_prompt(self, elements: List[Dict], job_description: str) -> str:
        """构建优化提示词"""
        prompt = (
            "请对以下简历内容进行优化，要求：\n"
            "1. 保持原文结构，只优化表达和内容。\n"
            "2. 返回JSON数组，每项格式为：\n"
            "{\"original\": \"原文\", \"optimized\": \"优化后\", \"type\": \"元素类型\"}\n"
            "3. 元素类型包括：paragraph（段落）、table_cell（表格单元格）、header（页眉）、footer（页脚）\n"
            "4. 不得编造或添加原文没有的信息。\n"
            f"职位描述：{job_description}\n"
            "需要优化的内容：\n"
        )
        
        for elem in elements:
            prompt += f"类型：{elem['type']}\n"
            prompt += f"原文：{elem['text']}\n"
            prompt += "---\n"
            
        return prompt

    def _optimize_text_elements(self, elements: List[Dict], job_description: str) -> List[Dict]:
        """使用AI优化文本元素"""
        prompt = self._build_optimization_prompt(elements, job_description)
        
        try:
            response = self.client.chat.completions.create(
                model="gpt-4-turbo-preview",
                messages=[
                    {"role": "system", "content": "你是一个专业的简历优化专家，擅长保持原文结构的同时优化表达。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.7,
                max_tokens=4000
            )
            
            # 解析AI返回的JSON
            optimized_elements = json.loads(response.choices[0].message.content)
            return optimized_elements
            
        except Exception as e:
            logger.error(f"AI优化失败: {str(e)}")
            return elements  # 如果优化失败，返回原始元素

    def _apply_optimized_text(self, doc: Document, optimized_elements: List[Dict]) -> None:
        """将优化后的文本应用到文档中"""
        for elem in optimized_elements:
            if elem['type'] == 'paragraph':
                if 0 <= elem['index'] < len(doc.paragraphs):
                    para = doc.paragraphs[elem['index']]
                    self._update_paragraph_text(para, elem['optimized'])
                    
            elif elem['type'] == 'table_cell':
                if (0 <= elem['table_index'] < len(doc.tables) and
                    0 <= elem['row_index'] < len(doc.tables[elem['table_index']].rows) and
                    0 <= elem['cell_index'] < len(doc.tables[elem['table_index']].rows[elem['row_index']].cells)):
                    cell = doc.tables[elem['table_index']].rows[elem['row_index']].cells[elem['cell_index']]
                    if 0 <= elem['para_index'] < len(cell.paragraphs):
                        para = cell.paragraphs[elem['para_index']]
                        self._update_paragraph_text(para, elem['optimized'])
                        
            elif elem['type'] == 'header':
                for section in doc.sections:
                    for header in section.header.paragraphs:
                        if header.text == elem['original']:
                            self._update_paragraph_text(header, elem['optimized'])
                            
            elif elem['type'] == 'footer':
                for section in doc.sections:
                    for footer in section.footer.paragraphs:
                        if footer.text == elem['original']:
                            self._update_paragraph_text(footer, elem['optimized'])

    def _update_paragraph_text(self, paragraph, new_text: str) -> None:
        """更新段落文本，保持原有格式"""
        # 保存原有格式
        runs = paragraph.runs
        if not runs:
            paragraph.text = new_text
            return
            
        # 如果有多个run，保持第一个run的格式，删除其他run
        first_run = runs[0]
        first_run.text = new_text
        for run in runs[1:]:
            run.text = ""

    def optimize_docx(self, input_path: str, output_path: str, job_description: str) -> bool:
        """优化Word文档，保持格式"""
        try:
            # 1. 读取文档
            doc = Document(input_path)
            
            # 2. 提取所有文本元素
            elements = self._extract_all_text_elements(doc)
            
            # 3. AI优化文本
            optimized_elements = self._optimize_text_elements(elements, job_description)
            
            # 4. 应用优化后的文本
            self._apply_optimized_text(doc, optimized_elements)
            
            # 5. 保存文档
            doc.save(output_path)
            
            return True
            
        except Exception as e:
            logger.error(f"文档优化失败: {str(e)}")
            return False

def optimize_docx_resume_with_format_preservation(
    input_path: str,
    output_path: str,
    job_description: str,
    api_key: str
) -> bool:
    """优化简历文档，保持格式"""
    optimizer = DocxOptimizer(api_key)
    return optimizer.optimize_docx(input_path, output_path, job_description) 