# DOCX Format Preservation Feature

## Overview
SmartCV now supports **Enhanced Feature** functionality, which can preserve the original document formatting, fonts, and styles while only replacing and optimizing resume content.

## Core Features

### 🎯 Precise Content Replacement
- Maintain original font, size, and color
- Maintain paragraph style and alignment
- Maintain bold, italic, underline, etc. formatting
- Maintain document structure such as tables and lists

### 🤖 AI-driven Paragraph-level Optimization
- GPT-4o analyzes original resume and job description
- Generates paragraph-level optimization suggestions
- Provides modification reasons and comparison
- Intelligent keyword matching and skill highlighting

### 📥 Original Format File Download
- Download optimized DOCX file in original format
- Supports Word native editing and further modification
- Perfectly compatible with Microsoft Word and other office software

## Usage

### 1. Upload DOCX File
- Select your DOCX format resume file
- System automatically recognizes file format

### 2. Enable Enhanced Feature
- When a DOCX file is uploaded, the "Enhanced Feature" option will be displayed
- Click the toggle switch to enable this feature
- See the purple "Enhanced Feature" label

### 3. Input Job Description
- Paste detailed description of target job
- Or upload job description file

### 4. Optimize Resume
- Click "Optimize" button to start processing
- AI analyzes your resume and job requirements
- Generates paragraph-level optimization suggestions

### 5. View Results
- **Optimized Preview**: View text version of optimized result
- **Paragraph Modification**: View specific paragraph-level modification comparison
- **Download Original Format**: Download DOCX file in original format

## Technical Implementation

### Backend Architecture
```
DocxOptimizer 类
├── extract_document_structure()     # 提取文档结构和样式
├── generate_optimized_content()     # GPT生成优化内容
├── apply_optimizations()           # 应用优化到原文档
└── preserve_formatting()          # 保持原始格式
```

### API Endpoints
- `POST /api/optimize-resume-docx` - Enhanced Feature
- `GET /api/download-optimized-docx/<filename>` - Download optimized file

### Frontend Components
- **Enhanced Feature Option**: Display toggle switch in file upload area
- **Paragraph Modification Display**: Show specific modification comparison
- **Original Format Download**: Purple download button

## Technical Details

### Document Structure Extraction
```python
# Extract paragraph style information
para_data = {
    'text': paragraph.text,
    'style': paragraph.style.name,
    'alignment': paragraph.alignment,
    'runs': [...]  # Format information for each text block
}
```

### GPT Prompt Strategy
```
Requirements:
1. Maintain the authenticity of original content, only adjust wording and keywords
2. Optimize skill keywords and experience description for job requirements
3. Output format as JSON, containing paragraph-level modification mapping
```

### Format Preservation Mechanism
```python
# Save original Run style
original_style = {
    'bold': run.bold,
    'italic': run.italic,
    'font_name': run.font.name,
    'font_size': run.font.size
}

# Apply to new content
new_run = paragraph.add_run(optimized_text)
apply_style(new_run, original_style)
```

## Advantage Comparison

| Feature | Regular Optimization | Enhanced Feature |
|---------|---------------------|----------------|
| Content Optimization | ✅ | ✅ |
| Format Preservation | ❌ | ✅ |
| Font Style | ❌ | ✅ |
| Document Structure | ❌ | ✅ |
| Native Editing | ❌ | ✅ |
| Paragraph Comparison | ❌ | ✅ |

## Usage Suggestions

### Best Practices
1. **Use Clear Document Structure**: Ensure original DOCX has clear paragraph division
2. **Avoid Complex Formatting**: Reduce use of complex tables and images
3. **Provide Detailed JD**: More detailed job description can lead to better optimization effect
4. **Check Results**: Download and carefully check format to ensure it meets expectations

### Restrictions and Notes
- Only supports DOCX format (does not support DOC, PDF, etc.)
- Complex tables and images may require manual adjustment
- Suggest backing up original file before use
- Processing time may be slightly longer than regular optimization

## Error Handling

### Common Issues
1. **File Format Error**: Ensure uploaded file is DOCX format
2. **Style Loss**: Check if original document used standard styles
3. **Download Failure**: Check network connection, try again later

### Debug Information
- Backend log records detailed processing steps
- Frontend console shows API call status
- Error information displays in user-friendly manner

## Future Extensions

### Planned Features
- [ ] Support more Office formats (PPT, Excel)
- [ ] Batch process multiple documents
- [ ] Custom style template
- [ ] Version comparison and rollback
- [ ] Collaboration editing and comment

### Technical Optimization
- [ ] Processing speed optimization
- [ ] More precise format matching
- [ ] Support more complex document structure
- [ ] Enhanced error recovery mechanism

---

**SmartCV Team** - Make AI Resume Optimization More Professional and Intelligent! 