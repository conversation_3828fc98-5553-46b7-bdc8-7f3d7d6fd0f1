#!/usr/bin/env python3
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

from ai_optimizer import create_enhanced_fallback_optimization

# 测试fallback函数
result = create_enhanced_fallback_optimization(
    "Sample resume text with some content", 
    "Looking for Python developer with experience", 
    "Test error message"
)

print("Generated fallback content:")
print("=" * 50)
print(result)
print("=" * 50)
print(f"Length: {len(result)}")
print("Contains Chinese?", any('\u4e00' <= char <= '\u9fff' for char in result)) 