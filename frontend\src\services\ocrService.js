import Tesseract from 'tesseract.js';

/**
 * Extract text from image file using OCR
 * @param {File} imageFile - Image file to process
 * @param {function} onProgress - Progress callback function
 * @returns {Promise<string>} - Extracted text
 */
export const extractTextFromImage = async (imageFile, onProgress = null) => {
  try {
    console.log('Starting OCR text extraction...');
    
    const { data: { text } } = await Tesseract.recognize(
      imageFile,
      'eng',
      {
        logger: (m) => {
          if (onProgress && m.status === 'recognizing text') {
            onProgress(Math.round(m.progress * 100));
          }
          console.log('OCR Progress:', m);
        }
      }
    );
    
    console.log('OCR extraction completed');
    return text.trim();
    
  } catch (error) {
    console.error('OCR extraction failed:', error);
    throw new Error('Failed to extract text from image. Please try a clearer image or different format.');
  }
};

/**
 * Check if file is an image
 * @param {File} file - File to check
 * @returns {boolean} - Whether file is an image
 */
export const isImageFile = (file) => {
  return file && file.type.startsWith('image/');
};

/**
 * Validate image file for OCR processing
 * @param {File} file - Image file to validate
 * @returns {object} - Validation result with isValid and message
 */
export const validateImageForOCR = (file) => {
  if (!file) {
    return { isValid: false, message: 'No file provided' };
  }
  
  if (!isImageFile(file)) {
    return { isValid: false, message: 'File must be an image' };
  }
  
  // Check file size (max 10MB for OCR)
  const maxSize = 10 * 1024 * 1024; // 10MB
  if (file.size > maxSize) {
    return { isValid: false, message: 'Image file too large. Please use an image smaller than 10MB.' };
  }
  
  // 简化格式检查 - 只要是image/*类型就接受
  console.log('Image validation passed:', file.name, file.type);
  return { isValid: true, message: 'Valid image file' };
}; 