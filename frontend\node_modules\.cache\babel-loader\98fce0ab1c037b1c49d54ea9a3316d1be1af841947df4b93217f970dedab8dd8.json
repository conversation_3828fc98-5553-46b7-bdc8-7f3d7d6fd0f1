{"ast": null, "code": "import { __extends } from \"tslib\";\nimport * as util from './core/util.js';\nimport * as vec2 from './core/vector.js';\nimport Draggable from './mixin/Draggable.js';\nimport Eventful from './core/Eventful.js';\nimport * as eventTool from './core/event.js';\nimport { GestureMgr } from './core/GestureMgr.js';\nimport BoundingRect from './core/BoundingRect.js';\nvar SILENT = 'silent';\nfunction makeEventPacket(eveType, targetInfo, event) {\n  return {\n    type: eveType,\n    event: event,\n    target: targetInfo.target,\n    topTarget: targetInfo.topTarget,\n    cancelBubble: false,\n    offsetX: event.zrX,\n    offsetY: event.zrY,\n    gestureEvent: event.gestureEvent,\n    pinchX: event.pinchX,\n    pinchY: event.pinchY,\n    pinchScale: event.pinchScale,\n    wheelDelta: event.zrDelta,\n    zrByTouch: event.zrByTouch,\n    which: event.which,\n    stop: stopEvent\n  };\n}\nfunction stopEvent() {\n  eventTool.stop(this.event);\n}\nvar EmptyProxy = function (_super) {\n  __extends(EmptyProxy, _super);\n  function EmptyProxy() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.handler = null;\n    return _this;\n  }\n  EmptyProxy.prototype.dispose = function () {};\n  EmptyProxy.prototype.setCursor = function () {};\n  return EmptyProxy;\n}(Eventful);\nvar HoveredResult = function () {\n  function HoveredResult(x, y) {\n    this.x = x;\n    this.y = y;\n  }\n  return HoveredResult;\n}();\nvar handlerNames = ['click', 'dblclick', 'mousewheel', 'mouseout', 'mouseup', 'mousedown', 'mousemove', 'contextmenu'];\nvar tmpRect = new BoundingRect(0, 0, 0, 0);\nvar Handler = function (_super) {\n  __extends(Handler, _super);\n  function Handler(storage, painter, proxy, painterRoot, pointerSize) {\n    var _this = _super.call(this) || this;\n    _this._hovered = new HoveredResult(0, 0);\n    _this.storage = storage;\n    _this.painter = painter;\n    _this.painterRoot = painterRoot;\n    _this._pointerSize = pointerSize;\n    proxy = proxy || new EmptyProxy();\n    _this.proxy = null;\n    _this.setHandlerProxy(proxy);\n    _this._draggingMgr = new Draggable(_this);\n    return _this;\n  }\n  Handler.prototype.setHandlerProxy = function (proxy) {\n    if (this.proxy) {\n      this.proxy.dispose();\n    }\n    if (proxy) {\n      util.each(handlerNames, function (name) {\n        proxy.on && proxy.on(name, this[name], this);\n      }, this);\n      proxy.handler = this;\n    }\n    this.proxy = proxy;\n  };\n  Handler.prototype.mousemove = function (event) {\n    var x = event.zrX;\n    var y = event.zrY;\n    var isOutside = isOutsideBoundary(this, x, y);\n    var lastHovered = this._hovered;\n    var lastHoveredTarget = lastHovered.target;\n    if (lastHoveredTarget && !lastHoveredTarget.__zr) {\n      lastHovered = this.findHover(lastHovered.x, lastHovered.y);\n      lastHoveredTarget = lastHovered.target;\n    }\n    var hovered = this._hovered = isOutside ? new HoveredResult(x, y) : this.findHover(x, y);\n    var hoveredTarget = hovered.target;\n    var proxy = this.proxy;\n    proxy.setCursor && proxy.setCursor(hoveredTarget ? hoveredTarget.cursor : 'default');\n    if (lastHoveredTarget && hoveredTarget !== lastHoveredTarget) {\n      this.dispatchToElement(lastHovered, 'mouseout', event);\n    }\n    this.dispatchToElement(hovered, 'mousemove', event);\n    if (hoveredTarget && hoveredTarget !== lastHoveredTarget) {\n      this.dispatchToElement(hovered, 'mouseover', event);\n    }\n  };\n  Handler.prototype.mouseout = function (event) {\n    var eventControl = event.zrEventControl;\n    if (eventControl !== 'only_globalout') {\n      this.dispatchToElement(this._hovered, 'mouseout', event);\n    }\n    if (eventControl !== 'no_globalout') {\n      this.trigger('globalout', {\n        type: 'globalout',\n        event: event\n      });\n    }\n  };\n  Handler.prototype.resize = function () {\n    this._hovered = new HoveredResult(0, 0);\n  };\n  Handler.prototype.dispatch = function (eventName, eventArgs) {\n    var handler = this[eventName];\n    handler && handler.call(this, eventArgs);\n  };\n  Handler.prototype.dispose = function () {\n    this.proxy.dispose();\n    this.storage = null;\n    this.proxy = null;\n    this.painter = null;\n  };\n  Handler.prototype.setCursorStyle = function (cursorStyle) {\n    var proxy = this.proxy;\n    proxy.setCursor && proxy.setCursor(cursorStyle);\n  };\n  Handler.prototype.dispatchToElement = function (targetInfo, eventName, event) {\n    targetInfo = targetInfo || {};\n    var el = targetInfo.target;\n    if (el && el.silent) {\n      return;\n    }\n    var eventKey = 'on' + eventName;\n    var eventPacket = makeEventPacket(eventName, targetInfo, event);\n    while (el) {\n      el[eventKey] && (eventPacket.cancelBubble = !!el[eventKey].call(el, eventPacket));\n      el.trigger(eventName, eventPacket);\n      el = el.__hostTarget ? el.__hostTarget : el.parent;\n      if (eventPacket.cancelBubble) {\n        break;\n      }\n    }\n    if (!eventPacket.cancelBubble) {\n      this.trigger(eventName, eventPacket);\n      if (this.painter && this.painter.eachOtherLayer) {\n        this.painter.eachOtherLayer(function (layer) {\n          if (typeof layer[eventKey] === 'function') {\n            layer[eventKey].call(layer, eventPacket);\n          }\n          if (layer.trigger) {\n            layer.trigger(eventName, eventPacket);\n          }\n        });\n      }\n    }\n  };\n  Handler.prototype.findHover = function (x, y, exclude) {\n    var list = this.storage.getDisplayList();\n    var out = new HoveredResult(x, y);\n    setHoverTarget(list, out, x, y, exclude);\n    if (this._pointerSize && !out.target) {\n      var candidates = [];\n      var pointerSize = this._pointerSize;\n      var targetSizeHalf = pointerSize / 2;\n      var pointerRect = new BoundingRect(x - targetSizeHalf, y - targetSizeHalf, pointerSize, pointerSize);\n      for (var i = list.length - 1; i >= 0; i--) {\n        var el = list[i];\n        if (el !== exclude && !el.ignore && !el.ignoreCoarsePointer && (!el.parent || !el.parent.ignoreCoarsePointer)) {\n          tmpRect.copy(el.getBoundingRect());\n          if (el.transform) {\n            tmpRect.applyTransform(el.transform);\n          }\n          if (tmpRect.intersect(pointerRect)) {\n            candidates.push(el);\n          }\n        }\n      }\n      if (candidates.length) {\n        var rStep = 4;\n        var thetaStep = Math.PI / 12;\n        var PI2 = Math.PI * 2;\n        for (var r = 0; r < targetSizeHalf; r += rStep) {\n          for (var theta = 0; theta < PI2; theta += thetaStep) {\n            var x1 = x + r * Math.cos(theta);\n            var y1 = y + r * Math.sin(theta);\n            setHoverTarget(candidates, out, x1, y1, exclude);\n            if (out.target) {\n              return out;\n            }\n          }\n        }\n      }\n    }\n    return out;\n  };\n  Handler.prototype.processGesture = function (event, stage) {\n    if (!this._gestureMgr) {\n      this._gestureMgr = new GestureMgr();\n    }\n    var gestureMgr = this._gestureMgr;\n    stage === 'start' && gestureMgr.clear();\n    var gestureInfo = gestureMgr.recognize(event, this.findHover(event.zrX, event.zrY, null).target, this.proxy.dom);\n    stage === 'end' && gestureMgr.clear();\n    if (gestureInfo) {\n      var type = gestureInfo.type;\n      event.gestureEvent = type;\n      var res = new HoveredResult();\n      res.target = gestureInfo.target;\n      this.dispatchToElement(res, type, gestureInfo.event);\n    }\n  };\n  return Handler;\n}(Eventful);\nutil.each(['click', 'mousedown', 'mouseup', 'mousewheel', 'dblclick', 'contextmenu'], function (name) {\n  Handler.prototype[name] = function (event) {\n    var x = event.zrX;\n    var y = event.zrY;\n    var isOutside = isOutsideBoundary(this, x, y);\n    var hovered;\n    var hoveredTarget;\n    if (name !== 'mouseup' || !isOutside) {\n      hovered = this.findHover(x, y);\n      hoveredTarget = hovered.target;\n    }\n    if (name === 'mousedown') {\n      this._downEl = hoveredTarget;\n      this._downPoint = [event.zrX, event.zrY];\n      this._upEl = hoveredTarget;\n    } else if (name === 'mouseup') {\n      this._upEl = hoveredTarget;\n    } else if (name === 'click') {\n      if (this._downEl !== this._upEl || !this._downPoint || vec2.dist(this._downPoint, [event.zrX, event.zrY]) > 4) {\n        return;\n      }\n      this._downPoint = null;\n    }\n    this.dispatchToElement(hovered, name, event);\n  };\n});\nfunction isHover(displayable, x, y) {\n  if (displayable[displayable.rectHover ? 'rectContain' : 'contain'](x, y)) {\n    var el = displayable;\n    var isSilent = void 0;\n    var ignoreClip = false;\n    while (el) {\n      if (el.ignoreClip) {\n        ignoreClip = true;\n      }\n      if (!ignoreClip) {\n        var clipPath = el.getClipPath();\n        if (clipPath && !clipPath.contain(x, y)) {\n          return false;\n        }\n      }\n      if (el.silent) {\n        isSilent = true;\n      }\n      var hostEl = el.__hostTarget;\n      el = hostEl ? hostEl : el.parent;\n    }\n    return isSilent ? SILENT : true;\n  }\n  return false;\n}\nfunction setHoverTarget(list, out, x, y, exclude) {\n  for (var i = list.length - 1; i >= 0; i--) {\n    var el = list[i];\n    var hoverCheckResult = void 0;\n    if (el !== exclude && !el.ignore && (hoverCheckResult = isHover(el, x, y))) {\n      !out.topTarget && (out.topTarget = el);\n      if (hoverCheckResult !== SILENT) {\n        out.target = el;\n        break;\n      }\n    }\n  }\n}\nfunction isOutsideBoundary(handlerInstance, x, y) {\n  var painter = handlerInstance.painter;\n  return x < 0 || x > painter.getWidth() || y < 0 || y > painter.getHeight();\n}\nexport default Handler;", "map": {"version": 3, "names": ["__extends", "util", "vec2", "Draggable", "Eventful", "eventTool", "GestureMgr", "BoundingRect", "SILENT", "makeEventPacket", "eveType", "targetInfo", "event", "type", "target", "topTarget", "cancelBubble", "offsetX", "zrX", "offsetY", "zrY", "gestureEvent", "pinchX", "pinchY", "pinchScale", "wheelDelta", "zrDelta", "zrByTouch", "which", "stop", "stopEvent", "EmptyProxy", "_super", "_this", "apply", "arguments", "handler", "prototype", "dispose", "setCursor", "HoveredResult", "x", "y", "handlerNames", "tmpRect", "Handler", "storage", "painter", "proxy", "<PERSON><PERSON><PERSON>", "pointerSize", "call", "_hovered", "_pointerSize", "setHandlerProxy", "_draggingMgr", "each", "name", "on", "mousemove", "isOutside", "isOutsideBoundary", "lastHovered", "lastHoveredTarget", "__zr", "findHover", "hovered", "<PERSON><PERSON><PERSON><PERSON>", "cursor", "dispatchToElement", "mouseout", "eventControl", "zrEventControl", "trigger", "resize", "dispatch", "eventName", "eventArgs", "setCursorStyle", "cursorStyle", "el", "silent", "eventKey", "eventPacket", "__host<PERSON><PERSON>get", "parent", "eachOther<PERSON>ayer", "layer", "exclude", "list", "getDisplayList", "out", "setHoverTarget", "candidates", "targetSizeHalf", "pointerRect", "i", "length", "ignore", "ignoreCoarsePointer", "copy", "getBoundingRect", "transform", "applyTransform", "intersect", "push", "rStep", "thetaStep", "Math", "PI", "PI2", "r", "theta", "x1", "cos", "y1", "sin", "processGesture", "stage", "_gestureMgr", "gestureMgr", "clear", "gestureInfo", "recognize", "dom", "res", "_downEl", "_downPoint", "_upEl", "dist", "isHover", "displayable", "rectHover", "isSilent", "ignoreClip", "clipPath", "getClipPath", "contain", "hostEl", "hoverCheckResult", "handlerInstance", "getWidth", "getHeight"], "sources": ["E:/AI/SmartCV/node_modules/zrender/lib/Handler.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport * as util from './core/util.js';\nimport * as vec2 from './core/vector.js';\nimport Draggable from './mixin/Draggable.js';\nimport Eventful from './core/Eventful.js';\nimport * as eventTool from './core/event.js';\nimport { GestureMgr } from './core/GestureMgr.js';\nimport BoundingRect from './core/BoundingRect.js';\nvar SILENT = 'silent';\nfunction makeEventPacket(eveType, targetInfo, event) {\n    return {\n        type: eveType,\n        event: event,\n        target: targetInfo.target,\n        topTarget: targetInfo.topTarget,\n        cancelBubble: false,\n        offsetX: event.zrX,\n        offsetY: event.zrY,\n        gestureEvent: event.gestureEvent,\n        pinchX: event.pinchX,\n        pinchY: event.pinchY,\n        pinchScale: event.pinchScale,\n        wheelDelta: event.zrDelta,\n        zrByTouch: event.zrByTouch,\n        which: event.which,\n        stop: stopEvent\n    };\n}\nfunction stopEvent() {\n    eventTool.stop(this.event);\n}\nvar EmptyProxy = (function (_super) {\n    __extends(EmptyProxy, _super);\n    function EmptyProxy() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.handler = null;\n        return _this;\n    }\n    EmptyProxy.prototype.dispose = function () { };\n    EmptyProxy.prototype.setCursor = function () { };\n    return EmptyProxy;\n}(Eventful));\nvar HoveredResult = (function () {\n    function HoveredResult(x, y) {\n        this.x = x;\n        this.y = y;\n    }\n    return HoveredResult;\n}());\nvar handlerNames = [\n    'click', 'dblclick', 'mousewheel', 'mouseout',\n    'mouseup', 'mousedown', 'mousemove', 'contextmenu'\n];\nvar tmpRect = new BoundingRect(0, 0, 0, 0);\nvar Handler = (function (_super) {\n    __extends(Handler, _super);\n    function Handler(storage, painter, proxy, painterRoot, pointerSize) {\n        var _this = _super.call(this) || this;\n        _this._hovered = new HoveredResult(0, 0);\n        _this.storage = storage;\n        _this.painter = painter;\n        _this.painterRoot = painterRoot;\n        _this._pointerSize = pointerSize;\n        proxy = proxy || new EmptyProxy();\n        _this.proxy = null;\n        _this.setHandlerProxy(proxy);\n        _this._draggingMgr = new Draggable(_this);\n        return _this;\n    }\n    Handler.prototype.setHandlerProxy = function (proxy) {\n        if (this.proxy) {\n            this.proxy.dispose();\n        }\n        if (proxy) {\n            util.each(handlerNames, function (name) {\n                proxy.on && proxy.on(name, this[name], this);\n            }, this);\n            proxy.handler = this;\n        }\n        this.proxy = proxy;\n    };\n    Handler.prototype.mousemove = function (event) {\n        var x = event.zrX;\n        var y = event.zrY;\n        var isOutside = isOutsideBoundary(this, x, y);\n        var lastHovered = this._hovered;\n        var lastHoveredTarget = lastHovered.target;\n        if (lastHoveredTarget && !lastHoveredTarget.__zr) {\n            lastHovered = this.findHover(lastHovered.x, lastHovered.y);\n            lastHoveredTarget = lastHovered.target;\n        }\n        var hovered = this._hovered = isOutside ? new HoveredResult(x, y) : this.findHover(x, y);\n        var hoveredTarget = hovered.target;\n        var proxy = this.proxy;\n        proxy.setCursor && proxy.setCursor(hoveredTarget ? hoveredTarget.cursor : 'default');\n        if (lastHoveredTarget && hoveredTarget !== lastHoveredTarget) {\n            this.dispatchToElement(lastHovered, 'mouseout', event);\n        }\n        this.dispatchToElement(hovered, 'mousemove', event);\n        if (hoveredTarget && hoveredTarget !== lastHoveredTarget) {\n            this.dispatchToElement(hovered, 'mouseover', event);\n        }\n    };\n    Handler.prototype.mouseout = function (event) {\n        var eventControl = event.zrEventControl;\n        if (eventControl !== 'only_globalout') {\n            this.dispatchToElement(this._hovered, 'mouseout', event);\n        }\n        if (eventControl !== 'no_globalout') {\n            this.trigger('globalout', { type: 'globalout', event: event });\n        }\n    };\n    Handler.prototype.resize = function () {\n        this._hovered = new HoveredResult(0, 0);\n    };\n    Handler.prototype.dispatch = function (eventName, eventArgs) {\n        var handler = this[eventName];\n        handler && handler.call(this, eventArgs);\n    };\n    Handler.prototype.dispose = function () {\n        this.proxy.dispose();\n        this.storage = null;\n        this.proxy = null;\n        this.painter = null;\n    };\n    Handler.prototype.setCursorStyle = function (cursorStyle) {\n        var proxy = this.proxy;\n        proxy.setCursor && proxy.setCursor(cursorStyle);\n    };\n    Handler.prototype.dispatchToElement = function (targetInfo, eventName, event) {\n        targetInfo = targetInfo || {};\n        var el = targetInfo.target;\n        if (el && el.silent) {\n            return;\n        }\n        var eventKey = ('on' + eventName);\n        var eventPacket = makeEventPacket(eventName, targetInfo, event);\n        while (el) {\n            el[eventKey]\n                && (eventPacket.cancelBubble = !!el[eventKey].call(el, eventPacket));\n            el.trigger(eventName, eventPacket);\n            el = el.__hostTarget ? el.__hostTarget : el.parent;\n            if (eventPacket.cancelBubble) {\n                break;\n            }\n        }\n        if (!eventPacket.cancelBubble) {\n            this.trigger(eventName, eventPacket);\n            if (this.painter && this.painter.eachOtherLayer) {\n                this.painter.eachOtherLayer(function (layer) {\n                    if (typeof (layer[eventKey]) === 'function') {\n                        layer[eventKey].call(layer, eventPacket);\n                    }\n                    if (layer.trigger) {\n                        layer.trigger(eventName, eventPacket);\n                    }\n                });\n            }\n        }\n    };\n    Handler.prototype.findHover = function (x, y, exclude) {\n        var list = this.storage.getDisplayList();\n        var out = new HoveredResult(x, y);\n        setHoverTarget(list, out, x, y, exclude);\n        if (this._pointerSize && !out.target) {\n            var candidates = [];\n            var pointerSize = this._pointerSize;\n            var targetSizeHalf = pointerSize / 2;\n            var pointerRect = new BoundingRect(x - targetSizeHalf, y - targetSizeHalf, pointerSize, pointerSize);\n            for (var i = list.length - 1; i >= 0; i--) {\n                var el = list[i];\n                if (el !== exclude\n                    && !el.ignore\n                    && !el.ignoreCoarsePointer\n                    && (!el.parent || !el.parent.ignoreCoarsePointer)) {\n                    tmpRect.copy(el.getBoundingRect());\n                    if (el.transform) {\n                        tmpRect.applyTransform(el.transform);\n                    }\n                    if (tmpRect.intersect(pointerRect)) {\n                        candidates.push(el);\n                    }\n                }\n            }\n            if (candidates.length) {\n                var rStep = 4;\n                var thetaStep = Math.PI / 12;\n                var PI2 = Math.PI * 2;\n                for (var r = 0; r < targetSizeHalf; r += rStep) {\n                    for (var theta = 0; theta < PI2; theta += thetaStep) {\n                        var x1 = x + r * Math.cos(theta);\n                        var y1 = y + r * Math.sin(theta);\n                        setHoverTarget(candidates, out, x1, y1, exclude);\n                        if (out.target) {\n                            return out;\n                        }\n                    }\n                }\n            }\n        }\n        return out;\n    };\n    Handler.prototype.processGesture = function (event, stage) {\n        if (!this._gestureMgr) {\n            this._gestureMgr = new GestureMgr();\n        }\n        var gestureMgr = this._gestureMgr;\n        stage === 'start' && gestureMgr.clear();\n        var gestureInfo = gestureMgr.recognize(event, this.findHover(event.zrX, event.zrY, null).target, this.proxy.dom);\n        stage === 'end' && gestureMgr.clear();\n        if (gestureInfo) {\n            var type = gestureInfo.type;\n            event.gestureEvent = type;\n            var res = new HoveredResult();\n            res.target = gestureInfo.target;\n            this.dispatchToElement(res, type, gestureInfo.event);\n        }\n    };\n    return Handler;\n}(Eventful));\nutil.each(['click', 'mousedown', 'mouseup', 'mousewheel', 'dblclick', 'contextmenu'], function (name) {\n    Handler.prototype[name] = function (event) {\n        var x = event.zrX;\n        var y = event.zrY;\n        var isOutside = isOutsideBoundary(this, x, y);\n        var hovered;\n        var hoveredTarget;\n        if (name !== 'mouseup' || !isOutside) {\n            hovered = this.findHover(x, y);\n            hoveredTarget = hovered.target;\n        }\n        if (name === 'mousedown') {\n            this._downEl = hoveredTarget;\n            this._downPoint = [event.zrX, event.zrY];\n            this._upEl = hoveredTarget;\n        }\n        else if (name === 'mouseup') {\n            this._upEl = hoveredTarget;\n        }\n        else if (name === 'click') {\n            if (this._downEl !== this._upEl\n                || !this._downPoint\n                || vec2.dist(this._downPoint, [event.zrX, event.zrY]) > 4) {\n                return;\n            }\n            this._downPoint = null;\n        }\n        this.dispatchToElement(hovered, name, event);\n    };\n});\nfunction isHover(displayable, x, y) {\n    if (displayable[displayable.rectHover ? 'rectContain' : 'contain'](x, y)) {\n        var el = displayable;\n        var isSilent = void 0;\n        var ignoreClip = false;\n        while (el) {\n            if (el.ignoreClip) {\n                ignoreClip = true;\n            }\n            if (!ignoreClip) {\n                var clipPath = el.getClipPath();\n                if (clipPath && !clipPath.contain(x, y)) {\n                    return false;\n                }\n            }\n            if (el.silent) {\n                isSilent = true;\n            }\n            var hostEl = el.__hostTarget;\n            el = hostEl ? hostEl : el.parent;\n        }\n        return isSilent ? SILENT : true;\n    }\n    return false;\n}\nfunction setHoverTarget(list, out, x, y, exclude) {\n    for (var i = list.length - 1; i >= 0; i--) {\n        var el = list[i];\n        var hoverCheckResult = void 0;\n        if (el !== exclude\n            && !el.ignore\n            && (hoverCheckResult = isHover(el, x, y))) {\n            !out.topTarget && (out.topTarget = el);\n            if (hoverCheckResult !== SILENT) {\n                out.target = el;\n                break;\n            }\n        }\n    }\n}\nfunction isOutsideBoundary(handlerInstance, x, y) {\n    var painter = handlerInstance.painter;\n    return x < 0 || x > painter.getWidth() || y < 0 || y > painter.getHeight();\n}\nexport default Handler;\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,IAAI,MAAM,gBAAgB;AACtC,OAAO,KAAKC,IAAI,MAAM,kBAAkB;AACxC,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,QAAQ,MAAM,oBAAoB;AACzC,OAAO,KAAKC,SAAS,MAAM,iBAAiB;AAC5C,SAASC,UAAU,QAAQ,sBAAsB;AACjD,OAAOC,YAAY,MAAM,wBAAwB;AACjD,IAAIC,MAAM,GAAG,QAAQ;AACrB,SAASC,eAAeA,CAACC,OAAO,EAAEC,UAAU,EAAEC,KAAK,EAAE;EACjD,OAAO;IACHC,IAAI,EAAEH,OAAO;IACbE,KAAK,EAAEA,KAAK;IACZE,MAAM,EAAEH,UAAU,CAACG,MAAM;IACzBC,SAAS,EAAEJ,UAAU,CAACI,SAAS;IAC/BC,YAAY,EAAE,KAAK;IACnBC,OAAO,EAAEL,KAAK,CAACM,GAAG;IAClBC,OAAO,EAAEP,KAAK,CAACQ,GAAG;IAClBC,YAAY,EAAET,KAAK,CAACS,YAAY;IAChCC,MAAM,EAAEV,KAAK,CAACU,MAAM;IACpBC,MAAM,EAAEX,KAAK,CAACW,MAAM;IACpBC,UAAU,EAAEZ,KAAK,CAACY,UAAU;IAC5BC,UAAU,EAAEb,KAAK,CAACc,OAAO;IACzBC,SAAS,EAAEf,KAAK,CAACe,SAAS;IAC1BC,KAAK,EAAEhB,KAAK,CAACgB,KAAK;IAClBC,IAAI,EAAEC;EACV,CAAC;AACL;AACA,SAASA,SAASA,CAAA,EAAG;EACjBzB,SAAS,CAACwB,IAAI,CAAC,IAAI,CAACjB,KAAK,CAAC;AAC9B;AACA,IAAImB,UAAU,GAAI,UAAUC,MAAM,EAAE;EAChChC,SAAS,CAAC+B,UAAU,EAAEC,MAAM,CAAC;EAC7B,SAASD,UAAUA,CAAA,EAAG;IAClB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,OAAO,GAAG,IAAI;IACpB,OAAOH,KAAK;EAChB;EACAF,UAAU,CAACM,SAAS,CAACC,OAAO,GAAG,YAAY,CAAE,CAAC;EAC9CP,UAAU,CAACM,SAAS,CAACE,SAAS,GAAG,YAAY,CAAE,CAAC;EAChD,OAAOR,UAAU;AACrB,CAAC,CAAC3B,QAAQ,CAAE;AACZ,IAAIoC,aAAa,GAAI,YAAY;EAC7B,SAASA,aAAaA,CAACC,CAAC,EAAEC,CAAC,EAAE;IACzB,IAAI,CAACD,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,CAAC,GAAGA,CAAC;EACd;EACA,OAAOF,aAAa;AACxB,CAAC,CAAC,CAAE;AACJ,IAAIG,YAAY,GAAG,CACf,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,UAAU,EAC7C,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,aAAa,CACrD;AACD,IAAIC,OAAO,GAAG,IAAIrC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC1C,IAAIsC,OAAO,GAAI,UAAUb,MAAM,EAAE;EAC7BhC,SAAS,CAAC6C,OAAO,EAAEb,MAAM,CAAC;EAC1B,SAASa,OAAOA,CAACC,OAAO,EAAEC,OAAO,EAAEC,KAAK,EAAEC,WAAW,EAAEC,WAAW,EAAE;IAChE,IAAIjB,KAAK,GAAGD,MAAM,CAACmB,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACrClB,KAAK,CAACmB,QAAQ,GAAG,IAAIZ,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC;IACxCP,KAAK,CAACa,OAAO,GAAGA,OAAO;IACvBb,KAAK,CAACc,OAAO,GAAGA,OAAO;IACvBd,KAAK,CAACgB,WAAW,GAAGA,WAAW;IAC/BhB,KAAK,CAACoB,YAAY,GAAGH,WAAW;IAChCF,KAAK,GAAGA,KAAK,IAAI,IAAIjB,UAAU,CAAC,CAAC;IACjCE,KAAK,CAACe,KAAK,GAAG,IAAI;IAClBf,KAAK,CAACqB,eAAe,CAACN,KAAK,CAAC;IAC5Bf,KAAK,CAACsB,YAAY,GAAG,IAAIpD,SAAS,CAAC8B,KAAK,CAAC;IACzC,OAAOA,KAAK;EAChB;EACAY,OAAO,CAACR,SAAS,CAACiB,eAAe,GAAG,UAAUN,KAAK,EAAE;IACjD,IAAI,IAAI,CAACA,KAAK,EAAE;MACZ,IAAI,CAACA,KAAK,CAACV,OAAO,CAAC,CAAC;IACxB;IACA,IAAIU,KAAK,EAAE;MACP/C,IAAI,CAACuD,IAAI,CAACb,YAAY,EAAE,UAAUc,IAAI,EAAE;QACpCT,KAAK,CAACU,EAAE,IAAIV,KAAK,CAACU,EAAE,CAACD,IAAI,EAAE,IAAI,CAACA,IAAI,CAAC,EAAE,IAAI,CAAC;MAChD,CAAC,EAAE,IAAI,CAAC;MACRT,KAAK,CAACZ,OAAO,GAAG,IAAI;IACxB;IACA,IAAI,CAACY,KAAK,GAAGA,KAAK;EACtB,CAAC;EACDH,OAAO,CAACR,SAAS,CAACsB,SAAS,GAAG,UAAU/C,KAAK,EAAE;IAC3C,IAAI6B,CAAC,GAAG7B,KAAK,CAACM,GAAG;IACjB,IAAIwB,CAAC,GAAG9B,KAAK,CAACQ,GAAG;IACjB,IAAIwC,SAAS,GAAGC,iBAAiB,CAAC,IAAI,EAAEpB,CAAC,EAAEC,CAAC,CAAC;IAC7C,IAAIoB,WAAW,GAAG,IAAI,CAACV,QAAQ;IAC/B,IAAIW,iBAAiB,GAAGD,WAAW,CAAChD,MAAM;IAC1C,IAAIiD,iBAAiB,IAAI,CAACA,iBAAiB,CAACC,IAAI,EAAE;MAC9CF,WAAW,GAAG,IAAI,CAACG,SAAS,CAACH,WAAW,CAACrB,CAAC,EAAEqB,WAAW,CAACpB,CAAC,CAAC;MAC1DqB,iBAAiB,GAAGD,WAAW,CAAChD,MAAM;IAC1C;IACA,IAAIoD,OAAO,GAAG,IAAI,CAACd,QAAQ,GAAGQ,SAAS,GAAG,IAAIpB,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC,GAAG,IAAI,CAACuB,SAAS,CAACxB,CAAC,EAAEC,CAAC,CAAC;IACxF,IAAIyB,aAAa,GAAGD,OAAO,CAACpD,MAAM;IAClC,IAAIkC,KAAK,GAAG,IAAI,CAACA,KAAK;IACtBA,KAAK,CAACT,SAAS,IAAIS,KAAK,CAACT,SAAS,CAAC4B,aAAa,GAAGA,aAAa,CAACC,MAAM,GAAG,SAAS,CAAC;IACpF,IAAIL,iBAAiB,IAAII,aAAa,KAAKJ,iBAAiB,EAAE;MAC1D,IAAI,CAACM,iBAAiB,CAACP,WAAW,EAAE,UAAU,EAAElD,KAAK,CAAC;IAC1D;IACA,IAAI,CAACyD,iBAAiB,CAACH,OAAO,EAAE,WAAW,EAAEtD,KAAK,CAAC;IACnD,IAAIuD,aAAa,IAAIA,aAAa,KAAKJ,iBAAiB,EAAE;MACtD,IAAI,CAACM,iBAAiB,CAACH,OAAO,EAAE,WAAW,EAAEtD,KAAK,CAAC;IACvD;EACJ,CAAC;EACDiC,OAAO,CAACR,SAAS,CAACiC,QAAQ,GAAG,UAAU1D,KAAK,EAAE;IAC1C,IAAI2D,YAAY,GAAG3D,KAAK,CAAC4D,cAAc;IACvC,IAAID,YAAY,KAAK,gBAAgB,EAAE;MACnC,IAAI,CAACF,iBAAiB,CAAC,IAAI,CAACjB,QAAQ,EAAE,UAAU,EAAExC,KAAK,CAAC;IAC5D;IACA,IAAI2D,YAAY,KAAK,cAAc,EAAE;MACjC,IAAI,CAACE,OAAO,CAAC,WAAW,EAAE;QAAE5D,IAAI,EAAE,WAAW;QAAED,KAAK,EAAEA;MAAM,CAAC,CAAC;IAClE;EACJ,CAAC;EACDiC,OAAO,CAACR,SAAS,CAACqC,MAAM,GAAG,YAAY;IACnC,IAAI,CAACtB,QAAQ,GAAG,IAAIZ,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC;EAC3C,CAAC;EACDK,OAAO,CAACR,SAAS,CAACsC,QAAQ,GAAG,UAAUC,SAAS,EAAEC,SAAS,EAAE;IACzD,IAAIzC,OAAO,GAAG,IAAI,CAACwC,SAAS,CAAC;IAC7BxC,OAAO,IAAIA,OAAO,CAACe,IAAI,CAAC,IAAI,EAAE0B,SAAS,CAAC;EAC5C,CAAC;EACDhC,OAAO,CAACR,SAAS,CAACC,OAAO,GAAG,YAAY;IACpC,IAAI,CAACU,KAAK,CAACV,OAAO,CAAC,CAAC;IACpB,IAAI,CAACQ,OAAO,GAAG,IAAI;IACnB,IAAI,CAACE,KAAK,GAAG,IAAI;IACjB,IAAI,CAACD,OAAO,GAAG,IAAI;EACvB,CAAC;EACDF,OAAO,CAACR,SAAS,CAACyC,cAAc,GAAG,UAAUC,WAAW,EAAE;IACtD,IAAI/B,KAAK,GAAG,IAAI,CAACA,KAAK;IACtBA,KAAK,CAACT,SAAS,IAAIS,KAAK,CAACT,SAAS,CAACwC,WAAW,CAAC;EACnD,CAAC;EACDlC,OAAO,CAACR,SAAS,CAACgC,iBAAiB,GAAG,UAAU1D,UAAU,EAAEiE,SAAS,EAAEhE,KAAK,EAAE;IAC1ED,UAAU,GAAGA,UAAU,IAAI,CAAC,CAAC;IAC7B,IAAIqE,EAAE,GAAGrE,UAAU,CAACG,MAAM;IAC1B,IAAIkE,EAAE,IAAIA,EAAE,CAACC,MAAM,EAAE;MACjB;IACJ;IACA,IAAIC,QAAQ,GAAI,IAAI,GAAGN,SAAU;IACjC,IAAIO,WAAW,GAAG1E,eAAe,CAACmE,SAAS,EAAEjE,UAAU,EAAEC,KAAK,CAAC;IAC/D,OAAOoE,EAAE,EAAE;MACPA,EAAE,CAACE,QAAQ,CAAC,KACJC,WAAW,CAACnE,YAAY,GAAG,CAAC,CAACgE,EAAE,CAACE,QAAQ,CAAC,CAAC/B,IAAI,CAAC6B,EAAE,EAAEG,WAAW,CAAC,CAAC;MACxEH,EAAE,CAACP,OAAO,CAACG,SAAS,EAAEO,WAAW,CAAC;MAClCH,EAAE,GAAGA,EAAE,CAACI,YAAY,GAAGJ,EAAE,CAACI,YAAY,GAAGJ,EAAE,CAACK,MAAM;MAClD,IAAIF,WAAW,CAACnE,YAAY,EAAE;QAC1B;MACJ;IACJ;IACA,IAAI,CAACmE,WAAW,CAACnE,YAAY,EAAE;MAC3B,IAAI,CAACyD,OAAO,CAACG,SAAS,EAAEO,WAAW,CAAC;MACpC,IAAI,IAAI,CAACpC,OAAO,IAAI,IAAI,CAACA,OAAO,CAACuC,cAAc,EAAE;QAC7C,IAAI,CAACvC,OAAO,CAACuC,cAAc,CAAC,UAAUC,KAAK,EAAE;UACzC,IAAI,OAAQA,KAAK,CAACL,QAAQ,CAAE,KAAK,UAAU,EAAE;YACzCK,KAAK,CAACL,QAAQ,CAAC,CAAC/B,IAAI,CAACoC,KAAK,EAAEJ,WAAW,CAAC;UAC5C;UACA,IAAII,KAAK,CAACd,OAAO,EAAE;YACfc,KAAK,CAACd,OAAO,CAACG,SAAS,EAAEO,WAAW,CAAC;UACzC;QACJ,CAAC,CAAC;MACN;IACJ;EACJ,CAAC;EACDtC,OAAO,CAACR,SAAS,CAAC4B,SAAS,GAAG,UAAUxB,CAAC,EAAEC,CAAC,EAAE8C,OAAO,EAAE;IACnD,IAAIC,IAAI,GAAG,IAAI,CAAC3C,OAAO,CAAC4C,cAAc,CAAC,CAAC;IACxC,IAAIC,GAAG,GAAG,IAAInD,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACjCkD,cAAc,CAACH,IAAI,EAAEE,GAAG,EAAElD,CAAC,EAAEC,CAAC,EAAE8C,OAAO,CAAC;IACxC,IAAI,IAAI,CAACnC,YAAY,IAAI,CAACsC,GAAG,CAAC7E,MAAM,EAAE;MAClC,IAAI+E,UAAU,GAAG,EAAE;MACnB,IAAI3C,WAAW,GAAG,IAAI,CAACG,YAAY;MACnC,IAAIyC,cAAc,GAAG5C,WAAW,GAAG,CAAC;MACpC,IAAI6C,WAAW,GAAG,IAAIxF,YAAY,CAACkC,CAAC,GAAGqD,cAAc,EAAEpD,CAAC,GAAGoD,cAAc,EAAE5C,WAAW,EAAEA,WAAW,CAAC;MACpG,KAAK,IAAI8C,CAAC,GAAGP,IAAI,CAACQ,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QACvC,IAAIhB,EAAE,GAAGS,IAAI,CAACO,CAAC,CAAC;QAChB,IAAIhB,EAAE,KAAKQ,OAAO,IACX,CAACR,EAAE,CAACkB,MAAM,IACV,CAAClB,EAAE,CAACmB,mBAAmB,KACtB,CAACnB,EAAE,CAACK,MAAM,IAAI,CAACL,EAAE,CAACK,MAAM,CAACc,mBAAmB,CAAC,EAAE;UACnDvD,OAAO,CAACwD,IAAI,CAACpB,EAAE,CAACqB,eAAe,CAAC,CAAC,CAAC;UAClC,IAAIrB,EAAE,CAACsB,SAAS,EAAE;YACd1D,OAAO,CAAC2D,cAAc,CAACvB,EAAE,CAACsB,SAAS,CAAC;UACxC;UACA,IAAI1D,OAAO,CAAC4D,SAAS,CAACT,WAAW,CAAC,EAAE;YAChCF,UAAU,CAACY,IAAI,CAACzB,EAAE,CAAC;UACvB;QACJ;MACJ;MACA,IAAIa,UAAU,CAACI,MAAM,EAAE;QACnB,IAAIS,KAAK,GAAG,CAAC;QACb,IAAIC,SAAS,GAAGC,IAAI,CAACC,EAAE,GAAG,EAAE;QAC5B,IAAIC,GAAG,GAAGF,IAAI,CAACC,EAAE,GAAG,CAAC;QACrB,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjB,cAAc,EAAEiB,CAAC,IAAIL,KAAK,EAAE;UAC5C,KAAK,IAAIM,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGF,GAAG,EAAEE,KAAK,IAAIL,SAAS,EAAE;YACjD,IAAIM,EAAE,GAAGxE,CAAC,GAAGsE,CAAC,GAAGH,IAAI,CAACM,GAAG,CAACF,KAAK,CAAC;YAChC,IAAIG,EAAE,GAAGzE,CAAC,GAAGqE,CAAC,GAAGH,IAAI,CAACQ,GAAG,CAACJ,KAAK,CAAC;YAChCpB,cAAc,CAACC,UAAU,EAAEF,GAAG,EAAEsB,EAAE,EAAEE,EAAE,EAAE3B,OAAO,CAAC;YAChD,IAAIG,GAAG,CAAC7E,MAAM,EAAE;cACZ,OAAO6E,GAAG;YACd;UACJ;QACJ;MACJ;IACJ;IACA,OAAOA,GAAG;EACd,CAAC;EACD9C,OAAO,CAACR,SAAS,CAACgF,cAAc,GAAG,UAAUzG,KAAK,EAAE0G,KAAK,EAAE;IACvD,IAAI,CAAC,IAAI,CAACC,WAAW,EAAE;MACnB,IAAI,CAACA,WAAW,GAAG,IAAIjH,UAAU,CAAC,CAAC;IACvC;IACA,IAAIkH,UAAU,GAAG,IAAI,CAACD,WAAW;IACjCD,KAAK,KAAK,OAAO,IAAIE,UAAU,CAACC,KAAK,CAAC,CAAC;IACvC,IAAIC,WAAW,GAAGF,UAAU,CAACG,SAAS,CAAC/G,KAAK,EAAE,IAAI,CAACqD,SAAS,CAACrD,KAAK,CAACM,GAAG,EAAEN,KAAK,CAACQ,GAAG,EAAE,IAAI,CAAC,CAACN,MAAM,EAAE,IAAI,CAACkC,KAAK,CAAC4E,GAAG,CAAC;IAChHN,KAAK,KAAK,KAAK,IAAIE,UAAU,CAACC,KAAK,CAAC,CAAC;IACrC,IAAIC,WAAW,EAAE;MACb,IAAI7G,IAAI,GAAG6G,WAAW,CAAC7G,IAAI;MAC3BD,KAAK,CAACS,YAAY,GAAGR,IAAI;MACzB,IAAIgH,GAAG,GAAG,IAAIrF,aAAa,CAAC,CAAC;MAC7BqF,GAAG,CAAC/G,MAAM,GAAG4G,WAAW,CAAC5G,MAAM;MAC/B,IAAI,CAACuD,iBAAiB,CAACwD,GAAG,EAAEhH,IAAI,EAAE6G,WAAW,CAAC9G,KAAK,CAAC;IACxD;EACJ,CAAC;EACD,OAAOiC,OAAO;AAClB,CAAC,CAACzC,QAAQ,CAAE;AACZH,IAAI,CAACuD,IAAI,CAAC,CAAC,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,aAAa,CAAC,EAAE,UAAUC,IAAI,EAAE;EAClGZ,OAAO,CAACR,SAAS,CAACoB,IAAI,CAAC,GAAG,UAAU7C,KAAK,EAAE;IACvC,IAAI6B,CAAC,GAAG7B,KAAK,CAACM,GAAG;IACjB,IAAIwB,CAAC,GAAG9B,KAAK,CAACQ,GAAG;IACjB,IAAIwC,SAAS,GAAGC,iBAAiB,CAAC,IAAI,EAAEpB,CAAC,EAAEC,CAAC,CAAC;IAC7C,IAAIwB,OAAO;IACX,IAAIC,aAAa;IACjB,IAAIV,IAAI,KAAK,SAAS,IAAI,CAACG,SAAS,EAAE;MAClCM,OAAO,GAAG,IAAI,CAACD,SAAS,CAACxB,CAAC,EAAEC,CAAC,CAAC;MAC9ByB,aAAa,GAAGD,OAAO,CAACpD,MAAM;IAClC;IACA,IAAI2C,IAAI,KAAK,WAAW,EAAE;MACtB,IAAI,CAACqE,OAAO,GAAG3D,aAAa;MAC5B,IAAI,CAAC4D,UAAU,GAAG,CAACnH,KAAK,CAACM,GAAG,EAAEN,KAAK,CAACQ,GAAG,CAAC;MACxC,IAAI,CAAC4G,KAAK,GAAG7D,aAAa;IAC9B,CAAC,MACI,IAAIV,IAAI,KAAK,SAAS,EAAE;MACzB,IAAI,CAACuE,KAAK,GAAG7D,aAAa;IAC9B,CAAC,MACI,IAAIV,IAAI,KAAK,OAAO,EAAE;MACvB,IAAI,IAAI,CAACqE,OAAO,KAAK,IAAI,CAACE,KAAK,IACxB,CAAC,IAAI,CAACD,UAAU,IAChB7H,IAAI,CAAC+H,IAAI,CAAC,IAAI,CAACF,UAAU,EAAE,CAACnH,KAAK,CAACM,GAAG,EAAEN,KAAK,CAACQ,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE;QAC3D;MACJ;MACA,IAAI,CAAC2G,UAAU,GAAG,IAAI;IAC1B;IACA,IAAI,CAAC1D,iBAAiB,CAACH,OAAO,EAAET,IAAI,EAAE7C,KAAK,CAAC;EAChD,CAAC;AACL,CAAC,CAAC;AACF,SAASsH,OAAOA,CAACC,WAAW,EAAE1F,CAAC,EAAEC,CAAC,EAAE;EAChC,IAAIyF,WAAW,CAACA,WAAW,CAACC,SAAS,GAAG,aAAa,GAAG,SAAS,CAAC,CAAC3F,CAAC,EAAEC,CAAC,CAAC,EAAE;IACtE,IAAIsC,EAAE,GAAGmD,WAAW;IACpB,IAAIE,QAAQ,GAAG,KAAK,CAAC;IACrB,IAAIC,UAAU,GAAG,KAAK;IACtB,OAAOtD,EAAE,EAAE;MACP,IAAIA,EAAE,CAACsD,UAAU,EAAE;QACfA,UAAU,GAAG,IAAI;MACrB;MACA,IAAI,CAACA,UAAU,EAAE;QACb,IAAIC,QAAQ,GAAGvD,EAAE,CAACwD,WAAW,CAAC,CAAC;QAC/B,IAAID,QAAQ,IAAI,CAACA,QAAQ,CAACE,OAAO,CAAChG,CAAC,EAAEC,CAAC,CAAC,EAAE;UACrC,OAAO,KAAK;QAChB;MACJ;MACA,IAAIsC,EAAE,CAACC,MAAM,EAAE;QACXoD,QAAQ,GAAG,IAAI;MACnB;MACA,IAAIK,MAAM,GAAG1D,EAAE,CAACI,YAAY;MAC5BJ,EAAE,GAAG0D,MAAM,GAAGA,MAAM,GAAG1D,EAAE,CAACK,MAAM;IACpC;IACA,OAAOgD,QAAQ,GAAG7H,MAAM,GAAG,IAAI;EACnC;EACA,OAAO,KAAK;AAChB;AACA,SAASoF,cAAcA,CAACH,IAAI,EAAEE,GAAG,EAAElD,CAAC,EAAEC,CAAC,EAAE8C,OAAO,EAAE;EAC9C,KAAK,IAAIQ,CAAC,GAAGP,IAAI,CAACQ,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACvC,IAAIhB,EAAE,GAAGS,IAAI,CAACO,CAAC,CAAC;IAChB,IAAI2C,gBAAgB,GAAG,KAAK,CAAC;IAC7B,IAAI3D,EAAE,KAAKQ,OAAO,IACX,CAACR,EAAE,CAACkB,MAAM,KACTyC,gBAAgB,GAAGT,OAAO,CAAClD,EAAE,EAAEvC,CAAC,EAAEC,CAAC,CAAC,CAAC,EAAE;MAC3C,CAACiD,GAAG,CAAC5E,SAAS,KAAK4E,GAAG,CAAC5E,SAAS,GAAGiE,EAAE,CAAC;MACtC,IAAI2D,gBAAgB,KAAKnI,MAAM,EAAE;QAC7BmF,GAAG,CAAC7E,MAAM,GAAGkE,EAAE;QACf;MACJ;IACJ;EACJ;AACJ;AACA,SAASnB,iBAAiBA,CAAC+E,eAAe,EAAEnG,CAAC,EAAEC,CAAC,EAAE;EAC9C,IAAIK,OAAO,GAAG6F,eAAe,CAAC7F,OAAO;EACrC,OAAON,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAGM,OAAO,CAAC8F,QAAQ,CAAC,CAAC,IAAInG,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAGK,OAAO,CAAC+F,SAAS,CAAC,CAAC;AAC9E;AACA,eAAejG,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}