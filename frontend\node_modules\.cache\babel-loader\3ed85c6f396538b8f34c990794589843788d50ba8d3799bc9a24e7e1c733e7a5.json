{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/**\r\n * Tree data structure\r\n */\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport linkSeriesData from './helper/linkSeriesData.js';\nimport SeriesData from './SeriesData.js';\nimport prepareSeriesDataSchema from './helper/createDimensions.js';\nimport { convertOptionIdName } from '../util/model.js';\nvar TreeNode = /** @class */function () {\n  function TreeNode(name, hostTree) {\n    this.depth = 0;\n    this.height = 0;\n    /**\r\n     * Reference to list item.\r\n     * Do not persistent dataIndex outside,\r\n     * besause it may be changed by list.\r\n     * If dataIndex -1,\r\n     * this node is logical deleted (filtered) in list.\r\n     */\n    this.dataIndex = -1;\n    this.children = [];\n    this.viewChildren = [];\n    this.isExpand = false;\n    this.name = name || '';\n    this.hostTree = hostTree;\n  }\n  /**\r\n   * The node is removed.\r\n   */\n  TreeNode.prototype.isRemoved = function () {\n    return this.dataIndex < 0;\n  };\n  TreeNode.prototype.eachNode = function (options, cb, context) {\n    if (zrUtil.isFunction(options)) {\n      context = cb;\n      cb = options;\n      options = null;\n    }\n    options = options || {};\n    if (zrUtil.isString(options)) {\n      options = {\n        order: options\n      };\n    }\n    var order = options.order || 'preorder';\n    var children = this[options.attr || 'children'];\n    var suppressVisitSub;\n    order === 'preorder' && (suppressVisitSub = cb.call(context, this));\n    for (var i = 0; !suppressVisitSub && i < children.length; i++) {\n      children[i].eachNode(options, cb, context);\n    }\n    order === 'postorder' && cb.call(context, this);\n  };\n  /**\r\n   * Update depth and height of this subtree.\r\n   */\n  TreeNode.prototype.updateDepthAndHeight = function (depth) {\n    var height = 0;\n    this.depth = depth;\n    for (var i = 0; i < this.children.length; i++) {\n      var child = this.children[i];\n      child.updateDepthAndHeight(depth + 1);\n      if (child.height > height) {\n        height = child.height;\n      }\n    }\n    this.height = height + 1;\n  };\n  TreeNode.prototype.getNodeById = function (id) {\n    if (this.getId() === id) {\n      return this;\n    }\n    for (var i = 0, children = this.children, len = children.length; i < len; i++) {\n      var res = children[i].getNodeById(id);\n      if (res) {\n        return res;\n      }\n    }\n  };\n  TreeNode.prototype.contains = function (node) {\n    if (node === this) {\n      return true;\n    }\n    for (var i = 0, children = this.children, len = children.length; i < len; i++) {\n      var res = children[i].contains(node);\n      if (res) {\n        return res;\n      }\n    }\n  };\n  /**\r\n   * @param includeSelf Default false.\r\n   * @return order: [root, child, grandchild, ...]\r\n   */\n  TreeNode.prototype.getAncestors = function (includeSelf) {\n    var ancestors = [];\n    var node = includeSelf ? this : this.parentNode;\n    while (node) {\n      ancestors.push(node);\n      node = node.parentNode;\n    }\n    ancestors.reverse();\n    return ancestors;\n  };\n  TreeNode.prototype.getAncestorsIndices = function () {\n    var indices = [];\n    var currNode = this;\n    while (currNode) {\n      indices.push(currNode.dataIndex);\n      currNode = currNode.parentNode;\n    }\n    indices.reverse();\n    return indices;\n  };\n  TreeNode.prototype.getDescendantIndices = function () {\n    var indices = [];\n    this.eachNode(function (childNode) {\n      indices.push(childNode.dataIndex);\n    });\n    return indices;\n  };\n  TreeNode.prototype.getValue = function (dimension) {\n    var data = this.hostTree.data;\n    return data.getStore().get(data.getDimensionIndex(dimension || 'value'), this.dataIndex);\n  };\n  TreeNode.prototype.setLayout = function (layout, merge) {\n    this.dataIndex >= 0 && this.hostTree.data.setItemLayout(this.dataIndex, layout, merge);\n  };\n  /**\r\n   * @return {Object} layout\r\n   */\n  TreeNode.prototype.getLayout = function () {\n    return this.hostTree.data.getItemLayout(this.dataIndex);\n  };\n  // @depcrecated\n  // getModel<T = unknown, S extends keyof T = keyof T>(path: S): Model<T[S]>\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  TreeNode.prototype.getModel = function (path) {\n    if (this.dataIndex < 0) {\n      return;\n    }\n    var hostTree = this.hostTree;\n    var itemModel = hostTree.data.getItemModel(this.dataIndex);\n    return itemModel.getModel(path);\n  };\n  // TODO: TYPE More specific model\n  TreeNode.prototype.getLevelModel = function () {\n    return (this.hostTree.levelModels || [])[this.depth];\n  };\n  TreeNode.prototype.setVisual = function (key, value) {\n    this.dataIndex >= 0 && this.hostTree.data.setItemVisual(this.dataIndex, key, value);\n  };\n  /**\r\n   * Get item visual\r\n   * FIXME: make return type better\r\n   */\n  TreeNode.prototype.getVisual = function (key) {\n    return this.hostTree.data.getItemVisual(this.dataIndex, key);\n  };\n  TreeNode.prototype.getRawIndex = function () {\n    return this.hostTree.data.getRawIndex(this.dataIndex);\n  };\n  TreeNode.prototype.getId = function () {\n    return this.hostTree.data.getId(this.dataIndex);\n  };\n  /**\r\n   * index in parent's children\r\n   */\n  TreeNode.prototype.getChildIndex = function () {\n    if (this.parentNode) {\n      var children = this.parentNode.children;\n      for (var i = 0; i < children.length; ++i) {\n        if (children[i] === this) {\n          return i;\n        }\n      }\n      return -1;\n    }\n    return -1;\n  };\n  /**\r\n   * if this is an ancestor of another node\r\n   *\r\n   * @param node another node\r\n   * @return if is ancestor\r\n   */\n  TreeNode.prototype.isAncestorOf = function (node) {\n    var parent = node.parentNode;\n    while (parent) {\n      if (parent === this) {\n        return true;\n      }\n      parent = parent.parentNode;\n    }\n    return false;\n  };\n  /**\r\n   * if this is an descendant of another node\r\n   *\r\n   * @param node another node\r\n   * @return if is descendant\r\n   */\n  TreeNode.prototype.isDescendantOf = function (node) {\n    return node !== this && node.isAncestorOf(this);\n  };\n  return TreeNode;\n}();\nexport { TreeNode };\n;\nvar Tree = /** @class */function () {\n  function Tree(hostModel) {\n    this.type = 'tree';\n    this._nodes = [];\n    this.hostModel = hostModel;\n  }\n  Tree.prototype.eachNode = function (options, cb, context) {\n    this.root.eachNode(options, cb, context);\n  };\n  Tree.prototype.getNodeByDataIndex = function (dataIndex) {\n    var rawIndex = this.data.getRawIndex(dataIndex);\n    return this._nodes[rawIndex];\n  };\n  Tree.prototype.getNodeById = function (name) {\n    return this.root.getNodeById(name);\n  };\n  /**\r\n   * Update item available by list,\r\n   * when list has been performed options like 'filterSelf' or 'map'.\r\n   */\n  Tree.prototype.update = function () {\n    var data = this.data;\n    var nodes = this._nodes;\n    for (var i = 0, len = nodes.length; i < len; i++) {\n      nodes[i].dataIndex = -1;\n    }\n    for (var i = 0, len = data.count(); i < len; i++) {\n      nodes[data.getRawIndex(i)].dataIndex = i;\n    }\n  };\n  /**\r\n   * Clear all layouts\r\n   */\n  Tree.prototype.clearLayouts = function () {\n    this.data.clearItemLayouts();\n  };\n  /**\r\n   * data node format:\r\n   * {\r\n   *     name: ...\r\n   *     value: ...\r\n   *     children: [\r\n   *         {\r\n   *             name: ...\r\n   *             value: ...\r\n   *             children: ...\r\n   *         },\r\n   *         ...\r\n   *     ]\r\n   * }\r\n   */\n  Tree.createTree = function (dataRoot, hostModel, beforeLink) {\n    var tree = new Tree(hostModel);\n    var listData = [];\n    var dimMax = 1;\n    buildHierarchy(dataRoot);\n    function buildHierarchy(dataNode, parentNode) {\n      var value = dataNode.value;\n      dimMax = Math.max(dimMax, zrUtil.isArray(value) ? value.length : 1);\n      listData.push(dataNode);\n      var node = new TreeNode(convertOptionIdName(dataNode.name, ''), tree);\n      parentNode ? addChild(node, parentNode) : tree.root = node;\n      tree._nodes.push(node);\n      var children = dataNode.children;\n      if (children) {\n        for (var i = 0; i < children.length; i++) {\n          buildHierarchy(children[i], node);\n        }\n      }\n    }\n    tree.root.updateDepthAndHeight(0);\n    var dimensions = prepareSeriesDataSchema(listData, {\n      coordDimensions: ['value'],\n      dimensionsCount: dimMax\n    }).dimensions;\n    var list = new SeriesData(dimensions, hostModel);\n    list.initData(listData);\n    beforeLink && beforeLink(list);\n    linkSeriesData({\n      mainData: list,\n      struct: tree,\n      structAttr: 'tree'\n    });\n    tree.update();\n    return tree;\n  };\n  return Tree;\n}();\n/**\r\n * It is needed to consider the mess of 'list', 'hostModel' when creating a TreeNote,\r\n * so this function is not ready and not necessary to be public.\r\n */\nfunction addChild(child, node) {\n  var children = node.children;\n  if (child.parentNode === node) {\n    return;\n  }\n  children.push(child);\n  child.parentNode = node;\n}\nexport default Tree;", "map": {"version": 3, "names": ["zrUtil", "linkSeriesData", "SeriesData", "prepareSeriesDataSchema", "convertOptionIdName", "TreeNode", "name", "hostTree", "depth", "height", "dataIndex", "children", "viewChil<PERSON>n", "isExpand", "prototype", "isRemoved", "eachNode", "options", "cb", "context", "isFunction", "isString", "order", "attr", "suppressVisitSub", "call", "i", "length", "updateDepthAndHeight", "child", "getNodeById", "id", "getId", "len", "res", "contains", "node", "getAncestors", "includeSelf", "ancestors", "parentNode", "push", "reverse", "getAncestorsIndices", "indices", "currNode", "getDescendantIndices", "childNode", "getValue", "dimension", "data", "getStore", "get", "getDimensionIndex", "setLayout", "layout", "merge", "setItemLayout", "getLayout", "getItemLayout", "getModel", "path", "itemModel", "getItemModel", "getLevelModel", "levelModels", "setVisual", "key", "value", "setItemVisual", "getVisual", "getItemVisual", "getRawIndex", "getChildIndex", "isAncestorOf", "parent", "isDescendantOf", "Tree", "hostModel", "type", "_nodes", "root", "getNodeByDataIndex", "rawIndex", "update", "nodes", "count", "clearLayouts", "clearItemLayouts", "createTree", "dataRoot", "beforeLink", "tree", "listData", "dimMax", "buildHierarchy", "dataNode", "Math", "max", "isArray", "<PERSON><PERSON><PERSON><PERSON>", "dimensions", "coordDimensions", "dimensionsCount", "list", "initData", "mainData", "struct", "structAttr"], "sources": ["E:/AI/SmartCV/node_modules/echarts/lib/data/Tree.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/**\r\n * Tree data structure\r\n */\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport linkSeriesData from './helper/linkSeriesData.js';\nimport SeriesData from './SeriesData.js';\nimport prepareSeriesDataSchema from './helper/createDimensions.js';\nimport { convertOptionIdName } from '../util/model.js';\nvar TreeNode = /** @class */function () {\n  function TreeNode(name, hostTree) {\n    this.depth = 0;\n    this.height = 0;\n    /**\r\n     * Reference to list item.\r\n     * Do not persistent dataIndex outside,\r\n     * besause it may be changed by list.\r\n     * If dataIndex -1,\r\n     * this node is logical deleted (filtered) in list.\r\n     */\n    this.dataIndex = -1;\n    this.children = [];\n    this.viewChildren = [];\n    this.isExpand = false;\n    this.name = name || '';\n    this.hostTree = hostTree;\n  }\n  /**\r\n   * The node is removed.\r\n   */\n  TreeNode.prototype.isRemoved = function () {\n    return this.dataIndex < 0;\n  };\n  TreeNode.prototype.eachNode = function (options, cb, context) {\n    if (zrUtil.isFunction(options)) {\n      context = cb;\n      cb = options;\n      options = null;\n    }\n    options = options || {};\n    if (zrUtil.isString(options)) {\n      options = {\n        order: options\n      };\n    }\n    var order = options.order || 'preorder';\n    var children = this[options.attr || 'children'];\n    var suppressVisitSub;\n    order === 'preorder' && (suppressVisitSub = cb.call(context, this));\n    for (var i = 0; !suppressVisitSub && i < children.length; i++) {\n      children[i].eachNode(options, cb, context);\n    }\n    order === 'postorder' && cb.call(context, this);\n  };\n  /**\r\n   * Update depth and height of this subtree.\r\n   */\n  TreeNode.prototype.updateDepthAndHeight = function (depth) {\n    var height = 0;\n    this.depth = depth;\n    for (var i = 0; i < this.children.length; i++) {\n      var child = this.children[i];\n      child.updateDepthAndHeight(depth + 1);\n      if (child.height > height) {\n        height = child.height;\n      }\n    }\n    this.height = height + 1;\n  };\n  TreeNode.prototype.getNodeById = function (id) {\n    if (this.getId() === id) {\n      return this;\n    }\n    for (var i = 0, children = this.children, len = children.length; i < len; i++) {\n      var res = children[i].getNodeById(id);\n      if (res) {\n        return res;\n      }\n    }\n  };\n  TreeNode.prototype.contains = function (node) {\n    if (node === this) {\n      return true;\n    }\n    for (var i = 0, children = this.children, len = children.length; i < len; i++) {\n      var res = children[i].contains(node);\n      if (res) {\n        return res;\n      }\n    }\n  };\n  /**\r\n   * @param includeSelf Default false.\r\n   * @return order: [root, child, grandchild, ...]\r\n   */\n  TreeNode.prototype.getAncestors = function (includeSelf) {\n    var ancestors = [];\n    var node = includeSelf ? this : this.parentNode;\n    while (node) {\n      ancestors.push(node);\n      node = node.parentNode;\n    }\n    ancestors.reverse();\n    return ancestors;\n  };\n  TreeNode.prototype.getAncestorsIndices = function () {\n    var indices = [];\n    var currNode = this;\n    while (currNode) {\n      indices.push(currNode.dataIndex);\n      currNode = currNode.parentNode;\n    }\n    indices.reverse();\n    return indices;\n  };\n  TreeNode.prototype.getDescendantIndices = function () {\n    var indices = [];\n    this.eachNode(function (childNode) {\n      indices.push(childNode.dataIndex);\n    });\n    return indices;\n  };\n  TreeNode.prototype.getValue = function (dimension) {\n    var data = this.hostTree.data;\n    return data.getStore().get(data.getDimensionIndex(dimension || 'value'), this.dataIndex);\n  };\n  TreeNode.prototype.setLayout = function (layout, merge) {\n    this.dataIndex >= 0 && this.hostTree.data.setItemLayout(this.dataIndex, layout, merge);\n  };\n  /**\r\n   * @return {Object} layout\r\n   */\n  TreeNode.prototype.getLayout = function () {\n    return this.hostTree.data.getItemLayout(this.dataIndex);\n  };\n  // @depcrecated\n  // getModel<T = unknown, S extends keyof T = keyof T>(path: S): Model<T[S]>\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  TreeNode.prototype.getModel = function (path) {\n    if (this.dataIndex < 0) {\n      return;\n    }\n    var hostTree = this.hostTree;\n    var itemModel = hostTree.data.getItemModel(this.dataIndex);\n    return itemModel.getModel(path);\n  };\n  // TODO: TYPE More specific model\n  TreeNode.prototype.getLevelModel = function () {\n    return (this.hostTree.levelModels || [])[this.depth];\n  };\n  TreeNode.prototype.setVisual = function (key, value) {\n    this.dataIndex >= 0 && this.hostTree.data.setItemVisual(this.dataIndex, key, value);\n  };\n  /**\r\n   * Get item visual\r\n   * FIXME: make return type better\r\n   */\n  TreeNode.prototype.getVisual = function (key) {\n    return this.hostTree.data.getItemVisual(this.dataIndex, key);\n  };\n  TreeNode.prototype.getRawIndex = function () {\n    return this.hostTree.data.getRawIndex(this.dataIndex);\n  };\n  TreeNode.prototype.getId = function () {\n    return this.hostTree.data.getId(this.dataIndex);\n  };\n  /**\r\n   * index in parent's children\r\n   */\n  TreeNode.prototype.getChildIndex = function () {\n    if (this.parentNode) {\n      var children = this.parentNode.children;\n      for (var i = 0; i < children.length; ++i) {\n        if (children[i] === this) {\n          return i;\n        }\n      }\n      return -1;\n    }\n    return -1;\n  };\n  /**\r\n   * if this is an ancestor of another node\r\n   *\r\n   * @param node another node\r\n   * @return if is ancestor\r\n   */\n  TreeNode.prototype.isAncestorOf = function (node) {\n    var parent = node.parentNode;\n    while (parent) {\n      if (parent === this) {\n        return true;\n      }\n      parent = parent.parentNode;\n    }\n    return false;\n  };\n  /**\r\n   * if this is an descendant of another node\r\n   *\r\n   * @param node another node\r\n   * @return if is descendant\r\n   */\n  TreeNode.prototype.isDescendantOf = function (node) {\n    return node !== this && node.isAncestorOf(this);\n  };\n  return TreeNode;\n}();\nexport { TreeNode };\n;\nvar Tree = /** @class */function () {\n  function Tree(hostModel) {\n    this.type = 'tree';\n    this._nodes = [];\n    this.hostModel = hostModel;\n  }\n  Tree.prototype.eachNode = function (options, cb, context) {\n    this.root.eachNode(options, cb, context);\n  };\n  Tree.prototype.getNodeByDataIndex = function (dataIndex) {\n    var rawIndex = this.data.getRawIndex(dataIndex);\n    return this._nodes[rawIndex];\n  };\n  Tree.prototype.getNodeById = function (name) {\n    return this.root.getNodeById(name);\n  };\n  /**\r\n   * Update item available by list,\r\n   * when list has been performed options like 'filterSelf' or 'map'.\r\n   */\n  Tree.prototype.update = function () {\n    var data = this.data;\n    var nodes = this._nodes;\n    for (var i = 0, len = nodes.length; i < len; i++) {\n      nodes[i].dataIndex = -1;\n    }\n    for (var i = 0, len = data.count(); i < len; i++) {\n      nodes[data.getRawIndex(i)].dataIndex = i;\n    }\n  };\n  /**\r\n   * Clear all layouts\r\n   */\n  Tree.prototype.clearLayouts = function () {\n    this.data.clearItemLayouts();\n  };\n  /**\r\n   * data node format:\r\n   * {\r\n   *     name: ...\r\n   *     value: ...\r\n   *     children: [\r\n   *         {\r\n   *             name: ...\r\n   *             value: ...\r\n   *             children: ...\r\n   *         },\r\n   *         ...\r\n   *     ]\r\n   * }\r\n   */\n  Tree.createTree = function (dataRoot, hostModel, beforeLink) {\n    var tree = new Tree(hostModel);\n    var listData = [];\n    var dimMax = 1;\n    buildHierarchy(dataRoot);\n    function buildHierarchy(dataNode, parentNode) {\n      var value = dataNode.value;\n      dimMax = Math.max(dimMax, zrUtil.isArray(value) ? value.length : 1);\n      listData.push(dataNode);\n      var node = new TreeNode(convertOptionIdName(dataNode.name, ''), tree);\n      parentNode ? addChild(node, parentNode) : tree.root = node;\n      tree._nodes.push(node);\n      var children = dataNode.children;\n      if (children) {\n        for (var i = 0; i < children.length; i++) {\n          buildHierarchy(children[i], node);\n        }\n      }\n    }\n    tree.root.updateDepthAndHeight(0);\n    var dimensions = prepareSeriesDataSchema(listData, {\n      coordDimensions: ['value'],\n      dimensionsCount: dimMax\n    }).dimensions;\n    var list = new SeriesData(dimensions, hostModel);\n    list.initData(listData);\n    beforeLink && beforeLink(list);\n    linkSeriesData({\n      mainData: list,\n      struct: tree,\n      structAttr: 'tree'\n    });\n    tree.update();\n    return tree;\n  };\n  return Tree;\n}();\n/**\r\n * It is needed to consider the mess of 'list', 'hostModel' when creating a TreeNote,\r\n * so this function is not ready and not necessary to be public.\r\n */\nfunction addChild(child, node) {\n  var children = node.children;\n  if (child.parentNode === node) {\n    return;\n  }\n  children.push(child);\n  child.parentNode = node;\n}\nexport default Tree;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,OAAOC,cAAc,MAAM,4BAA4B;AACvD,OAAOC,UAAU,MAAM,iBAAiB;AACxC,OAAOC,uBAAuB,MAAM,8BAA8B;AAClE,SAASC,mBAAmB,QAAQ,kBAAkB;AACtD,IAAIC,QAAQ,GAAG,aAAa,YAAY;EACtC,SAASA,QAAQA,CAACC,IAAI,EAAEC,QAAQ,EAAE;IAChC,IAAI,CAACC,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,MAAM,GAAG,CAAC;IACf;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC;IACnB,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACP,IAAI,GAAGA,IAAI,IAAI,EAAE;IACtB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EAC1B;EACA;AACF;AACA;EACEF,QAAQ,CAACS,SAAS,CAACC,SAAS,GAAG,YAAY;IACzC,OAAO,IAAI,CAACL,SAAS,GAAG,CAAC;EAC3B,CAAC;EACDL,QAAQ,CAACS,SAAS,CAACE,QAAQ,GAAG,UAAUC,OAAO,EAAEC,EAAE,EAAEC,OAAO,EAAE;IAC5D,IAAInB,MAAM,CAACoB,UAAU,CAACH,OAAO,CAAC,EAAE;MAC9BE,OAAO,GAAGD,EAAE;MACZA,EAAE,GAAGD,OAAO;MACZA,OAAO,GAAG,IAAI;IAChB;IACAA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;IACvB,IAAIjB,MAAM,CAACqB,QAAQ,CAACJ,OAAO,CAAC,EAAE;MAC5BA,OAAO,GAAG;QACRK,KAAK,EAAEL;MACT,CAAC;IACH;IACA,IAAIK,KAAK,GAAGL,OAAO,CAACK,KAAK,IAAI,UAAU;IACvC,IAAIX,QAAQ,GAAG,IAAI,CAACM,OAAO,CAACM,IAAI,IAAI,UAAU,CAAC;IAC/C,IAAIC,gBAAgB;IACpBF,KAAK,KAAK,UAAU,KAAKE,gBAAgB,GAAGN,EAAE,CAACO,IAAI,CAACN,OAAO,EAAE,IAAI,CAAC,CAAC;IACnE,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAE,CAACF,gBAAgB,IAAIE,CAAC,GAAGf,QAAQ,CAACgB,MAAM,EAAED,CAAC,EAAE,EAAE;MAC7Df,QAAQ,CAACe,CAAC,CAAC,CAACV,QAAQ,CAACC,OAAO,EAAEC,EAAE,EAAEC,OAAO,CAAC;IAC5C;IACAG,KAAK,KAAK,WAAW,IAAIJ,EAAE,CAACO,IAAI,CAACN,OAAO,EAAE,IAAI,CAAC;EACjD,CAAC;EACD;AACF;AACA;EACEd,QAAQ,CAACS,SAAS,CAACc,oBAAoB,GAAG,UAAUpB,KAAK,EAAE;IACzD,IAAIC,MAAM,GAAG,CAAC;IACd,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,KAAK,IAAIkB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACf,QAAQ,CAACgB,MAAM,EAAED,CAAC,EAAE,EAAE;MAC7C,IAAIG,KAAK,GAAG,IAAI,CAAClB,QAAQ,CAACe,CAAC,CAAC;MAC5BG,KAAK,CAACD,oBAAoB,CAACpB,KAAK,GAAG,CAAC,CAAC;MACrC,IAAIqB,KAAK,CAACpB,MAAM,GAAGA,MAAM,EAAE;QACzBA,MAAM,GAAGoB,KAAK,CAACpB,MAAM;MACvB;IACF;IACA,IAAI,CAACA,MAAM,GAAGA,MAAM,GAAG,CAAC;EAC1B,CAAC;EACDJ,QAAQ,CAACS,SAAS,CAACgB,WAAW,GAAG,UAAUC,EAAE,EAAE;IAC7C,IAAI,IAAI,CAACC,KAAK,CAAC,CAAC,KAAKD,EAAE,EAAE;MACvB,OAAO,IAAI;IACb;IACA,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEf,QAAQ,GAAG,IAAI,CAACA,QAAQ,EAAEsB,GAAG,GAAGtB,QAAQ,CAACgB,MAAM,EAAED,CAAC,GAAGO,GAAG,EAAEP,CAAC,EAAE,EAAE;MAC7E,IAAIQ,GAAG,GAAGvB,QAAQ,CAACe,CAAC,CAAC,CAACI,WAAW,CAACC,EAAE,CAAC;MACrC,IAAIG,GAAG,EAAE;QACP,OAAOA,GAAG;MACZ;IACF;EACF,CAAC;EACD7B,QAAQ,CAACS,SAAS,CAACqB,QAAQ,GAAG,UAAUC,IAAI,EAAE;IAC5C,IAAIA,IAAI,KAAK,IAAI,EAAE;MACjB,OAAO,IAAI;IACb;IACA,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEf,QAAQ,GAAG,IAAI,CAACA,QAAQ,EAAEsB,GAAG,GAAGtB,QAAQ,CAACgB,MAAM,EAAED,CAAC,GAAGO,GAAG,EAAEP,CAAC,EAAE,EAAE;MAC7E,IAAIQ,GAAG,GAAGvB,QAAQ,CAACe,CAAC,CAAC,CAACS,QAAQ,CAACC,IAAI,CAAC;MACpC,IAAIF,GAAG,EAAE;QACP,OAAOA,GAAG;MACZ;IACF;EACF,CAAC;EACD;AACF;AACA;AACA;EACE7B,QAAQ,CAACS,SAAS,CAACuB,YAAY,GAAG,UAAUC,WAAW,EAAE;IACvD,IAAIC,SAAS,GAAG,EAAE;IAClB,IAAIH,IAAI,GAAGE,WAAW,GAAG,IAAI,GAAG,IAAI,CAACE,UAAU;IAC/C,OAAOJ,IAAI,EAAE;MACXG,SAAS,CAACE,IAAI,CAACL,IAAI,CAAC;MACpBA,IAAI,GAAGA,IAAI,CAACI,UAAU;IACxB;IACAD,SAAS,CAACG,OAAO,CAAC,CAAC;IACnB,OAAOH,SAAS;EAClB,CAAC;EACDlC,QAAQ,CAACS,SAAS,CAAC6B,mBAAmB,GAAG,YAAY;IACnD,IAAIC,OAAO,GAAG,EAAE;IAChB,IAAIC,QAAQ,GAAG,IAAI;IACnB,OAAOA,QAAQ,EAAE;MACfD,OAAO,CAACH,IAAI,CAACI,QAAQ,CAACnC,SAAS,CAAC;MAChCmC,QAAQ,GAAGA,QAAQ,CAACL,UAAU;IAChC;IACAI,OAAO,CAACF,OAAO,CAAC,CAAC;IACjB,OAAOE,OAAO;EAChB,CAAC;EACDvC,QAAQ,CAACS,SAAS,CAACgC,oBAAoB,GAAG,YAAY;IACpD,IAAIF,OAAO,GAAG,EAAE;IAChB,IAAI,CAAC5B,QAAQ,CAAC,UAAU+B,SAAS,EAAE;MACjCH,OAAO,CAACH,IAAI,CAACM,SAAS,CAACrC,SAAS,CAAC;IACnC,CAAC,CAAC;IACF,OAAOkC,OAAO;EAChB,CAAC;EACDvC,QAAQ,CAACS,SAAS,CAACkC,QAAQ,GAAG,UAAUC,SAAS,EAAE;IACjD,IAAIC,IAAI,GAAG,IAAI,CAAC3C,QAAQ,CAAC2C,IAAI;IAC7B,OAAOA,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,GAAG,CAACF,IAAI,CAACG,iBAAiB,CAACJ,SAAS,IAAI,OAAO,CAAC,EAAE,IAAI,CAACvC,SAAS,CAAC;EAC1F,CAAC;EACDL,QAAQ,CAACS,SAAS,CAACwC,SAAS,GAAG,UAAUC,MAAM,EAAEC,KAAK,EAAE;IACtD,IAAI,CAAC9C,SAAS,IAAI,CAAC,IAAI,IAAI,CAACH,QAAQ,CAAC2C,IAAI,CAACO,aAAa,CAAC,IAAI,CAAC/C,SAAS,EAAE6C,MAAM,EAAEC,KAAK,CAAC;EACxF,CAAC;EACD;AACF;AACA;EACEnD,QAAQ,CAACS,SAAS,CAAC4C,SAAS,GAAG,YAAY;IACzC,OAAO,IAAI,CAACnD,QAAQ,CAAC2C,IAAI,CAACS,aAAa,CAAC,IAAI,CAACjD,SAAS,CAAC;EACzD,CAAC;EACD;EACA;EACA;EACAL,QAAQ,CAACS,SAAS,CAAC8C,QAAQ,GAAG,UAAUC,IAAI,EAAE;IAC5C,IAAI,IAAI,CAACnD,SAAS,GAAG,CAAC,EAAE;MACtB;IACF;IACA,IAAIH,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC5B,IAAIuD,SAAS,GAAGvD,QAAQ,CAAC2C,IAAI,CAACa,YAAY,CAAC,IAAI,CAACrD,SAAS,CAAC;IAC1D,OAAOoD,SAAS,CAACF,QAAQ,CAACC,IAAI,CAAC;EACjC,CAAC;EACD;EACAxD,QAAQ,CAACS,SAAS,CAACkD,aAAa,GAAG,YAAY;IAC7C,OAAO,CAAC,IAAI,CAACzD,QAAQ,CAAC0D,WAAW,IAAI,EAAE,EAAE,IAAI,CAACzD,KAAK,CAAC;EACtD,CAAC;EACDH,QAAQ,CAACS,SAAS,CAACoD,SAAS,GAAG,UAAUC,GAAG,EAAEC,KAAK,EAAE;IACnD,IAAI,CAAC1D,SAAS,IAAI,CAAC,IAAI,IAAI,CAACH,QAAQ,CAAC2C,IAAI,CAACmB,aAAa,CAAC,IAAI,CAAC3D,SAAS,EAAEyD,GAAG,EAAEC,KAAK,CAAC;EACrF,CAAC;EACD;AACF;AACA;AACA;EACE/D,QAAQ,CAACS,SAAS,CAACwD,SAAS,GAAG,UAAUH,GAAG,EAAE;IAC5C,OAAO,IAAI,CAAC5D,QAAQ,CAAC2C,IAAI,CAACqB,aAAa,CAAC,IAAI,CAAC7D,SAAS,EAAEyD,GAAG,CAAC;EAC9D,CAAC;EACD9D,QAAQ,CAACS,SAAS,CAAC0D,WAAW,GAAG,YAAY;IAC3C,OAAO,IAAI,CAACjE,QAAQ,CAAC2C,IAAI,CAACsB,WAAW,CAAC,IAAI,CAAC9D,SAAS,CAAC;EACvD,CAAC;EACDL,QAAQ,CAACS,SAAS,CAACkB,KAAK,GAAG,YAAY;IACrC,OAAO,IAAI,CAACzB,QAAQ,CAAC2C,IAAI,CAAClB,KAAK,CAAC,IAAI,CAACtB,SAAS,CAAC;EACjD,CAAC;EACD;AACF;AACA;EACEL,QAAQ,CAACS,SAAS,CAAC2D,aAAa,GAAG,YAAY;IAC7C,IAAI,IAAI,CAACjC,UAAU,EAAE;MACnB,IAAI7B,QAAQ,GAAG,IAAI,CAAC6B,UAAU,CAAC7B,QAAQ;MACvC,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,QAAQ,CAACgB,MAAM,EAAE,EAAED,CAAC,EAAE;QACxC,IAAIf,QAAQ,CAACe,CAAC,CAAC,KAAK,IAAI,EAAE;UACxB,OAAOA,CAAC;QACV;MACF;MACA,OAAO,CAAC,CAAC;IACX;IACA,OAAO,CAAC,CAAC;EACX,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;EACErB,QAAQ,CAACS,SAAS,CAAC4D,YAAY,GAAG,UAAUtC,IAAI,EAAE;IAChD,IAAIuC,MAAM,GAAGvC,IAAI,CAACI,UAAU;IAC5B,OAAOmC,MAAM,EAAE;MACb,IAAIA,MAAM,KAAK,IAAI,EAAE;QACnB,OAAO,IAAI;MACb;MACAA,MAAM,GAAGA,MAAM,CAACnC,UAAU;IAC5B;IACA,OAAO,KAAK;EACd,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;EACEnC,QAAQ,CAACS,SAAS,CAAC8D,cAAc,GAAG,UAAUxC,IAAI,EAAE;IAClD,OAAOA,IAAI,KAAK,IAAI,IAAIA,IAAI,CAACsC,YAAY,CAAC,IAAI,CAAC;EACjD,CAAC;EACD,OAAOrE,QAAQ;AACjB,CAAC,CAAC,CAAC;AACH,SAASA,QAAQ;AACjB;AACA,IAAIwE,IAAI,GAAG,aAAa,YAAY;EAClC,SAASA,IAAIA,CAACC,SAAS,EAAE;IACvB,IAAI,CAACC,IAAI,GAAG,MAAM;IAClB,IAAI,CAACC,MAAM,GAAG,EAAE;IAChB,IAAI,CAACF,SAAS,GAAGA,SAAS;EAC5B;EACAD,IAAI,CAAC/D,SAAS,CAACE,QAAQ,GAAG,UAAUC,OAAO,EAAEC,EAAE,EAAEC,OAAO,EAAE;IACxD,IAAI,CAAC8D,IAAI,CAACjE,QAAQ,CAACC,OAAO,EAAEC,EAAE,EAAEC,OAAO,CAAC;EAC1C,CAAC;EACD0D,IAAI,CAAC/D,SAAS,CAACoE,kBAAkB,GAAG,UAAUxE,SAAS,EAAE;IACvD,IAAIyE,QAAQ,GAAG,IAAI,CAACjC,IAAI,CAACsB,WAAW,CAAC9D,SAAS,CAAC;IAC/C,OAAO,IAAI,CAACsE,MAAM,CAACG,QAAQ,CAAC;EAC9B,CAAC;EACDN,IAAI,CAAC/D,SAAS,CAACgB,WAAW,GAAG,UAAUxB,IAAI,EAAE;IAC3C,OAAO,IAAI,CAAC2E,IAAI,CAACnD,WAAW,CAACxB,IAAI,CAAC;EACpC,CAAC;EACD;AACF;AACA;AACA;EACEuE,IAAI,CAAC/D,SAAS,CAACsE,MAAM,GAAG,YAAY;IAClC,IAAIlC,IAAI,GAAG,IAAI,CAACA,IAAI;IACpB,IAAImC,KAAK,GAAG,IAAI,CAACL,MAAM;IACvB,KAAK,IAAItD,CAAC,GAAG,CAAC,EAAEO,GAAG,GAAGoD,KAAK,CAAC1D,MAAM,EAAED,CAAC,GAAGO,GAAG,EAAEP,CAAC,EAAE,EAAE;MAChD2D,KAAK,CAAC3D,CAAC,CAAC,CAAChB,SAAS,GAAG,CAAC,CAAC;IACzB;IACA,KAAK,IAAIgB,CAAC,GAAG,CAAC,EAAEO,GAAG,GAAGiB,IAAI,CAACoC,KAAK,CAAC,CAAC,EAAE5D,CAAC,GAAGO,GAAG,EAAEP,CAAC,EAAE,EAAE;MAChD2D,KAAK,CAACnC,IAAI,CAACsB,WAAW,CAAC9C,CAAC,CAAC,CAAC,CAAChB,SAAS,GAAGgB,CAAC;IAC1C;EACF,CAAC;EACD;AACF;AACA;EACEmD,IAAI,CAAC/D,SAAS,CAACyE,YAAY,GAAG,YAAY;IACxC,IAAI,CAACrC,IAAI,CAACsC,gBAAgB,CAAC,CAAC;EAC9B,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEX,IAAI,CAACY,UAAU,GAAG,UAAUC,QAAQ,EAAEZ,SAAS,EAAEa,UAAU,EAAE;IAC3D,IAAIC,IAAI,GAAG,IAAIf,IAAI,CAACC,SAAS,CAAC;IAC9B,IAAIe,QAAQ,GAAG,EAAE;IACjB,IAAIC,MAAM,GAAG,CAAC;IACdC,cAAc,CAACL,QAAQ,CAAC;IACxB,SAASK,cAAcA,CAACC,QAAQ,EAAExD,UAAU,EAAE;MAC5C,IAAI4B,KAAK,GAAG4B,QAAQ,CAAC5B,KAAK;MAC1B0B,MAAM,GAAGG,IAAI,CAACC,GAAG,CAACJ,MAAM,EAAE9F,MAAM,CAACmG,OAAO,CAAC/B,KAAK,CAAC,GAAGA,KAAK,CAACzC,MAAM,GAAG,CAAC,CAAC;MACnEkE,QAAQ,CAACpD,IAAI,CAACuD,QAAQ,CAAC;MACvB,IAAI5D,IAAI,GAAG,IAAI/B,QAAQ,CAACD,mBAAmB,CAAC4F,QAAQ,CAAC1F,IAAI,EAAE,EAAE,CAAC,EAAEsF,IAAI,CAAC;MACrEpD,UAAU,GAAG4D,QAAQ,CAAChE,IAAI,EAAEI,UAAU,CAAC,GAAGoD,IAAI,CAACX,IAAI,GAAG7C,IAAI;MAC1DwD,IAAI,CAACZ,MAAM,CAACvC,IAAI,CAACL,IAAI,CAAC;MACtB,IAAIzB,QAAQ,GAAGqF,QAAQ,CAACrF,QAAQ;MAChC,IAAIA,QAAQ,EAAE;QACZ,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,QAAQ,CAACgB,MAAM,EAAED,CAAC,EAAE,EAAE;UACxCqE,cAAc,CAACpF,QAAQ,CAACe,CAAC,CAAC,EAAEU,IAAI,CAAC;QACnC;MACF;IACF;IACAwD,IAAI,CAACX,IAAI,CAACrD,oBAAoB,CAAC,CAAC,CAAC;IACjC,IAAIyE,UAAU,GAAGlG,uBAAuB,CAAC0F,QAAQ,EAAE;MACjDS,eAAe,EAAE,CAAC,OAAO,CAAC;MAC1BC,eAAe,EAAET;IACnB,CAAC,CAAC,CAACO,UAAU;IACb,IAAIG,IAAI,GAAG,IAAItG,UAAU,CAACmG,UAAU,EAAEvB,SAAS,CAAC;IAChD0B,IAAI,CAACC,QAAQ,CAACZ,QAAQ,CAAC;IACvBF,UAAU,IAAIA,UAAU,CAACa,IAAI,CAAC;IAC9BvG,cAAc,CAAC;MACbyG,QAAQ,EAAEF,IAAI;MACdG,MAAM,EAAEf,IAAI;MACZgB,UAAU,EAAE;IACd,CAAC,CAAC;IACFhB,IAAI,CAACR,MAAM,CAAC,CAAC;IACb,OAAOQ,IAAI;EACb,CAAC;EACD,OAAOf,IAAI;AACb,CAAC,CAAC,CAAC;AACH;AACA;AACA;AACA;AACA,SAASuB,QAAQA,CAACvE,KAAK,EAAEO,IAAI,EAAE;EAC7B,IAAIzB,QAAQ,GAAGyB,IAAI,CAACzB,QAAQ;EAC5B,IAAIkB,KAAK,CAACW,UAAU,KAAKJ,IAAI,EAAE;IAC7B;EACF;EACAzB,QAAQ,CAAC8B,IAAI,CAACZ,KAAK,CAAC;EACpBA,KAAK,CAACW,UAAU,GAAGJ,IAAI;AACzB;AACA,eAAeyC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}