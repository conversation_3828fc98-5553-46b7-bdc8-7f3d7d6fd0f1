@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: Inter, system-ui, sans-serif;
    scroll-behavior: smooth;
  }
  
  body {
    @apply bg-gray-50 text-gray-900;
  }
}

@layer components {
  /* Enhanced glassmorphism effect for modern UI */
  .glass-card {
    @apply bg-white/80 backdrop-blur-sm border border-white/20 shadow-xl;
  }
  
  /* Smooth gradient backgrounds */
  .gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
  
  /* Custom scrollbar */
  .custom-scrollbar::-webkit-scrollbar {
    width: 8px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-track {
    @apply bg-gray-100 rounded-full;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }
  
  /* Focus states */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
  }
}

/* Resume Preview Styles */
.resume-content h1,
.resume-preview h1 {
  font-size: 1.75rem !important;
  color: #1f2937 !important;
  margin-bottom: 1rem !important;
  border-bottom: 3px solid #f97316 !important;
  padding-bottom: 0.75rem !important;
  font-weight: bold !important;
  text-align: center !important;
  font-family: Georgia, serif !important;
}

.resume-content h2,
.resume-preview h2 {
  font-size: 1.375rem !important;
  color: #374151 !important;
  margin: 2rem 0 1rem 0 !important;
  font-weight: 600 !important;
  border-bottom: 1px solid #d1d5db !important;
  padding-bottom: 0.25rem !important;
  font-family: Georgia, serif !important;
}

.resume-content h3,
.resume-preview h3 {
  font-size: 1.125rem !important;
  color: #4b5563 !important;
  margin: 1.25rem 0 0.5rem 0 !important;
  font-weight: 500 !important;
  font-family: Georgia, serif !important;
}

.resume-content p,
.resume-preview p {
  margin-bottom: 1rem !important;
  color: #374151 !important;
  text-align: justify !important;
  font-family: Georgia, serif !important;
  line-height: 1.6 !important;
}

.resume-content li,
.resume-preview li {
  margin-left: 1.5rem !important;
  margin-bottom: 0.5rem !important;
  color: #374151 !important;
  list-style-type: disc !important;
  font-family: Georgia, serif !important;
}

.resume-content div,
.resume-preview div {
  margin-bottom: 0.75rem !important;
  font-family: Georgia, serif !important;
}

.resume-content span.font-semibold,
.resume-preview span.font-semibold {
  font-weight: 600 !important;
  color: #1f2937 !important;
}

/* Print Styles */
@media print {
  .resume-content,
  .resume-preview {
    font-size: 12pt !important;
    line-height: 1.4 !important;
  }
  
  .resume-content h1,
  .resume-preview h1 {
    font-size: 18pt !important;
  }
  
  .resume-content h2,
  .resume-preview h2 {
    font-size: 14pt !important;
  }
  
  .resume-content h3,
  .resume-preview h3 {
    font-size: 12pt !important;
  }
}

/* Paper effect for resume preview */
.paper-preview {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.paper-preview:hover {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

/* Original resume pre formatting */
.paper-preview pre {
  margin: 0;
  padding: 0;
  border: none;
  background: transparent;
  font-family: Georgia, serif !important;
  white-space: pre-wrap;
  word-wrap: break-word;
  color: #374151;
  line-height: 1.6;
}

/* 渐入动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* 脉动动画 */
@keyframes pulse-success {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(34, 197, 94, 0);
  }
}

.pulse-success {
  animation: pulse-success 2s infinite;
}

/* 卡片悬停效果 */
.card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 渐变背景动画 */
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.gradient-animated {
  background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
  background-size: 400% 400%;
  animation: gradient-shift 15s ease infinite;
}

/* 数字计数动画 */
@keyframes countUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.count-up {
  animation: countUp 0.8s ease-out;
}

/* 进度条动画 */
@keyframes progressFill {
  from {
    width: 0%;
  }
  to {
    width: var(--progress-width);
  }
}

.progress-animated {
  animation: progressFill 1.5s ease-out forwards;
} 