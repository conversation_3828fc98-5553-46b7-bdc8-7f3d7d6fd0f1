{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { createHashMap } from 'zrender/lib/core/util.js';\nimport { GeoSVGResource } from './GeoSVGResource.js';\nimport { GeoJSONResource } from './GeoJSONResource.js';\nvar storage = createHashMap();\nexport default {\n  /**\r\n   * Compatible with previous `echarts.registerMap`.\r\n   *\r\n   * @usage\r\n   * ```js\r\n   *\r\n   * echarts.registerMap('USA', geoJson, specialAreas);\r\n   *\r\n   * echarts.registerMap('USA', {\r\n   *     geoJson: geoJson,\r\n   *     specialAreas: {...}\r\n   * });\r\n   * echarts.registerMap('USA', {\r\n   *     geoJSON: geoJson,\r\n   *     specialAreas: {...}\r\n   * });\r\n   *\r\n   * echarts.registerMap('airport', {\r\n   *     svg: svg\r\n   * }\r\n   * ```\r\n   *\r\n   * Note:\r\n   * Do not support that register multiple geoJSON or SVG\r\n   * one map name. Because different geoJSON and SVG have\r\n   * different unit. It's not easy to make sure how those\r\n   * units are mapping/normalize.\r\n   * If intending to use multiple geoJSON or SVG, we can\r\n   * use multiple geo coordinate system.\r\n   */\n  registerMap: function (mapName, rawDef, rawSpecialAreas) {\n    if (rawDef.svg) {\n      var resource = new GeoSVGResource(mapName, rawDef.svg);\n      storage.set(mapName, resource);\n    } else {\n      // Recommend:\n      //     echarts.registerMap('eu', { geoJSON: xxx, specialAreas: xxx });\n      // Backward compatibility:\n      //     echarts.registerMap('eu', geoJSON, specialAreas);\n      //     echarts.registerMap('eu', { geoJson: xxx, specialAreas: xxx });\n      var geoJSON = rawDef.geoJson || rawDef.geoJSON;\n      if (geoJSON && !rawDef.features) {\n        rawSpecialAreas = rawDef.specialAreas;\n      } else {\n        geoJSON = rawDef;\n      }\n      var resource = new GeoJSONResource(mapName, geoJSON, rawSpecialAreas);\n      storage.set(mapName, resource);\n    }\n  },\n  getGeoResource: function (mapName) {\n    return storage.get(mapName);\n  },\n  /**\r\n   * Only for exporting to users.\r\n   * **MUST NOT** used internally.\r\n   */\n  getMapForUser: function (mapName) {\n    var resource = storage.get(mapName);\n    // Do not support return SVG until some real requirement come.\n    return resource && resource.type === 'geoJSON' && resource.getMapForUser();\n  },\n  load: function (mapName, nameMap, nameProperty) {\n    var resource = storage.get(mapName);\n    if (!resource) {\n      if (process.env.NODE_ENV !== 'production') {\n        console.error('Map ' + mapName + ' not exists. The GeoJSON of the map must be provided.');\n      }\n      return;\n    }\n    return resource.load(nameMap, nameProperty);\n  }\n};", "map": {"version": 3, "names": ["createHashMap", "GeoSVGResource", "GeoJSONResource", "storage", "registerMap", "mapName", "rawDef", "rawSpecialAreas", "svg", "resource", "set", "geoJSON", "geoJson", "features", "<PERSON><PERSON><PERSON><PERSON>", "getGeoResource", "get", "getMapForUser", "type", "load", "nameMap", "nameProperty", "process", "env", "NODE_ENV", "console", "error"], "sources": ["E:/AI/SmartCV/node_modules/echarts/lib/coord/geo/geoSourceManager.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { createHashMap } from 'zrender/lib/core/util.js';\nimport { GeoSVGResource } from './GeoSVGResource.js';\nimport { GeoJSONResource } from './GeoJSONResource.js';\nvar storage = createHashMap();\nexport default {\n  /**\r\n   * Compatible with previous `echarts.registerMap`.\r\n   *\r\n   * @usage\r\n   * ```js\r\n   *\r\n   * echarts.registerMap('USA', geoJson, specialAreas);\r\n   *\r\n   * echarts.registerMap('USA', {\r\n   *     geoJson: geoJson,\r\n   *     specialAreas: {...}\r\n   * });\r\n   * echarts.registerMap('USA', {\r\n   *     geoJSON: geoJson,\r\n   *     specialAreas: {...}\r\n   * });\r\n   *\r\n   * echarts.registerMap('airport', {\r\n   *     svg: svg\r\n   * }\r\n   * ```\r\n   *\r\n   * Note:\r\n   * Do not support that register multiple geoJSON or SVG\r\n   * one map name. Because different geoJSON and SVG have\r\n   * different unit. It's not easy to make sure how those\r\n   * units are mapping/normalize.\r\n   * If intending to use multiple geoJSON or SVG, we can\r\n   * use multiple geo coordinate system.\r\n   */\n  registerMap: function (mapName, rawDef, rawSpecialAreas) {\n    if (rawDef.svg) {\n      var resource = new GeoSVGResource(mapName, rawDef.svg);\n      storage.set(mapName, resource);\n    } else {\n      // Recommend:\n      //     echarts.registerMap('eu', { geoJSON: xxx, specialAreas: xxx });\n      // Backward compatibility:\n      //     echarts.registerMap('eu', geoJSON, specialAreas);\n      //     echarts.registerMap('eu', { geoJson: xxx, specialAreas: xxx });\n      var geoJSON = rawDef.geoJson || rawDef.geoJSON;\n      if (geoJSON && !rawDef.features) {\n        rawSpecialAreas = rawDef.specialAreas;\n      } else {\n        geoJSON = rawDef;\n      }\n      var resource = new GeoJSONResource(mapName, geoJSON, rawSpecialAreas);\n      storage.set(mapName, resource);\n    }\n  },\n  getGeoResource: function (mapName) {\n    return storage.get(mapName);\n  },\n  /**\r\n   * Only for exporting to users.\r\n   * **MUST NOT** used internally.\r\n   */\n  getMapForUser: function (mapName) {\n    var resource = storage.get(mapName);\n    // Do not support return SVG until some real requirement come.\n    return resource && resource.type === 'geoJSON' && resource.getMapForUser();\n  },\n  load: function (mapName, nameMap, nameProperty) {\n    var resource = storage.get(mapName);\n    if (!resource) {\n      if (process.env.NODE_ENV !== 'production') {\n        console.error('Map ' + mapName + ' not exists. The GeoJSON of the map must be provided.');\n      }\n      return;\n    }\n    return resource.load(nameMap, nameProperty);\n  }\n};"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,aAAa,QAAQ,0BAA0B;AACxD,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,eAAe,QAAQ,sBAAsB;AACtD,IAAIC,OAAO,GAAGH,aAAa,CAAC,CAAC;AAC7B,eAAe;EACb;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEI,WAAW,EAAE,SAAAA,CAAUC,OAAO,EAAEC,MAAM,EAAEC,eAAe,EAAE;IACvD,IAAID,MAAM,CAACE,GAAG,EAAE;MACd,IAAIC,QAAQ,GAAG,IAAIR,cAAc,CAACI,OAAO,EAAEC,MAAM,CAACE,GAAG,CAAC;MACtDL,OAAO,CAACO,GAAG,CAACL,OAAO,EAAEI,QAAQ,CAAC;IAChC,CAAC,MAAM;MACL;MACA;MACA;MACA;MACA;MACA,IAAIE,OAAO,GAAGL,MAAM,CAACM,OAAO,IAAIN,MAAM,CAACK,OAAO;MAC9C,IAAIA,OAAO,IAAI,CAACL,MAAM,CAACO,QAAQ,EAAE;QAC/BN,eAAe,GAAGD,MAAM,CAACQ,YAAY;MACvC,CAAC,MAAM;QACLH,OAAO,GAAGL,MAAM;MAClB;MACA,IAAIG,QAAQ,GAAG,IAAIP,eAAe,CAACG,OAAO,EAAEM,OAAO,EAAEJ,eAAe,CAAC;MACrEJ,OAAO,CAACO,GAAG,CAACL,OAAO,EAAEI,QAAQ,CAAC;IAChC;EACF,CAAC;EACDM,cAAc,EAAE,SAAAA,CAAUV,OAAO,EAAE;IACjC,OAAOF,OAAO,CAACa,GAAG,CAACX,OAAO,CAAC;EAC7B,CAAC;EACD;AACF;AACA;AACA;EACEY,aAAa,EAAE,SAAAA,CAAUZ,OAAO,EAAE;IAChC,IAAII,QAAQ,GAAGN,OAAO,CAACa,GAAG,CAACX,OAAO,CAAC;IACnC;IACA,OAAOI,QAAQ,IAAIA,QAAQ,CAACS,IAAI,KAAK,SAAS,IAAIT,QAAQ,CAACQ,aAAa,CAAC,CAAC;EAC5E,CAAC;EACDE,IAAI,EAAE,SAAAA,CAAUd,OAAO,EAAEe,OAAO,EAAEC,YAAY,EAAE;IAC9C,IAAIZ,QAAQ,GAAGN,OAAO,CAACa,GAAG,CAACX,OAAO,CAAC;IACnC,IAAI,CAACI,QAAQ,EAAE;MACb,IAAIa,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCC,OAAO,CAACC,KAAK,CAAC,MAAM,GAAGrB,OAAO,GAAG,uDAAuD,CAAC;MAC3F;MACA;IACF;IACA,OAAOI,QAAQ,CAACU,IAAI,CAACC,OAAO,EAAEC,YAAY,CAAC;EAC7C;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}