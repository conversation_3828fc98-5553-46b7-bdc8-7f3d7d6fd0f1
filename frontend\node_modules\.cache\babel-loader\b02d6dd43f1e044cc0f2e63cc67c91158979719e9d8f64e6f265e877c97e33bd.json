{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as graphic from '../../util/graphic.js';\nimport { round } from '../../util/number.js';\nimport { isFunction } from 'zrender/lib/core/util.js';\nfunction createGridClipPath(cartesian, hasAnimation, seriesModel, done, during) {\n  var rect = cartesian.getArea();\n  var x = rect.x;\n  var y = rect.y;\n  var width = rect.width;\n  var height = rect.height;\n  var lineWidth = seriesModel.get(['lineStyle', 'width']) || 0;\n  // Expand the clip path a bit to avoid the border is clipped and looks thinner\n  x -= lineWidth / 2;\n  y -= lineWidth / 2;\n  width += lineWidth;\n  height += lineWidth;\n  // fix: https://github.com/apache/incubator-echarts/issues/11369\n  width = Math.ceil(width);\n  if (x !== Math.floor(x)) {\n    x = Math.floor(x);\n    // if no extra 1px on `width`, it will still be clipped since `x` is floored\n    width++;\n  }\n  var clipPath = new graphic.Rect({\n    shape: {\n      x: x,\n      y: y,\n      width: width,\n      height: height\n    }\n  });\n  if (hasAnimation) {\n    var baseAxis = cartesian.getBaseAxis();\n    var isHorizontal = baseAxis.isHorizontal();\n    var isAxisInversed = baseAxis.inverse;\n    if (isHorizontal) {\n      if (isAxisInversed) {\n        clipPath.shape.x += width;\n      }\n      clipPath.shape.width = 0;\n    } else {\n      if (!isAxisInversed) {\n        clipPath.shape.y += height;\n      }\n      clipPath.shape.height = 0;\n    }\n    var duringCb = isFunction(during) ? function (percent) {\n      during(percent, clipPath);\n    } : null;\n    graphic.initProps(clipPath, {\n      shape: {\n        width: width,\n        height: height,\n        x: x,\n        y: y\n      }\n    }, seriesModel, null, done, duringCb);\n  }\n  return clipPath;\n}\nfunction createPolarClipPath(polar, hasAnimation, seriesModel) {\n  var sectorArea = polar.getArea();\n  // Avoid float number rounding error for symbol on the edge of axis extent.\n  var r0 = round(sectorArea.r0, 1);\n  var r = round(sectorArea.r, 1);\n  var clipPath = new graphic.Sector({\n    shape: {\n      cx: round(polar.cx, 1),\n      cy: round(polar.cy, 1),\n      r0: r0,\n      r: r,\n      startAngle: sectorArea.startAngle,\n      endAngle: sectorArea.endAngle,\n      clockwise: sectorArea.clockwise\n    }\n  });\n  if (hasAnimation) {\n    var isRadial = polar.getBaseAxis().dim === 'angle';\n    if (isRadial) {\n      clipPath.shape.endAngle = sectorArea.startAngle;\n    } else {\n      clipPath.shape.r = r0;\n    }\n    graphic.initProps(clipPath, {\n      shape: {\n        endAngle: sectorArea.endAngle,\n        r: r\n      }\n    }, seriesModel);\n  }\n  return clipPath;\n}\nfunction createClipPath(coordSys, hasAnimation, seriesModel, done, during) {\n  if (!coordSys) {\n    return null;\n  } else if (coordSys.type === 'polar') {\n    return createPolarClipPath(coordSys, hasAnimation, seriesModel);\n  } else if (coordSys.type === 'cartesian2d') {\n    return createGridClipPath(coordSys, hasAnimation, seriesModel, done, during);\n  }\n  return null;\n}\nexport { createGridClipPath, createPolarClipPath, createClipPath };", "map": {"version": 3, "names": ["graphic", "round", "isFunction", "createGridClipPath", "cartesian", "hasAnimation", "seriesModel", "done", "during", "rect", "getArea", "x", "y", "width", "height", "lineWidth", "get", "Math", "ceil", "floor", "clipPath", "Rect", "shape", "baseAxis", "getBaseAxis", "isHorizontal", "isAxisInversed", "inverse", "duringCb", "percent", "initProps", "createPolarClipPath", "polar", "sectorArea", "r0", "r", "Sector", "cx", "cy", "startAngle", "endAngle", "clockwise", "isRadial", "dim", "createClipPath", "coordSys", "type"], "sources": ["E:/AI/SmartCV/node_modules/echarts/lib/chart/helper/createClipPathFromCoordSys.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as graphic from '../../util/graphic.js';\nimport { round } from '../../util/number.js';\nimport { isFunction } from 'zrender/lib/core/util.js';\nfunction createGridClipPath(cartesian, hasAnimation, seriesModel, done, during) {\n  var rect = cartesian.getArea();\n  var x = rect.x;\n  var y = rect.y;\n  var width = rect.width;\n  var height = rect.height;\n  var lineWidth = seriesModel.get(['lineStyle', 'width']) || 0;\n  // Expand the clip path a bit to avoid the border is clipped and looks thinner\n  x -= lineWidth / 2;\n  y -= lineWidth / 2;\n  width += lineWidth;\n  height += lineWidth;\n  // fix: https://github.com/apache/incubator-echarts/issues/11369\n  width = Math.ceil(width);\n  if (x !== Math.floor(x)) {\n    x = Math.floor(x);\n    // if no extra 1px on `width`, it will still be clipped since `x` is floored\n    width++;\n  }\n  var clipPath = new graphic.Rect({\n    shape: {\n      x: x,\n      y: y,\n      width: width,\n      height: height\n    }\n  });\n  if (hasAnimation) {\n    var baseAxis = cartesian.getBaseAxis();\n    var isHorizontal = baseAxis.isHorizontal();\n    var isAxisInversed = baseAxis.inverse;\n    if (isHorizontal) {\n      if (isAxisInversed) {\n        clipPath.shape.x += width;\n      }\n      clipPath.shape.width = 0;\n    } else {\n      if (!isAxisInversed) {\n        clipPath.shape.y += height;\n      }\n      clipPath.shape.height = 0;\n    }\n    var duringCb = isFunction(during) ? function (percent) {\n      during(percent, clipPath);\n    } : null;\n    graphic.initProps(clipPath, {\n      shape: {\n        width: width,\n        height: height,\n        x: x,\n        y: y\n      }\n    }, seriesModel, null, done, duringCb);\n  }\n  return clipPath;\n}\nfunction createPolarClipPath(polar, hasAnimation, seriesModel) {\n  var sectorArea = polar.getArea();\n  // Avoid float number rounding error for symbol on the edge of axis extent.\n  var r0 = round(sectorArea.r0, 1);\n  var r = round(sectorArea.r, 1);\n  var clipPath = new graphic.Sector({\n    shape: {\n      cx: round(polar.cx, 1),\n      cy: round(polar.cy, 1),\n      r0: r0,\n      r: r,\n      startAngle: sectorArea.startAngle,\n      endAngle: sectorArea.endAngle,\n      clockwise: sectorArea.clockwise\n    }\n  });\n  if (hasAnimation) {\n    var isRadial = polar.getBaseAxis().dim === 'angle';\n    if (isRadial) {\n      clipPath.shape.endAngle = sectorArea.startAngle;\n    } else {\n      clipPath.shape.r = r0;\n    }\n    graphic.initProps(clipPath, {\n      shape: {\n        endAngle: sectorArea.endAngle,\n        r: r\n      }\n    }, seriesModel);\n  }\n  return clipPath;\n}\nfunction createClipPath(coordSys, hasAnimation, seriesModel, done, during) {\n  if (!coordSys) {\n    return null;\n  } else if (coordSys.type === 'polar') {\n    return createPolarClipPath(coordSys, hasAnimation, seriesModel);\n  } else if (coordSys.type === 'cartesian2d') {\n    return createGridClipPath(coordSys, hasAnimation, seriesModel, done, during);\n  }\n  return null;\n}\nexport { createGridClipPath, createPolarClipPath, createClipPath };"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,OAAO,MAAM,uBAAuB;AAChD,SAASC,KAAK,QAAQ,sBAAsB;AAC5C,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,kBAAkBA,CAACC,SAAS,EAAEC,YAAY,EAAEC,WAAW,EAAEC,IAAI,EAAEC,MAAM,EAAE;EAC9E,IAAIC,IAAI,GAAGL,SAAS,CAACM,OAAO,CAAC,CAAC;EAC9B,IAAIC,CAAC,GAAGF,IAAI,CAACE,CAAC;EACd,IAAIC,CAAC,GAAGH,IAAI,CAACG,CAAC;EACd,IAAIC,KAAK,GAAGJ,IAAI,CAACI,KAAK;EACtB,IAAIC,MAAM,GAAGL,IAAI,CAACK,MAAM;EACxB,IAAIC,SAAS,GAAGT,WAAW,CAACU,GAAG,CAAC,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC;EAC5D;EACAL,CAAC,IAAII,SAAS,GAAG,CAAC;EAClBH,CAAC,IAAIG,SAAS,GAAG,CAAC;EAClBF,KAAK,IAAIE,SAAS;EAClBD,MAAM,IAAIC,SAAS;EACnB;EACAF,KAAK,GAAGI,IAAI,CAACC,IAAI,CAACL,KAAK,CAAC;EACxB,IAAIF,CAAC,KAAKM,IAAI,CAACE,KAAK,CAACR,CAAC,CAAC,EAAE;IACvBA,CAAC,GAAGM,IAAI,CAACE,KAAK,CAACR,CAAC,CAAC;IACjB;IACAE,KAAK,EAAE;EACT;EACA,IAAIO,QAAQ,GAAG,IAAIpB,OAAO,CAACqB,IAAI,CAAC;IAC9BC,KAAK,EAAE;MACLX,CAAC,EAAEA,CAAC;MACJC,CAAC,EAAEA,CAAC;MACJC,KAAK,EAAEA,KAAK;MACZC,MAAM,EAAEA;IACV;EACF,CAAC,CAAC;EACF,IAAIT,YAAY,EAAE;IAChB,IAAIkB,QAAQ,GAAGnB,SAAS,CAACoB,WAAW,CAAC,CAAC;IACtC,IAAIC,YAAY,GAAGF,QAAQ,CAACE,YAAY,CAAC,CAAC;IAC1C,IAAIC,cAAc,GAAGH,QAAQ,CAACI,OAAO;IACrC,IAAIF,YAAY,EAAE;MAChB,IAAIC,cAAc,EAAE;QAClBN,QAAQ,CAACE,KAAK,CAACX,CAAC,IAAIE,KAAK;MAC3B;MACAO,QAAQ,CAACE,KAAK,CAACT,KAAK,GAAG,CAAC;IAC1B,CAAC,MAAM;MACL,IAAI,CAACa,cAAc,EAAE;QACnBN,QAAQ,CAACE,KAAK,CAACV,CAAC,IAAIE,MAAM;MAC5B;MACAM,QAAQ,CAACE,KAAK,CAACR,MAAM,GAAG,CAAC;IAC3B;IACA,IAAIc,QAAQ,GAAG1B,UAAU,CAACM,MAAM,CAAC,GAAG,UAAUqB,OAAO,EAAE;MACrDrB,MAAM,CAACqB,OAAO,EAAET,QAAQ,CAAC;IAC3B,CAAC,GAAG,IAAI;IACRpB,OAAO,CAAC8B,SAAS,CAACV,QAAQ,EAAE;MAC1BE,KAAK,EAAE;QACLT,KAAK,EAAEA,KAAK;QACZC,MAAM,EAAEA,MAAM;QACdH,CAAC,EAAEA,CAAC;QACJC,CAAC,EAAEA;MACL;IACF,CAAC,EAAEN,WAAW,EAAE,IAAI,EAAEC,IAAI,EAAEqB,QAAQ,CAAC;EACvC;EACA,OAAOR,QAAQ;AACjB;AACA,SAASW,mBAAmBA,CAACC,KAAK,EAAE3B,YAAY,EAAEC,WAAW,EAAE;EAC7D,IAAI2B,UAAU,GAAGD,KAAK,CAACtB,OAAO,CAAC,CAAC;EAChC;EACA,IAAIwB,EAAE,GAAGjC,KAAK,CAACgC,UAAU,CAACC,EAAE,EAAE,CAAC,CAAC;EAChC,IAAIC,CAAC,GAAGlC,KAAK,CAACgC,UAAU,CAACE,CAAC,EAAE,CAAC,CAAC;EAC9B,IAAIf,QAAQ,GAAG,IAAIpB,OAAO,CAACoC,MAAM,CAAC;IAChCd,KAAK,EAAE;MACLe,EAAE,EAAEpC,KAAK,CAAC+B,KAAK,CAACK,EAAE,EAAE,CAAC,CAAC;MACtBC,EAAE,EAAErC,KAAK,CAAC+B,KAAK,CAACM,EAAE,EAAE,CAAC,CAAC;MACtBJ,EAAE,EAAEA,EAAE;MACNC,CAAC,EAAEA,CAAC;MACJI,UAAU,EAAEN,UAAU,CAACM,UAAU;MACjCC,QAAQ,EAAEP,UAAU,CAACO,QAAQ;MAC7BC,SAAS,EAAER,UAAU,CAACQ;IACxB;EACF,CAAC,CAAC;EACF,IAAIpC,YAAY,EAAE;IAChB,IAAIqC,QAAQ,GAAGV,KAAK,CAACR,WAAW,CAAC,CAAC,CAACmB,GAAG,KAAK,OAAO;IAClD,IAAID,QAAQ,EAAE;MACZtB,QAAQ,CAACE,KAAK,CAACkB,QAAQ,GAAGP,UAAU,CAACM,UAAU;IACjD,CAAC,MAAM;MACLnB,QAAQ,CAACE,KAAK,CAACa,CAAC,GAAGD,EAAE;IACvB;IACAlC,OAAO,CAAC8B,SAAS,CAACV,QAAQ,EAAE;MAC1BE,KAAK,EAAE;QACLkB,QAAQ,EAAEP,UAAU,CAACO,QAAQ;QAC7BL,CAAC,EAAEA;MACL;IACF,CAAC,EAAE7B,WAAW,CAAC;EACjB;EACA,OAAOc,QAAQ;AACjB;AACA,SAASwB,cAAcA,CAACC,QAAQ,EAAExC,YAAY,EAAEC,WAAW,EAAEC,IAAI,EAAEC,MAAM,EAAE;EACzE,IAAI,CAACqC,QAAQ,EAAE;IACb,OAAO,IAAI;EACb,CAAC,MAAM,IAAIA,QAAQ,CAACC,IAAI,KAAK,OAAO,EAAE;IACpC,OAAOf,mBAAmB,CAACc,QAAQ,EAAExC,YAAY,EAAEC,WAAW,CAAC;EACjE,CAAC,MAAM,IAAIuC,QAAQ,CAACC,IAAI,KAAK,aAAa,EAAE;IAC1C,OAAO3C,kBAAkB,CAAC0C,QAAQ,EAAExC,YAAY,EAAEC,WAAW,EAAEC,IAAI,EAAEC,MAAM,CAAC;EAC9E;EACA,OAAO,IAAI;AACb;AACA,SAASL,kBAAkB,EAAE4B,mBAAmB,EAAEa,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}