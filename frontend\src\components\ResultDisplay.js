import React, { useState, useEffect } from 'react';
import { exportToPDF, exportToWord, exportToText } from '../utils/exportUtils';
import { downloadOptimizedDocx } from '../services/api';
import DocumentPreview from './DocumentPreview';
// import ReactECharts from 'echarts-for-react';

const ResultDisplay = ({ result, originalFile, jobDescription }) => {
  const [showComparison, setShowComparison] = useState(false);
  const [activeTab, setActiveTab] = useState('optimized'); // 'optimized', 'original', 'comparison'
  const [showFormattedOptimized, setShowFormattedOptimized] = useState(false); // Toggle for formatted view in comparison
  const [downloadingDocx, setDownloadingDocx] = useState(false);
  const [showExportDropdown, setShowExportDropdown] = useState(false);
  const [viewMode, setViewMode] = useState('raw'); // 'raw', 'enhanced', 'word'
  const [wordPreviewMode, setWordPreviewMode] = useState('office'); // 'office', 'google', 'download'
  const [wordPreviewError, setWordPreviewError] = useState(null);
  // 新增：用于控制每个分类的展开/收起状态
  const [expandedMatched, setExpandedMatched] = useState({});
  const [expandedMissing, setExpandedMissing] = useState({});
  const [showAllSuggestions, setShowAllSuggestions] = useState(false);

  // 点击外部区域关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showExportDropdown && !event.target.closest('.export-dropdown')) {
        setShowExportDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showExportDropdown]);

  const formatOptimizedResume = (text) => {
    if (!text) return '';
    
    // Process Markdown-formatted resume
    return text
      .replace(/^#\s+(.+)$/gm, '<h1 class="text-2xl font-bold text-gray-900 mb-4 border-b-2 border-orange-500 pb-2">$1</h1>')
      .replace(/^##\s+(.+)$/gm, '<h2 class="text-xl font-semibold text-gray-800 mb-3 mt-6">$1</h2>')
      .replace(/^###\s+(.+)$/gm, '<h3 class="text-lg font-medium text-gray-700 mb-2 mt-4">$3</h3>')
      .replace(/^\*\*(.+?)\*\*:?(.*)$/gm, '<div class="mb-2"><span class="font-semibold text-gray-900">$1</span>$2</div>')
      .replace(/^\* (.+)$/gm, '<li class="ml-4 mb-1 text-gray-700">$1</li>')
      .replace(/^- (.+)$/gm, '<li class="ml-4 mb-1 text-gray-700">$1</li>')
      .replace(/\*\*(.+?)\*\*/g, '<span class="font-semibold">$1</span>')
      .replace(/\*(.+?)\*/g, '<span class="italic">$1</span>')
      .replace(/\n\n/g, '</p><p class="mb-3">')
      .replace(/\n/g, '<br>')
      .replace(/^(?!<[h|l|d])(.+)$/gm, '<p class="mb-3 text-gray-700">$1</p>');
  };

  const generateATSScore = (optimizedText, originalText) => {
    // 确保输入文本不为空，提供默认值
    const safeOptimizedText = optimizedText || '';
    const safeOriginalText = originalText || '';
    
    // Simple ATS scoring algorithm
    const factors = {
      hasQuantifiedAchievements: /\d+(%|k|million|billion|\$|year|month)/.test(safeOptimizedText),
      hasActionVerbs: /(developed|implemented|managed|led|created|optimized|improved)/.test(safeOptimizedText.toLowerCase()),
      hasStandardSections: /(experience|education|skills)/.test(safeOptimizedText.toLowerCase()),
      hasContactInfo: /(email|phone|linkedin)/.test(safeOptimizedText.toLowerCase()),
      appropriateLength: safeOptimizedText.length > 500 && safeOptimizedText.length < 4000,
      noSpecialCharacters: !/[★☆▪▫◦]/.test(safeOptimizedText)
    };

    const score = Object.values(factors).filter(Boolean).length;
    const maxScore = Object.keys(factors).length;
    const percentage = Math.round((score / maxScore) * 100);

    return {
      score: percentage,
      factors,
      improvements: maxScore - score
    };
  };

  // 兼容处理不同的数据结构
  const getOptimizedText = () => {
    return result?.optimized_resume || result?.optimized_text || '';
  };
  
  const getOriginalText = () => {
    return result?.original_resume || result?.original_text || '';
  };

  // 更精确的文本差异检测函数
  const detectTextDifferences = (originalText, optimizedText) => {
    const originalLines = originalText.split('\n').filter(line => line.trim());
    const optimizedLines = optimizedText.split('\n').filter(line => line.trim());
    
    const result = [];
    const usedOptimizedIndices = new Set();
    
    originalLines.forEach((originalLine, originalIndex) => {
      const trimmedOriginal = originalLine.trim();
      if (!trimmedOriginal) return;
      
      // 寻找最佳匹配
      let bestMatch = null;
      let bestSimilarity = 0;
      let bestOptimizedIndex = -1;
      
      optimizedLines.forEach((optimizedLine, optimizedIdx) => {
        if (usedOptimizedIndices.has(optimizedIdx)) return;
        
        const trimmedOptimized = optimizedLine.trim();
        const similarity = calculateSimilarity(trimmedOriginal, trimmedOptimized);
        
        if (similarity > bestSimilarity && similarity > 0.3) {
          bestMatch = optimizedLine;
          bestSimilarity = similarity;
          bestOptimizedIndex = optimizedIdx;
        }
      });
      
      if (bestMatch && bestSimilarity > 0.7) {
        // 高相似度，可能只是微调
        usedOptimizedIndices.add(bestOptimizedIndex);
        result.push({
          type: 'modified',
          original: originalLine,
          optimized: bestMatch,
          similarity: bestSimilarity
        });
      } else if (bestMatch && bestSimilarity > 0.3) {
        // 中等相似度，显著修改
        usedOptimizedIndices.add(bestOptimizedIndex);
        result.push({
          type: 'significantly_modified',
          original: originalLine,
          optimized: bestMatch,
          similarity: bestSimilarity
        });
      } else {
        // 没有找到匹配，可能被删除或完全重写
        result.push({
          type: 'deleted',
          original: originalLine,
          optimized: null
        });
      }
    });
    
    return result;
  };
  
  // 计算两个字符串的相似度
  const calculateSimilarity = (str1, str2) => {
    if (str1 === str2) return 1;
    if (!str1 || !str2) return 0;
    
    const words1 = str1.toLowerCase().split(/\s+/);
    const words2 = str2.toLowerCase().split(/\s+/);
    
    const commonWords = words1.filter(word => words2.includes(word));
    const totalWords = Math.max(words1.length, words2.length);
    
    return commonWords.length / totalWords;
  };

  // 在优化文本中高亮新增、修改、删除内容
  const highlightAdditionsInOptimized = (optimizedText) => {
    const originalText = getOriginalText();
    if (!originalText || !optimizedText || originalText === optimizedText) {
      // 没有原文或内容一致，直接返回普通格式
      return formatOptimizedResume(optimizedText);
    }
    // 差异检测
    const differences = detectTextDifferences(originalText, optimizedText);
    const optimizedLines = optimizedText.split('\n').filter(line => line.trim());
    const originalLines = originalText.split('\n').filter(line => line.trim());
    const usedOriginalIndices = new Set();
    const usedOptimizedIndices = new Set();
    // 标记已匹配的行
    differences.forEach((diff, idx) => {
      if (diff.type !== 'deleted') {
        const optIdx = optimizedLines.findIndex(line => line.trim() === diff.optimized?.trim());
        if (optIdx !== -1) usedOptimizedIndices.add(optIdx);
      }
      if (diff.type !== 'added') {
        const oriIdx = originalLines.findIndex(line => line.trim() === diff.original?.trim());
        if (oriIdx !== -1) usedOriginalIndices.add(oriIdx);
      }
    });
    // 渲染优化内容
    return optimizedLines.map((line, idx) => {
      const escapedLine = line.replace(/</g, '&lt;').replace(/>/g, '&gt;');
      // 检查是否为修改说明（如以This revised resume/This resume等开头，且为最后一段）
      const isRevisionNote = (idx === optimizedLines.length - 1) && /^(This revised resume|This resume|优化说明|修改说明|优化总结|本次修改说明|优化建议)/i.test(line.trim());
      if (isRevisionNote) {
        // 检查是否为中文优化说明，若是则替换为英文模板
        let revisionContent = line.trim();
        if (/^(本次修改说明|优化建议)/.test(revisionContent)) {
          revisionContent = `This revision note summarizes the main changes and optimization suggestions:\n\n- Integrated keywords from the target job, such as \"data analysis\", \"project management\", \"big data\", etc.\n- Reorganized the order of work experience to highlight the most relevant experiences.\n- Used action verbs to describe responsibilities and achievements.\n- Formatted the resume to be ATS-friendly, removed tables and special characters.\n\nOptimization Suggestions:\n- Quantified achievements for the \"Administrative Assistant\" role.\n- Added specific descriptions of data collection and analysis for the \"Intern\" position.`;
        }
        return `<p class=\"mb-3 leading-relaxed bg-gray-50 border-l-4 border-gray-400 px-4 py-2 italic text-gray-700 font-semibold\" style=\"font-style:italic;\"><span class=\"text-gray-500 font-bold mr-2\">Revision Note:</span>${revisionContent.replace(/\n/g, '<br>')}</p>`;
      }
      // 新增内容
      if (!usedOriginalIndices.has(idx) && line.trim().length > 0) {
        return `<p class=\"mb-3 leading-relaxed\"><span class=\"inline-block bg-green-50 text-green-700 font-medium px-2 py-1 rounded border border-green-200 mr-1\" title=\"新增内容\">✓ ${escapedLine}</span></p>`;
      }
      // 修改内容
      const diff = differences.find(d => d.optimized?.trim() === line.trim() && d.type === 'significantly_modified');
      if (diff) {
        return `<p class=\"mb-3 leading-relaxed\"><span class=\"inline-block bg-orange-50 text-orange-700 font-medium px-2 py-1 rounded border border-orange-200 mr-1\" title=\"修改内容\">${escapedLine}</span></p>`;
      }
      // 轻微修改
      const diff2 = differences.find(d => d.optimized?.trim() === line.trim() && d.type === 'modified');
      if (diff2) {
        return `<p class=\"mb-3 leading-relaxed\"><span class=\"inline-block bg-yellow-50 text-yellow-700 font-medium px-2 py-1 rounded border border-yellow-200 mr-1\" title=\"轻微修改\">${escapedLine}</span></p>`;
      }
      // 普通内容
      return `<p class=\"mb-3 text-gray-700 leading-relaxed\">${escapedLine}</p>`;
    }).join('')
    // 删除内容在优化内容中不显示，但可选在末尾加提示
    // differences.filter(d => d.type === 'deleted').map(...)
  };

  const atsScore = generateATSScore(getOptimizedText(), getOriginalText());

  const getScoreColor = (score) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-orange-600';
    return 'text-red-600';
  };

  const getScoreBg = (score) => {
    if (score >= 80) return 'bg-green-100';
    if (score >= 60) return 'bg-orange-100';
    return 'bg-red-100';
  };

  const getHeaderBg = (score) => {
    if (score >= 80) return 'bg-green-50 border-green-200';
    if (score >= 60) return 'bg-orange-50 border-orange-200';
    return 'bg-red-50 border-red-200';
  };

  const getIconBg = (score) => {
    if (score >= 80) return 'bg-green-500';
    if (score >= 60) return 'bg-orange-500';
    return 'bg-red-500';
  };

  const getProgressBarColor = (score) => {
    if (score >= 80) return 'bg-green-500';
    if (score >= 60) return 'bg-orange-500';
    return 'bg-red-500';
  };

  const getBorderColor = (score) => {
    if (score >= 80) return 'border-green-200';
    if (score >= 60) return 'border-orange-200';
    return 'border-red-200';
  };

  // 处理DOCX下载
  const handleDownloadDocx = async () => {
    try {
      const formData = new FormData();
      formData.append('file', originalFile);
      formData.append('job_description', jobDescription);
      
      const response = await fetch('/api/optimize-resume-docx', {
        method: 'POST',
        body: formData
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || '下载失败');
      }
      
      // 获取文件名
      const contentDisposition = response.headers.get('content-disposition');
      const filename = contentDisposition
        ? contentDisposition.split('filename=')[1].replace(/"/g, '')
        : 'optimized_resume.docx';
      
      // 下载文件
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
    } catch (error) {
      console.error('下载DOCX失败:', error);
      alert('下载失败: ' + error.message);
    }
  };

  const toggleExpand = (key, type) => {
    if (type === 'matched') {
      setExpandedMatched(prev => ({ ...prev, [key]: !prev[key] }));
    } else {
      setExpandedMissing(prev => ({ ...prev, [key]: !prev[key] }));
    }
  };

  return (
    <div>
      {/* 第一个卡片：Resume Optimization Complete + Job Match Analysis */}
      <div className="mb-6">
        <div className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
          {/* Header with ATS Score */}
          <div className={`${getHeaderBg(atsScore.score)} px-8 py-8 border-b relative overflow-hidden`}>
            {/* 背景装饰 */}
            <div className="absolute top-0 right-0 transform translate-x-4 -translate-y-4 opacity-10">
              <svg className="w-32 h-32" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            
            <div className="relative">
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                <div className="mb-6 lg:mb-0">
                  <div className="flex items-center mb-3">
                    <div className={`${getIconBg(atsScore.score)} text-white rounded-full w-12 h-12 flex items-center justify-center mr-4 shadow-lg`}>
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                    <div>
                      <h2 className="text-3xl font-bold text-gray-900 mb-1">
                        Resume Optimization Complete
                      </h2>
                      <div className="flex items-center text-sm text-gray-600">
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                        AI has optimized your resume according to job requirements, improving ATS pass rate
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-6">
                  {/* ATS 分数卡片 */}
                  <div className={`bg-white border-2 ${getBorderColor(atsScore.score)} rounded-2xl px-8 py-6 text-center min-w-[160px] shadow-lg transform hover:scale-105 transition-transform`}>
                    <div className={`text-4xl font-extrabold mb-2 ${getScoreColor(atsScore.score)}`}>
                      {atsScore.score}%
                    </div>
                    <div className="text-sm font-semibold text-gray-600 mb-2">ATS Match</div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full transition-all duration-1000 ${getProgressBarColor(atsScore.score)}`}
                        style={{ width: `${atsScore.score}%` }}
                      ></div>
                    </div>
                  </div>
                  
                  {/* 状态指示器 */}
                  <div className="text-center">
                    <div className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium ${
                      atsScore.score >= 80 ? 'bg-green-100 text-green-800' : 
                      atsScore.score >= 60 ? 'bg-yellow-100 text-yellow-800' : 
                      'bg-red-100 text-red-800'
                    }`}>
                      {atsScore.score >= 80 ? 'Excellent' : atsScore.score >= 60 ? 'Good' : 'Needs Improvement'}
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      {atsScore.score >= 80 ? 'Very likely to pass ATS screening' : 
                       atsScore.score >= 60 ? 'High chance to pass ATS screening' : 
                       'Recommend further optimization'}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Job Match Analysis - 在同一个卡片内 */}
          {result?.job_match_analysis && (
            <div className="p-8">
              {/* 简约标题 */}
              <div className="text-center mb-8">
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Job Match Analysis</h3>
                <p className="text-gray-600">AI analysis shows significant improvement in resume-job alignment</p>
              </div>
              
              {/* 核心指标卡片 - 简约风格 */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                {/* 原始匹配度 */}
                <div className="bg-gray-50 rounded-lg p-6 text-center">
                  <div className="text-2xl font-bold text-gray-500 mb-2">
                    {result.job_match_analysis.original_match_score?.overall_match_score || 0}%
                  </div>
                  <div className="text-sm text-gray-600 mb-3">Original Match</div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-gray-400 h-2 rounded-full transition-all duration-1000"
                      style={{ width: `${result.job_match_analysis.original_match_score?.overall_match_score || 0}%` }}
                    ></div>
                  </div>
                </div>
                
                {/* 优化后匹配度 */}
                <div className="bg-green-50 rounded-lg p-6 border-2 border-green-200 text-center">
                  <div className="flex items-center justify-center mb-2">
                    <svg className="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <div className="text-2xl font-bold text-green-600">
                      {result.job_match_analysis.optimized_match_score?.overall_match_score || 0}%
                    </div>
                  </div>
                  <div className="text-sm text-green-600 font-medium mb-3">Optimized Match</div>
                  <div className="w-full bg-green-100 rounded-full h-2">
                    <div 
                      className="bg-green-500 h-2 rounded-full transition-all duration-1000"
                      style={{ width: `${result.job_match_analysis.optimized_match_score?.overall_match_score || 0}%` }}
                    ></div>
                  </div>
                </div>
                
                {/* 提升幅度 */}
                <div className="bg-blue-50 rounded-lg p-6 text-center">
                  <div className={`text-2xl font-bold mb-2 ${result.job_match_analysis.match_improvement >= 0 ? 'text-blue-600' : 'text-orange-600'}`}>
                    {result.job_match_analysis.match_improvement >= 0 ? '+' : ''}{result.job_match_analysis.match_improvement}%
                  </div>
                  <div className="text-sm text-gray-600 mb-3">Improvement</div>
                  <div className="flex items-center justify-center text-xs text-gray-500">
                    <svg className="w-3 h-3 mr-1 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                    Significant Growth
                  </div>
                </div>
              </div>
              
              {/* 技能匹配详情 - 简约展示 */}
              {result.job_match_analysis.optimized_match_score?.breakdown && (
                <div className="bg-gray-50 rounded-lg p-6">
                  <h4 className="text-md font-medium text-gray-800 mb-4">Skill Match Breakdown</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {Object.entries(result.job_match_analysis.optimized_match_score.breakdown).map(([key, data]) => (
                      <div key={key} className="flex items-center justify-between p-3 bg-white rounded-lg border border-gray-100">
                        <span className="text-sm text-gray-700 capitalize font-medium">
                          {key.replace('_', ' ')}
                        </span>
                        <div className="flex items-center">
                          <span className={`text-sm font-semibold mr-3 ${data.score >= 70 ? 'text-green-600' : data.score >= 50 ? 'text-yellow-600' : 'text-red-600'}`}>
                            {data.score}%
                          </span>
                          <div className="w-16 bg-gray-200 rounded-full h-2">
                            <div 
                              className={`h-2 rounded-full ${
                                data.score >= 70 ? 'bg-green-500' : 
                                data.score >= 50 ? 'bg-yellow-500' : 'bg-red-500'
                              }`}
                              style={{ width: `${data.score}%` }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* 第二个卡片：Optimized Resume + ATS Analysis */}
      <div className="mb-6">
        <div className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
          {/* Tab Navigation */}
          <div className="border-b border-gray-200 bg-gray-50">
            <div className="px-8">
              <nav className="flex space-x-8">
                <button
                  onClick={() => setActiveTab('optimized')}
                  className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === 'optimized'
                      ? 'border-orange-500 text-orange-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <span className="flex items-center space-x-2">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span>Optimized Resume</span>
                  </span>
                </button>
                
                <button
                  onClick={() => setActiveTab('analysis')}
                  className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === 'analysis'
                      ? 'border-orange-500 text-orange-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <span className="flex items-center space-x-2">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    <span>ATS Analysis</span>
                  </span>
                </button>
              </nav>
            </div>
          </div>

          {/* 统计信息区块，仅在 Optimized Resume 页签下显示 */}
          {activeTab === 'optimized' && (
            <div className="px-8 py-6 border-b border-gray-100">
              <h4 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                <svg className="w-6 h-6 mr-3 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                Optimization Statistics
              </h4>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {/* Original Length */}
                <div className="bg-gray-50 rounded-lg p-6 text-center">
                  <div className="text-xs font-medium text-gray-500 mb-2">Original Length</div>
                  <div className="text-2xl font-bold text-gray-900 mb-1">
                    {result?.original_length?.toLocaleString() || 'N/A'}
                  </div>
                  <div className="text-xs text-gray-400">Characters</div>
                </div>
                
                {/* Final Length */}
                <div className="bg-gray-50 rounded-lg p-6 text-center">
                  <div className="text-xs font-medium text-gray-500 mb-2">Optimized Length</div>
                  <div className="text-2xl font-bold text-gray-900 mb-1">
                    {result?.optimized_length?.toLocaleString() || 'N/A'}
                  </div>
                  <div className="text-xs text-gray-400">Characters</div>
                </div>
                
                {/* Length Change */}
                <div className="bg-blue-50 rounded-lg p-6 text-center">
                  <div className="text-xs font-medium text-gray-500 mb-2">Change</div>
                  <div className="text-2xl font-bold mb-1">
                    {(() => {
                      const original = result?.original_length || 0;
                      const optimized = result?.optimized_length || 0;
                      const diff = optimized - original;
                      if (original === 0 || optimized === 0) return <span className="text-gray-400">N/A</span>;
                      return (
                        <span className={diff >= 0 ? 'text-blue-600' : 'text-orange-600'}>
                          {diff >= 0 ? '+' : ''}{diff.toLocaleString()}
                        </span>
                      );
                    })()}
                  </div>
                  <div className="text-xs text-gray-400">Characters</div>
                </div>
                
                {/* Optimization Ratio */}
                <div className="bg-green-50 rounded-lg p-6 text-center">
                  <div className="text-xs font-medium text-gray-500 mb-2">Optimization Rate</div>
                  <div className="text-2xl font-bold mb-1">
                    {(() => {
                      const original = result?.original_length || 0;
                      const optimized = result?.optimized_length || 0;
                      if (original === 0 || optimized === 0) return <span className="text-gray-400">N/A</span>;
                      let displayRate = 0;
                      if (optimized > original) {
                        displayRate = ((optimized - original) / optimized) * 100;
                      } else {
                        displayRate = ((original - optimized) / original) * 100;
                      }
                      displayRate = Math.max(0, displayRate);
                      const colorClass = displayRate > 15 ? 'text-green-600' : displayRate > 5 ? 'text-blue-600' : 'text-orange-600';
                      return (
                        <span className={colorClass}>
                          {displayRate.toFixed(1)}%
                        </span>
                      );
                    })()}
                  </div>
                  <div className="text-xs text-gray-400">Adjustment</div>
                </div>
              </div>
            </div>
          )}

          {/* Content Area */}
          <div className="p-8">
            {/* Optimized Resume Tab */}
            {activeTab === 'optimized' && (
              <div>
                <div className="mb-6 flex justify-between items-center">
                  <div className="flex items-center space-x-4 text-sm">
                    <span className="text-gray-600">Optimized Resume View:</span>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => setViewMode('raw')}
                        className={`px-3 py-1 rounded-md text-xs font-medium transition-colors ${
                          viewMode === 'raw'
                            ? 'bg-orange-100 text-orange-700'
                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                        }`}
                      >
                        Raw Text
                      </button>
                      <button
                        onClick={() => setViewMode('enhanced')}
                        className={`px-3 py-1 rounded-md text-xs font-medium transition-colors ${
                          viewMode === 'enhanced'
                            ? 'bg-orange-100 text-orange-700'
                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                        }`}
                      >
                        Enhanced View
                      </button>
                      <button
                        onClick={() => setViewMode('word')}
                        className={`px-3 py-1 rounded-md text-xs font-medium transition-colors ${
                          viewMode === 'word'
                            ? 'bg-orange-100 text-orange-700'
                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                        }`}
                        disabled={!result?.optimized_docx_url}
                      >
                        Word Preview
                      </button>
                    </div>
                  </div>
                  <div className="relative export-dropdown">
                    <button
                      onClick={() => setShowExportDropdown(!showExportDropdown)}
                      className="inline-flex items-center px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors text-sm font-medium"
                    >
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      Export & Download
                      <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </button>
                    
                    {showExportDropdown && (
                      <div className="absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                        <div className="py-2">
                          {result?.isFormatPreserved && result?.download_url && (
                            <>
                              <button
                                onClick={() => {
                                  handleDownloadDocx();
                                  setShowExportDropdown(false);
                                }}
                                disabled={downloadingDocx}
                                className="w-full text-left px-4 py-3 hover:bg-purple-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                              >
                                <div className="flex items-center">
                                  <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                                    {downloadingDocx ? (
                                      <svg className="animate-spin w-4 h-4 text-purple-600" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                      </svg>
                                    ) : (
                                      <svg className="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                                      </svg>
                                    )}
                                  </div>
                                  <div>
                                    <div className="font-medium text-purple-900">
                                      {downloadingDocx ? 'Downloading...' : 'Download Original Format'}
                                    </div>
                                    <div className="text-xs text-purple-600">DOCX with preserved formatting</div>
                                  </div>
                                </div>
                              </button>
                              <div className="border-t border-gray-100 my-1"></div>
                            </>
                          )}
                          
                          <button
                            onClick={() => {
                              exportToPDF(getOptimizedText());
                              setShowExportDropdown(false);
                            }}
                            className="w-full text-left px-4 py-3 hover:bg-red-50 transition-colors"
                          >
                            <div className="flex items-center">
                              <div className="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center mr-3">
                                <svg className="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                              </div>
                              <div>
                                <div className="font-medium text-gray-900">Export PDF</div>
                                <div className="text-xs text-gray-500">Download as PDF document</div>
                              </div>
                            </div>
                          </button>
                          
                          <button
                            onClick={() => {
                              exportToWord(getOptimizedText());
                              setShowExportDropdown(false);
                            }}
                            className="w-full text-left px-4 py-3 hover:bg-blue-50 transition-colors"
                          >
                            <div className="flex items-center">
                              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                              </div>
                              <div>
                                <div className="font-medium text-gray-900">Export Word</div>
                                <div className="text-xs text-gray-500">Download as DOCX document</div>
                              </div>
                            </div>
                          </button>
                          
                          <div className="border-t border-gray-100 my-1"></div>
                          
                          <button
                            onClick={() => {
                              window.print();
                              setShowExportDropdown(false);
                            }}
                            className="w-full text-left px-4 py-3 hover:bg-gray-50 transition-colors"
                          >
                            <div className="flex items-center">
                              <div className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
                                <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
                                </svg>
                              </div>
                              <div>
                                <div className="font-medium text-gray-900">Print Resume</div>
                                <div className="text-xs text-gray-500">Print directly from browser</div>
                              </div>
                            </div>
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
                
                {/* Paper-style Resume Preview */}
                <div className="paper-preview rounded-lg p-10 mx-auto max-w-5xl min-h-[800px] relative">
                  {/* 变化标记图例说明 */}
                  {viewMode === 'raw' && (
                    <>
                      {(() => {
                        const text = getOptimizedText() || 'Optimized resume content not available';
                        const lines = text.split('\n');
                        const lastLine = lines[lines.length - 1]?.trim();
                        const isRevisionNote = /^(This revised resume|This resume|优化说明|修改说明|优化总结|本次修改说明|优化建议)/i.test(lastLine);
                        if (isRevisionNote) {
                          const mainText = lines.slice(0, -1).join('\n');
                          return (
                            <>
                              <pre className="resume-content text-sm leading-relaxed text-gray-800 whitespace-pre-wrap">
                                {mainText}
                              </pre>
                              <div className="mb-3 leading-relaxed bg-gray-50 border-l-4 border-gray-400 px-4 py-2 italic text-gray-700 font-semibold" style={{fontStyle:'italic'}}>
                                <span className="text-gray-500 font-bold mr-2">Revision Note:</span>{lastLine}
                              </div>
                            </>
                          );
                        } else {
                          return (
                            <pre className="resume-content text-sm leading-relaxed text-gray-800 whitespace-pre-wrap">
                              {text}
                            </pre>
                          );
                        }
                      })()}
                    </>
                  )}
                  {viewMode === 'enhanced' && (
                    <div 
                      className="resume-content text-sm leading-relaxed text-gray-800"
                      dangerouslySetInnerHTML={{ 
                        __html: highlightAdditionsInOptimized(getOptimizedText()) 
                      }}
                    />
                  )}
                  {viewMode === 'word' && result?.optimized_docx_url && (
                    <div className="word-preview-container">
                      <div className="preview-tabs mb-4">
                        <button
                          onClick={() => setWordPreviewMode('office')}
                          className={`px-3 py-2 mr-2 rounded text-sm font-medium transition-colors ${
                            wordPreviewMode === 'office'
                              ? 'bg-blue-500 text-white'
                              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                          }`}
                        >
                          Office Online
                        </button>
                        <button
                          onClick={() => setWordPreviewMode('google')}
                          className={`px-3 py-2 mr-2 rounded text-sm font-medium transition-colors ${
                            wordPreviewMode === 'google'
                              ? 'bg-blue-500 text-white'
                              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                          }`}
                        >
                          Google Docs
                        </button>
                        <button
                          onClick={() => setWordPreviewMode('download')}
                          className={`px-3 py-2 rounded text-sm font-medium transition-colors ${
                            wordPreviewMode === 'download'
                              ? 'bg-green-500 text-white'
                              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                          }`}
                        >
                          下载查看
                        </button>
                      </div>

                      {wordPreviewMode === 'office' && (
                        <div className="preview-frame-container">
                          <iframe
                            src={`https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(result.optimized_docx_url)}`}
                            width="100%"
                            height="800"
                            frameBorder="0"
                            title="Office Online Preview"
                            style={{ background: '#fff', borderRadius: '8px' }}
                            onError={() => setWordPreviewError('Office Online预览失败，请尝试其他方式')}
                          ></iframe>
                        </div>
                      )}

                      {wordPreviewMode === 'google' && (
                        <div className="preview-frame-container">
                          <iframe
                            src={`https://docs.google.com/gview?url=${encodeURIComponent(result.optimized_docx_url)}&embedded=true`}
                            width="100%"
                            height="800"
                            frameBorder="0"
                            title="Google Docs Preview"
                            style={{ background: '#fff', borderRadius: '8px' }}
                            onError={() => setWordPreviewError('Google Docs预览失败，请尝试其他方式')}
                          ></iframe>
                        </div>
                      )}

                      {wordPreviewMode === 'download' && (
                        <div className="download-preview-container p-8 text-center bg-gray-50 rounded-lg">
                          <div className="mb-4">
                            <svg className="w-16 h-16 mx-auto text-blue-500 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            <h3 className="text-lg font-semibold text-gray-800 mb-2">Word文档已准备就绪</h3>
                            <p className="text-gray-600 mb-4">
                              由于浏览器限制，无法直接预览Word文档。
                              <br />
                              请点击下载按钮获取优化后的文档。
                            </p>
                          </div>
                          <a
                            href={result.optimized_docx_url}
                            download="optimized_resume.docx"
                            className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
                          >
                            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            下载优化后的Word文档
                          </a>
                        </div>
                      )}

                      {wordPreviewError && (
                        <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                          <div className="flex">
                            <svg className="w-5 h-5 text-yellow-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z" />
                            </svg>
                            <div>
                              <p className="text-yellow-800 font-medium">预览提示</p>
                              <p className="text-yellow-700 text-sm mt-1">{wordPreviewError}</p>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
                
                {/* Paragraph Changes - Only show for format preserved results */}
                {result?.isFormatPreserved && result?.paragraph_changes && result.paragraph_changes.length > 0 && (
                  <div className="mt-8 bg-purple-50 border border-purple-200 rounded-lg p-6">
                    <h4 className="text-lg font-semibold text-purple-900 mb-4 flex items-center">
                      <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                      </svg>
                      Paragraph-Level Changes Applied
                    </h4>
                    <div className="space-y-4">
                      {(result.paragraph_changes || []).map((change, index) => (
                        <div key={index} className="bg-white rounded-lg p-4 border border-purple-100">
                          <div className="text-sm font-medium text-purple-700 mb-2">
                            Change #{index + 1}: {change.change_reason}
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <div className="text-xs font-medium text-red-600 mb-1">Original:</div>
                              <div className="text-sm text-gray-700 bg-red-50 p-2 rounded border">
                                {change.original_text}
                              </div>
                            </div>
                            <div>
                              <div className="text-xs font-medium text-green-600 mb-1">Optimized:</div>
                              <div className="text-sm text-gray-700 bg-green-50 p-2 rounded border">
                                {change.optimized_text}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* ATS Analysis Tab */}
            {activeTab === 'analysis' && (
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">ATS Compatibility Analysis</h3>
                {/* 只保留详细分类匹配度分析 */}
                {result?.job_match_analysis?.optimized_match_score?.breakdown && (
                  <div>
                    <h5 className="text-md font-semibold text-gray-800 mb-3">Detailed Match Breakdown</h5>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {Object.entries(result.job_match_analysis.optimized_match_score.breakdown).map(([key, data]) => {
                        const matchedLimit = 5;
                        const missingLimit = 3;
                        const matchedExpanded = expandedMatched[key] || false;
                        const missingExpanded = expandedMissing[key] || false;
                        return (
                          <div key={key} className="bg-gray-50 rounded-lg p-4 border">
                            <div className="flex justify-between items-start mb-2">
                              <div>
                                <h6 className="font-semibold text-gray-700 capitalize">
                                  {key.replace('_', ' ')}
                                </h6>
                                <span className="text-xs text-gray-500 font-medium">Weight: {data.weight}</span>
                              </div>
                              <div className={`text-lg font-bold ${data.score >= 70 ? 'text-green-600' : data.score >= 50 ? 'text-yellow-600' : 'text-red-600'}`}>{data.score}%</div>
                            </div>
                            {/* 进度条 */}
                            <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                              <div 
                                className={`h-2 rounded-full transition-all duration-500 ${
                                  data.score >= 70 ? 'bg-green-500' : 
                                  data.score >= 50 ? 'bg-yellow-500' : 'bg-red-500'
                                }`}
                                style={{ width: `${data.score}%` }}
                              ></div>
                            </div>
                            {/* 匹配详情 */}
                            {data.matched && data.total && (
                              <div className="text-xs text-gray-600">
                                <div className="mb-1">
                                  <span className="font-medium">Matched ({data.matched.length}/{data.total.length}):</span>
                                </div>
                                {data.matched.length > 0 ? (
                                  <div className="flex flex-wrap gap-1 mb-2">
                                    {(matchedExpanded ? data.matched : data.matched.slice(0, matchedLimit)).map((item, idx) => (
                                      <span key={idx} className="bg-green-100 text-green-700 px-2 py-1 rounded text-xs">{item}</span>
                                    ))}
                                    {data.matched.length > matchedLimit && (
                                      <span
                                        className="text-gray-500 cursor-pointer underline"
                                        onClick={() => toggleExpand(key, 'matched')}
                                      >
                                        {matchedExpanded ? '收起' : `+${data.matched.length - matchedLimit} more`}
                                      </span>
                                    )}
                                  </div>
                                ) : (
                                  <div className="text-gray-500 mb-2">No matches found</div>
                                )}
                                {data.total.length > data.matched.length && (
                                  <div>
                                    <span className="font-medium text-red-600">Missing:</span>
                                    <div className="flex flex-wrap gap-1 mt-1">
                                      {(missingExpanded ? data.total.filter(item => !data.matched.includes(item)) : data.total.filter(item => !data.matched.includes(item)).slice(0, missingLimit)).map((item, idx) => (
                                        <span key={idx} className="bg-red-100 text-red-700 px-2 py-1 rounded text-xs mr-1">{item}</span>
                                      ))}
                                      {data.total.length - data.matched.length > missingLimit && (
                                        <span
                                          className="text-gray-500 cursor-pointer underline"
                                          onClick={() => toggleExpand(key, 'missing')}
                                        >
                                          {missingExpanded ? '收起' : `+${data.total.length - data.matched.length - missingLimit} more`}
                                        </span>
                                      )}
                                    </div>
                                  </div>
                                )}
                              </div>
                            )}
                          </div>
                        );
                      })}
                    </div>

                    {/* 关键差距卡片 */}
                    {(() => {
                      const allMissing = Object.entries(result.job_match_analysis.optimized_match_score.breakdown)
                        .map(([key, data]) => ({
                          key,
                          weight: data.weight,
                          missing: data.total ? data.total.filter(item => !data.matched.includes(item)) : [],
                        }))
                        .filter(item => item.missing.length > 0)
                        .sort((a, b) => b.weight - a.weight);
                      if (allMissing.length === 0) return null;
                      return (
                        <div className="bg-red-50 border border-red-200 rounded-lg p-6 mt-8 mb-4">
                          <h4 className="text-lg font-bold text-red-700 mb-2 flex items-center">
                            <svg className="w-5 h-5 mr-2 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-1.414 1.414A9 9 0 105.636 18.364l1.414-1.414A7 7 0 1116.95 7.05z" /></svg>
                            与岗位描述的关键差距
                          </h4>
                          <div className="flex flex-wrap gap-2">
                            {allMissing.slice(0, 3).map(item => (
                              <div key={item.key} className="mr-4 mb-2 flex items-center flex-wrap">
                                <span className="font-semibold text-gray-700 mr-2">{item.key.replace('_', ' ')}:</span>
                                {item.missing.slice(0, 3).map((miss, idx) => (
                                  <span key={idx} className="bg-red-100 text-red-700 px-2 py-1 rounded text-xs mr-1">{miss}</span>
                                ))}
                                {item.missing.length > 3 && (
                                  <span className="text-gray-500">+{item.missing.length - 3} more</span>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      );
                    })()}

                    {/* 优化建议卡片和一键展开按钮 */}
                    {(() => {
                      const suggestions = Object.entries(result.job_match_analysis.optimized_match_score.breakdown)
                        .filter(([key, data]) => data.score < 60)
                        .map(([key, data]) => {
                          let tip = '';
                          if (key.toLowerCase().includes('action')) tip = '建议补充更多动词，突出行动力。';
                          else if (key.toLowerCase().includes('skill')) tip = '建议补充与岗位相关的技能。';
                          else if (key.toLowerCase().includes('industry')) tip = '建议增加行业术语，提升专业性。';
                          else if (key.toLowerCase().includes('experience')) tip = '建议丰富相关经验描述。';
                          else if (key.toLowerCase().includes('education')) tip = '建议完善教育经历信息。';
                          else tip = '建议补充相关内容，提升匹配度。';
                          return `【${key.replace('_', ' ')}】${tip}`;
                        });
                      if (suggestions.length === 0) return null;
                      return (
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mt-4 mb-4">
                          <div className="flex items-center mb-2">
                            <h4 className="text-lg font-bold text-blue-700 mr-4">优化建议</h4>
                            <button
                              className="ml-auto px-4 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm"
                              onClick={() => setShowAllSuggestions(v => !v)}
                            >{showAllSuggestions ? '收起全部建议' : '一键展开全部建议'}</button>
                          </div>
                          <ul className="list-disc pl-6 text-blue-800">
                            {(showAllSuggestions ? suggestions : suggestions.slice(0, 2)).map((s, i) => (
                              <li key={i}>{s}</li>
                            ))}
                            {!showAllSuggestions && suggestions.length > 2 && (
                              <li className="text-gray-500">...更多建议请点击上方按钮展开</li>
                            )}
                          </ul>
                        </div>
                      );
                    })()}

                    {/* 雷达图可视化 - 暂时禁用，需要安装echarts-for-react */}
                    {false && (() => {
                      const radarData = Object.entries(result.job_match_analysis.optimized_match_score.breakdown).map(([key, data]) => ({
                        name: key.replace('_', ' '),
                        value: data.score,
                      }));
                      if (radarData.length < 3) return null; // 至少3项才显示雷达图
                      const indicator = radarData.map(item => ({ name: item.name, max: 100 }));
                      const option = {
                        tooltip: {},
                        radar: {
                          indicator,
                          radius: 80,
                        },
                        series: [{
                          type: 'radar',
                          data: [{ value: radarData.map(d => d.value), name: '覆盖度' }],
                          areaStyle: { opacity: 0.2 },
                          lineStyle: { width: 2 },
                        }],
                      };
                      return (
                        <div className="bg-white border border-gray-200 rounded-lg p-6 mt-4 mb-4 flex flex-col items-center">
                          <h4 className="text-lg font-bold text-gray-800 mb-2">简历内容覆盖度雷达图</h4>
                          <div style={{ width: 350, height: 300 }}>
                            {/* <ReactECharts option={option} style={{ width: '100%', height: '100%' }} /> */}
                            <div className="flex items-center justify-center h-full text-gray-500">
                              雷达图功能暂时禁用
                            </div>
                          </div>
                        </div>
                      );
                    })()}

                    {/* ATS常见问题检测卡片 */}
                    {(() => {
                      const optimizedText = getOptimizedText();
                      const issues = [];
                      if (/[★☆▪▫◦]/.test(optimizedText)) issues.push('检测到特殊符号，建议删除。');
                      if (!/(email|邮箱|@)/i.test(optimizedText)) issues.push('未检测到邮箱信息。');
                      if (!/(phone|电话|\d{11,})/i.test(optimizedText)) issues.push('未检测到电话信息。');
                      if (optimizedText.length < 500) issues.push('简历内容过短，建议丰富。');
                      if (optimizedText.length > 4000) issues.push('简历内容过长，建议精简。');
                      if (issues.length === 0) return null;
                      return (
                        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mt-4 mb-4">
                          <h4 className="text-lg font-bold text-yellow-700 mb-2 flex items-center">
                            <svg className="w-5 h-5 mr-2 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z" /></svg>
                            ATS常见问题检测
                          </h4>
                          <ul className="list-disc pl-6 text-yellow-800">
                            {issues.map((issue, idx) => <li key={idx}>{issue}</li>)}
                          </ul>
                        </div>
                      );
                    })()}

                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResultDisplay; 