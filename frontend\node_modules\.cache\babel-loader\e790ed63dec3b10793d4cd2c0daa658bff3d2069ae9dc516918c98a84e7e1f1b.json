{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/**\r\n * Helper for model references.\r\n * There are many manners to refer axis/coordSys.\r\n */\n// TODO\n// merge relevant logic to this file?\n// check: \"modelHelper\" of tooltip and \"BrushTargetManager\".\nimport { createHashMap, retrieve, each } from 'zrender/lib/core/util.js';\nimport { SINGLE_REFERRING } from '../util/model.js';\n/**\r\n * @class\r\n * For example:\r\n * {\r\n *     coordSysName: 'cartesian2d',\r\n *     coordSysDims: ['x', 'y', ...],\r\n *     axisMap: HashMap({\r\n *         x: xAxisModel,\r\n *         y: yAxisModel\r\n *     }),\r\n *     categoryAxisMap: HashMap({\r\n *         x: xAxisModel,\r\n *         y: undefined\r\n *     }),\r\n *     // The index of the first category axis in `coordSysDims`.\r\n *     // `null/undefined` means no category axis exists.\r\n *     firstCategoryDimIndex: 1,\r\n *     // To replace user specified encode.\r\n * }\r\n */\nvar CoordSysInfo = /** @class */function () {\n  function CoordSysInfo(coordSysName) {\n    this.coordSysDims = [];\n    this.axisMap = createHashMap();\n    this.categoryAxisMap = createHashMap();\n    this.coordSysName = coordSysName;\n  }\n  return CoordSysInfo;\n}();\nexport function getCoordSysInfoBySeries(seriesModel) {\n  var coordSysName = seriesModel.get('coordinateSystem');\n  var result = new CoordSysInfo(coordSysName);\n  var fetch = fetchers[coordSysName];\n  if (fetch) {\n    fetch(seriesModel, result, result.axisMap, result.categoryAxisMap);\n    return result;\n  }\n}\nvar fetchers = {\n  cartesian2d: function (seriesModel, result, axisMap, categoryAxisMap) {\n    var xAxisModel = seriesModel.getReferringComponents('xAxis', SINGLE_REFERRING).models[0];\n    var yAxisModel = seriesModel.getReferringComponents('yAxis', SINGLE_REFERRING).models[0];\n    if (process.env.NODE_ENV !== 'production') {\n      if (!xAxisModel) {\n        throw new Error('xAxis \"' + retrieve(seriesModel.get('xAxisIndex'), seriesModel.get('xAxisId'), 0) + '\" not found');\n      }\n      if (!yAxisModel) {\n        throw new Error('yAxis \"' + retrieve(seriesModel.get('xAxisIndex'), seriesModel.get('yAxisId'), 0) + '\" not found');\n      }\n    }\n    result.coordSysDims = ['x', 'y'];\n    axisMap.set('x', xAxisModel);\n    axisMap.set('y', yAxisModel);\n    if (isCategory(xAxisModel)) {\n      categoryAxisMap.set('x', xAxisModel);\n      result.firstCategoryDimIndex = 0;\n    }\n    if (isCategory(yAxisModel)) {\n      categoryAxisMap.set('y', yAxisModel);\n      result.firstCategoryDimIndex == null && (result.firstCategoryDimIndex = 1);\n    }\n  },\n  singleAxis: function (seriesModel, result, axisMap, categoryAxisMap) {\n    var singleAxisModel = seriesModel.getReferringComponents('singleAxis', SINGLE_REFERRING).models[0];\n    if (process.env.NODE_ENV !== 'production') {\n      if (!singleAxisModel) {\n        throw new Error('singleAxis should be specified.');\n      }\n    }\n    result.coordSysDims = ['single'];\n    axisMap.set('single', singleAxisModel);\n    if (isCategory(singleAxisModel)) {\n      categoryAxisMap.set('single', singleAxisModel);\n      result.firstCategoryDimIndex = 0;\n    }\n  },\n  polar: function (seriesModel, result, axisMap, categoryAxisMap) {\n    var polarModel = seriesModel.getReferringComponents('polar', SINGLE_REFERRING).models[0];\n    var radiusAxisModel = polarModel.findAxisModel('radiusAxis');\n    var angleAxisModel = polarModel.findAxisModel('angleAxis');\n    if (process.env.NODE_ENV !== 'production') {\n      if (!angleAxisModel) {\n        throw new Error('angleAxis option not found');\n      }\n      if (!radiusAxisModel) {\n        throw new Error('radiusAxis option not found');\n      }\n    }\n    result.coordSysDims = ['radius', 'angle'];\n    axisMap.set('radius', radiusAxisModel);\n    axisMap.set('angle', angleAxisModel);\n    if (isCategory(radiusAxisModel)) {\n      categoryAxisMap.set('radius', radiusAxisModel);\n      result.firstCategoryDimIndex = 0;\n    }\n    if (isCategory(angleAxisModel)) {\n      categoryAxisMap.set('angle', angleAxisModel);\n      result.firstCategoryDimIndex == null && (result.firstCategoryDimIndex = 1);\n    }\n  },\n  geo: function (seriesModel, result, axisMap, categoryAxisMap) {\n    result.coordSysDims = ['lng', 'lat'];\n  },\n  parallel: function (seriesModel, result, axisMap, categoryAxisMap) {\n    var ecModel = seriesModel.ecModel;\n    var parallelModel = ecModel.getComponent('parallel', seriesModel.get('parallelIndex'));\n    var coordSysDims = result.coordSysDims = parallelModel.dimensions.slice();\n    each(parallelModel.parallelAxisIndex, function (axisIndex, index) {\n      var axisModel = ecModel.getComponent('parallelAxis', axisIndex);\n      var axisDim = coordSysDims[index];\n      axisMap.set(axisDim, axisModel);\n      if (isCategory(axisModel)) {\n        categoryAxisMap.set(axisDim, axisModel);\n        if (result.firstCategoryDimIndex == null) {\n          result.firstCategoryDimIndex = index;\n        }\n      }\n    });\n  }\n};\nfunction isCategory(axisModel) {\n  return axisModel.get('type') === 'category';\n}", "map": {"version": 3, "names": ["createHashMap", "retrieve", "each", "SINGLE_REFERRING", "CoordSysInfo", "coordSysName", "coordSysDims", "axisMap", "categoryAxisMap", "getCoordSysInfoBySeries", "seriesModel", "get", "result", "fetch", "fetchers", "cartesian2d", "xAxisModel", "getReferringComponents", "models", "yAxisModel", "process", "env", "NODE_ENV", "Error", "set", "isCategory", "firstCategoryDimIndex", "singleAxis", "singleAxisModel", "polar", "polarModel", "radiusAxisModel", "findAxisModel", "angleAxisModel", "geo", "parallel", "ecModel", "parallelModel", "getComponent", "dimensions", "slice", "parallelAxisIndex", "axisIndex", "index", "axisModel", "axisDim"], "sources": ["E:/AI/SmartCV/node_modules/echarts/lib/model/referHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/**\r\n * Helper for model references.\r\n * There are many manners to refer axis/coordSys.\r\n */\n// TODO\n// merge relevant logic to this file?\n// check: \"modelHelper\" of tooltip and \"BrushTargetManager\".\nimport { createHashMap, retrieve, each } from 'zrender/lib/core/util.js';\nimport { SINGLE_REFERRING } from '../util/model.js';\n/**\r\n * @class\r\n * For example:\r\n * {\r\n *     coordSysName: 'cartesian2d',\r\n *     coordSysDims: ['x', 'y', ...],\r\n *     axisMap: HashMap({\r\n *         x: xAxisModel,\r\n *         y: yAxisModel\r\n *     }),\r\n *     categoryAxisMap: HashMap({\r\n *         x: xAxisModel,\r\n *         y: undefined\r\n *     }),\r\n *     // The index of the first category axis in `coordSysDims`.\r\n *     // `null/undefined` means no category axis exists.\r\n *     firstCategoryDimIndex: 1,\r\n *     // To replace user specified encode.\r\n * }\r\n */\nvar CoordSysInfo = /** @class */function () {\n  function CoordSysInfo(coordSysName) {\n    this.coordSysDims = [];\n    this.axisMap = createHashMap();\n    this.categoryAxisMap = createHashMap();\n    this.coordSysName = coordSysName;\n  }\n  return CoordSysInfo;\n}();\nexport function getCoordSysInfoBySeries(seriesModel) {\n  var coordSysName = seriesModel.get('coordinateSystem');\n  var result = new CoordSysInfo(coordSysName);\n  var fetch = fetchers[coordSysName];\n  if (fetch) {\n    fetch(seriesModel, result, result.axisMap, result.categoryAxisMap);\n    return result;\n  }\n}\nvar fetchers = {\n  cartesian2d: function (seriesModel, result, axisMap, categoryAxisMap) {\n    var xAxisModel = seriesModel.getReferringComponents('xAxis', SINGLE_REFERRING).models[0];\n    var yAxisModel = seriesModel.getReferringComponents('yAxis', SINGLE_REFERRING).models[0];\n    if (process.env.NODE_ENV !== 'production') {\n      if (!xAxisModel) {\n        throw new Error('xAxis \"' + retrieve(seriesModel.get('xAxisIndex'), seriesModel.get('xAxisId'), 0) + '\" not found');\n      }\n      if (!yAxisModel) {\n        throw new Error('yAxis \"' + retrieve(seriesModel.get('xAxisIndex'), seriesModel.get('yAxisId'), 0) + '\" not found');\n      }\n    }\n    result.coordSysDims = ['x', 'y'];\n    axisMap.set('x', xAxisModel);\n    axisMap.set('y', yAxisModel);\n    if (isCategory(xAxisModel)) {\n      categoryAxisMap.set('x', xAxisModel);\n      result.firstCategoryDimIndex = 0;\n    }\n    if (isCategory(yAxisModel)) {\n      categoryAxisMap.set('y', yAxisModel);\n      result.firstCategoryDimIndex == null && (result.firstCategoryDimIndex = 1);\n    }\n  },\n  singleAxis: function (seriesModel, result, axisMap, categoryAxisMap) {\n    var singleAxisModel = seriesModel.getReferringComponents('singleAxis', SINGLE_REFERRING).models[0];\n    if (process.env.NODE_ENV !== 'production') {\n      if (!singleAxisModel) {\n        throw new Error('singleAxis should be specified.');\n      }\n    }\n    result.coordSysDims = ['single'];\n    axisMap.set('single', singleAxisModel);\n    if (isCategory(singleAxisModel)) {\n      categoryAxisMap.set('single', singleAxisModel);\n      result.firstCategoryDimIndex = 0;\n    }\n  },\n  polar: function (seriesModel, result, axisMap, categoryAxisMap) {\n    var polarModel = seriesModel.getReferringComponents('polar', SINGLE_REFERRING).models[0];\n    var radiusAxisModel = polarModel.findAxisModel('radiusAxis');\n    var angleAxisModel = polarModel.findAxisModel('angleAxis');\n    if (process.env.NODE_ENV !== 'production') {\n      if (!angleAxisModel) {\n        throw new Error('angleAxis option not found');\n      }\n      if (!radiusAxisModel) {\n        throw new Error('radiusAxis option not found');\n      }\n    }\n    result.coordSysDims = ['radius', 'angle'];\n    axisMap.set('radius', radiusAxisModel);\n    axisMap.set('angle', angleAxisModel);\n    if (isCategory(radiusAxisModel)) {\n      categoryAxisMap.set('radius', radiusAxisModel);\n      result.firstCategoryDimIndex = 0;\n    }\n    if (isCategory(angleAxisModel)) {\n      categoryAxisMap.set('angle', angleAxisModel);\n      result.firstCategoryDimIndex == null && (result.firstCategoryDimIndex = 1);\n    }\n  },\n  geo: function (seriesModel, result, axisMap, categoryAxisMap) {\n    result.coordSysDims = ['lng', 'lat'];\n  },\n  parallel: function (seriesModel, result, axisMap, categoryAxisMap) {\n    var ecModel = seriesModel.ecModel;\n    var parallelModel = ecModel.getComponent('parallel', seriesModel.get('parallelIndex'));\n    var coordSysDims = result.coordSysDims = parallelModel.dimensions.slice();\n    each(parallelModel.parallelAxisIndex, function (axisIndex, index) {\n      var axisModel = ecModel.getComponent('parallelAxis', axisIndex);\n      var axisDim = coordSysDims[index];\n      axisMap.set(axisDim, axisModel);\n      if (isCategory(axisModel)) {\n        categoryAxisMap.set(axisDim, axisModel);\n        if (result.firstCategoryDimIndex == null) {\n          result.firstCategoryDimIndex = index;\n        }\n      }\n    });\n  }\n};\nfunction isCategory(axisModel) {\n  return axisModel.get('type') === 'category';\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,aAAa,EAAEC,QAAQ,EAAEC,IAAI,QAAQ,0BAA0B;AACxE,SAASC,gBAAgB,QAAQ,kBAAkB;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,YAAY,GAAG,aAAa,YAAY;EAC1C,SAASA,YAAYA,CAACC,YAAY,EAAE;IAClC,IAAI,CAACC,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,OAAO,GAAGP,aAAa,CAAC,CAAC;IAC9B,IAAI,CAACQ,eAAe,GAAGR,aAAa,CAAC,CAAC;IACtC,IAAI,CAACK,YAAY,GAAGA,YAAY;EAClC;EACA,OAAOD,YAAY;AACrB,CAAC,CAAC,CAAC;AACH,OAAO,SAASK,uBAAuBA,CAACC,WAAW,EAAE;EACnD,IAAIL,YAAY,GAAGK,WAAW,CAACC,GAAG,CAAC,kBAAkB,CAAC;EACtD,IAAIC,MAAM,GAAG,IAAIR,YAAY,CAACC,YAAY,CAAC;EAC3C,IAAIQ,KAAK,GAAGC,QAAQ,CAACT,YAAY,CAAC;EAClC,IAAIQ,KAAK,EAAE;IACTA,KAAK,CAACH,WAAW,EAAEE,MAAM,EAAEA,MAAM,CAACL,OAAO,EAAEK,MAAM,CAACJ,eAAe,CAAC;IAClE,OAAOI,MAAM;EACf;AACF;AACA,IAAIE,QAAQ,GAAG;EACbC,WAAW,EAAE,SAAAA,CAAUL,WAAW,EAAEE,MAAM,EAAEL,OAAO,EAAEC,eAAe,EAAE;IACpE,IAAIQ,UAAU,GAAGN,WAAW,CAACO,sBAAsB,CAAC,OAAO,EAAEd,gBAAgB,CAAC,CAACe,MAAM,CAAC,CAAC,CAAC;IACxF,IAAIC,UAAU,GAAGT,WAAW,CAACO,sBAAsB,CAAC,OAAO,EAAEd,gBAAgB,CAAC,CAACe,MAAM,CAAC,CAAC,CAAC;IACxF,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI,CAACN,UAAU,EAAE;QACf,MAAM,IAAIO,KAAK,CAAC,SAAS,GAAGtB,QAAQ,CAACS,WAAW,CAACC,GAAG,CAAC,YAAY,CAAC,EAAED,WAAW,CAACC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,GAAG,aAAa,CAAC;MACrH;MACA,IAAI,CAACQ,UAAU,EAAE;QACf,MAAM,IAAII,KAAK,CAAC,SAAS,GAAGtB,QAAQ,CAACS,WAAW,CAACC,GAAG,CAAC,YAAY,CAAC,EAAED,WAAW,CAACC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,GAAG,aAAa,CAAC;MACrH;IACF;IACAC,MAAM,CAACN,YAAY,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;IAChCC,OAAO,CAACiB,GAAG,CAAC,GAAG,EAAER,UAAU,CAAC;IAC5BT,OAAO,CAACiB,GAAG,CAAC,GAAG,EAAEL,UAAU,CAAC;IAC5B,IAAIM,UAAU,CAACT,UAAU,CAAC,EAAE;MAC1BR,eAAe,CAACgB,GAAG,CAAC,GAAG,EAAER,UAAU,CAAC;MACpCJ,MAAM,CAACc,qBAAqB,GAAG,CAAC;IAClC;IACA,IAAID,UAAU,CAACN,UAAU,CAAC,EAAE;MAC1BX,eAAe,CAACgB,GAAG,CAAC,GAAG,EAAEL,UAAU,CAAC;MACpCP,MAAM,CAACc,qBAAqB,IAAI,IAAI,KAAKd,MAAM,CAACc,qBAAqB,GAAG,CAAC,CAAC;IAC5E;EACF,CAAC;EACDC,UAAU,EAAE,SAAAA,CAAUjB,WAAW,EAAEE,MAAM,EAAEL,OAAO,EAAEC,eAAe,EAAE;IACnE,IAAIoB,eAAe,GAAGlB,WAAW,CAACO,sBAAsB,CAAC,YAAY,EAAEd,gBAAgB,CAAC,CAACe,MAAM,CAAC,CAAC,CAAC;IAClG,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI,CAACM,eAAe,EAAE;QACpB,MAAM,IAAIL,KAAK,CAAC,iCAAiC,CAAC;MACpD;IACF;IACAX,MAAM,CAACN,YAAY,GAAG,CAAC,QAAQ,CAAC;IAChCC,OAAO,CAACiB,GAAG,CAAC,QAAQ,EAAEI,eAAe,CAAC;IACtC,IAAIH,UAAU,CAACG,eAAe,CAAC,EAAE;MAC/BpB,eAAe,CAACgB,GAAG,CAAC,QAAQ,EAAEI,eAAe,CAAC;MAC9ChB,MAAM,CAACc,qBAAqB,GAAG,CAAC;IAClC;EACF,CAAC;EACDG,KAAK,EAAE,SAAAA,CAAUnB,WAAW,EAAEE,MAAM,EAAEL,OAAO,EAAEC,eAAe,EAAE;IAC9D,IAAIsB,UAAU,GAAGpB,WAAW,CAACO,sBAAsB,CAAC,OAAO,EAAEd,gBAAgB,CAAC,CAACe,MAAM,CAAC,CAAC,CAAC;IACxF,IAAIa,eAAe,GAAGD,UAAU,CAACE,aAAa,CAAC,YAAY,CAAC;IAC5D,IAAIC,cAAc,GAAGH,UAAU,CAACE,aAAa,CAAC,WAAW,CAAC;IAC1D,IAAIZ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI,CAACW,cAAc,EAAE;QACnB,MAAM,IAAIV,KAAK,CAAC,4BAA4B,CAAC;MAC/C;MACA,IAAI,CAACQ,eAAe,EAAE;QACpB,MAAM,IAAIR,KAAK,CAAC,6BAA6B,CAAC;MAChD;IACF;IACAX,MAAM,CAACN,YAAY,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC;IACzCC,OAAO,CAACiB,GAAG,CAAC,QAAQ,EAAEO,eAAe,CAAC;IACtCxB,OAAO,CAACiB,GAAG,CAAC,OAAO,EAAES,cAAc,CAAC;IACpC,IAAIR,UAAU,CAACM,eAAe,CAAC,EAAE;MAC/BvB,eAAe,CAACgB,GAAG,CAAC,QAAQ,EAAEO,eAAe,CAAC;MAC9CnB,MAAM,CAACc,qBAAqB,GAAG,CAAC;IAClC;IACA,IAAID,UAAU,CAACQ,cAAc,CAAC,EAAE;MAC9BzB,eAAe,CAACgB,GAAG,CAAC,OAAO,EAAES,cAAc,CAAC;MAC5CrB,MAAM,CAACc,qBAAqB,IAAI,IAAI,KAAKd,MAAM,CAACc,qBAAqB,GAAG,CAAC,CAAC;IAC5E;EACF,CAAC;EACDQ,GAAG,EAAE,SAAAA,CAAUxB,WAAW,EAAEE,MAAM,EAAEL,OAAO,EAAEC,eAAe,EAAE;IAC5DI,MAAM,CAACN,YAAY,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC;EACtC,CAAC;EACD6B,QAAQ,EAAE,SAAAA,CAAUzB,WAAW,EAAEE,MAAM,EAAEL,OAAO,EAAEC,eAAe,EAAE;IACjE,IAAI4B,OAAO,GAAG1B,WAAW,CAAC0B,OAAO;IACjC,IAAIC,aAAa,GAAGD,OAAO,CAACE,YAAY,CAAC,UAAU,EAAE5B,WAAW,CAACC,GAAG,CAAC,eAAe,CAAC,CAAC;IACtF,IAAIL,YAAY,GAAGM,MAAM,CAACN,YAAY,GAAG+B,aAAa,CAACE,UAAU,CAACC,KAAK,CAAC,CAAC;IACzEtC,IAAI,CAACmC,aAAa,CAACI,iBAAiB,EAAE,UAAUC,SAAS,EAAEC,KAAK,EAAE;MAChE,IAAIC,SAAS,GAAGR,OAAO,CAACE,YAAY,CAAC,cAAc,EAAEI,SAAS,CAAC;MAC/D,IAAIG,OAAO,GAAGvC,YAAY,CAACqC,KAAK,CAAC;MACjCpC,OAAO,CAACiB,GAAG,CAACqB,OAAO,EAAED,SAAS,CAAC;MAC/B,IAAInB,UAAU,CAACmB,SAAS,CAAC,EAAE;QACzBpC,eAAe,CAACgB,GAAG,CAACqB,OAAO,EAAED,SAAS,CAAC;QACvC,IAAIhC,MAAM,CAACc,qBAAqB,IAAI,IAAI,EAAE;UACxCd,MAAM,CAACc,qBAAqB,GAAGiB,KAAK;QACtC;MACF;IACF,CAAC,CAAC;EACJ;AACF,CAAC;AACD,SAASlB,UAAUA,CAACmB,SAAS,EAAE;EAC7B,OAAOA,SAAS,CAACjC,GAAG,CAAC,MAAM,CAAC,KAAK,UAAU;AAC7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}