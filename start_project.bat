@echo off
echo ========================================
echo    SmartCV AI简历优化平台 - 启动脚本
echo ========================================
echo.

echo 正在检查项目环境...

REM 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未检测到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

REM 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未检测到Node.js，请先安装Node.js 16+
    pause
    exit /b 1
)

echo [✓] Python和Node.js环境检查通过

REM 启动后端服务
echo.
echo 正在启动后端服务...
cd backend
if not exist .env (
    echo [警告] 未找到.env文件，请复制env_example.txt为.env并配置OpenAI API Key
    echo 按任意键继续...
    pause >nul
)

echo 安装后端依赖...
pip install -r requirements.txt

echo 启动Flask后端服务...
start cmd /k "python app.py"

REM 等待后端启动
timeout /t 3 >nul

REM 启动前端服务
echo.
echo 正在启动前端服务...
cd ..\frontend

echo 安装前端依赖...
npm install

echo 启动React前端服务...
start cmd /k "npm start"

echo.
echo ========================================
echo    启动完成！
echo ========================================
echo 后端服务: http://localhost:5000
echo 前端服务: http://localhost:3000
echo.
echo 请确保已在backend/.env文件中配置OpenAI API Key
echo 按任意键退出...
pause >nul 