{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n// TODO clockwise\nimport IndicatorAxis from './IndicatorAxis.js';\nimport IntervalScale from '../../scale/Interval.js';\nimport * as numberUtil from '../../util/number.js';\nimport { map, each, isString, isNumber } from 'zrender/lib/core/util.js';\nimport { alignScaleTicks } from '../axisAlignTicks.js';\nvar Radar = /** @class */function () {\n  function Radar(radarModel, ecModel, api) {\n    /**\r\n     *\r\n     * Radar dimensions\r\n     */\n    this.dimensions = [];\n    this._model = radarModel;\n    this._indicatorAxes = map(radarModel.getIndicatorModels(), function (indicatorModel, idx) {\n      var dim = 'indicator_' + idx;\n      var indicatorAxis = new IndicatorAxis(dim, new IntervalScale()\n      // (indicatorModel.get('axisType') === 'log') ? new LogScale() : new IntervalScale()\n      );\n      indicatorAxis.name = indicatorModel.get('name');\n      // Inject model and axis\n      indicatorAxis.model = indicatorModel;\n      indicatorModel.axis = indicatorAxis;\n      this.dimensions.push(dim);\n      return indicatorAxis;\n    }, this);\n    this.resize(radarModel, api);\n  }\n  Radar.prototype.getIndicatorAxes = function () {\n    return this._indicatorAxes;\n  };\n  Radar.prototype.dataToPoint = function (value, indicatorIndex) {\n    var indicatorAxis = this._indicatorAxes[indicatorIndex];\n    return this.coordToPoint(indicatorAxis.dataToCoord(value), indicatorIndex);\n  };\n  // TODO: API should be coordToPoint([coord, indicatorIndex])\n  Radar.prototype.coordToPoint = function (coord, indicatorIndex) {\n    var indicatorAxis = this._indicatorAxes[indicatorIndex];\n    var angle = indicatorAxis.angle;\n    var x = this.cx + coord * Math.cos(angle);\n    var y = this.cy - coord * Math.sin(angle);\n    return [x, y];\n  };\n  Radar.prototype.pointToData = function (pt) {\n    var dx = pt[0] - this.cx;\n    var dy = pt[1] - this.cy;\n    var radius = Math.sqrt(dx * dx + dy * dy);\n    dx /= radius;\n    dy /= radius;\n    var radian = Math.atan2(-dy, dx);\n    // Find the closest angle\n    // FIXME index can calculated directly\n    var minRadianDiff = Infinity;\n    var closestAxis;\n    var closestAxisIdx = -1;\n    for (var i = 0; i < this._indicatorAxes.length; i++) {\n      var indicatorAxis = this._indicatorAxes[i];\n      var diff = Math.abs(radian - indicatorAxis.angle);\n      if (diff < minRadianDiff) {\n        closestAxis = indicatorAxis;\n        closestAxisIdx = i;\n        minRadianDiff = diff;\n      }\n    }\n    return [closestAxisIdx, +(closestAxis && closestAxis.coordToData(radius))];\n  };\n  Radar.prototype.resize = function (radarModel, api) {\n    var center = radarModel.get('center');\n    var viewWidth = api.getWidth();\n    var viewHeight = api.getHeight();\n    var viewSize = Math.min(viewWidth, viewHeight) / 2;\n    this.cx = numberUtil.parsePercent(center[0], viewWidth);\n    this.cy = numberUtil.parsePercent(center[1], viewHeight);\n    this.startAngle = radarModel.get('startAngle') * Math.PI / 180;\n    // radius may be single value like `20`, `'80%'`, or array like `[10, '80%']`\n    var radius = radarModel.get('radius');\n    if (isString(radius) || isNumber(radius)) {\n      radius = [0, radius];\n    }\n    this.r0 = numberUtil.parsePercent(radius[0], viewSize);\n    this.r = numberUtil.parsePercent(radius[1], viewSize);\n    each(this._indicatorAxes, function (indicatorAxis, idx) {\n      indicatorAxis.setExtent(this.r0, this.r);\n      var angle = this.startAngle + idx * Math.PI * 2 / this._indicatorAxes.length;\n      // Normalize to [-PI, PI]\n      angle = Math.atan2(Math.sin(angle), Math.cos(angle));\n      indicatorAxis.angle = angle;\n    }, this);\n  };\n  Radar.prototype.update = function (ecModel, api) {\n    var indicatorAxes = this._indicatorAxes;\n    var radarModel = this._model;\n    each(indicatorAxes, function (indicatorAxis) {\n      indicatorAxis.scale.setExtent(Infinity, -Infinity);\n    });\n    ecModel.eachSeriesByType('radar', function (radarSeries, idx) {\n      if (radarSeries.get('coordinateSystem') !== 'radar'\n      // @ts-ignore\n      || ecModel.getComponent('radar', radarSeries.get('radarIndex')) !== radarModel) {\n        return;\n      }\n      var data = radarSeries.getData();\n      each(indicatorAxes, function (indicatorAxis) {\n        indicatorAxis.scale.unionExtentFromData(data, data.mapDimension(indicatorAxis.dim));\n      });\n    }, this);\n    var splitNumber = radarModel.get('splitNumber');\n    var dummyScale = new IntervalScale();\n    dummyScale.setExtent(0, splitNumber);\n    dummyScale.setInterval(1);\n    // Force all the axis fixing the maxSplitNumber.\n    each(indicatorAxes, function (indicatorAxis, idx) {\n      alignScaleTicks(indicatorAxis.scale, indicatorAxis.model, dummyScale);\n    });\n  };\n  Radar.prototype.convertToPixel = function (ecModel, finder, value) {\n    console.warn('Not implemented.');\n    return null;\n  };\n  Radar.prototype.convertFromPixel = function (ecModel, finder, pixel) {\n    console.warn('Not implemented.');\n    return null;\n  };\n  Radar.prototype.containPoint = function (point) {\n    console.warn('Not implemented.');\n    return false;\n  };\n  Radar.create = function (ecModel, api) {\n    var radarList = [];\n    ecModel.eachComponent('radar', function (radarModel) {\n      var radar = new Radar(radarModel, ecModel, api);\n      radarList.push(radar);\n      radarModel.coordinateSystem = radar;\n    });\n    ecModel.eachSeriesByType('radar', function (radarSeries) {\n      if (radarSeries.get('coordinateSystem') === 'radar') {\n        // Inject coordinate system\n        // @ts-ignore\n        radarSeries.coordinateSystem = radarList[radarSeries.get('radarIndex') || 0];\n      }\n    });\n    return radarList;\n  };\n  /**\r\n   * Radar dimensions is based on the data\r\n   */\n  Radar.dimensions = [];\n  return Radar;\n}();\nexport default Radar;", "map": {"version": 3, "names": ["IndicatorAxis", "IntervalScale", "numberUtil", "map", "each", "isString", "isNumber", "alignScaleTicks", "Radar", "radarModel", "ecModel", "api", "dimensions", "_model", "_indicatorAxes", "getIndicatorModels", "indicatorModel", "idx", "dim", "indicatorAxis", "name", "get", "model", "axis", "push", "resize", "prototype", "getIndicatorAxes", "dataToPoint", "value", "indicatorIndex", "coordToPoint", "dataToCoord", "coord", "angle", "x", "cx", "Math", "cos", "y", "cy", "sin", "pointToData", "pt", "dx", "dy", "radius", "sqrt", "radian", "atan2", "minRadianDiff", "Infinity", "closestAxis", "closestAxisIdx", "i", "length", "diff", "abs", "coordToData", "center", "viewWidth", "getWidth", "viewHeight", "getHeight", "viewSize", "min", "parsePercent", "startAngle", "PI", "r0", "r", "setExtent", "update", "indicatorAxes", "scale", "eachSeriesByType", "radarSeries", "getComponent", "data", "getData", "unionExtentFromData", "mapDimension", "splitNumber", "dummyScale", "setInterval", "convertToPixel", "finder", "console", "warn", "convertFromPixel", "pixel", "containPoint", "point", "create", "radarList", "eachComponent", "radar", "coordinateSystem"], "sources": ["E:/AI/SmartCV/node_modules/echarts/lib/coord/radar/Radar.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n// TODO clockwise\nimport IndicatorAxis from './IndicatorAxis.js';\nimport IntervalScale from '../../scale/Interval.js';\nimport * as numberUtil from '../../util/number.js';\nimport { map, each, isString, isNumber } from 'zrender/lib/core/util.js';\nimport { alignScaleTicks } from '../axisAlignTicks.js';\nvar Radar = /** @class */function () {\n  function Radar(radarModel, ecModel, api) {\n    /**\r\n     *\r\n     * Radar dimensions\r\n     */\n    this.dimensions = [];\n    this._model = radarModel;\n    this._indicatorAxes = map(radarModel.getIndicatorModels(), function (indicatorModel, idx) {\n      var dim = 'indicator_' + idx;\n      var indicatorAxis = new IndicatorAxis(dim, new IntervalScale()\n      // (indicatorModel.get('axisType') === 'log') ? new LogScale() : new IntervalScale()\n      );\n      indicatorAxis.name = indicatorModel.get('name');\n      // Inject model and axis\n      indicatorAxis.model = indicatorModel;\n      indicatorModel.axis = indicatorAxis;\n      this.dimensions.push(dim);\n      return indicatorAxis;\n    }, this);\n    this.resize(radarModel, api);\n  }\n  Radar.prototype.getIndicatorAxes = function () {\n    return this._indicatorAxes;\n  };\n  Radar.prototype.dataToPoint = function (value, indicatorIndex) {\n    var indicatorAxis = this._indicatorAxes[indicatorIndex];\n    return this.coordToPoint(indicatorAxis.dataToCoord(value), indicatorIndex);\n  };\n  // TODO: API should be coordToPoint([coord, indicatorIndex])\n  Radar.prototype.coordToPoint = function (coord, indicatorIndex) {\n    var indicatorAxis = this._indicatorAxes[indicatorIndex];\n    var angle = indicatorAxis.angle;\n    var x = this.cx + coord * Math.cos(angle);\n    var y = this.cy - coord * Math.sin(angle);\n    return [x, y];\n  };\n  Radar.prototype.pointToData = function (pt) {\n    var dx = pt[0] - this.cx;\n    var dy = pt[1] - this.cy;\n    var radius = Math.sqrt(dx * dx + dy * dy);\n    dx /= radius;\n    dy /= radius;\n    var radian = Math.atan2(-dy, dx);\n    // Find the closest angle\n    // FIXME index can calculated directly\n    var minRadianDiff = Infinity;\n    var closestAxis;\n    var closestAxisIdx = -1;\n    for (var i = 0; i < this._indicatorAxes.length; i++) {\n      var indicatorAxis = this._indicatorAxes[i];\n      var diff = Math.abs(radian - indicatorAxis.angle);\n      if (diff < minRadianDiff) {\n        closestAxis = indicatorAxis;\n        closestAxisIdx = i;\n        minRadianDiff = diff;\n      }\n    }\n    return [closestAxisIdx, +(closestAxis && closestAxis.coordToData(radius))];\n  };\n  Radar.prototype.resize = function (radarModel, api) {\n    var center = radarModel.get('center');\n    var viewWidth = api.getWidth();\n    var viewHeight = api.getHeight();\n    var viewSize = Math.min(viewWidth, viewHeight) / 2;\n    this.cx = numberUtil.parsePercent(center[0], viewWidth);\n    this.cy = numberUtil.parsePercent(center[1], viewHeight);\n    this.startAngle = radarModel.get('startAngle') * Math.PI / 180;\n    // radius may be single value like `20`, `'80%'`, or array like `[10, '80%']`\n    var radius = radarModel.get('radius');\n    if (isString(radius) || isNumber(radius)) {\n      radius = [0, radius];\n    }\n    this.r0 = numberUtil.parsePercent(radius[0], viewSize);\n    this.r = numberUtil.parsePercent(radius[1], viewSize);\n    each(this._indicatorAxes, function (indicatorAxis, idx) {\n      indicatorAxis.setExtent(this.r0, this.r);\n      var angle = this.startAngle + idx * Math.PI * 2 / this._indicatorAxes.length;\n      // Normalize to [-PI, PI]\n      angle = Math.atan2(Math.sin(angle), Math.cos(angle));\n      indicatorAxis.angle = angle;\n    }, this);\n  };\n  Radar.prototype.update = function (ecModel, api) {\n    var indicatorAxes = this._indicatorAxes;\n    var radarModel = this._model;\n    each(indicatorAxes, function (indicatorAxis) {\n      indicatorAxis.scale.setExtent(Infinity, -Infinity);\n    });\n    ecModel.eachSeriesByType('radar', function (radarSeries, idx) {\n      if (radarSeries.get('coordinateSystem') !== 'radar'\n      // @ts-ignore\n      || ecModel.getComponent('radar', radarSeries.get('radarIndex')) !== radarModel) {\n        return;\n      }\n      var data = radarSeries.getData();\n      each(indicatorAxes, function (indicatorAxis) {\n        indicatorAxis.scale.unionExtentFromData(data, data.mapDimension(indicatorAxis.dim));\n      });\n    }, this);\n    var splitNumber = radarModel.get('splitNumber');\n    var dummyScale = new IntervalScale();\n    dummyScale.setExtent(0, splitNumber);\n    dummyScale.setInterval(1);\n    // Force all the axis fixing the maxSplitNumber.\n    each(indicatorAxes, function (indicatorAxis, idx) {\n      alignScaleTicks(indicatorAxis.scale, indicatorAxis.model, dummyScale);\n    });\n  };\n  Radar.prototype.convertToPixel = function (ecModel, finder, value) {\n    console.warn('Not implemented.');\n    return null;\n  };\n  Radar.prototype.convertFromPixel = function (ecModel, finder, pixel) {\n    console.warn('Not implemented.');\n    return null;\n  };\n  Radar.prototype.containPoint = function (point) {\n    console.warn('Not implemented.');\n    return false;\n  };\n  Radar.create = function (ecModel, api) {\n    var radarList = [];\n    ecModel.eachComponent('radar', function (radarModel) {\n      var radar = new Radar(radarModel, ecModel, api);\n      radarList.push(radar);\n      radarModel.coordinateSystem = radar;\n    });\n    ecModel.eachSeriesByType('radar', function (radarSeries) {\n      if (radarSeries.get('coordinateSystem') === 'radar') {\n        // Inject coordinate system\n        // @ts-ignore\n        radarSeries.coordinateSystem = radarList[radarSeries.get('radarIndex') || 0];\n      }\n    });\n    return radarList;\n  };\n  /**\r\n   * Radar dimensions is based on the data\r\n   */\n  Radar.dimensions = [];\n  return Radar;\n}();\nexport default Radar;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,aAAa,MAAM,yBAAyB;AACnD,OAAO,KAAKC,UAAU,MAAM,sBAAsB;AAClD,SAASC,GAAG,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,0BAA0B;AACxE,SAASC,eAAe,QAAQ,sBAAsB;AACtD,IAAIC,KAAK,GAAG,aAAa,YAAY;EACnC,SAASA,KAAKA,CAACC,UAAU,EAAEC,OAAO,EAAEC,GAAG,EAAE;IACvC;AACJ;AACA;AACA;IACI,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,MAAM,GAAGJ,UAAU;IACxB,IAAI,CAACK,cAAc,GAAGX,GAAG,CAACM,UAAU,CAACM,kBAAkB,CAAC,CAAC,EAAE,UAAUC,cAAc,EAAEC,GAAG,EAAE;MACxF,IAAIC,GAAG,GAAG,YAAY,GAAGD,GAAG;MAC5B,IAAIE,aAAa,GAAG,IAAInB,aAAa,CAACkB,GAAG,EAAE,IAAIjB,aAAa,CAAC;MAC7D;MACA,CAAC;MACDkB,aAAa,CAACC,IAAI,GAAGJ,cAAc,CAACK,GAAG,CAAC,MAAM,CAAC;MAC/C;MACAF,aAAa,CAACG,KAAK,GAAGN,cAAc;MACpCA,cAAc,CAACO,IAAI,GAAGJ,aAAa;MACnC,IAAI,CAACP,UAAU,CAACY,IAAI,CAACN,GAAG,CAAC;MACzB,OAAOC,aAAa;IACtB,CAAC,EAAE,IAAI,CAAC;IACR,IAAI,CAACM,MAAM,CAAChB,UAAU,EAAEE,GAAG,CAAC;EAC9B;EACAH,KAAK,CAACkB,SAAS,CAACC,gBAAgB,GAAG,YAAY;IAC7C,OAAO,IAAI,CAACb,cAAc;EAC5B,CAAC;EACDN,KAAK,CAACkB,SAAS,CAACE,WAAW,GAAG,UAAUC,KAAK,EAAEC,cAAc,EAAE;IAC7D,IAAIX,aAAa,GAAG,IAAI,CAACL,cAAc,CAACgB,cAAc,CAAC;IACvD,OAAO,IAAI,CAACC,YAAY,CAACZ,aAAa,CAACa,WAAW,CAACH,KAAK,CAAC,EAAEC,cAAc,CAAC;EAC5E,CAAC;EACD;EACAtB,KAAK,CAACkB,SAAS,CAACK,YAAY,GAAG,UAAUE,KAAK,EAAEH,cAAc,EAAE;IAC9D,IAAIX,aAAa,GAAG,IAAI,CAACL,cAAc,CAACgB,cAAc,CAAC;IACvD,IAAII,KAAK,GAAGf,aAAa,CAACe,KAAK;IAC/B,IAAIC,CAAC,GAAG,IAAI,CAACC,EAAE,GAAGH,KAAK,GAAGI,IAAI,CAACC,GAAG,CAACJ,KAAK,CAAC;IACzC,IAAIK,CAAC,GAAG,IAAI,CAACC,EAAE,GAAGP,KAAK,GAAGI,IAAI,CAACI,GAAG,CAACP,KAAK,CAAC;IACzC,OAAO,CAACC,CAAC,EAAEI,CAAC,CAAC;EACf,CAAC;EACD/B,KAAK,CAACkB,SAAS,CAACgB,WAAW,GAAG,UAAUC,EAAE,EAAE;IAC1C,IAAIC,EAAE,GAAGD,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,CAACP,EAAE;IACxB,IAAIS,EAAE,GAAGF,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,CAACH,EAAE;IACxB,IAAIM,MAAM,GAAGT,IAAI,CAACU,IAAI,CAACH,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC;IACzCD,EAAE,IAAIE,MAAM;IACZD,EAAE,IAAIC,MAAM;IACZ,IAAIE,MAAM,GAAGX,IAAI,CAACY,KAAK,CAAC,CAACJ,EAAE,EAAED,EAAE,CAAC;IAChC;IACA;IACA,IAAIM,aAAa,GAAGC,QAAQ;IAC5B,IAAIC,WAAW;IACf,IAAIC,cAAc,GAAG,CAAC,CAAC;IACvB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACxC,cAAc,CAACyC,MAAM,EAAED,CAAC,EAAE,EAAE;MACnD,IAAInC,aAAa,GAAG,IAAI,CAACL,cAAc,CAACwC,CAAC,CAAC;MAC1C,IAAIE,IAAI,GAAGnB,IAAI,CAACoB,GAAG,CAACT,MAAM,GAAG7B,aAAa,CAACe,KAAK,CAAC;MACjD,IAAIsB,IAAI,GAAGN,aAAa,EAAE;QACxBE,WAAW,GAAGjC,aAAa;QAC3BkC,cAAc,GAAGC,CAAC;QAClBJ,aAAa,GAAGM,IAAI;MACtB;IACF;IACA,OAAO,CAACH,cAAc,EAAE,EAAED,WAAW,IAAIA,WAAW,CAACM,WAAW,CAACZ,MAAM,CAAC,CAAC,CAAC;EAC5E,CAAC;EACDtC,KAAK,CAACkB,SAAS,CAACD,MAAM,GAAG,UAAUhB,UAAU,EAAEE,GAAG,EAAE;IAClD,IAAIgD,MAAM,GAAGlD,UAAU,CAACY,GAAG,CAAC,QAAQ,CAAC;IACrC,IAAIuC,SAAS,GAAGjD,GAAG,CAACkD,QAAQ,CAAC,CAAC;IAC9B,IAAIC,UAAU,GAAGnD,GAAG,CAACoD,SAAS,CAAC,CAAC;IAChC,IAAIC,QAAQ,GAAG3B,IAAI,CAAC4B,GAAG,CAACL,SAAS,EAAEE,UAAU,CAAC,GAAG,CAAC;IAClD,IAAI,CAAC1B,EAAE,GAAGlC,UAAU,CAACgE,YAAY,CAACP,MAAM,CAAC,CAAC,CAAC,EAAEC,SAAS,CAAC;IACvD,IAAI,CAACpB,EAAE,GAAGtC,UAAU,CAACgE,YAAY,CAACP,MAAM,CAAC,CAAC,CAAC,EAAEG,UAAU,CAAC;IACxD,IAAI,CAACK,UAAU,GAAG1D,UAAU,CAACY,GAAG,CAAC,YAAY,CAAC,GAAGgB,IAAI,CAAC+B,EAAE,GAAG,GAAG;IAC9D;IACA,IAAItB,MAAM,GAAGrC,UAAU,CAACY,GAAG,CAAC,QAAQ,CAAC;IACrC,IAAIhB,QAAQ,CAACyC,MAAM,CAAC,IAAIxC,QAAQ,CAACwC,MAAM,CAAC,EAAE;MACxCA,MAAM,GAAG,CAAC,CAAC,EAAEA,MAAM,CAAC;IACtB;IACA,IAAI,CAACuB,EAAE,GAAGnE,UAAU,CAACgE,YAAY,CAACpB,MAAM,CAAC,CAAC,CAAC,EAAEkB,QAAQ,CAAC;IACtD,IAAI,CAACM,CAAC,GAAGpE,UAAU,CAACgE,YAAY,CAACpB,MAAM,CAAC,CAAC,CAAC,EAAEkB,QAAQ,CAAC;IACrD5D,IAAI,CAAC,IAAI,CAACU,cAAc,EAAE,UAAUK,aAAa,EAAEF,GAAG,EAAE;MACtDE,aAAa,CAACoD,SAAS,CAAC,IAAI,CAACF,EAAE,EAAE,IAAI,CAACC,CAAC,CAAC;MACxC,IAAIpC,KAAK,GAAG,IAAI,CAACiC,UAAU,GAAGlD,GAAG,GAAGoB,IAAI,CAAC+B,EAAE,GAAG,CAAC,GAAG,IAAI,CAACtD,cAAc,CAACyC,MAAM;MAC5E;MACArB,KAAK,GAAGG,IAAI,CAACY,KAAK,CAACZ,IAAI,CAACI,GAAG,CAACP,KAAK,CAAC,EAAEG,IAAI,CAACC,GAAG,CAACJ,KAAK,CAAC,CAAC;MACpDf,aAAa,CAACe,KAAK,GAAGA,KAAK;IAC7B,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EACD1B,KAAK,CAACkB,SAAS,CAAC8C,MAAM,GAAG,UAAU9D,OAAO,EAAEC,GAAG,EAAE;IAC/C,IAAI8D,aAAa,GAAG,IAAI,CAAC3D,cAAc;IACvC,IAAIL,UAAU,GAAG,IAAI,CAACI,MAAM;IAC5BT,IAAI,CAACqE,aAAa,EAAE,UAAUtD,aAAa,EAAE;MAC3CA,aAAa,CAACuD,KAAK,CAACH,SAAS,CAACpB,QAAQ,EAAE,CAACA,QAAQ,CAAC;IACpD,CAAC,CAAC;IACFzC,OAAO,CAACiE,gBAAgB,CAAC,OAAO,EAAE,UAAUC,WAAW,EAAE3D,GAAG,EAAE;MAC5D,IAAI2D,WAAW,CAACvD,GAAG,CAAC,kBAAkB,CAAC,KAAK;MAC5C;MAAA,GACGX,OAAO,CAACmE,YAAY,CAAC,OAAO,EAAED,WAAW,CAACvD,GAAG,CAAC,YAAY,CAAC,CAAC,KAAKZ,UAAU,EAAE;QAC9E;MACF;MACA,IAAIqE,IAAI,GAAGF,WAAW,CAACG,OAAO,CAAC,CAAC;MAChC3E,IAAI,CAACqE,aAAa,EAAE,UAAUtD,aAAa,EAAE;QAC3CA,aAAa,CAACuD,KAAK,CAACM,mBAAmB,CAACF,IAAI,EAAEA,IAAI,CAACG,YAAY,CAAC9D,aAAa,CAACD,GAAG,CAAC,CAAC;MACrF,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;IACR,IAAIgE,WAAW,GAAGzE,UAAU,CAACY,GAAG,CAAC,aAAa,CAAC;IAC/C,IAAI8D,UAAU,GAAG,IAAIlF,aAAa,CAAC,CAAC;IACpCkF,UAAU,CAACZ,SAAS,CAAC,CAAC,EAAEW,WAAW,CAAC;IACpCC,UAAU,CAACC,WAAW,CAAC,CAAC,CAAC;IACzB;IACAhF,IAAI,CAACqE,aAAa,EAAE,UAAUtD,aAAa,EAAEF,GAAG,EAAE;MAChDV,eAAe,CAACY,aAAa,CAACuD,KAAK,EAAEvD,aAAa,CAACG,KAAK,EAAE6D,UAAU,CAAC;IACvE,CAAC,CAAC;EACJ,CAAC;EACD3E,KAAK,CAACkB,SAAS,CAAC2D,cAAc,GAAG,UAAU3E,OAAO,EAAE4E,MAAM,EAAEzD,KAAK,EAAE;IACjE0D,OAAO,CAACC,IAAI,CAAC,kBAAkB,CAAC;IAChC,OAAO,IAAI;EACb,CAAC;EACDhF,KAAK,CAACkB,SAAS,CAAC+D,gBAAgB,GAAG,UAAU/E,OAAO,EAAE4E,MAAM,EAAEI,KAAK,EAAE;IACnEH,OAAO,CAACC,IAAI,CAAC,kBAAkB,CAAC;IAChC,OAAO,IAAI;EACb,CAAC;EACDhF,KAAK,CAACkB,SAAS,CAACiE,YAAY,GAAG,UAAUC,KAAK,EAAE;IAC9CL,OAAO,CAACC,IAAI,CAAC,kBAAkB,CAAC;IAChC,OAAO,KAAK;EACd,CAAC;EACDhF,KAAK,CAACqF,MAAM,GAAG,UAAUnF,OAAO,EAAEC,GAAG,EAAE;IACrC,IAAImF,SAAS,GAAG,EAAE;IAClBpF,OAAO,CAACqF,aAAa,CAAC,OAAO,EAAE,UAAUtF,UAAU,EAAE;MACnD,IAAIuF,KAAK,GAAG,IAAIxF,KAAK,CAACC,UAAU,EAAEC,OAAO,EAAEC,GAAG,CAAC;MAC/CmF,SAAS,CAACtE,IAAI,CAACwE,KAAK,CAAC;MACrBvF,UAAU,CAACwF,gBAAgB,GAAGD,KAAK;IACrC,CAAC,CAAC;IACFtF,OAAO,CAACiE,gBAAgB,CAAC,OAAO,EAAE,UAAUC,WAAW,EAAE;MACvD,IAAIA,WAAW,CAACvD,GAAG,CAAC,kBAAkB,CAAC,KAAK,OAAO,EAAE;QACnD;QACA;QACAuD,WAAW,CAACqB,gBAAgB,GAAGH,SAAS,CAAClB,WAAW,CAACvD,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;MAC9E;IACF,CAAC,CAAC;IACF,OAAOyE,SAAS;EAClB,CAAC;EACD;AACF;AACA;EACEtF,KAAK,CAACI,UAAU,GAAG,EAAE;EACrB,OAAOJ,KAAK;AACd,CAAC,CAAC,CAAC;AACH,eAAeA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}