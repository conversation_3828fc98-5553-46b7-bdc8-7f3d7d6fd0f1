{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { makeInner, normalizeToArray } from '../util/model.js';\nimport { assert, bind, each, eqNaN, extend, hasOwn, indexOf, isArrayLike, keys, reduce } from 'zrender/lib/core/util.js';\nimport { cloneValue } from 'zrender/lib/animation/Animator.js';\nimport Displayable from 'zrender/lib/graphic/Displayable.js';\nimport { getAnimationConfig } from './basicTransition.js';\nimport { Path } from '../util/graphic.js';\nimport { warn } from '../util/log.js';\nimport { TRANSFORMABLE_PROPS } from 'zrender/lib/core/Transformable.js';\nvar LEGACY_TRANSFORM_PROPS_MAP = {\n  position: ['x', 'y'],\n  scale: ['scaleX', 'scaleY'],\n  origin: ['originX', 'originY']\n};\nvar LEGACY_TRANSFORM_PROPS = keys(LEGACY_TRANSFORM_PROPS_MAP);\nvar TRANSFORM_PROPS_MAP = reduce(TRANSFORMABLE_PROPS, function (obj, key) {\n  obj[key] = 1;\n  return obj;\n}, {});\nvar transformPropNamesStr = TRANSFORMABLE_PROPS.join(', ');\n// '' means root\nexport var ELEMENT_ANIMATABLE_PROPS = ['', 'style', 'shape', 'extra'];\n;\nvar transitionInnerStore = makeInner();\n;\nfunction getElementAnimationConfig(animationType, el, elOption, parentModel, dataIndex) {\n  var animationProp = animationType + \"Animation\";\n  var config = getAnimationConfig(animationType, parentModel, dataIndex) || {};\n  var userDuring = transitionInnerStore(el).userDuring;\n  // Only set when duration is > 0 and it's need to be animated.\n  if (config.duration > 0) {\n    // For simplicity, if during not specified, the previous during will not work any more.\n    config.during = userDuring ? bind(duringCall, {\n      el: el,\n      userDuring: userDuring\n    }) : null;\n    config.setToFinal = true;\n    config.scope = animationType;\n  }\n  extend(config, elOption[animationProp]);\n  return config;\n}\nexport function applyUpdateTransition(el, elOption, animatableModel, opts) {\n  opts = opts || {};\n  var dataIndex = opts.dataIndex,\n    isInit = opts.isInit,\n    clearStyle = opts.clearStyle;\n  var hasAnimation = animatableModel.isAnimationEnabled();\n  // Save the meta info for further morphing. Like apply on the sub morphing elements.\n  var store = transitionInnerStore(el);\n  var styleOpt = elOption.style;\n  store.userDuring = elOption.during;\n  var transFromProps = {};\n  var propsToSet = {};\n  prepareTransformAllPropsFinal(el, elOption, propsToSet);\n  prepareShapeOrExtraAllPropsFinal('shape', elOption, propsToSet);\n  prepareShapeOrExtraAllPropsFinal('extra', elOption, propsToSet);\n  if (!isInit && hasAnimation) {\n    prepareTransformTransitionFrom(el, elOption, transFromProps);\n    prepareShapeOrExtraTransitionFrom('shape', el, elOption, transFromProps);\n    prepareShapeOrExtraTransitionFrom('extra', el, elOption, transFromProps);\n    prepareStyleTransitionFrom(el, elOption, styleOpt, transFromProps);\n  }\n  propsToSet.style = styleOpt;\n  applyPropsDirectly(el, propsToSet, clearStyle);\n  applyMiscProps(el, elOption);\n  if (hasAnimation) {\n    if (isInit) {\n      var enterFromProps_1 = {};\n      each(ELEMENT_ANIMATABLE_PROPS, function (propName) {\n        var prop = propName ? elOption[propName] : elOption;\n        if (prop && prop.enterFrom) {\n          if (propName) {\n            enterFromProps_1[propName] = enterFromProps_1[propName] || {};\n          }\n          extend(propName ? enterFromProps_1[propName] : enterFromProps_1, prop.enterFrom);\n        }\n      });\n      var config = getElementAnimationConfig('enter', el, elOption, animatableModel, dataIndex);\n      if (config.duration > 0) {\n        el.animateFrom(enterFromProps_1, config);\n      }\n    } else {\n      applyPropsTransition(el, elOption, dataIndex || 0, animatableModel, transFromProps);\n    }\n  }\n  // Store leave to be used in leave transition.\n  updateLeaveTo(el, elOption);\n  styleOpt ? el.dirty() : el.markRedraw();\n}\nexport function updateLeaveTo(el, elOption) {\n  // Try merge to previous set leaveTo\n  var leaveToProps = transitionInnerStore(el).leaveToProps;\n  for (var i = 0; i < ELEMENT_ANIMATABLE_PROPS.length; i++) {\n    var propName = ELEMENT_ANIMATABLE_PROPS[i];\n    var prop = propName ? elOption[propName] : elOption;\n    if (prop && prop.leaveTo) {\n      if (!leaveToProps) {\n        leaveToProps = transitionInnerStore(el).leaveToProps = {};\n      }\n      if (propName) {\n        leaveToProps[propName] = leaveToProps[propName] || {};\n      }\n      extend(propName ? leaveToProps[propName] : leaveToProps, prop.leaveTo);\n    }\n  }\n}\nexport function applyLeaveTransition(el, elOption, animatableModel, onRemove) {\n  if (el) {\n    var parent_1 = el.parent;\n    var leaveToProps = transitionInnerStore(el).leaveToProps;\n    if (leaveToProps) {\n      // TODO TODO use leave after leaveAnimation in series is introduced\n      // TODO Data index?\n      var config = getElementAnimationConfig('update', el, elOption, animatableModel, 0);\n      config.done = function () {\n        parent_1.remove(el);\n        onRemove && onRemove();\n      };\n      el.animateTo(leaveToProps, config);\n    } else {\n      parent_1.remove(el);\n      onRemove && onRemove();\n    }\n  }\n}\nexport function isTransitionAll(transition) {\n  return transition === 'all';\n}\nfunction applyPropsDirectly(el,\n// Can be null/undefined\nallPropsFinal, clearStyle) {\n  var styleOpt = allPropsFinal.style;\n  if (!el.isGroup && styleOpt) {\n    if (clearStyle) {\n      el.useStyle({});\n      // When style object changed, how to trade the existing animation?\n      // It is probably complicated and not needed to cover all the cases.\n      // But still need consider the case:\n      // (1) When using init animation on `style.opacity`, and before the animation\n      //     ended users triggers an update by mousewhel. At that time the init\n      //     animation should better be continued rather than terminated.\n      //     So after `useStyle` called, we should change the animation target manually\n      //     to continue the effect of the init animation.\n      // (2) PENDING: If the previous animation targeted at a `val1`, and currently we need\n      //     to update the value to `val2` and no animation declared, should be terminate\n      //     the previous animation or just modify the target of the animation?\n      //     Therotically That will happen not only on `style` but also on `shape` and\n      //     `transfrom` props. But we haven't handle this case at present yet.\n      // (3) PENDING: Is it proper to visit `animators` and `targetName`?\n      var animators = el.animators;\n      for (var i = 0; i < animators.length; i++) {\n        var animator = animators[i];\n        // targetName is the \"topKey\".\n        if (animator.targetName === 'style') {\n          animator.changeTarget(el.style);\n        }\n      }\n    }\n    el.setStyle(styleOpt);\n  }\n  if (allPropsFinal) {\n    // Not set style here.\n    allPropsFinal.style = null;\n    // Set el to the final state firstly.\n    allPropsFinal && el.attr(allPropsFinal);\n    allPropsFinal.style = styleOpt;\n  }\n}\nfunction applyPropsTransition(el, elOption, dataIndex, model,\n// Can be null/undefined\ntransFromProps) {\n  if (transFromProps) {\n    var config = getElementAnimationConfig('update', el, elOption, model, dataIndex);\n    if (config.duration > 0) {\n      el.animateFrom(transFromProps, config);\n    }\n  }\n}\nfunction applyMiscProps(el, elOption) {\n  // Merge by default.\n  hasOwn(elOption, 'silent') && (el.silent = elOption.silent);\n  hasOwn(elOption, 'ignore') && (el.ignore = elOption.ignore);\n  if (el instanceof Displayable) {\n    hasOwn(elOption, 'invisible') && (el.invisible = elOption.invisible);\n  }\n  if (el instanceof Path) {\n    hasOwn(elOption, 'autoBatch') && (el.autoBatch = elOption.autoBatch);\n  }\n}\n// Use it to avoid it be exposed to user.\nvar tmpDuringScope = {};\nvar transitionDuringAPI = {\n  // Usually other props do not need to be changed in animation during.\n  setTransform: function (key, val) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(hasOwn(TRANSFORM_PROPS_MAP, key), 'Only ' + transformPropNamesStr + ' available in `setTransform`.');\n    }\n    tmpDuringScope.el[key] = val;\n    return this;\n  },\n  getTransform: function (key) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(hasOwn(TRANSFORM_PROPS_MAP, key), 'Only ' + transformPropNamesStr + ' available in `getTransform`.');\n    }\n    return tmpDuringScope.el[key];\n  },\n  setShape: function (key, val) {\n    if (process.env.NODE_ENV !== 'production') {\n      assertNotReserved(key);\n    }\n    var el = tmpDuringScope.el;\n    var shape = el.shape || (el.shape = {});\n    shape[key] = val;\n    el.dirtyShape && el.dirtyShape();\n    return this;\n  },\n  getShape: function (key) {\n    if (process.env.NODE_ENV !== 'production') {\n      assertNotReserved(key);\n    }\n    var shape = tmpDuringScope.el.shape;\n    if (shape) {\n      return shape[key];\n    }\n  },\n  setStyle: function (key, val) {\n    if (process.env.NODE_ENV !== 'production') {\n      assertNotReserved(key);\n    }\n    var el = tmpDuringScope.el;\n    var style = el.style;\n    if (style) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (eqNaN(val)) {\n          warn('style.' + key + ' must not be assigned with NaN.');\n        }\n      }\n      style[key] = val;\n      el.dirtyStyle && el.dirtyStyle();\n    }\n    return this;\n  },\n  getStyle: function (key) {\n    if (process.env.NODE_ENV !== 'production') {\n      assertNotReserved(key);\n    }\n    var style = tmpDuringScope.el.style;\n    if (style) {\n      return style[key];\n    }\n  },\n  setExtra: function (key, val) {\n    if (process.env.NODE_ENV !== 'production') {\n      assertNotReserved(key);\n    }\n    var extra = tmpDuringScope.el.extra || (tmpDuringScope.el.extra = {});\n    extra[key] = val;\n    return this;\n  },\n  getExtra: function (key) {\n    if (process.env.NODE_ENV !== 'production') {\n      assertNotReserved(key);\n    }\n    var extra = tmpDuringScope.el.extra;\n    if (extra) {\n      return extra[key];\n    }\n  }\n};\nfunction assertNotReserved(key) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (key === 'transition' || key === 'enterFrom' || key === 'leaveTo') {\n      throw new Error('key must not be \"' + key + '\"');\n    }\n  }\n}\nfunction duringCall() {\n  // Do not provide \"percent\" until some requirements come.\n  // Because consider thies case:\n  // enterFrom: {x: 100, y: 30}, transition: 'x'.\n  // And enter duration is different from update duration.\n  // Thus it might be confused about the meaning of \"percent\" in during callback.\n  var scope = this;\n  var el = scope.el;\n  if (!el) {\n    return;\n  }\n  // If el is remove from zr by reason like legend, during still need to called,\n  // because el will be added back to zr and the prop value should not be incorrect.\n  var latestUserDuring = transitionInnerStore(el).userDuring;\n  var scopeUserDuring = scope.userDuring;\n  // Ensured a during is only called once in each animation frame.\n  // If a during is called multiple times in one frame, maybe some users' calculation logic\n  // might be wrong (not sure whether this usage exists).\n  // The case of a during might be called twice can be: by default there is a animator for\n  // 'x', 'y' when init. Before the init animation finished, call `setOption` to start\n  // another animators for 'style'/'shape'/'extra'.\n  if (latestUserDuring !== scopeUserDuring) {\n    // release\n    scope.el = scope.userDuring = null;\n    return;\n  }\n  tmpDuringScope.el = el;\n  // Give no `this` to user in \"during\" calling.\n  scopeUserDuring(transitionDuringAPI);\n  // FIXME: if in future meet the case that some prop will be both modified in `during` and `state`,\n  // consider the issue that the prop might be incorrect when return to \"normal\" state.\n}\nfunction prepareShapeOrExtraTransitionFrom(mainAttr, fromEl, elOption, transFromProps) {\n  var attrOpt = elOption[mainAttr];\n  if (!attrOpt) {\n    return;\n  }\n  var elPropsInAttr = fromEl[mainAttr];\n  var transFromPropsInAttr;\n  if (elPropsInAttr) {\n    var transition = elOption.transition;\n    var attrTransition = attrOpt.transition;\n    if (attrTransition) {\n      !transFromPropsInAttr && (transFromPropsInAttr = transFromProps[mainAttr] = {});\n      if (isTransitionAll(attrTransition)) {\n        extend(transFromPropsInAttr, elPropsInAttr);\n      } else {\n        var transitionKeys = normalizeToArray(attrTransition);\n        for (var i = 0; i < transitionKeys.length; i++) {\n          var key = transitionKeys[i];\n          var elVal = elPropsInAttr[key];\n          transFromPropsInAttr[key] = elVal;\n        }\n      }\n    } else if (isTransitionAll(transition) || indexOf(transition, mainAttr) >= 0) {\n      !transFromPropsInAttr && (transFromPropsInAttr = transFromProps[mainAttr] = {});\n      var elPropsInAttrKeys = keys(elPropsInAttr);\n      for (var i = 0; i < elPropsInAttrKeys.length; i++) {\n        var key = elPropsInAttrKeys[i];\n        var elVal = elPropsInAttr[key];\n        if (isNonStyleTransitionEnabled(attrOpt[key], elVal)) {\n          transFromPropsInAttr[key] = elVal;\n        }\n      }\n    }\n  }\n}\nfunction prepareShapeOrExtraAllPropsFinal(mainAttr, elOption, allProps) {\n  var attrOpt = elOption[mainAttr];\n  if (!attrOpt) {\n    return;\n  }\n  var allPropsInAttr = allProps[mainAttr] = {};\n  var keysInAttr = keys(attrOpt);\n  for (var i = 0; i < keysInAttr.length; i++) {\n    var key = keysInAttr[i];\n    // To avoid share one object with different element, and\n    // to avoid user modify the object inexpectedly, have to clone.\n    allPropsInAttr[key] = cloneValue(attrOpt[key]);\n  }\n}\nfunction prepareTransformTransitionFrom(el, elOption, transFromProps) {\n  var transition = elOption.transition;\n  var transitionKeys = isTransitionAll(transition) ? TRANSFORMABLE_PROPS : normalizeToArray(transition || []);\n  for (var i = 0; i < transitionKeys.length; i++) {\n    var key = transitionKeys[i];\n    if (key === 'style' || key === 'shape' || key === 'extra') {\n      continue;\n    }\n    var elVal = el[key];\n    if (process.env.NODE_ENV !== 'production') {\n      checkTransformPropRefer(key, 'el.transition');\n    }\n    // Do not clone, animator will perform that clone.\n    transFromProps[key] = elVal;\n  }\n}\nfunction prepareTransformAllPropsFinal(el, elOption, allProps) {\n  for (var i = 0; i < LEGACY_TRANSFORM_PROPS.length; i++) {\n    var legacyName = LEGACY_TRANSFORM_PROPS[i];\n    var xyName = LEGACY_TRANSFORM_PROPS_MAP[legacyName];\n    var legacyArr = elOption[legacyName];\n    if (legacyArr) {\n      allProps[xyName[0]] = legacyArr[0];\n      allProps[xyName[1]] = legacyArr[1];\n    }\n  }\n  for (var i = 0; i < TRANSFORMABLE_PROPS.length; i++) {\n    var key = TRANSFORMABLE_PROPS[i];\n    if (elOption[key] != null) {\n      allProps[key] = elOption[key];\n    }\n  }\n}\nfunction prepareStyleTransitionFrom(fromEl, elOption, styleOpt, transFromProps) {\n  if (!styleOpt) {\n    return;\n  }\n  var fromElStyle = fromEl.style;\n  var transFromStyleProps;\n  if (fromElStyle) {\n    var styleTransition = styleOpt.transition;\n    var elTransition = elOption.transition;\n    if (styleTransition && !isTransitionAll(styleTransition)) {\n      var transitionKeys = normalizeToArray(styleTransition);\n      !transFromStyleProps && (transFromStyleProps = transFromProps.style = {});\n      for (var i = 0; i < transitionKeys.length; i++) {\n        var key = transitionKeys[i];\n        var elVal = fromElStyle[key];\n        // Do not clone, see `checkNonStyleTansitionRefer`.\n        transFromStyleProps[key] = elVal;\n      }\n    } else if (fromEl.getAnimationStyleProps && (isTransitionAll(elTransition) || isTransitionAll(styleTransition) || indexOf(elTransition, 'style') >= 0)) {\n      var animationProps = fromEl.getAnimationStyleProps();\n      var animationStyleProps = animationProps ? animationProps.style : null;\n      if (animationStyleProps) {\n        !transFromStyleProps && (transFromStyleProps = transFromProps.style = {});\n        var styleKeys = keys(styleOpt);\n        for (var i = 0; i < styleKeys.length; i++) {\n          var key = styleKeys[i];\n          if (animationStyleProps[key]) {\n            var elVal = fromElStyle[key];\n            transFromStyleProps[key] = elVal;\n          }\n        }\n      }\n    }\n  }\n}\nfunction isNonStyleTransitionEnabled(optVal, elVal) {\n  // The same as `checkNonStyleTansitionRefer`.\n  return !isArrayLike(optVal) ? optVal != null && isFinite(optVal) : optVal !== elVal;\n}\nvar checkTransformPropRefer;\nif (process.env.NODE_ENV !== 'production') {\n  checkTransformPropRefer = function (key, usedIn) {\n    if (!hasOwn(TRANSFORM_PROPS_MAP, key)) {\n      warn('Prop `' + key + '` is not a permitted in `' + usedIn + '`. ' + 'Only `' + keys(TRANSFORM_PROPS_MAP).join('`, `') + '` are permitted.');\n    }\n  };\n}", "map": {"version": 3, "names": ["makeInner", "normalizeToArray", "assert", "bind", "each", "eqNaN", "extend", "hasOwn", "indexOf", "isArrayLike", "keys", "reduce", "cloneValue", "Displayable", "getAnimationConfig", "Path", "warn", "TRANSFORMABLE_PROPS", "LEGACY_TRANSFORM_PROPS_MAP", "position", "scale", "origin", "LEGACY_TRANSFORM_PROPS", "TRANSFORM_PROPS_MAP", "obj", "key", "transformPropNamesStr", "join", "ELEMENT_ANIMATABLE_PROPS", "transitionInnerStore", "getElementAnimationConfig", "animationType", "el", "elOption", "parentModel", "dataIndex", "animationProp", "config", "userDuring", "duration", "during", "duringCall", "setToFinal", "scope", "applyUpdateTransition", "animatableModel", "opts", "isInit", "clearStyle", "hasAnimation", "isAnimationEnabled", "store", "styleOpt", "style", "transFromProps", "propsToSet", "prepareTransformAllPropsFinal", "prepareShapeOrExtraAllPropsFinal", "prepareTransformTransitionFrom", "prepareShapeOrExtraTransitionFrom", "prepareStyleTransitionFrom", "applyPropsDirectly", "applyMiscProps", "enterFromProps_1", "propName", "prop", "enterFrom", "animateFrom", "applyPropsTransition", "updateLeaveTo", "dirty", "mark<PERSON><PERSON><PERSON>", "leaveToProps", "i", "length", "leaveTo", "applyLeaveTransition", "onRemove", "parent_1", "parent", "done", "remove", "animateTo", "isTransitionAll", "transition", "allPropsFinal", "isGroup", "useStyle", "animators", "animator", "targetName", "change<PERSON>arget", "setStyle", "attr", "model", "silent", "ignore", "invisible", "autoBatch", "tmpDuringScope", "transitionDuringAPI", "setTransform", "val", "process", "env", "NODE_ENV", "getTransform", "setShape", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shape", "dirtyShape", "getShape", "dirtyStyle", "getStyle", "setExtra", "extra", "getExtra", "Error", "latestUserDuring", "scopeUserDuring", "mainAttr", "fromEl", "attrOpt", "elPropsInAttr", "transFromPropsInAttr", "attrTransition", "<PERSON><PERSON><PERSON><PERSON>", "elVal", "elPropsInAttrKeys", "isNonStyleTransitionEnabled", "allProps", "allPropsInAttr", "keysInAttr", "checkTransformPropRefer", "legacyName", "xyName", "legacyArr", "fromElStyle", "transFromStyleProps", "styleTransition", "elTransition", "getAnimationStyleProps", "animationProps", "animationStyleProps", "styleKeys", "optVal", "isFinite", "usedIn"], "sources": ["E:/AI/SmartCV/node_modules/echarts/lib/animation/customGraphicTransition.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { makeInner, normalizeToArray } from '../util/model.js';\nimport { assert, bind, each, eqNaN, extend, hasOwn, indexOf, isArrayLike, keys, reduce } from 'zrender/lib/core/util.js';\nimport { cloneValue } from 'zrender/lib/animation/Animator.js';\nimport Displayable from 'zrender/lib/graphic/Displayable.js';\nimport { getAnimationConfig } from './basicTransition.js';\nimport { Path } from '../util/graphic.js';\nimport { warn } from '../util/log.js';\nimport { TRANSFORMABLE_PROPS } from 'zrender/lib/core/Transformable.js';\nvar LEGACY_TRANSFORM_PROPS_MAP = {\n  position: ['x', 'y'],\n  scale: ['scaleX', 'scaleY'],\n  origin: ['originX', 'originY']\n};\nvar LEGACY_TRANSFORM_PROPS = keys(LEGACY_TRANSFORM_PROPS_MAP);\nvar TRANSFORM_PROPS_MAP = reduce(TRANSFORMABLE_PROPS, function (obj, key) {\n  obj[key] = 1;\n  return obj;\n}, {});\nvar transformPropNamesStr = TRANSFORMABLE_PROPS.join(', ');\n// '' means root\nexport var ELEMENT_ANIMATABLE_PROPS = ['', 'style', 'shape', 'extra'];\n;\nvar transitionInnerStore = makeInner();\n;\nfunction getElementAnimationConfig(animationType, el, elOption, parentModel, dataIndex) {\n  var animationProp = animationType + \"Animation\";\n  var config = getAnimationConfig(animationType, parentModel, dataIndex) || {};\n  var userDuring = transitionInnerStore(el).userDuring;\n  // Only set when duration is > 0 and it's need to be animated.\n  if (config.duration > 0) {\n    // For simplicity, if during not specified, the previous during will not work any more.\n    config.during = userDuring ? bind(duringCall, {\n      el: el,\n      userDuring: userDuring\n    }) : null;\n    config.setToFinal = true;\n    config.scope = animationType;\n  }\n  extend(config, elOption[animationProp]);\n  return config;\n}\nexport function applyUpdateTransition(el, elOption, animatableModel, opts) {\n  opts = opts || {};\n  var dataIndex = opts.dataIndex,\n    isInit = opts.isInit,\n    clearStyle = opts.clearStyle;\n  var hasAnimation = animatableModel.isAnimationEnabled();\n  // Save the meta info for further morphing. Like apply on the sub morphing elements.\n  var store = transitionInnerStore(el);\n  var styleOpt = elOption.style;\n  store.userDuring = elOption.during;\n  var transFromProps = {};\n  var propsToSet = {};\n  prepareTransformAllPropsFinal(el, elOption, propsToSet);\n  prepareShapeOrExtraAllPropsFinal('shape', elOption, propsToSet);\n  prepareShapeOrExtraAllPropsFinal('extra', elOption, propsToSet);\n  if (!isInit && hasAnimation) {\n    prepareTransformTransitionFrom(el, elOption, transFromProps);\n    prepareShapeOrExtraTransitionFrom('shape', el, elOption, transFromProps);\n    prepareShapeOrExtraTransitionFrom('extra', el, elOption, transFromProps);\n    prepareStyleTransitionFrom(el, elOption, styleOpt, transFromProps);\n  }\n  propsToSet.style = styleOpt;\n  applyPropsDirectly(el, propsToSet, clearStyle);\n  applyMiscProps(el, elOption);\n  if (hasAnimation) {\n    if (isInit) {\n      var enterFromProps_1 = {};\n      each(ELEMENT_ANIMATABLE_PROPS, function (propName) {\n        var prop = propName ? elOption[propName] : elOption;\n        if (prop && prop.enterFrom) {\n          if (propName) {\n            enterFromProps_1[propName] = enterFromProps_1[propName] || {};\n          }\n          extend(propName ? enterFromProps_1[propName] : enterFromProps_1, prop.enterFrom);\n        }\n      });\n      var config = getElementAnimationConfig('enter', el, elOption, animatableModel, dataIndex);\n      if (config.duration > 0) {\n        el.animateFrom(enterFromProps_1, config);\n      }\n    } else {\n      applyPropsTransition(el, elOption, dataIndex || 0, animatableModel, transFromProps);\n    }\n  }\n  // Store leave to be used in leave transition.\n  updateLeaveTo(el, elOption);\n  styleOpt ? el.dirty() : el.markRedraw();\n}\nexport function updateLeaveTo(el, elOption) {\n  // Try merge to previous set leaveTo\n  var leaveToProps = transitionInnerStore(el).leaveToProps;\n  for (var i = 0; i < ELEMENT_ANIMATABLE_PROPS.length; i++) {\n    var propName = ELEMENT_ANIMATABLE_PROPS[i];\n    var prop = propName ? elOption[propName] : elOption;\n    if (prop && prop.leaveTo) {\n      if (!leaveToProps) {\n        leaveToProps = transitionInnerStore(el).leaveToProps = {};\n      }\n      if (propName) {\n        leaveToProps[propName] = leaveToProps[propName] || {};\n      }\n      extend(propName ? leaveToProps[propName] : leaveToProps, prop.leaveTo);\n    }\n  }\n}\nexport function applyLeaveTransition(el, elOption, animatableModel, onRemove) {\n  if (el) {\n    var parent_1 = el.parent;\n    var leaveToProps = transitionInnerStore(el).leaveToProps;\n    if (leaveToProps) {\n      // TODO TODO use leave after leaveAnimation in series is introduced\n      // TODO Data index?\n      var config = getElementAnimationConfig('update', el, elOption, animatableModel, 0);\n      config.done = function () {\n        parent_1.remove(el);\n        onRemove && onRemove();\n      };\n      el.animateTo(leaveToProps, config);\n    } else {\n      parent_1.remove(el);\n      onRemove && onRemove();\n    }\n  }\n}\nexport function isTransitionAll(transition) {\n  return transition === 'all';\n}\nfunction applyPropsDirectly(el,\n// Can be null/undefined\nallPropsFinal, clearStyle) {\n  var styleOpt = allPropsFinal.style;\n  if (!el.isGroup && styleOpt) {\n    if (clearStyle) {\n      el.useStyle({});\n      // When style object changed, how to trade the existing animation?\n      // It is probably complicated and not needed to cover all the cases.\n      // But still need consider the case:\n      // (1) When using init animation on `style.opacity`, and before the animation\n      //     ended users triggers an update by mousewhel. At that time the init\n      //     animation should better be continued rather than terminated.\n      //     So after `useStyle` called, we should change the animation target manually\n      //     to continue the effect of the init animation.\n      // (2) PENDING: If the previous animation targeted at a `val1`, and currently we need\n      //     to update the value to `val2` and no animation declared, should be terminate\n      //     the previous animation or just modify the target of the animation?\n      //     Therotically That will happen not only on `style` but also on `shape` and\n      //     `transfrom` props. But we haven't handle this case at present yet.\n      // (3) PENDING: Is it proper to visit `animators` and `targetName`?\n      var animators = el.animators;\n      for (var i = 0; i < animators.length; i++) {\n        var animator = animators[i];\n        // targetName is the \"topKey\".\n        if (animator.targetName === 'style') {\n          animator.changeTarget(el.style);\n        }\n      }\n    }\n    el.setStyle(styleOpt);\n  }\n  if (allPropsFinal) {\n    // Not set style here.\n    allPropsFinal.style = null;\n    // Set el to the final state firstly.\n    allPropsFinal && el.attr(allPropsFinal);\n    allPropsFinal.style = styleOpt;\n  }\n}\nfunction applyPropsTransition(el, elOption, dataIndex, model,\n// Can be null/undefined\ntransFromProps) {\n  if (transFromProps) {\n    var config = getElementAnimationConfig('update', el, elOption, model, dataIndex);\n    if (config.duration > 0) {\n      el.animateFrom(transFromProps, config);\n    }\n  }\n}\nfunction applyMiscProps(el, elOption) {\n  // Merge by default.\n  hasOwn(elOption, 'silent') && (el.silent = elOption.silent);\n  hasOwn(elOption, 'ignore') && (el.ignore = elOption.ignore);\n  if (el instanceof Displayable) {\n    hasOwn(elOption, 'invisible') && (el.invisible = elOption.invisible);\n  }\n  if (el instanceof Path) {\n    hasOwn(elOption, 'autoBatch') && (el.autoBatch = elOption.autoBatch);\n  }\n}\n// Use it to avoid it be exposed to user.\nvar tmpDuringScope = {};\nvar transitionDuringAPI = {\n  // Usually other props do not need to be changed in animation during.\n  setTransform: function (key, val) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(hasOwn(TRANSFORM_PROPS_MAP, key), 'Only ' + transformPropNamesStr + ' available in `setTransform`.');\n    }\n    tmpDuringScope.el[key] = val;\n    return this;\n  },\n  getTransform: function (key) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(hasOwn(TRANSFORM_PROPS_MAP, key), 'Only ' + transformPropNamesStr + ' available in `getTransform`.');\n    }\n    return tmpDuringScope.el[key];\n  },\n  setShape: function (key, val) {\n    if (process.env.NODE_ENV !== 'production') {\n      assertNotReserved(key);\n    }\n    var el = tmpDuringScope.el;\n    var shape = el.shape || (el.shape = {});\n    shape[key] = val;\n    el.dirtyShape && el.dirtyShape();\n    return this;\n  },\n  getShape: function (key) {\n    if (process.env.NODE_ENV !== 'production') {\n      assertNotReserved(key);\n    }\n    var shape = tmpDuringScope.el.shape;\n    if (shape) {\n      return shape[key];\n    }\n  },\n  setStyle: function (key, val) {\n    if (process.env.NODE_ENV !== 'production') {\n      assertNotReserved(key);\n    }\n    var el = tmpDuringScope.el;\n    var style = el.style;\n    if (style) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (eqNaN(val)) {\n          warn('style.' + key + ' must not be assigned with NaN.');\n        }\n      }\n      style[key] = val;\n      el.dirtyStyle && el.dirtyStyle();\n    }\n    return this;\n  },\n  getStyle: function (key) {\n    if (process.env.NODE_ENV !== 'production') {\n      assertNotReserved(key);\n    }\n    var style = tmpDuringScope.el.style;\n    if (style) {\n      return style[key];\n    }\n  },\n  setExtra: function (key, val) {\n    if (process.env.NODE_ENV !== 'production') {\n      assertNotReserved(key);\n    }\n    var extra = tmpDuringScope.el.extra || (tmpDuringScope.el.extra = {});\n    extra[key] = val;\n    return this;\n  },\n  getExtra: function (key) {\n    if (process.env.NODE_ENV !== 'production') {\n      assertNotReserved(key);\n    }\n    var extra = tmpDuringScope.el.extra;\n    if (extra) {\n      return extra[key];\n    }\n  }\n};\nfunction assertNotReserved(key) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (key === 'transition' || key === 'enterFrom' || key === 'leaveTo') {\n      throw new Error('key must not be \"' + key + '\"');\n    }\n  }\n}\nfunction duringCall() {\n  // Do not provide \"percent\" until some requirements come.\n  // Because consider thies case:\n  // enterFrom: {x: 100, y: 30}, transition: 'x'.\n  // And enter duration is different from update duration.\n  // Thus it might be confused about the meaning of \"percent\" in during callback.\n  var scope = this;\n  var el = scope.el;\n  if (!el) {\n    return;\n  }\n  // If el is remove from zr by reason like legend, during still need to called,\n  // because el will be added back to zr and the prop value should not be incorrect.\n  var latestUserDuring = transitionInnerStore(el).userDuring;\n  var scopeUserDuring = scope.userDuring;\n  // Ensured a during is only called once in each animation frame.\n  // If a during is called multiple times in one frame, maybe some users' calculation logic\n  // might be wrong (not sure whether this usage exists).\n  // The case of a during might be called twice can be: by default there is a animator for\n  // 'x', 'y' when init. Before the init animation finished, call `setOption` to start\n  // another animators for 'style'/'shape'/'extra'.\n  if (latestUserDuring !== scopeUserDuring) {\n    // release\n    scope.el = scope.userDuring = null;\n    return;\n  }\n  tmpDuringScope.el = el;\n  // Give no `this` to user in \"during\" calling.\n  scopeUserDuring(transitionDuringAPI);\n  // FIXME: if in future meet the case that some prop will be both modified in `during` and `state`,\n  // consider the issue that the prop might be incorrect when return to \"normal\" state.\n}\nfunction prepareShapeOrExtraTransitionFrom(mainAttr, fromEl, elOption, transFromProps) {\n  var attrOpt = elOption[mainAttr];\n  if (!attrOpt) {\n    return;\n  }\n  var elPropsInAttr = fromEl[mainAttr];\n  var transFromPropsInAttr;\n  if (elPropsInAttr) {\n    var transition = elOption.transition;\n    var attrTransition = attrOpt.transition;\n    if (attrTransition) {\n      !transFromPropsInAttr && (transFromPropsInAttr = transFromProps[mainAttr] = {});\n      if (isTransitionAll(attrTransition)) {\n        extend(transFromPropsInAttr, elPropsInAttr);\n      } else {\n        var transitionKeys = normalizeToArray(attrTransition);\n        for (var i = 0; i < transitionKeys.length; i++) {\n          var key = transitionKeys[i];\n          var elVal = elPropsInAttr[key];\n          transFromPropsInAttr[key] = elVal;\n        }\n      }\n    } else if (isTransitionAll(transition) || indexOf(transition, mainAttr) >= 0) {\n      !transFromPropsInAttr && (transFromPropsInAttr = transFromProps[mainAttr] = {});\n      var elPropsInAttrKeys = keys(elPropsInAttr);\n      for (var i = 0; i < elPropsInAttrKeys.length; i++) {\n        var key = elPropsInAttrKeys[i];\n        var elVal = elPropsInAttr[key];\n        if (isNonStyleTransitionEnabled(attrOpt[key], elVal)) {\n          transFromPropsInAttr[key] = elVal;\n        }\n      }\n    }\n  }\n}\nfunction prepareShapeOrExtraAllPropsFinal(mainAttr, elOption, allProps) {\n  var attrOpt = elOption[mainAttr];\n  if (!attrOpt) {\n    return;\n  }\n  var allPropsInAttr = allProps[mainAttr] = {};\n  var keysInAttr = keys(attrOpt);\n  for (var i = 0; i < keysInAttr.length; i++) {\n    var key = keysInAttr[i];\n    // To avoid share one object with different element, and\n    // to avoid user modify the object inexpectedly, have to clone.\n    allPropsInAttr[key] = cloneValue(attrOpt[key]);\n  }\n}\nfunction prepareTransformTransitionFrom(el, elOption, transFromProps) {\n  var transition = elOption.transition;\n  var transitionKeys = isTransitionAll(transition) ? TRANSFORMABLE_PROPS : normalizeToArray(transition || []);\n  for (var i = 0; i < transitionKeys.length; i++) {\n    var key = transitionKeys[i];\n    if (key === 'style' || key === 'shape' || key === 'extra') {\n      continue;\n    }\n    var elVal = el[key];\n    if (process.env.NODE_ENV !== 'production') {\n      checkTransformPropRefer(key, 'el.transition');\n    }\n    // Do not clone, animator will perform that clone.\n    transFromProps[key] = elVal;\n  }\n}\nfunction prepareTransformAllPropsFinal(el, elOption, allProps) {\n  for (var i = 0; i < LEGACY_TRANSFORM_PROPS.length; i++) {\n    var legacyName = LEGACY_TRANSFORM_PROPS[i];\n    var xyName = LEGACY_TRANSFORM_PROPS_MAP[legacyName];\n    var legacyArr = elOption[legacyName];\n    if (legacyArr) {\n      allProps[xyName[0]] = legacyArr[0];\n      allProps[xyName[1]] = legacyArr[1];\n    }\n  }\n  for (var i = 0; i < TRANSFORMABLE_PROPS.length; i++) {\n    var key = TRANSFORMABLE_PROPS[i];\n    if (elOption[key] != null) {\n      allProps[key] = elOption[key];\n    }\n  }\n}\nfunction prepareStyleTransitionFrom(fromEl, elOption, styleOpt, transFromProps) {\n  if (!styleOpt) {\n    return;\n  }\n  var fromElStyle = fromEl.style;\n  var transFromStyleProps;\n  if (fromElStyle) {\n    var styleTransition = styleOpt.transition;\n    var elTransition = elOption.transition;\n    if (styleTransition && !isTransitionAll(styleTransition)) {\n      var transitionKeys = normalizeToArray(styleTransition);\n      !transFromStyleProps && (transFromStyleProps = transFromProps.style = {});\n      for (var i = 0; i < transitionKeys.length; i++) {\n        var key = transitionKeys[i];\n        var elVal = fromElStyle[key];\n        // Do not clone, see `checkNonStyleTansitionRefer`.\n        transFromStyleProps[key] = elVal;\n      }\n    } else if (fromEl.getAnimationStyleProps && (isTransitionAll(elTransition) || isTransitionAll(styleTransition) || indexOf(elTransition, 'style') >= 0)) {\n      var animationProps = fromEl.getAnimationStyleProps();\n      var animationStyleProps = animationProps ? animationProps.style : null;\n      if (animationStyleProps) {\n        !transFromStyleProps && (transFromStyleProps = transFromProps.style = {});\n        var styleKeys = keys(styleOpt);\n        for (var i = 0; i < styleKeys.length; i++) {\n          var key = styleKeys[i];\n          if (animationStyleProps[key]) {\n            var elVal = fromElStyle[key];\n            transFromStyleProps[key] = elVal;\n          }\n        }\n      }\n    }\n  }\n}\nfunction isNonStyleTransitionEnabled(optVal, elVal) {\n  // The same as `checkNonStyleTansitionRefer`.\n  return !isArrayLike(optVal) ? optVal != null && isFinite(optVal) : optVal !== elVal;\n}\nvar checkTransformPropRefer;\nif (process.env.NODE_ENV !== 'production') {\n  checkTransformPropRefer = function (key, usedIn) {\n    if (!hasOwn(TRANSFORM_PROPS_MAP, key)) {\n      warn('Prop `' + key + '` is not a permitted in `' + usedIn + '`. ' + 'Only `' + keys(TRANSFORM_PROPS_MAP).join('`, `') + '` are permitted.');\n    }\n  };\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,EAAEC,gBAAgB,QAAQ,kBAAkB;AAC9D,SAASC,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,WAAW,EAAEC,IAAI,EAAEC,MAAM,QAAQ,0BAA0B;AACxH,SAASC,UAAU,QAAQ,mCAAmC;AAC9D,OAAOC,WAAW,MAAM,oCAAoC;AAC5D,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,IAAI,QAAQ,oBAAoB;AACzC,SAASC,IAAI,QAAQ,gBAAgB;AACrC,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,IAAIC,0BAA0B,GAAG;EAC/BC,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;EACpBC,KAAK,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;EAC3BC,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS;AAC/B,CAAC;AACD,IAAIC,sBAAsB,GAAGZ,IAAI,CAACQ,0BAA0B,CAAC;AAC7D,IAAIK,mBAAmB,GAAGZ,MAAM,CAACM,mBAAmB,EAAE,UAAUO,GAAG,EAAEC,GAAG,EAAE;EACxED,GAAG,CAACC,GAAG,CAAC,GAAG,CAAC;EACZ,OAAOD,GAAG;AACZ,CAAC,EAAE,CAAC,CAAC,CAAC;AACN,IAAIE,qBAAqB,GAAGT,mBAAmB,CAACU,IAAI,CAAC,IAAI,CAAC;AAC1D;AACA,OAAO,IAAIC,wBAAwB,GAAG,CAAC,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;AACrE;AACA,IAAIC,oBAAoB,GAAG7B,SAAS,CAAC,CAAC;AACtC;AACA,SAAS8B,yBAAyBA,CAACC,aAAa,EAAEC,EAAE,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,SAAS,EAAE;EACtF,IAAIC,aAAa,GAAGL,aAAa,GAAG,WAAW;EAC/C,IAAIM,MAAM,GAAGvB,kBAAkB,CAACiB,aAAa,EAAEG,WAAW,EAAEC,SAAS,CAAC,IAAI,CAAC,CAAC;EAC5E,IAAIG,UAAU,GAAGT,oBAAoB,CAACG,EAAE,CAAC,CAACM,UAAU;EACpD;EACA,IAAID,MAAM,CAACE,QAAQ,GAAG,CAAC,EAAE;IACvB;IACAF,MAAM,CAACG,MAAM,GAAGF,UAAU,GAAGnC,IAAI,CAACsC,UAAU,EAAE;MAC5CT,EAAE,EAAEA,EAAE;MACNM,UAAU,EAAEA;IACd,CAAC,CAAC,GAAG,IAAI;IACTD,MAAM,CAACK,UAAU,GAAG,IAAI;IACxBL,MAAM,CAACM,KAAK,GAAGZ,aAAa;EAC9B;EACAzB,MAAM,CAAC+B,MAAM,EAAEJ,QAAQ,CAACG,aAAa,CAAC,CAAC;EACvC,OAAOC,MAAM;AACf;AACA,OAAO,SAASO,qBAAqBA,CAACZ,EAAE,EAAEC,QAAQ,EAAEY,eAAe,EAAEC,IAAI,EAAE;EACzEA,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;EACjB,IAAIX,SAAS,GAAGW,IAAI,CAACX,SAAS;IAC5BY,MAAM,GAAGD,IAAI,CAACC,MAAM;IACpBC,UAAU,GAAGF,IAAI,CAACE,UAAU;EAC9B,IAAIC,YAAY,GAAGJ,eAAe,CAACK,kBAAkB,CAAC,CAAC;EACvD;EACA,IAAIC,KAAK,GAAGtB,oBAAoB,CAACG,EAAE,CAAC;EACpC,IAAIoB,QAAQ,GAAGnB,QAAQ,CAACoB,KAAK;EAC7BF,KAAK,CAACb,UAAU,GAAGL,QAAQ,CAACO,MAAM;EAClC,IAAIc,cAAc,GAAG,CAAC,CAAC;EACvB,IAAIC,UAAU,GAAG,CAAC,CAAC;EACnBC,6BAA6B,CAACxB,EAAE,EAAEC,QAAQ,EAAEsB,UAAU,CAAC;EACvDE,gCAAgC,CAAC,OAAO,EAAExB,QAAQ,EAAEsB,UAAU,CAAC;EAC/DE,gCAAgC,CAAC,OAAO,EAAExB,QAAQ,EAAEsB,UAAU,CAAC;EAC/D,IAAI,CAACR,MAAM,IAAIE,YAAY,EAAE;IAC3BS,8BAA8B,CAAC1B,EAAE,EAAEC,QAAQ,EAAEqB,cAAc,CAAC;IAC5DK,iCAAiC,CAAC,OAAO,EAAE3B,EAAE,EAAEC,QAAQ,EAAEqB,cAAc,CAAC;IACxEK,iCAAiC,CAAC,OAAO,EAAE3B,EAAE,EAAEC,QAAQ,EAAEqB,cAAc,CAAC;IACxEM,0BAA0B,CAAC5B,EAAE,EAAEC,QAAQ,EAAEmB,QAAQ,EAAEE,cAAc,CAAC;EACpE;EACAC,UAAU,CAACF,KAAK,GAAGD,QAAQ;EAC3BS,kBAAkB,CAAC7B,EAAE,EAAEuB,UAAU,EAAEP,UAAU,CAAC;EAC9Cc,cAAc,CAAC9B,EAAE,EAAEC,QAAQ,CAAC;EAC5B,IAAIgB,YAAY,EAAE;IAChB,IAAIF,MAAM,EAAE;MACV,IAAIgB,gBAAgB,GAAG,CAAC,CAAC;MACzB3D,IAAI,CAACwB,wBAAwB,EAAE,UAAUoC,QAAQ,EAAE;QACjD,IAAIC,IAAI,GAAGD,QAAQ,GAAG/B,QAAQ,CAAC+B,QAAQ,CAAC,GAAG/B,QAAQ;QACnD,IAAIgC,IAAI,IAAIA,IAAI,CAACC,SAAS,EAAE;UAC1B,IAAIF,QAAQ,EAAE;YACZD,gBAAgB,CAACC,QAAQ,CAAC,GAAGD,gBAAgB,CAACC,QAAQ,CAAC,IAAI,CAAC,CAAC;UAC/D;UACA1D,MAAM,CAAC0D,QAAQ,GAAGD,gBAAgB,CAACC,QAAQ,CAAC,GAAGD,gBAAgB,EAAEE,IAAI,CAACC,SAAS,CAAC;QAClF;MACF,CAAC,CAAC;MACF,IAAI7B,MAAM,GAAGP,yBAAyB,CAAC,OAAO,EAAEE,EAAE,EAAEC,QAAQ,EAAEY,eAAe,EAAEV,SAAS,CAAC;MACzF,IAAIE,MAAM,CAACE,QAAQ,GAAG,CAAC,EAAE;QACvBP,EAAE,CAACmC,WAAW,CAACJ,gBAAgB,EAAE1B,MAAM,CAAC;MAC1C;IACF,CAAC,MAAM;MACL+B,oBAAoB,CAACpC,EAAE,EAAEC,QAAQ,EAAEE,SAAS,IAAI,CAAC,EAAEU,eAAe,EAAES,cAAc,CAAC;IACrF;EACF;EACA;EACAe,aAAa,CAACrC,EAAE,EAAEC,QAAQ,CAAC;EAC3BmB,QAAQ,GAAGpB,EAAE,CAACsC,KAAK,CAAC,CAAC,GAAGtC,EAAE,CAACuC,UAAU,CAAC,CAAC;AACzC;AACA,OAAO,SAASF,aAAaA,CAACrC,EAAE,EAAEC,QAAQ,EAAE;EAC1C;EACA,IAAIuC,YAAY,GAAG3C,oBAAoB,CAACG,EAAE,CAAC,CAACwC,YAAY;EACxD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG7C,wBAAwB,CAAC8C,MAAM,EAAED,CAAC,EAAE,EAAE;IACxD,IAAIT,QAAQ,GAAGpC,wBAAwB,CAAC6C,CAAC,CAAC;IAC1C,IAAIR,IAAI,GAAGD,QAAQ,GAAG/B,QAAQ,CAAC+B,QAAQ,CAAC,GAAG/B,QAAQ;IACnD,IAAIgC,IAAI,IAAIA,IAAI,CAACU,OAAO,EAAE;MACxB,IAAI,CAACH,YAAY,EAAE;QACjBA,YAAY,GAAG3C,oBAAoB,CAACG,EAAE,CAAC,CAACwC,YAAY,GAAG,CAAC,CAAC;MAC3D;MACA,IAAIR,QAAQ,EAAE;QACZQ,YAAY,CAACR,QAAQ,CAAC,GAAGQ,YAAY,CAACR,QAAQ,CAAC,IAAI,CAAC,CAAC;MACvD;MACA1D,MAAM,CAAC0D,QAAQ,GAAGQ,YAAY,CAACR,QAAQ,CAAC,GAAGQ,YAAY,EAAEP,IAAI,CAACU,OAAO,CAAC;IACxE;EACF;AACF;AACA,OAAO,SAASC,oBAAoBA,CAAC5C,EAAE,EAAEC,QAAQ,EAAEY,eAAe,EAAEgC,QAAQ,EAAE;EAC5E,IAAI7C,EAAE,EAAE;IACN,IAAI8C,QAAQ,GAAG9C,EAAE,CAAC+C,MAAM;IACxB,IAAIP,YAAY,GAAG3C,oBAAoB,CAACG,EAAE,CAAC,CAACwC,YAAY;IACxD,IAAIA,YAAY,EAAE;MAChB;MACA;MACA,IAAInC,MAAM,GAAGP,yBAAyB,CAAC,QAAQ,EAAEE,EAAE,EAAEC,QAAQ,EAAEY,eAAe,EAAE,CAAC,CAAC;MAClFR,MAAM,CAAC2C,IAAI,GAAG,YAAY;QACxBF,QAAQ,CAACG,MAAM,CAACjD,EAAE,CAAC;QACnB6C,QAAQ,IAAIA,QAAQ,CAAC,CAAC;MACxB,CAAC;MACD7C,EAAE,CAACkD,SAAS,CAACV,YAAY,EAAEnC,MAAM,CAAC;IACpC,CAAC,MAAM;MACLyC,QAAQ,CAACG,MAAM,CAACjD,EAAE,CAAC;MACnB6C,QAAQ,IAAIA,QAAQ,CAAC,CAAC;IACxB;EACF;AACF;AACA,OAAO,SAASM,eAAeA,CAACC,UAAU,EAAE;EAC1C,OAAOA,UAAU,KAAK,KAAK;AAC7B;AACA,SAASvB,kBAAkBA,CAAC7B,EAAE;AAC9B;AACAqD,aAAa,EAAErC,UAAU,EAAE;EACzB,IAAII,QAAQ,GAAGiC,aAAa,CAAChC,KAAK;EAClC,IAAI,CAACrB,EAAE,CAACsD,OAAO,IAAIlC,QAAQ,EAAE;IAC3B,IAAIJ,UAAU,EAAE;MACdhB,EAAE,CAACuD,QAAQ,CAAC,CAAC,CAAC,CAAC;MACf;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAIC,SAAS,GAAGxD,EAAE,CAACwD,SAAS;MAC5B,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGe,SAAS,CAACd,MAAM,EAAED,CAAC,EAAE,EAAE;QACzC,IAAIgB,QAAQ,GAAGD,SAAS,CAACf,CAAC,CAAC;QAC3B;QACA,IAAIgB,QAAQ,CAACC,UAAU,KAAK,OAAO,EAAE;UACnCD,QAAQ,CAACE,YAAY,CAAC3D,EAAE,CAACqB,KAAK,CAAC;QACjC;MACF;IACF;IACArB,EAAE,CAAC4D,QAAQ,CAACxC,QAAQ,CAAC;EACvB;EACA,IAAIiC,aAAa,EAAE;IACjB;IACAA,aAAa,CAAChC,KAAK,GAAG,IAAI;IAC1B;IACAgC,aAAa,IAAIrD,EAAE,CAAC6D,IAAI,CAACR,aAAa,CAAC;IACvCA,aAAa,CAAChC,KAAK,GAAGD,QAAQ;EAChC;AACF;AACA,SAASgB,oBAAoBA,CAACpC,EAAE,EAAEC,QAAQ,EAAEE,SAAS,EAAE2D,KAAK;AAC5D;AACAxC,cAAc,EAAE;EACd,IAAIA,cAAc,EAAE;IAClB,IAAIjB,MAAM,GAAGP,yBAAyB,CAAC,QAAQ,EAAEE,EAAE,EAAEC,QAAQ,EAAE6D,KAAK,EAAE3D,SAAS,CAAC;IAChF,IAAIE,MAAM,CAACE,QAAQ,GAAG,CAAC,EAAE;MACvBP,EAAE,CAACmC,WAAW,CAACb,cAAc,EAAEjB,MAAM,CAAC;IACxC;EACF;AACF;AACA,SAASyB,cAAcA,CAAC9B,EAAE,EAAEC,QAAQ,EAAE;EACpC;EACA1B,MAAM,CAAC0B,QAAQ,EAAE,QAAQ,CAAC,KAAKD,EAAE,CAAC+D,MAAM,GAAG9D,QAAQ,CAAC8D,MAAM,CAAC;EAC3DxF,MAAM,CAAC0B,QAAQ,EAAE,QAAQ,CAAC,KAAKD,EAAE,CAACgE,MAAM,GAAG/D,QAAQ,CAAC+D,MAAM,CAAC;EAC3D,IAAIhE,EAAE,YAAYnB,WAAW,EAAE;IAC7BN,MAAM,CAAC0B,QAAQ,EAAE,WAAW,CAAC,KAAKD,EAAE,CAACiE,SAAS,GAAGhE,QAAQ,CAACgE,SAAS,CAAC;EACtE;EACA,IAAIjE,EAAE,YAAYjB,IAAI,EAAE;IACtBR,MAAM,CAAC0B,QAAQ,EAAE,WAAW,CAAC,KAAKD,EAAE,CAACkE,SAAS,GAAGjE,QAAQ,CAACiE,SAAS,CAAC;EACtE;AACF;AACA;AACA,IAAIC,cAAc,GAAG,CAAC,CAAC;AACvB,IAAIC,mBAAmB,GAAG;EACxB;EACAC,YAAY,EAAE,SAAAA,CAAU5E,GAAG,EAAE6E,GAAG,EAAE;IAChC,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCvG,MAAM,CAACK,MAAM,CAACgB,mBAAmB,EAAEE,GAAG,CAAC,EAAE,OAAO,GAAGC,qBAAqB,GAAG,+BAA+B,CAAC;IAC7G;IACAyE,cAAc,CAACnE,EAAE,CAACP,GAAG,CAAC,GAAG6E,GAAG;IAC5B,OAAO,IAAI;EACb,CAAC;EACDI,YAAY,EAAE,SAAAA,CAAUjF,GAAG,EAAE;IAC3B,IAAI8E,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCvG,MAAM,CAACK,MAAM,CAACgB,mBAAmB,EAAEE,GAAG,CAAC,EAAE,OAAO,GAAGC,qBAAqB,GAAG,+BAA+B,CAAC;IAC7G;IACA,OAAOyE,cAAc,CAACnE,EAAE,CAACP,GAAG,CAAC;EAC/B,CAAC;EACDkF,QAAQ,EAAE,SAAAA,CAAUlF,GAAG,EAAE6E,GAAG,EAAE;IAC5B,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCG,iBAAiB,CAACnF,GAAG,CAAC;IACxB;IACA,IAAIO,EAAE,GAAGmE,cAAc,CAACnE,EAAE;IAC1B,IAAI6E,KAAK,GAAG7E,EAAE,CAAC6E,KAAK,KAAK7E,EAAE,CAAC6E,KAAK,GAAG,CAAC,CAAC,CAAC;IACvCA,KAAK,CAACpF,GAAG,CAAC,GAAG6E,GAAG;IAChBtE,EAAE,CAAC8E,UAAU,IAAI9E,EAAE,CAAC8E,UAAU,CAAC,CAAC;IAChC,OAAO,IAAI;EACb,CAAC;EACDC,QAAQ,EAAE,SAAAA,CAAUtF,GAAG,EAAE;IACvB,IAAI8E,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCG,iBAAiB,CAACnF,GAAG,CAAC;IACxB;IACA,IAAIoF,KAAK,GAAGV,cAAc,CAACnE,EAAE,CAAC6E,KAAK;IACnC,IAAIA,KAAK,EAAE;MACT,OAAOA,KAAK,CAACpF,GAAG,CAAC;IACnB;EACF,CAAC;EACDmE,QAAQ,EAAE,SAAAA,CAAUnE,GAAG,EAAE6E,GAAG,EAAE;IAC5B,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCG,iBAAiB,CAACnF,GAAG,CAAC;IACxB;IACA,IAAIO,EAAE,GAAGmE,cAAc,CAACnE,EAAE;IAC1B,IAAIqB,KAAK,GAAGrB,EAAE,CAACqB,KAAK;IACpB,IAAIA,KAAK,EAAE;MACT,IAAIkD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,IAAIpG,KAAK,CAACiG,GAAG,CAAC,EAAE;UACdtF,IAAI,CAAC,QAAQ,GAAGS,GAAG,GAAG,iCAAiC,CAAC;QAC1D;MACF;MACA4B,KAAK,CAAC5B,GAAG,CAAC,GAAG6E,GAAG;MAChBtE,EAAE,CAACgF,UAAU,IAAIhF,EAAE,CAACgF,UAAU,CAAC,CAAC;IAClC;IACA,OAAO,IAAI;EACb,CAAC;EACDC,QAAQ,EAAE,SAAAA,CAAUxF,GAAG,EAAE;IACvB,IAAI8E,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCG,iBAAiB,CAACnF,GAAG,CAAC;IACxB;IACA,IAAI4B,KAAK,GAAG8C,cAAc,CAACnE,EAAE,CAACqB,KAAK;IACnC,IAAIA,KAAK,EAAE;MACT,OAAOA,KAAK,CAAC5B,GAAG,CAAC;IACnB;EACF,CAAC;EACDyF,QAAQ,EAAE,SAAAA,CAAUzF,GAAG,EAAE6E,GAAG,EAAE;IAC5B,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCG,iBAAiB,CAACnF,GAAG,CAAC;IACxB;IACA,IAAI0F,KAAK,GAAGhB,cAAc,CAACnE,EAAE,CAACmF,KAAK,KAAKhB,cAAc,CAACnE,EAAE,CAACmF,KAAK,GAAG,CAAC,CAAC,CAAC;IACrEA,KAAK,CAAC1F,GAAG,CAAC,GAAG6E,GAAG;IAChB,OAAO,IAAI;EACb,CAAC;EACDc,QAAQ,EAAE,SAAAA,CAAU3F,GAAG,EAAE;IACvB,IAAI8E,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCG,iBAAiB,CAACnF,GAAG,CAAC;IACxB;IACA,IAAI0F,KAAK,GAAGhB,cAAc,CAACnE,EAAE,CAACmF,KAAK;IACnC,IAAIA,KAAK,EAAE;MACT,OAAOA,KAAK,CAAC1F,GAAG,CAAC;IACnB;EACF;AACF,CAAC;AACD,SAASmF,iBAAiBA,CAACnF,GAAG,EAAE;EAC9B,IAAI8E,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIhF,GAAG,KAAK,YAAY,IAAIA,GAAG,KAAK,WAAW,IAAIA,GAAG,KAAK,SAAS,EAAE;MACpE,MAAM,IAAI4F,KAAK,CAAC,mBAAmB,GAAG5F,GAAG,GAAG,GAAG,CAAC;IAClD;EACF;AACF;AACA,SAASgB,UAAUA,CAAA,EAAG;EACpB;EACA;EACA;EACA;EACA;EACA,IAAIE,KAAK,GAAG,IAAI;EAChB,IAAIX,EAAE,GAAGW,KAAK,CAACX,EAAE;EACjB,IAAI,CAACA,EAAE,EAAE;IACP;EACF;EACA;EACA;EACA,IAAIsF,gBAAgB,GAAGzF,oBAAoB,CAACG,EAAE,CAAC,CAACM,UAAU;EAC1D,IAAIiF,eAAe,GAAG5E,KAAK,CAACL,UAAU;EACtC;EACA;EACA;EACA;EACA;EACA;EACA,IAAIgF,gBAAgB,KAAKC,eAAe,EAAE;IACxC;IACA5E,KAAK,CAACX,EAAE,GAAGW,KAAK,CAACL,UAAU,GAAG,IAAI;IAClC;EACF;EACA6D,cAAc,CAACnE,EAAE,GAAGA,EAAE;EACtB;EACAuF,eAAe,CAACnB,mBAAmB,CAAC;EACpC;EACA;AACF;AACA,SAASzC,iCAAiCA,CAAC6D,QAAQ,EAAEC,MAAM,EAAExF,QAAQ,EAAEqB,cAAc,EAAE;EACrF,IAAIoE,OAAO,GAAGzF,QAAQ,CAACuF,QAAQ,CAAC;EAChC,IAAI,CAACE,OAAO,EAAE;IACZ;EACF;EACA,IAAIC,aAAa,GAAGF,MAAM,CAACD,QAAQ,CAAC;EACpC,IAAII,oBAAoB;EACxB,IAAID,aAAa,EAAE;IACjB,IAAIvC,UAAU,GAAGnD,QAAQ,CAACmD,UAAU;IACpC,IAAIyC,cAAc,GAAGH,OAAO,CAACtC,UAAU;IACvC,IAAIyC,cAAc,EAAE;MAClB,CAACD,oBAAoB,KAAKA,oBAAoB,GAAGtE,cAAc,CAACkE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;MAC/E,IAAIrC,eAAe,CAAC0C,cAAc,CAAC,EAAE;QACnCvH,MAAM,CAACsH,oBAAoB,EAAED,aAAa,CAAC;MAC7C,CAAC,MAAM;QACL,IAAIG,cAAc,GAAG7H,gBAAgB,CAAC4H,cAAc,CAAC;QACrD,KAAK,IAAIpD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqD,cAAc,CAACpD,MAAM,EAAED,CAAC,EAAE,EAAE;UAC9C,IAAIhD,GAAG,GAAGqG,cAAc,CAACrD,CAAC,CAAC;UAC3B,IAAIsD,KAAK,GAAGJ,aAAa,CAAClG,GAAG,CAAC;UAC9BmG,oBAAoB,CAACnG,GAAG,CAAC,GAAGsG,KAAK;QACnC;MACF;IACF,CAAC,MAAM,IAAI5C,eAAe,CAACC,UAAU,CAAC,IAAI5E,OAAO,CAAC4E,UAAU,EAAEoC,QAAQ,CAAC,IAAI,CAAC,EAAE;MAC5E,CAACI,oBAAoB,KAAKA,oBAAoB,GAAGtE,cAAc,CAACkE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;MAC/E,IAAIQ,iBAAiB,GAAGtH,IAAI,CAACiH,aAAa,CAAC;MAC3C,KAAK,IAAIlD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuD,iBAAiB,CAACtD,MAAM,EAAED,CAAC,EAAE,EAAE;QACjD,IAAIhD,GAAG,GAAGuG,iBAAiB,CAACvD,CAAC,CAAC;QAC9B,IAAIsD,KAAK,GAAGJ,aAAa,CAAClG,GAAG,CAAC;QAC9B,IAAIwG,2BAA2B,CAACP,OAAO,CAACjG,GAAG,CAAC,EAAEsG,KAAK,CAAC,EAAE;UACpDH,oBAAoB,CAACnG,GAAG,CAAC,GAAGsG,KAAK;QACnC;MACF;IACF;EACF;AACF;AACA,SAAStE,gCAAgCA,CAAC+D,QAAQ,EAAEvF,QAAQ,EAAEiG,QAAQ,EAAE;EACtE,IAAIR,OAAO,GAAGzF,QAAQ,CAACuF,QAAQ,CAAC;EAChC,IAAI,CAACE,OAAO,EAAE;IACZ;EACF;EACA,IAAIS,cAAc,GAAGD,QAAQ,CAACV,QAAQ,CAAC,GAAG,CAAC,CAAC;EAC5C,IAAIY,UAAU,GAAG1H,IAAI,CAACgH,OAAO,CAAC;EAC9B,KAAK,IAAIjD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2D,UAAU,CAAC1D,MAAM,EAAED,CAAC,EAAE,EAAE;IAC1C,IAAIhD,GAAG,GAAG2G,UAAU,CAAC3D,CAAC,CAAC;IACvB;IACA;IACA0D,cAAc,CAAC1G,GAAG,CAAC,GAAGb,UAAU,CAAC8G,OAAO,CAACjG,GAAG,CAAC,CAAC;EAChD;AACF;AACA,SAASiC,8BAA8BA,CAAC1B,EAAE,EAAEC,QAAQ,EAAEqB,cAAc,EAAE;EACpE,IAAI8B,UAAU,GAAGnD,QAAQ,CAACmD,UAAU;EACpC,IAAI0C,cAAc,GAAG3C,eAAe,CAACC,UAAU,CAAC,GAAGnE,mBAAmB,GAAGhB,gBAAgB,CAACmF,UAAU,IAAI,EAAE,CAAC;EAC3G,KAAK,IAAIX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqD,cAAc,CAACpD,MAAM,EAAED,CAAC,EAAE,EAAE;IAC9C,IAAIhD,GAAG,GAAGqG,cAAc,CAACrD,CAAC,CAAC;IAC3B,IAAIhD,GAAG,KAAK,OAAO,IAAIA,GAAG,KAAK,OAAO,IAAIA,GAAG,KAAK,OAAO,EAAE;MACzD;IACF;IACA,IAAIsG,KAAK,GAAG/F,EAAE,CAACP,GAAG,CAAC;IACnB,IAAI8E,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC4B,uBAAuB,CAAC5G,GAAG,EAAE,eAAe,CAAC;IAC/C;IACA;IACA6B,cAAc,CAAC7B,GAAG,CAAC,GAAGsG,KAAK;EAC7B;AACF;AACA,SAASvE,6BAA6BA,CAACxB,EAAE,EAAEC,QAAQ,EAAEiG,QAAQ,EAAE;EAC7D,KAAK,IAAIzD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnD,sBAAsB,CAACoD,MAAM,EAAED,CAAC,EAAE,EAAE;IACtD,IAAI6D,UAAU,GAAGhH,sBAAsB,CAACmD,CAAC,CAAC;IAC1C,IAAI8D,MAAM,GAAGrH,0BAA0B,CAACoH,UAAU,CAAC;IACnD,IAAIE,SAAS,GAAGvG,QAAQ,CAACqG,UAAU,CAAC;IACpC,IAAIE,SAAS,EAAE;MACbN,QAAQ,CAACK,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGC,SAAS,CAAC,CAAC,CAAC;MAClCN,QAAQ,CAACK,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGC,SAAS,CAAC,CAAC,CAAC;IACpC;EACF;EACA,KAAK,IAAI/D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGxD,mBAAmB,CAACyD,MAAM,EAAED,CAAC,EAAE,EAAE;IACnD,IAAIhD,GAAG,GAAGR,mBAAmB,CAACwD,CAAC,CAAC;IAChC,IAAIxC,QAAQ,CAACR,GAAG,CAAC,IAAI,IAAI,EAAE;MACzByG,QAAQ,CAACzG,GAAG,CAAC,GAAGQ,QAAQ,CAACR,GAAG,CAAC;IAC/B;EACF;AACF;AACA,SAASmC,0BAA0BA,CAAC6D,MAAM,EAAExF,QAAQ,EAAEmB,QAAQ,EAAEE,cAAc,EAAE;EAC9E,IAAI,CAACF,QAAQ,EAAE;IACb;EACF;EACA,IAAIqF,WAAW,GAAGhB,MAAM,CAACpE,KAAK;EAC9B,IAAIqF,mBAAmB;EACvB,IAAID,WAAW,EAAE;IACf,IAAIE,eAAe,GAAGvF,QAAQ,CAACgC,UAAU;IACzC,IAAIwD,YAAY,GAAG3G,QAAQ,CAACmD,UAAU;IACtC,IAAIuD,eAAe,IAAI,CAACxD,eAAe,CAACwD,eAAe,CAAC,EAAE;MACxD,IAAIb,cAAc,GAAG7H,gBAAgB,CAAC0I,eAAe,CAAC;MACtD,CAACD,mBAAmB,KAAKA,mBAAmB,GAAGpF,cAAc,CAACD,KAAK,GAAG,CAAC,CAAC,CAAC;MACzE,KAAK,IAAIoB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqD,cAAc,CAACpD,MAAM,EAAED,CAAC,EAAE,EAAE;QAC9C,IAAIhD,GAAG,GAAGqG,cAAc,CAACrD,CAAC,CAAC;QAC3B,IAAIsD,KAAK,GAAGU,WAAW,CAAChH,GAAG,CAAC;QAC5B;QACAiH,mBAAmB,CAACjH,GAAG,CAAC,GAAGsG,KAAK;MAClC;IACF,CAAC,MAAM,IAAIN,MAAM,CAACoB,sBAAsB,KAAK1D,eAAe,CAACyD,YAAY,CAAC,IAAIzD,eAAe,CAACwD,eAAe,CAAC,IAAInI,OAAO,CAACoI,YAAY,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE;MACtJ,IAAIE,cAAc,GAAGrB,MAAM,CAACoB,sBAAsB,CAAC,CAAC;MACpD,IAAIE,mBAAmB,GAAGD,cAAc,GAAGA,cAAc,CAACzF,KAAK,GAAG,IAAI;MACtE,IAAI0F,mBAAmB,EAAE;QACvB,CAACL,mBAAmB,KAAKA,mBAAmB,GAAGpF,cAAc,CAACD,KAAK,GAAG,CAAC,CAAC,CAAC;QACzE,IAAI2F,SAAS,GAAGtI,IAAI,CAAC0C,QAAQ,CAAC;QAC9B,KAAK,IAAIqB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuE,SAAS,CAACtE,MAAM,EAAED,CAAC,EAAE,EAAE;UACzC,IAAIhD,GAAG,GAAGuH,SAAS,CAACvE,CAAC,CAAC;UACtB,IAAIsE,mBAAmB,CAACtH,GAAG,CAAC,EAAE;YAC5B,IAAIsG,KAAK,GAAGU,WAAW,CAAChH,GAAG,CAAC;YAC5BiH,mBAAmB,CAACjH,GAAG,CAAC,GAAGsG,KAAK;UAClC;QACF;MACF;IACF;EACF;AACF;AACA,SAASE,2BAA2BA,CAACgB,MAAM,EAAElB,KAAK,EAAE;EAClD;EACA,OAAO,CAACtH,WAAW,CAACwI,MAAM,CAAC,GAAGA,MAAM,IAAI,IAAI,IAAIC,QAAQ,CAACD,MAAM,CAAC,GAAGA,MAAM,KAAKlB,KAAK;AACrF;AACA,IAAIM,uBAAuB;AAC3B,IAAI9B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC4B,uBAAuB,GAAG,SAAAA,CAAU5G,GAAG,EAAE0H,MAAM,EAAE;IAC/C,IAAI,CAAC5I,MAAM,CAACgB,mBAAmB,EAAEE,GAAG,CAAC,EAAE;MACrCT,IAAI,CAAC,QAAQ,GAAGS,GAAG,GAAG,2BAA2B,GAAG0H,MAAM,GAAG,KAAK,GAAG,QAAQ,GAAGzI,IAAI,CAACa,mBAAmB,CAAC,CAACI,IAAI,CAAC,MAAM,CAAC,GAAG,kBAAkB,CAAC;IAC9I;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}