{"ast": null, "code": "var _jsxFileName = \"E:\\\\AI\\\\SmartCV\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport './index.css';\nimport FileUpload from './components/FileUpload';\nimport JobDescriptionInput from './components/JobDescriptionInput';\nimport ResultDisplay from './components/ResultDisplay';\nimport LoadingSpinner from './components/LoadingSpinner';\nimport { optimizeResume, optimizeResumeDocxWithFormat, downloadOptimizedDocx } from './services/api';\nimport { extractTextFromImage, isImageFile, validateImageForOCR } from './services/ocrService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [resumeFile, setResumeFile] = useState(null);\n  const [extractedText, setExtractedText] = useState('');\n  const [ocrProgress, setOcrProgress] = useState(0);\n  const [isExtractingText, setIsExtractingText] = useState(false);\n  const [jobDescription, setJobDescription] = useState('');\n  const [jobDescriptionFile, setJobDescriptionFile] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [result, setResult] = useState(null);\n  const [error, setError] = useState(null);\n  const resultRef = useRef(null);\n\n  // Keyboard shortcuts\n  useEffect(() => {\n    const handleKeyPress = e => {\n      // Ctrl/Cmd + Enter to optimize\n      if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {\n        e.preventDefault();\n        if (!isLoading && resumeFile) {\n          handleOptimize();\n        }\n      }\n      // Ctrl/Cmd + R to reset\n      if ((e.ctrlKey || e.metaKey) && e.key === 'r') {\n        e.preventDefault();\n        handleReset();\n      }\n    };\n    document.addEventListener('keydown', handleKeyPress);\n    return () => document.removeEventListener('keydown', handleKeyPress);\n  }, [isLoading, resumeFile]);\n\n  // Handle file upload and OCR extraction\n  const handleFileUpload = async file => {\n    console.log('handleFileUpload called with file:', file);\n    setResumeFile(file);\n    setError(null);\n    setExtractedText('');\n    if (file && isImageFile(file)) {\n      console.log('File is an image, validating...');\n      const validation = validateImageForOCR(file);\n      console.log('Validation result:', validation);\n      if (!validation.isValid) {\n        setError(validation.message);\n        return;\n      }\n      setIsExtractingText(true);\n      setOcrProgress(0);\n      try {\n        const text = await extractTextFromImage(file, setOcrProgress);\n        setExtractedText(text);\n        console.log('Extracted text:', text);\n      } catch (err) {\n        console.error('OCR Error:', err);\n        setError(err.message || 'Failed to extract text from image');\n      } finally {\n        setIsExtractingText(false);\n        setOcrProgress(0);\n      }\n    } else {\n      console.log('File is not an image or file is null');\n    }\n  };\n  const handleOptimize = async () => {\n    if (!resumeFile) {\n      setError('Please upload your resume file first. We support PDF, Word documents, and image formats.');\n      return;\n    }\n    if (!jobDescription.trim() && !jobDescriptionFile) {\n      setError('Please provide a job description either by typing it in or uploading a file.');\n      return;\n    }\n\n    // 检查图片文件是否需要等待OCR提取\n    if (isImageFile(resumeFile) && !extractedText && !isExtractingText) {\n      setError('Please wait for text extraction to complete before optimizing.');\n      return;\n    }\n    setIsLoading(true);\n    setError(null);\n    setResult(null);\n    try {\n      let response;\n      if (isImageFile(resumeFile) && extractedText) {\n        // 图片文件：使用OCR提取的文本\n        const textBlob = new Blob([extractedText], {\n          type: 'text/plain'\n        });\n        const textFile = new File([textBlob], 'extracted_resume.txt', {\n          type: 'text/plain'\n        });\n        response = await optimizeResume(textFile, jobDescription, jobDescriptionFile);\n\n        // 存储原始图片文件供显示使用\n        response.originalFile = resumeFile;\n        response.extractedText = extractedText;\n      } else {\n        // PDF和Word文件：使用常规优化\n        response = await optimizeResume(resumeFile, jobDescription, jobDescriptionFile);\n      }\n      setResult(response);\n\n      // 滚动到结果区域\n      setTimeout(() => {\n        var _resultRef$current;\n        (_resultRef$current = resultRef.current) === null || _resultRef$current === void 0 ? void 0 : _resultRef$current.scrollIntoView({\n          behavior: 'smooth'\n        });\n      }, 100);\n    } catch (err) {\n      console.error('Optimization error:', err);\n      let errorMessage = 'An error occurred during optimization. ';\n      if (err.message) {\n        errorMessage += err.message;\n      } else {\n        errorMessage += 'Please check your internet connection and try again.';\n      }\n      setError(errorMessage);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleReset = () => {\n    setResumeFile(null);\n    setExtractedText('');\n    setOcrProgress(0);\n    setIsExtractingText(false);\n    setJobDescription('');\n    setJobDescriptionFile(null);\n    setResult(null);\n    setError(null);\n  };\n\n  // 检查是否为DOCX文件\n  const isDocxFile = file => {\n    var _file$name$split$pop;\n    if (!file) return false;\n    const fileExtension = (_file$name$split$pop = file.name.split('.').pop()) === null || _file$name$split$pop === void 0 ? void 0 : _file$name$split$pop.toLowerCase();\n    return fileExtension === 'docx';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-white\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"bg-white/80 backdrop-blur-sm shadow-sm border-b border-gray-100 sticky top-0 z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-start\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-16 h-16 flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: `/ICON.png?t=${Date.now()}`,\n                alt: \"SmartCV Logo\",\n                className: \"w-full h-full object-contain\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-2xl font-bold text-orange-600\",\n                children: \"SmartCV\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-600 font-medium\",\n                children: \"AI-Powered Resume Optimization Platform\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center space-y-4 mb-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-3xl md:text-4xl font-bold text-gray-800 leading-tight\",\n              children: \"Tailor CV to Match Your Dream Job\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-base text-gray-600 max-w-2xl mx-auto leading-relaxed\",\n              children: \"Instantly reshape your resume to fit target job, bypass ATS filters and stand out to HR.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-red-50 border-l-4 border-red-400 rounded-lg p-6 shadow-sm mb-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-5 h-5 text-red-400\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 20 20\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-3\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-red-800 font-medium\",\n                children: error\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-2xl shadow-xl border border-gray-100 p-10 mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center mb-10\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-10 h-10 bg-yellow-500 rounded-full flex items-center justify-center text-white font-bold text-sm\",\n                  children: \"1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-lg font-semibold text-gray-900\",\n                  children: \"Upload CV\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-0.5 bg-orange-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center text-white font-bold text-sm\",\n                  children: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-lg font-semibold text-gray-900\",\n                  children: \"Input Job Description\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-0.5 bg-orange-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-10 h-10 bg-red-500 rounded-full flex items-center justify-center text-white font-bold text-sm\",\n                  children: \"3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-lg font-semibold text-gray-900\",\n                  children: \"Optimize\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 lg:grid-cols-12 gap-8 items-start\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"lg:col-span-5 space-y-6\",\n              children: [/*#__PURE__*/_jsxDEV(FileUpload, {\n                file: resumeFile,\n                onFileSelect: handleFileUpload,\n                accept: \".pdf,.docx,.doc,.jpg,.jpeg,.png,.gif,.bmp,.webp\",\n                placeholder: \"Drop your resume file here (PDF, Word, or Image formats)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 17\n              }, this), isExtractingText && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3 mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 258,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-blue-700 font-medium\",\n                    children: \"Extracting text from image...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 259,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-full bg-blue-200 rounded-full h-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-blue-500 h-2 rounded-full transition-all duration-300\",\n                    style: {\n                      width: `${ocrProgress}%`\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 262,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-blue-600 mt-2\",\n                  children: [ocrProgress, \"% complete\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 19\n              }, this), extractedText && !isExtractingText && isImageFile(resumeFile) && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-green-50 border border-green-200 rounded-lg p-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2 mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-5 h-5 text-green-600\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M5 13l4 4L19 7\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 276,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 275,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-green-700 font-medium\",\n                    children: \"Text extracted from image successfully!\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 278,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-600 max-h-32 overflow-y-auto bg-white p-2 rounded border\",\n                  children: [extractedText.substring(0, 200), extractedText.length > 200 && '...']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-green-600 mt-1\",\n                  children: [extractedText.length, \" characters extracted\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 19\n              }, this), resumeFile && !isImageFile(resumeFile) && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2 mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-5 h-5 text-blue-600\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 295,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 294,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-blue-700 font-medium\",\n                    children: \"Document uploaded successfully!\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 297,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-blue-600\",\n                  children: resumeFile.type === 'application/pdf' ? 'PDF document ready for optimization' : resumeFile.type.includes('word') ? 'Word document ready for optimization' : 'Document ready for optimization'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"lg:col-span-5 space-y-6 mt-[16px]\",\n              children: /*#__PURE__*/_jsxDEV(JobDescriptionInput, {\n                textValue: jobDescription,\n                onTextChange: setJobDescription,\n                file: jobDescriptionFile,\n                onFileChange: setJobDescriptionFile\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"lg:col-span-2 flex flex-col justify-start space-y-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3 mt-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: handleOptimize,\n                    disabled: isLoading || !resumeFile,\n                    className: `w-full px-4 py-3 text-white font-semibold rounded-lg shadow-md transition-all duration-200 text-sm ${!resumeFile ? 'bg-gray-400 cursor-not-allowed' : isLoading ? 'bg-orange-500 opacity-75 cursor-wait' : 'bg-orange-500 hover:bg-orange-600 hover:shadow-lg transform hover:-translate-y-0.5'}`,\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"flex items-center justify-center space-x-2\",\n                      children: isLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                          className: \"animate-spin h-4 w-4 text-white\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                            className: \"opacity-25\",\n                            cx: \"12\",\n                            cy: \"12\",\n                            r: \"10\",\n                            stroke: \"currentColor\",\n                            strokeWidth: \"4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 337,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                            className: \"opacity-75\",\n                            fill: \"currentColor\",\n                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 338,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 336,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: \"Optimizing...\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 340,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                          className: \"w-4 h-4\",\n                          fill: \"none\",\n                          stroke: \"currentColor\",\n                          viewBox: \"0 0 24 24\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 345,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 344,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: \"Optimize\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 347,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 333,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 322,\n                    columnNumber: 21\n                  }, this), !resumeFile && !isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-gray-800 text-white text-xs rounded py-1 px-2 whitespace-nowrap\",\n                      children: [\"Upload resume first\", /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-800\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 358,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 356,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 355,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleReset,\n                  className: \"w-full px-4 py-3 bg-gray-100 text-gray-700 font-medium rounded-lg hover:bg-gray-200 transform hover:-translate-y-0.5 transition-all duration-200 border border-gray-200 text-sm\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"flex items-center justify-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-4 h-4\",\n                      fill: \"none\",\n                      stroke: \"currentColor\",\n                      viewBox: \"0 0 24 24\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 370,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 369,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Reset\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 372,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 368,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 364,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6 text-right\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-orange-600 font-medium\",\n              children: \"\\uD83D\\uDCA1 More detailed descriptions lead to better optimization\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-center mb-8\",\n          children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 13\n        }, this), result && /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: resultRef,\n          className: \"mb-8\",\n          children: /*#__PURE__*/_jsxDEV(ResultDisplay, {\n            result: result,\n            originalFile: resumeFile,\n            jobDescription: jobDescription\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 13\n        }, this), !result && !isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '150px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center mb-12\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-2xl font-bold text-gray-900 mb-4\",\n              children: \"Why SmartCV?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 405,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 max-w-2xl mx-auto\",\n              children: \"Your resume. Smarter, sharper, and ready to win that dream job.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center group\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-16 h-16 bg-yellow-500 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-8 h-8 text-white\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 415,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 414,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 413,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"text-lg font-semibold text-gray-900 mb-2\",\n                children: \"AI-Powered Analysis\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 text-sm\",\n                children: \"Advanced algorithms analyze job requirements and optimize your resume accordingly\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 419,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center group\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-16 h-16 bg-orange-500 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-8 h-8 text-white\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 425,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 424,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"text-lg font-semibold text-gray-900 mb-2\",\n                children: \"Instant Results\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 text-sm\",\n                children: \"Get professionally optimized resumes in seconds, not hours\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center group\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-16 h-16 bg-red-500 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-8 h-8 text-white\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 435,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 434,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 433,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"text-lg font-semibold text-gray-900 mb-2\",\n                children: \"Multiple Formats\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 438,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 text-sm\",\n                children: \"Export your optimized resume in PDF, DOCX, or plain text format\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 439,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"bg-gray-50 border-t border-gray-200 mt-20\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-8 flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: `/ICON.png?t=${Date.now()}`,\n                alt: \"SmartCV Logo\",\n                className: \"w-full h-full object-contain\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xl font-bold text-gray-900\",\n              children: \"SmartCV\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 459,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"\\xA9 2024 SmartCV - AI-Powered Resume Optimization Platform\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center space-x-2 text-sm text-gray-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Powered by\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"https://vlisoft.com\",\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              className: \"text-orange-600 hover:text-orange-700 font-medium transition-colors duration-200\",\n              children: \"Vlisoft.com\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u2022\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Empowering careers with AI\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 473,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4 text-xs text-gray-400 text-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"inline-flex items-center space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"/privacy-notice\",\n                className: \"px-3 py-2 bg-gray-700 text-white rounded hover:bg-gray-600 transition-colors\",\n                onClick: e => {\n                  e.preventDefault();\n                  window.open('/privacy-notice.html', '_blank');\n                },\n                children: \"Privacy Notice\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 479,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"/terms-privacy\",\n                className: \"px-3 py-2 bg-gray-700 text-white rounded hover:bg-gray-600 transition-colors\",\n                onClick: e => {\n                  e.preventDefault();\n                  window.open('/terms-privacy.html', '_blank');\n                },\n                children: \"Terms & Privacy\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 489,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 450,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 449,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 448,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 161,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"DixGaknMe36pPWOBoP31M41fnA4=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "FileUpload", "JobDescriptionInput", "ResultDisplay", "LoadingSpinner", "optimizeResume", "optimizeResumeDocxWithFormat", "downloadOptimizedDocx", "extractTextFromImage", "isImageFile", "validateImageForOCR", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "App", "_s", "resumeFile", "setResumeFile", "extractedText", "setExtractedText", "ocrProgress", "setOcrProgress", "isExtractingText", "setIsExtractingText", "jobDescription", "setJobDescription", "jobDescriptionFile", "setJobDescriptionFile", "isLoading", "setIsLoading", "result", "setResult", "error", "setError", "resultRef", "handleKeyPress", "e", "ctrl<PERSON>ey", "metaKey", "key", "preventDefault", "handleOptimize", "handleReset", "document", "addEventListener", "removeEventListener", "handleFileUpload", "file", "console", "log", "validation", "<PERSON><PERSON><PERSON><PERSON>", "message", "text", "err", "trim", "response", "textBlob", "Blob", "type", "textFile", "File", "originalFile", "setTimeout", "_resultRef$current", "current", "scrollIntoView", "behavior", "errorMessage", "isDocxFile", "_file$name$split$pop", "fileExtension", "name", "split", "pop", "toLowerCase", "className", "children", "src", "Date", "now", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fill", "viewBox", "fillRule", "d", "clipRule", "onFileSelect", "accept", "placeholder", "style", "width", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "substring", "length", "includes", "textValue", "onTextChange", "onFileChange", "onClick", "disabled", "cx", "cy", "r", "ref", "marginTop", "href", "target", "rel", "window", "open", "_c", "$RefreshReg$"], "sources": ["E:/AI/SmartCV/frontend/src/App.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\r\nimport './index.css';\r\nimport FileUpload from './components/FileUpload';\r\nimport JobDescriptionInput from './components/JobDescriptionInput';\r\nimport ResultDisplay from './components/ResultDisplay';\r\nimport LoadingSpinner from './components/LoadingSpinner';\r\nimport { optimizeResume, optimizeResumeDocxWithFormat, downloadOptimizedDocx } from './services/api';\r\nimport { extractTextFromImage, isImageFile, validateImageForOCR } from './services/ocrService';\r\n\r\nfunction App() {\r\n  const [resumeFile, setResumeFile] = useState(null);\r\n  const [extractedText, setExtractedText] = useState('');\r\n  const [ocrProgress, setOcrProgress] = useState(0);\r\n  const [isExtractingText, setIsExtractingText] = useState(false);\r\n  const [jobDescription, setJobDescription] = useState('');\r\n  const [jobDescriptionFile, setJobDescriptionFile] = useState(null);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [result, setResult] = useState(null);\r\n  const [error, setError] = useState(null);\r\n  \r\n  const resultRef = useRef(null);\r\n\r\n  // Keyboard shortcuts\r\n  useEffect(() => {\r\n    const handleKeyPress = (e) => {\r\n      // Ctrl/Cmd + Enter to optimize\r\n      if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {\r\n        e.preventDefault();\r\n        if (!isLoading && resumeFile) {\r\n          handleOptimize();\r\n        }\r\n      }\r\n      // Ctrl/Cmd + R to reset\r\n      if ((e.ctrlKey || e.metaKey) && e.key === 'r') {\r\n        e.preventDefault();\r\n        handleReset();\r\n      }\r\n    };\r\n\r\n    document.addEventListener('keydown', handleKeyPress);\r\n    return () => document.removeEventListener('keydown', handleKeyPress);\r\n  }, [isLoading, resumeFile]);\r\n\r\n  // Handle file upload and OCR extraction\r\n  const handleFileUpload = async (file) => {\r\n    console.log('handleFileUpload called with file:', file);\r\n    \r\n    setResumeFile(file);\r\n    setError(null);\r\n    setExtractedText('');\r\n    \r\n    if (file && isImageFile(file)) {\r\n      console.log('File is an image, validating...');\r\n      const validation = validateImageForOCR(file);\r\n      console.log('Validation result:', validation);\r\n      \r\n      if (!validation.isValid) {\r\n        setError(validation.message);\r\n        return;\r\n      }\r\n      \r\n      setIsExtractingText(true);\r\n      setOcrProgress(0);\r\n      \r\n      try {\r\n        const text = await extractTextFromImage(file, setOcrProgress);\r\n        setExtractedText(text);\r\n        console.log('Extracted text:', text);\r\n      } catch (err) {\r\n        console.error('OCR Error:', err);\r\n        setError(err.message || 'Failed to extract text from image');\r\n      } finally {\r\n        setIsExtractingText(false);\r\n        setOcrProgress(0);\r\n      }\r\n    } else {\r\n      console.log('File is not an image or file is null');\r\n    }\r\n  };\r\n\r\n  const handleOptimize = async () => {\r\n    if (!resumeFile) {\r\n      setError('Please upload your resume file first. We support PDF, Word documents, and image formats.');\r\n      return;\r\n    }\r\n    \r\n    if (!jobDescription.trim() && !jobDescriptionFile) {\r\n      setError('Please provide a job description either by typing it in or uploading a file.');\r\n      return;\r\n    }\r\n\r\n    // 检查图片文件是否需要等待OCR提取\r\n    if (isImageFile(resumeFile) && !extractedText && !isExtractingText) {\r\n      setError('Please wait for text extraction to complete before optimizing.');\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n    setError(null);\r\n    setResult(null);\r\n\r\n    try {\r\n      let response;\r\n      \r\n      if (isImageFile(resumeFile) && extractedText) {\r\n        // 图片文件：使用OCR提取的文本\r\n        const textBlob = new Blob([extractedText], { type: 'text/plain' });\r\n        const textFile = new File([textBlob], 'extracted_resume.txt', { type: 'text/plain' });\r\n        response = await optimizeResume(textFile, jobDescription, jobDescriptionFile);\r\n        \r\n        // 存储原始图片文件供显示使用\r\n        response.originalFile = resumeFile;\r\n        response.extractedText = extractedText;\r\n      } else {\r\n        // PDF和Word文件：使用常规优化\r\n        response = await optimizeResume(resumeFile, jobDescription, jobDescriptionFile);\r\n      }\r\n      \r\n      setResult(response);\r\n      \r\n      // 滚动到结果区域\r\n      setTimeout(() => {\r\n        resultRef.current?.scrollIntoView({ behavior: 'smooth' });\r\n      }, 100);\r\n      \r\n    } catch (err) {\r\n      console.error('Optimization error:', err);\r\n      let errorMessage = 'An error occurred during optimization. ';\r\n      \r\n      if (err.message) {\r\n        errorMessage += err.message;\r\n      } else {\r\n        errorMessage += 'Please check your internet connection and try again.';\r\n      }\r\n      \r\n      setError(errorMessage);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleReset = () => {\r\n    setResumeFile(null);\r\n    setExtractedText('');\r\n    setOcrProgress(0);\r\n    setIsExtractingText(false);\r\n    setJobDescription('');\r\n    setJobDescriptionFile(null);\r\n    setResult(null);\r\n    setError(null);\r\n  };\r\n\r\n  // 检查是否为DOCX文件\r\n  const isDocxFile = (file) => {\r\n    if (!file) return false;\r\n    const fileExtension = file.name.split('.').pop()?.toLowerCase();\r\n    return fileExtension === 'docx';\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-white\">\r\n      {/* Enhanced Header */}\r\n      <header className=\"bg-white/80 backdrop-blur-sm shadow-sm border-b border-gray-100 sticky top-0 z-50\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3\">\r\n          <div className=\"flex items-center justify-start\">\r\n            <div className=\"flex items-center space-x-3\">\r\n              <div className=\"w-16 h-16 flex items-center justify-center\">\r\n                <img \r\n                  src={`/ICON.png?t=${Date.now()}`}\r\n                  alt=\"SmartCV Logo\" \r\n                  className=\"w-full h-full object-contain\"\r\n                />\r\n              </div>\r\n              <div>\r\n                <h1 className=\"text-2xl font-bold text-orange-600\">\r\n                  SmartCV\r\n                </h1>\r\n                <p className=\"text-xs text-gray-600 font-medium\">AI-Powered Resume Optimization Platform</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </header>\r\n\r\n      {/* Main Content */}\r\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\r\n        <div className=\"\">\r\n          {/* Hero Section */}\r\n          <div className=\"text-center space-y-4 mb-8\">\r\n            <div className=\"space-y-3\">\r\n              <h2 className=\"text-3xl md:text-4xl font-bold text-gray-800 leading-tight\">\r\n                Tailor CV to Match Your Dream Job\r\n              </h2>\r\n              <p className=\"text-base text-gray-600 max-w-2xl mx-auto leading-relaxed\">\r\n                Instantly reshape your resume to fit target job, bypass ATS filters and stand out to HR.\r\n              </p>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Error Display */}\r\n          {error && (\r\n            <div className=\"bg-red-50 border-l-4 border-red-400 rounded-lg p-6 shadow-sm mb-8\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"flex-shrink-0\">\r\n                  <svg className=\"w-5 h-5 text-red-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                    <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\r\n                  </svg>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-red-800 font-medium\">{error}</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* Upload Section */}\r\n          <div className=\"bg-white rounded-2xl shadow-xl border border-gray-100 p-10 mb-8\">\r\n            {/* Step Progress Indicator */}\r\n            <div className=\"flex items-center justify-center mb-10\">\r\n              <div className=\"flex items-center space-x-8\">\r\n                <div className=\"flex items-center space-x-3\">\r\n                  <div className=\"w-10 h-10 bg-yellow-500 rounded-full flex items-center justify-center text-white font-bold text-sm\">\r\n                    1\r\n                  </div>\r\n                  <span className=\"text-lg font-semibold text-gray-900\">Upload CV</span>\r\n                </div>\r\n                <div className=\"w-12 h-0.5 bg-orange-300\"></div>\r\n                <div className=\"flex items-center space-x-3\">\r\n                  <div className=\"w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center text-white font-bold text-sm\">\r\n                    2\r\n                  </div>\r\n                  <span className=\"text-lg font-semibold text-gray-900\">Input Job Description</span>\r\n                </div>\r\n                <div className=\"w-12 h-0.5 bg-orange-300\"></div>\r\n                <div className=\"flex items-center space-x-3\">\r\n                  <div className=\"w-10 h-10 bg-red-500 rounded-full flex items-center justify-center text-white font-bold text-sm\">\r\n                    3\r\n                  </div>\r\n                  <span className=\"text-lg font-semibold text-gray-900\">Optimize</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"grid grid-cols-1 lg:grid-cols-12 gap-8 items-start\">\r\n              {/* Step 1: Resume Upload - 40% width */}\r\n              <div className=\"lg:col-span-5 space-y-6\">\r\n                <FileUpload\r\n                  file={resumeFile}\r\n                  onFileSelect={handleFileUpload}\r\n                  accept=\".pdf,.docx,.doc,.jpg,.jpeg,.png,.gif,.bmp,.webp\"\r\n                  placeholder=\"Drop your resume file here (PDF, Word, or Image formats)\"\r\n                />\r\n                \r\n                {/* OCR Progress */}\r\n                {isExtractingText && (\r\n                  <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\r\n                    <div className=\"flex items-center space-x-3 mb-3\">\r\n                      <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500\"></div>\r\n                      <span className=\"text-blue-700 font-medium\">Extracting text from image...</span>\r\n                    </div>\r\n                    <div className=\"w-full bg-blue-200 rounded-full h-2\">\r\n                      <div \r\n                        className=\"bg-blue-500 h-2 rounded-full transition-all duration-300\"\r\n                        style={{ width: `${ocrProgress}%` }}\r\n                      ></div>\r\n                    </div>\r\n                    <div className=\"text-sm text-blue-600 mt-2\">{ocrProgress}% complete</div>\r\n                  </div>\r\n                )}\r\n                \r\n                {/* Extracted Text Preview - Only for images */}\r\n                {extractedText && !isExtractingText && isImageFile(resumeFile) && (\r\n                  <div className=\"bg-green-50 border border-green-200 rounded-lg p-4\">\r\n                    <div className=\"flex items-center space-x-2 mb-2\">\r\n                      <svg className=\"w-5 h-5 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\r\n                      </svg>\r\n                      <span className=\"text-green-700 font-medium\">Text extracted from image successfully!</span>\r\n                    </div>\r\n                    <div className=\"text-sm text-gray-600 max-h-32 overflow-y-auto bg-white p-2 rounded border\">\r\n                      {extractedText.substring(0, 200)}\r\n                      {extractedText.length > 200 && '...'}\r\n                    </div>\r\n                    <div className=\"text-xs text-green-600 mt-1\">\r\n                      {extractedText.length} characters extracted\r\n                    </div>\r\n                  </div>\r\n                )}\r\n                \r\n                {/* File Type Info - Only for PDF/Word */}\r\n                {resumeFile && !isImageFile(resumeFile) && (\r\n                  <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\r\n                    <div className=\"flex items-center space-x-2 mb-2\">\r\n                      <svg className=\"w-5 h-5 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                      </svg>\r\n                      <span className=\"text-blue-700 font-medium\">Document uploaded successfully!</span>\r\n                    </div>\r\n                    <div className=\"text-sm text-blue-600\">\r\n                      {resumeFile.type === 'application/pdf' ? 'PDF document ready for optimization' :\r\n                       resumeFile.type.includes('word') ? 'Word document ready for optimization' :\r\n                       'Document ready for optimization'}\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n\r\n              {/* Step 2: Job Description Input - 40% width */}\r\n              <div className=\"lg:col-span-5 space-y-6 mt-[16px]\">\r\n                <JobDescriptionInput\r\n                  textValue={jobDescription}\r\n                  onTextChange={setJobDescription}\r\n                  file={jobDescriptionFile}\r\n                  onFileChange={setJobDescriptionFile}\r\n                />\r\n              </div>\r\n\r\n              {/* Step 3: Action Buttons - 20% width */}\r\n              <div className=\"lg:col-span-2 flex flex-col justify-start space-y-4\">\r\n                <div className=\"space-y-3 mt-4\">\r\n                  <div className=\"relative group\">\r\n                    <button\r\n                      onClick={handleOptimize}\r\n                      disabled={isLoading || !resumeFile}\r\n                      className={`w-full px-4 py-3 text-white font-semibold rounded-lg shadow-md transition-all duration-200 text-sm ${\r\n                        !resumeFile \r\n                          ? 'bg-gray-400 cursor-not-allowed' \r\n                          : isLoading \r\n                            ? 'bg-orange-500 opacity-75 cursor-wait' \r\n                            : 'bg-orange-500 hover:bg-orange-600 hover:shadow-lg transform hover:-translate-y-0.5'\r\n                      }`}\r\n                    >\r\n                      <span className=\"flex items-center justify-center space-x-2\">\r\n                        {isLoading ? (\r\n                          <>\r\n                            <svg className=\"animate-spin h-4 w-4 text-white\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                              <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n                              <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                            </svg>\r\n                            <span>Optimizing...</span>\r\n                          </>\r\n                        ) : (\r\n                          <>\r\n                            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\r\n                            </svg>\r\n                            <span>Optimize</span>\r\n                          </>\r\n                        )}\r\n                      </span>\r\n                    </button>\r\n                    \r\n                    {/* Tooltip for disabled state */}\r\n                    {!resumeFile && !isLoading && (\r\n                      <div className=\"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200\">\r\n                        <div className=\"bg-gray-800 text-white text-xs rounded py-1 px-2 whitespace-nowrap\">\r\n                          Upload resume first\r\n                          <div className=\"absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-800\"></div>\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                  \r\n                  <button\r\n                    onClick={handleReset}\r\n                    className=\"w-full px-4 py-3 bg-gray-100 text-gray-700 font-medium rounded-lg hover:bg-gray-200 transform hover:-translate-y-0.5 transition-all duration-200 border border-gray-200 text-sm\"\r\n                  >\r\n                    <span className=\"flex items-center justify-center space-x-2\">\r\n                      <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\r\n                      </svg>\r\n                      <span>Reset</span>\r\n                    </span>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            \r\n            {/* Tip at bottom right corner of the card */}\r\n            <div className=\"mt-6 text-right\">\r\n              <span className=\"text-sm text-orange-600 font-medium\">\r\n                💡 More detailed descriptions lead to better optimization\r\n              </span>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Loading Spinner */}\r\n          {isLoading && (\r\n            <div className=\"flex justify-center mb-8\">\r\n              <LoadingSpinner />\r\n            </div>\r\n          )}\r\n\r\n          {/* Results */}\r\n          {result && (\r\n            <div ref={resultRef} className=\"mb-8\">\r\n              <ResultDisplay result={result} originalFile={resumeFile} jobDescription={jobDescription} />\r\n            </div>\r\n          )}\r\n\r\n          {/* Features Section - Only show when no results */}\r\n          {!result && !isLoading && (\r\n            <div style={{ marginTop: '150px' }}>\r\n              <div className=\"text-center mb-12\">\r\n                <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">Why SmartCV?</h3>\r\n                <p className=\"text-gray-600 max-w-2xl mx-auto\">\r\n                  Your resume. Smarter, sharper, and ready to win that dream job.\r\n                </p>\r\n              </div>\r\n              \r\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\r\n                <div className=\"text-center group\">\r\n                  <div className=\"w-16 h-16 bg-yellow-500 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300\">\r\n                    <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\r\n                    </svg>\r\n                  </div>\r\n                  <h4 className=\"text-lg font-semibold text-gray-900 mb-2\">AI-Powered Analysis</h4>\r\n                  <p className=\"text-gray-600 text-sm\">Advanced algorithms analyze job requirements and optimize your resume accordingly</p>\r\n                </div>\r\n                \r\n                <div className=\"text-center group\">\r\n                  <div className=\"w-16 h-16 bg-orange-500 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300\">\r\n                    <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                    </svg>\r\n                  </div>\r\n                  <h4 className=\"text-lg font-semibold text-gray-900 mb-2\">Instant Results</h4>\r\n                  <p className=\"text-gray-600 text-sm\">Get professionally optimized resumes in seconds, not hours</p>\r\n                </div>\r\n                \r\n                <div className=\"text-center group\">\r\n                  <div className=\"w-16 h-16 bg-red-500 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300\">\r\n                    <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\r\n                    </svg>\r\n                  </div>\r\n                  <h4 className=\"text-lg font-semibold text-gray-900 mb-2\">Multiple Formats</h4>\r\n                  <p className=\"text-gray-600 text-sm\">Export your optimized resume in PDF, DOCX, or plain text format</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </main>\r\n\r\n      {/* Enhanced Footer */}\r\n      <footer className=\"bg-gray-50 border-t border-gray-200 mt-20\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\r\n          <div className=\"text-center space-y-4\">\r\n            <div className=\"flex items-center justify-center space-x-2\">\r\n              <div className=\"w-8 h-8 flex items-center justify-center\">\r\n                <img \r\n                  src={`/ICON.png?t=${Date.now()}`}\r\n                  alt=\"SmartCV Logo\" \r\n                  className=\"w-full h-full object-contain\"\r\n                />\r\n              </div>\r\n              <span className=\"text-xl font-bold text-gray-900\">SmartCV</span>\r\n            </div>\r\n            <p className=\"text-gray-600\">© 2024 SmartCV - AI-Powered Resume Optimization Platform</p>\r\n            <div className=\"flex items-center justify-center space-x-2 text-sm text-gray-500\">\r\n              <span>Powered by</span>\r\n              <a \r\n                href=\"https://vlisoft.com\" \r\n                target=\"_blank\" \r\n                rel=\"noopener noreferrer\"\r\n                className=\"text-orange-600 hover:text-orange-700 font-medium transition-colors duration-200\"\r\n              >\r\n                Vlisoft.com\r\n              </a>\r\n              <span>•</span>\r\n              <span>Empowering careers with AI</span>\r\n            </div>\r\n            \r\n            {/* Privacy and Terms Links */}\r\n            <div className=\"mt-4 text-xs text-gray-400 text-center\">\r\n              <span className=\"inline-flex items-center space-x-4\">\r\n                <a\r\n                  href=\"/privacy-notice\"\r\n                  className=\"px-3 py-2 bg-gray-700 text-white rounded hover:bg-gray-600 transition-colors\"\r\n                  onClick={(e) => {\r\n                    e.preventDefault();\r\n                    window.open('/privacy-notice.html', '_blank');\r\n                  }}\r\n                >\r\n                  Privacy Notice\r\n                </a>\r\n                <a\r\n                  href=\"/terms-privacy\"\r\n                  className=\"px-3 py-2 bg-gray-700 text-white rounded hover:bg-gray-600 transition-colors\"\r\n                  onClick={(e) => {\r\n                    e.preventDefault();\r\n                    window.open('/terms-privacy.html', '_blank');\r\n                  }}\r\n                >\r\n                  Terms & Privacy\r\n                </a>\r\n              </span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </footer>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default App; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,OAAO,aAAa;AACpB,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,mBAAmB,MAAM,kCAAkC;AAClE,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,cAAc,MAAM,6BAA6B;AACxD,SAASC,cAAc,EAAEC,4BAA4B,EAAEC,qBAAqB,QAAQ,gBAAgB;AACpG,SAASC,oBAAoB,EAAEC,WAAW,EAAEC,mBAAmB,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/F,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACqB,aAAa,EAAEC,gBAAgB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACyB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC2B,cAAc,EAAEC,iBAAiB,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC6B,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAClE,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACiC,MAAM,EAAEC,SAAS,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACmC,KAAK,EAAEC,QAAQ,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EAExC,MAAMqC,SAAS,GAAGpC,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACAC,SAAS,CAAC,MAAM;IACd,MAAMoC,cAAc,GAAIC,CAAC,IAAK;MAC5B;MACA,IAAI,CAACA,CAAC,CAACC,OAAO,IAAID,CAAC,CAACE,OAAO,KAAKF,CAAC,CAACG,GAAG,KAAK,OAAO,EAAE;QACjDH,CAAC,CAACI,cAAc,CAAC,CAAC;QAClB,IAAI,CAACZ,SAAS,IAAIZ,UAAU,EAAE;UAC5ByB,cAAc,CAAC,CAAC;QAClB;MACF;MACA;MACA,IAAI,CAACL,CAAC,CAACC,OAAO,IAAID,CAAC,CAACE,OAAO,KAAKF,CAAC,CAACG,GAAG,KAAK,GAAG,EAAE;QAC7CH,CAAC,CAACI,cAAc,CAAC,CAAC;QAClBE,WAAW,CAAC,CAAC;MACf;IACF,CAAC;IAEDC,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAET,cAAc,CAAC;IACpD,OAAO,MAAMQ,QAAQ,CAACE,mBAAmB,CAAC,SAAS,EAAEV,cAAc,CAAC;EACtE,CAAC,EAAE,CAACP,SAAS,EAAEZ,UAAU,CAAC,CAAC;;EAE3B;EACA,MAAM8B,gBAAgB,GAAG,MAAOC,IAAI,IAAK;IACvCC,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEF,IAAI,CAAC;IAEvD9B,aAAa,CAAC8B,IAAI,CAAC;IACnBd,QAAQ,CAAC,IAAI,CAAC;IACdd,gBAAgB,CAAC,EAAE,CAAC;IAEpB,IAAI4B,IAAI,IAAIvC,WAAW,CAACuC,IAAI,CAAC,EAAE;MAC7BC,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;MAC9C,MAAMC,UAAU,GAAGzC,mBAAmB,CAACsC,IAAI,CAAC;MAC5CC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEC,UAAU,CAAC;MAE7C,IAAI,CAACA,UAAU,CAACC,OAAO,EAAE;QACvBlB,QAAQ,CAACiB,UAAU,CAACE,OAAO,CAAC;QAC5B;MACF;MAEA7B,mBAAmB,CAAC,IAAI,CAAC;MACzBF,cAAc,CAAC,CAAC,CAAC;MAEjB,IAAI;QACF,MAAMgC,IAAI,GAAG,MAAM9C,oBAAoB,CAACwC,IAAI,EAAE1B,cAAc,CAAC;QAC7DF,gBAAgB,CAACkC,IAAI,CAAC;QACtBL,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEI,IAAI,CAAC;MACtC,CAAC,CAAC,OAAOC,GAAG,EAAE;QACZN,OAAO,CAAChB,KAAK,CAAC,YAAY,EAAEsB,GAAG,CAAC;QAChCrB,QAAQ,CAACqB,GAAG,CAACF,OAAO,IAAI,mCAAmC,CAAC;MAC9D,CAAC,SAAS;QACR7B,mBAAmB,CAAC,KAAK,CAAC;QAC1BF,cAAc,CAAC,CAAC,CAAC;MACnB;IACF,CAAC,MAAM;MACL2B,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;IACrD;EACF,CAAC;EAED,MAAMR,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI,CAACzB,UAAU,EAAE;MACfiB,QAAQ,CAAC,0FAA0F,CAAC;MACpG;IACF;IAEA,IAAI,CAACT,cAAc,CAAC+B,IAAI,CAAC,CAAC,IAAI,CAAC7B,kBAAkB,EAAE;MACjDO,QAAQ,CAAC,8EAA8E,CAAC;MACxF;IACF;;IAEA;IACA,IAAIzB,WAAW,CAACQ,UAAU,CAAC,IAAI,CAACE,aAAa,IAAI,CAACI,gBAAgB,EAAE;MAClEW,QAAQ,CAAC,gEAAgE,CAAC;MAC1E;IACF;IAEAJ,YAAY,CAAC,IAAI,CAAC;IAClBI,QAAQ,CAAC,IAAI,CAAC;IACdF,SAAS,CAAC,IAAI,CAAC;IAEf,IAAI;MACF,IAAIyB,QAAQ;MAEZ,IAAIhD,WAAW,CAACQ,UAAU,CAAC,IAAIE,aAAa,EAAE;QAC5C;QACA,MAAMuC,QAAQ,GAAG,IAAIC,IAAI,CAAC,CAACxC,aAAa,CAAC,EAAE;UAAEyC,IAAI,EAAE;QAAa,CAAC,CAAC;QAClE,MAAMC,QAAQ,GAAG,IAAIC,IAAI,CAAC,CAACJ,QAAQ,CAAC,EAAE,sBAAsB,EAAE;UAAEE,IAAI,EAAE;QAAa,CAAC,CAAC;QACrFH,QAAQ,GAAG,MAAMpD,cAAc,CAACwD,QAAQ,EAAEpC,cAAc,EAAEE,kBAAkB,CAAC;;QAE7E;QACA8B,QAAQ,CAACM,YAAY,GAAG9C,UAAU;QAClCwC,QAAQ,CAACtC,aAAa,GAAGA,aAAa;MACxC,CAAC,MAAM;QACL;QACAsC,QAAQ,GAAG,MAAMpD,cAAc,CAACY,UAAU,EAAEQ,cAAc,EAAEE,kBAAkB,CAAC;MACjF;MAEAK,SAAS,CAACyB,QAAQ,CAAC;;MAEnB;MACAO,UAAU,CAAC,MAAM;QAAA,IAAAC,kBAAA;QACf,CAAAA,kBAAA,GAAA9B,SAAS,CAAC+B,OAAO,cAAAD,kBAAA,uBAAjBA,kBAAA,CAAmBE,cAAc,CAAC;UAAEC,QAAQ,EAAE;QAAS,CAAC,CAAC;MAC3D,CAAC,EAAE,GAAG,CAAC;IAET,CAAC,CAAC,OAAOb,GAAG,EAAE;MACZN,OAAO,CAAChB,KAAK,CAAC,qBAAqB,EAAEsB,GAAG,CAAC;MACzC,IAAIc,YAAY,GAAG,yCAAyC;MAE5D,IAAId,GAAG,CAACF,OAAO,EAAE;QACfgB,YAAY,IAAId,GAAG,CAACF,OAAO;MAC7B,CAAC,MAAM;QACLgB,YAAY,IAAI,sDAAsD;MACxE;MAEAnC,QAAQ,CAACmC,YAAY,CAAC;IACxB,CAAC,SAAS;MACRvC,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMa,WAAW,GAAGA,CAAA,KAAM;IACxBzB,aAAa,CAAC,IAAI,CAAC;IACnBE,gBAAgB,CAAC,EAAE,CAAC;IACpBE,cAAc,CAAC,CAAC,CAAC;IACjBE,mBAAmB,CAAC,KAAK,CAAC;IAC1BE,iBAAiB,CAAC,EAAE,CAAC;IACrBE,qBAAqB,CAAC,IAAI,CAAC;IAC3BI,SAAS,CAAC,IAAI,CAAC;IACfE,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC;;EAED;EACA,MAAMoC,UAAU,GAAItB,IAAI,IAAK;IAAA,IAAAuB,oBAAA;IAC3B,IAAI,CAACvB,IAAI,EAAE,OAAO,KAAK;IACvB,MAAMwB,aAAa,IAAAD,oBAAA,GAAGvB,IAAI,CAACyB,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,cAAAJ,oBAAA,uBAA1BA,oBAAA,CAA4BK,WAAW,CAAC,CAAC;IAC/D,OAAOJ,aAAa,KAAK,MAAM;EACjC,CAAC;EAED,oBACE5D,OAAA;IAAKiE,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBAEpClE,OAAA;MAAQiE,SAAS,EAAC,mFAAmF;MAAAC,QAAA,eACnGlE,OAAA;QAAKiE,SAAS,EAAC,6CAA6C;QAAAC,QAAA,eAC1DlE,OAAA;UAAKiE,SAAS,EAAC,iCAAiC;UAAAC,QAAA,eAC9ClE,OAAA;YAAKiE,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1ClE,OAAA;cAAKiE,SAAS,EAAC,4CAA4C;cAAAC,QAAA,eACzDlE,OAAA;gBACEmE,GAAG,EAAE,eAAeC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAG;gBACjCC,GAAG,EAAC,cAAc;gBAClBL,SAAS,EAAC;cAA8B;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN1E,OAAA;cAAAkE,QAAA,gBACElE,OAAA;gBAAIiE,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAAC;cAEnD;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL1E,OAAA;gBAAGiE,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAuC;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGT1E,OAAA;MAAMiE,SAAS,EAAC,6CAA6C;MAAAC,QAAA,eAC3DlE,OAAA;QAAKiE,SAAS,EAAC,EAAE;QAAAC,QAAA,gBAEflE,OAAA;UAAKiE,SAAS,EAAC,4BAA4B;UAAAC,QAAA,eACzClE,OAAA;YAAKiE,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBlE,OAAA;cAAIiE,SAAS,EAAC,4DAA4D;cAAAC,QAAA,EAAC;YAE3E;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL1E,OAAA;cAAGiE,SAAS,EAAC,2DAA2D;cAAAC,QAAA,EAAC;YAEzE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGLrD,KAAK,iBACJrB,OAAA;UAAKiE,SAAS,EAAC,mEAAmE;UAAAC,QAAA,eAChFlE,OAAA;YAAKiE,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChClE,OAAA;cAAKiE,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5BlE,OAAA;gBAAKiE,SAAS,EAAC,sBAAsB;gBAACU,IAAI,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAV,QAAA,eAC3ElE,OAAA;kBAAM6E,QAAQ,EAAC,SAAS;kBAACC,CAAC,EAAC,yNAAyN;kBAACC,QAAQ,EAAC;gBAAS;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN1E,OAAA;cAAKiE,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBlE,OAAA;gBAAGiE,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAE7C;cAAK;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGD1E,OAAA;UAAKiE,SAAS,EAAC,iEAAiE;UAAAC,QAAA,gBAE9ElE,OAAA;YAAKiE,SAAS,EAAC,wCAAwC;YAAAC,QAAA,eACrDlE,OAAA;cAAKiE,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1ClE,OAAA;gBAAKiE,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1ClE,OAAA;kBAAKiE,SAAS,EAAC,oGAAoG;kBAAAC,QAAA,EAAC;gBAEpH;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN1E,OAAA;kBAAMiE,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAAC;gBAAS;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE,CAAC,eACN1E,OAAA;gBAAKiE,SAAS,EAAC;cAA0B;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChD1E,OAAA;gBAAKiE,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1ClE,OAAA;kBAAKiE,SAAS,EAAC,oGAAoG;kBAAAC,QAAA,EAAC;gBAEpH;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN1E,OAAA;kBAAMiE,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAAC;gBAAqB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/E,CAAC,eACN1E,OAAA;gBAAKiE,SAAS,EAAC;cAA0B;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChD1E,OAAA;gBAAKiE,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1ClE,OAAA;kBAAKiE,SAAS,EAAC,iGAAiG;kBAAAC,QAAA,EAAC;gBAEjH;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN1E,OAAA;kBAAMiE,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN1E,OAAA;YAAKiE,SAAS,EAAC,oDAAoD;YAAAC,QAAA,gBAEjElE,OAAA;cAAKiE,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtClE,OAAA,CAACX,UAAU;gBACT+C,IAAI,EAAE/B,UAAW;gBACjB2E,YAAY,EAAE7C,gBAAiB;gBAC/B8C,MAAM,EAAC,iDAAiD;gBACxDC,WAAW,EAAC;cAA0D;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvE,CAAC,EAGD/D,gBAAgB,iBACfX,OAAA;gBAAKiE,SAAS,EAAC,kDAAkD;gBAAAC,QAAA,gBAC/DlE,OAAA;kBAAKiE,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,gBAC/ClE,OAAA;oBAAKiE,SAAS,EAAC;kBAA8D;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACpF1E,OAAA;oBAAMiE,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EAAC;kBAA6B;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7E,CAAC,eACN1E,OAAA;kBAAKiE,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,eAClDlE,OAAA;oBACEiE,SAAS,EAAC,0DAA0D;oBACpEkB,KAAK,EAAE;sBAAEC,KAAK,EAAE,GAAG3E,WAAW;oBAAI;kBAAE;oBAAA8D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACN1E,OAAA;kBAAKiE,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,GAAEzD,WAAW,EAAC,YAAU;gBAAA;kBAAA8D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtE,CACN,EAGAnE,aAAa,IAAI,CAACI,gBAAgB,IAAId,WAAW,CAACQ,UAAU,CAAC,iBAC5DL,OAAA;gBAAKiE,SAAS,EAAC,oDAAoD;gBAAAC,QAAA,gBACjElE,OAAA;kBAAKiE,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,gBAC/ClE,OAAA;oBAAKiE,SAAS,EAAC,wBAAwB;oBAACU,IAAI,EAAC,MAAM;oBAACU,MAAM,EAAC,cAAc;oBAACT,OAAO,EAAC,WAAW;oBAAAV,QAAA,eAC3FlE,OAAA;sBAAMsF,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACV,CAAC,EAAC;oBAAgB;sBAAAP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrF,CAAC,eACN1E,OAAA;oBAAMiE,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAC;kBAAuC;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxF,CAAC,eACN1E,OAAA;kBAAKiE,SAAS,EAAC,4EAA4E;kBAAAC,QAAA,GACxF3D,aAAa,CAACkF,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EAC/BlF,aAAa,CAACmF,MAAM,GAAG,GAAG,IAAI,KAAK;gBAAA;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eACN1E,OAAA;kBAAKiE,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,GACzC3D,aAAa,CAACmF,MAAM,EAAC,uBACxB;gBAAA;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,EAGArE,UAAU,IAAI,CAACR,WAAW,CAACQ,UAAU,CAAC,iBACrCL,OAAA;gBAAKiE,SAAS,EAAC,kDAAkD;gBAAAC,QAAA,gBAC/DlE,OAAA;kBAAKiE,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,gBAC/ClE,OAAA;oBAAKiE,SAAS,EAAC,uBAAuB;oBAACU,IAAI,EAAC,MAAM;oBAACU,MAAM,EAAC,cAAc;oBAACT,OAAO,EAAC,WAAW;oBAAAV,QAAA,eAC1FlE,OAAA;sBAAMsF,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACV,CAAC,EAAC;oBAA+C;sBAAAP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpH,CAAC,eACN1E,OAAA;oBAAMiE,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EAAC;kBAA+B;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/E,CAAC,eACN1E,OAAA;kBAAKiE,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EACnC7D,UAAU,CAAC2C,IAAI,KAAK,iBAAiB,GAAG,qCAAqC,GAC7E3C,UAAU,CAAC2C,IAAI,CAAC2C,QAAQ,CAAC,MAAM,CAAC,GAAG,sCAAsC,GACzE;gBAAiC;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGN1E,OAAA;cAAKiE,SAAS,EAAC,mCAAmC;cAAAC,QAAA,eAChDlE,OAAA,CAACV,mBAAmB;gBAClBsG,SAAS,EAAE/E,cAAe;gBAC1BgF,YAAY,EAAE/E,iBAAkB;gBAChCsB,IAAI,EAAErB,kBAAmB;gBACzB+E,YAAY,EAAE9E;cAAsB;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGN1E,OAAA;cAAKiE,SAAS,EAAC,qDAAqD;cAAAC,QAAA,eAClElE,OAAA;gBAAKiE,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BlE,OAAA;kBAAKiE,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7BlE,OAAA;oBACE+F,OAAO,EAAEjE,cAAe;oBACxBkE,QAAQ,EAAE/E,SAAS,IAAI,CAACZ,UAAW;oBACnC4D,SAAS,EAAE,sGACT,CAAC5D,UAAU,GACP,gCAAgC,GAChCY,SAAS,GACP,sCAAsC,GACtC,oFAAoF,EACzF;oBAAAiD,QAAA,eAEHlE,OAAA;sBAAMiE,SAAS,EAAC,4CAA4C;sBAAAC,QAAA,EACzDjD,SAAS,gBACRjB,OAAA,CAAAE,SAAA;wBAAAgE,QAAA,gBACElE,OAAA;0BAAKiE,SAAS,EAAC,iCAAiC;0BAACU,IAAI,EAAC,MAAM;0BAACC,OAAO,EAAC,WAAW;0BAAAV,QAAA,gBAC9ElE,OAAA;4BAAQiE,SAAS,EAAC,YAAY;4BAACgC,EAAE,EAAC,IAAI;4BAACC,EAAE,EAAC,IAAI;4BAACC,CAAC,EAAC,IAAI;4BAACd,MAAM,EAAC,cAAc;4BAACG,WAAW,EAAC;0BAAG;4BAAAjB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAS,CAAC,eACrG1E,OAAA;4BAAMiE,SAAS,EAAC,YAAY;4BAACU,IAAI,EAAC,cAAc;4BAACG,CAAC,EAAC;0BAAiH;4BAAAP,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACzK,CAAC,eACN1E,OAAA;0BAAAkE,QAAA,EAAM;wBAAa;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA,eAC1B,CAAC,gBAEH1E,OAAA,CAAAE,SAAA;wBAAAgE,QAAA,gBACElE,OAAA;0BAAKiE,SAAS,EAAC,SAAS;0BAACU,IAAI,EAAC,MAAM;0BAACU,MAAM,EAAC,cAAc;0BAACT,OAAO,EAAC,WAAW;0BAAAV,QAAA,eAC5ElE,OAAA;4BAAMsF,aAAa,EAAC,OAAO;4BAACC,cAAc,EAAC,OAAO;4BAACC,WAAW,EAAE,CAAE;4BAACV,CAAC,EAAC;0BAA4B;4BAAAP,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjG,CAAC,eACN1E,OAAA;0BAAAkE,QAAA,EAAM;wBAAQ;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA,eACrB;oBACH;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,EAGR,CAACrE,UAAU,IAAI,CAACY,SAAS,iBACxBjB,OAAA;oBAAKiE,SAAS,EAAC,iIAAiI;oBAAAC,QAAA,eAC9IlE,OAAA;sBAAKiE,SAAS,EAAC,oEAAoE;sBAAAC,QAAA,GAAC,qBAElF,eAAAlE,OAAA;wBAAKiE,SAAS,EAAC;sBAAqG;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAEN1E,OAAA;kBACE+F,OAAO,EAAEhE,WAAY;kBACrBkC,SAAS,EAAC,iLAAiL;kBAAAC,QAAA,eAE3LlE,OAAA;oBAAMiE,SAAS,EAAC,4CAA4C;oBAAAC,QAAA,gBAC1DlE,OAAA;sBAAKiE,SAAS,EAAC,SAAS;sBAACU,IAAI,EAAC,MAAM;sBAACU,MAAM,EAAC,cAAc;sBAACT,OAAO,EAAC,WAAW;sBAAAV,QAAA,eAC5ElE,OAAA;wBAAMsF,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAE,CAAE;wBAACV,CAAC,EAAC;sBAA6G;wBAAAP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClL,CAAC,eACN1E,OAAA;sBAAAkE,QAAA,EAAM;oBAAK;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN1E,OAAA;YAAKiE,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9BlE,OAAA;cAAMiE,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAEtD;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGLzD,SAAS,iBACRjB,OAAA;UAAKiE,SAAS,EAAC,0BAA0B;UAAAC,QAAA,eACvClE,OAAA,CAACR,cAAc;YAAA+E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CACN,EAGAvD,MAAM,iBACLnB,OAAA;UAAKoG,GAAG,EAAE7E,SAAU;UAAC0C,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnClE,OAAA,CAACT,aAAa;YAAC4B,MAAM,EAAEA,MAAO;YAACgC,YAAY,EAAE9C,UAAW;YAACQ,cAAc,EAAEA;UAAe;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxF,CACN,EAGA,CAACvD,MAAM,IAAI,CAACF,SAAS,iBACpBjB,OAAA;UAAKmF,KAAK,EAAE;YAAEkB,SAAS,EAAE;UAAQ,CAAE;UAAAnC,QAAA,gBACjClE,OAAA;YAAKiE,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChClE,OAAA;cAAIiE,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAY;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvE1E,OAAA;cAAGiE,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAAC;YAE/C;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAEN1E,OAAA;YAAKiE,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDlE,OAAA;cAAKiE,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChClE,OAAA;gBAAKiE,SAAS,EAAC,2IAA2I;gBAAAC,QAAA,eACxJlE,OAAA;kBAAKiE,SAAS,EAAC,oBAAoB;kBAACU,IAAI,EAAC,MAAM;kBAACU,MAAM,EAAC,cAAc;kBAACT,OAAO,EAAC,WAAW;kBAAAV,QAAA,eACvFlE,OAAA;oBAAMsF,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACV,CAAC,EAAC;kBAA4B;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN1E,OAAA;gBAAIiE,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAAmB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjF1E,OAAA;gBAAGiE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAiF;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvH,CAAC,eAEN1E,OAAA;cAAKiE,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChClE,OAAA;gBAAKiE,SAAS,EAAC,2IAA2I;gBAAAC,QAAA,eACxJlE,OAAA;kBAAKiE,SAAS,EAAC,oBAAoB;kBAACU,IAAI,EAAC,MAAM;kBAACU,MAAM,EAAC,cAAc;kBAACT,OAAO,EAAC,WAAW;kBAAAV,QAAA,eACvFlE,OAAA;oBAAMsF,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACV,CAAC,EAAC;kBAA+C;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN1E,OAAA;gBAAIiE,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAAe;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7E1E,OAAA;gBAAGiE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAA0D;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChG,CAAC,eAEN1E,OAAA;cAAKiE,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChClE,OAAA;gBAAKiE,SAAS,EAAC,wIAAwI;gBAAAC,QAAA,eACrJlE,OAAA;kBAAKiE,SAAS,EAAC,oBAAoB;kBAACU,IAAI,EAAC,MAAM;kBAACU,MAAM,EAAC,cAAc;kBAACT,OAAO,EAAC,WAAW;kBAAAV,QAAA,eACvFlE,OAAA;oBAAMsF,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACV,CAAC,EAAC;kBAAiI;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN1E,OAAA;gBAAIiE,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAAgB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9E1E,OAAA;gBAAGiE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAA+D;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGP1E,OAAA;MAAQiE,SAAS,EAAC,2CAA2C;MAAAC,QAAA,eAC3DlE,OAAA;QAAKiE,SAAS,EAAC,8CAA8C;QAAAC,QAAA,eAC3DlE,OAAA;UAAKiE,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBACpClE,OAAA;YAAKiE,SAAS,EAAC,4CAA4C;YAAAC,QAAA,gBACzDlE,OAAA;cAAKiE,SAAS,EAAC,0CAA0C;cAAAC,QAAA,eACvDlE,OAAA;gBACEmE,GAAG,EAAE,eAAeC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAG;gBACjCC,GAAG,EAAC,cAAc;gBAClBL,SAAS,EAAC;cAA8B;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN1E,OAAA;cAAMiE,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAAC;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC,eACN1E,OAAA;YAAGiE,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAwD;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACzF1E,OAAA;YAAKiE,SAAS,EAAC,kEAAkE;YAAAC,QAAA,gBAC/ElE,OAAA;cAAAkE,QAAA,EAAM;YAAU;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvB1E,OAAA;cACEsG,IAAI,EAAC,qBAAqB;cAC1BC,MAAM,EAAC,QAAQ;cACfC,GAAG,EAAC,qBAAqB;cACzBvC,SAAS,EAAC,kFAAkF;cAAAC,QAAA,EAC7F;YAED;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ1E,OAAA;cAAAkE,QAAA,EAAM;YAAC;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACd1E,OAAA;cAAAkE,QAAA,EAAM;YAA0B;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eAGN1E,OAAA;YAAKiE,SAAS,EAAC,wCAAwC;YAAAC,QAAA,eACrDlE,OAAA;cAAMiE,SAAS,EAAC,oCAAoC;cAAAC,QAAA,gBAClDlE,OAAA;gBACEsG,IAAI,EAAC,iBAAiB;gBACtBrC,SAAS,EAAC,8EAA8E;gBACxF8B,OAAO,EAAGtE,CAAC,IAAK;kBACdA,CAAC,CAACI,cAAc,CAAC,CAAC;kBAClB4E,MAAM,CAACC,IAAI,CAAC,sBAAsB,EAAE,QAAQ,CAAC;gBAC/C,CAAE;gBAAAxC,QAAA,EACH;cAED;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJ1E,OAAA;gBACEsG,IAAI,EAAC,gBAAgB;gBACrBrC,SAAS,EAAC,8EAA8E;gBACxF8B,OAAO,EAAGtE,CAAC,IAAK;kBACdA,CAAC,CAACI,cAAc,CAAC,CAAC;kBAClB4E,MAAM,CAACC,IAAI,CAAC,qBAAqB,EAAE,QAAQ,CAAC;gBAC9C,CAAE;gBAAAxC,QAAA,EACH;cAED;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV;AAACtE,EAAA,CAhfQD,GAAG;AAAAwG,EAAA,GAAHxG,GAAG;AAkfZ,eAAeA,GAAG;AAAC,IAAAwG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}