{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.createSensor = void 0;\nvar _constant = require(\"../constant\");\nvar _debounce = _interopRequireDefault(require(\"../debounce\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    \"default\": obj\n  };\n}\n/**\n * Created by hustcc on 18/7/5.\n * Contract: <EMAIL>\n */\n\nvar createSensor = function createSensor(element, whenDestroy) {\n  var sensor = undefined;\n  // callback\n  var listeners = [];\n\n  /**\n   * trigger listeners\n   */\n  var resizeListener = (0, _debounce[\"default\"])(function () {\n    // trigger all\n    listeners.forEach(function (listener) {\n      listener(element);\n    });\n  });\n\n  /**\n   * create ResizeObserver sensor\n   * @returns\n   */\n  var newSensor = function newSensor() {\n    var s = new ResizeObserver(resizeListener);\n    // listen element\n    s.observe(element);\n\n    // trigger once\n    resizeListener();\n    return s;\n  };\n\n  /**\n   * listen with callback\n   * @param cb\n   */\n  var bind = function bind(cb) {\n    if (!sensor) {\n      sensor = newSensor();\n    }\n    if (listeners.indexOf(cb) === -1) {\n      listeners.push(cb);\n    }\n  };\n\n  /**\n   * destroy\n   */\n  var destroy = function destroy() {\n    sensor.disconnect();\n    listeners = [];\n    sensor = undefined;\n    element.removeAttribute(_constant.SizeSensorId);\n    whenDestroy && whenDestroy();\n  };\n\n  /**\n   * cancel bind\n   * @param cb\n   */\n  var unbind = function unbind(cb) {\n    var idx = listeners.indexOf(cb);\n    if (idx !== -1) {\n      listeners.splice(idx, 1);\n    }\n\n    // no listener, and sensor is exist\n    // then destroy the sensor\n    if (listeners.length === 0 && sensor) {\n      destroy();\n    }\n  };\n  return {\n    element: element,\n    bind: bind,\n    destroy: destroy,\n    unbind: unbind\n  };\n};\nexports.createSensor = createSensor;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "createSensor", "_constant", "require", "_debounce", "_interopRequireDefault", "obj", "__esModule", "element", "<PERSON><PERSON><PERSON><PERSON>", "sensor", "undefined", "listeners", "resizeListener", "for<PERSON>ach", "listener", "newSensor", "s", "ResizeObserver", "observe", "bind", "cb", "indexOf", "push", "destroy", "disconnect", "removeAttribute", "SizeSensorId", "unbind", "idx", "splice", "length"], "sources": ["E:/AI/SmartCV/node_modules/size-sensor/lib/sensors/resizeObserver.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.createSensor = void 0;\nvar _constant = require(\"../constant\");\nvar _debounce = _interopRequireDefault(require(\"../debounce\"));\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\n/**\n * Created by hustcc on 18/7/5.\n * Contract: <EMAIL>\n */\n\nvar createSensor = function createSensor(element, whenDestroy) {\n  var sensor = undefined;\n  // callback\n  var listeners = [];\n\n  /**\n   * trigger listeners\n   */\n  var resizeListener = (0, _debounce[\"default\"])(function () {\n    // trigger all\n    listeners.forEach(function (listener) {\n      listener(element);\n    });\n  });\n\n  /**\n   * create ResizeObserver sensor\n   * @returns\n   */\n  var newSensor = function newSensor() {\n    var s = new ResizeObserver(resizeListener);\n    // listen element\n    s.observe(element);\n\n    // trigger once\n    resizeListener();\n    return s;\n  };\n\n  /**\n   * listen with callback\n   * @param cb\n   */\n  var bind = function bind(cb) {\n    if (!sensor) {\n      sensor = newSensor();\n    }\n    if (listeners.indexOf(cb) === -1) {\n      listeners.push(cb);\n    }\n  };\n\n  /**\n   * destroy\n   */\n  var destroy = function destroy() {\n    sensor.disconnect();\n    listeners = [];\n    sensor = undefined;\n    element.removeAttribute(_constant.SizeSensorId);\n    whenDestroy && whenDestroy();\n  };\n\n  /**\n   * cancel bind\n   * @param cb\n   */\n  var unbind = function unbind(cb) {\n    var idx = listeners.indexOf(cb);\n    if (idx !== -1) {\n      listeners.splice(idx, 1);\n    }\n\n    // no listener, and sensor is exist\n    // then destroy the sensor\n    if (listeners.length === 0 && sensor) {\n      destroy();\n    }\n  };\n  return {\n    element: element,\n    bind: bind,\n    destroy: destroy,\n    unbind: unbind\n  };\n};\nexports.createSensor = createSensor;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,YAAY,GAAG,KAAK,CAAC;AAC7B,IAAIC,SAAS,GAAGC,OAAO,CAAC,aAAa,CAAC;AACtC,IAAIC,SAAS,GAAGC,sBAAsB,CAACF,OAAO,CAAC,aAAa,CAAC,CAAC;AAC9D,SAASE,sBAAsBA,CAACC,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAE,SAAS,EAAEA;EAAI,CAAC;AAAE;AAChG;AACA;AACA;AACA;;AAEA,IAAIL,YAAY,GAAG,SAASA,YAAYA,CAACO,OAAO,EAAEC,WAAW,EAAE;EAC7D,IAAIC,MAAM,GAAGC,SAAS;EACtB;EACA,IAAIC,SAAS,GAAG,EAAE;;EAElB;AACF;AACA;EACE,IAAIC,cAAc,GAAG,CAAC,CAAC,EAAET,SAAS,CAAC,SAAS,CAAC,EAAE,YAAY;IACzD;IACAQ,SAAS,CAACE,OAAO,CAAC,UAAUC,QAAQ,EAAE;MACpCA,QAAQ,CAACP,OAAO,CAAC;IACnB,CAAC,CAAC;EACJ,CAAC,CAAC;;EAEF;AACF;AACA;AACA;EACE,IAAIQ,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;IACnC,IAAIC,CAAC,GAAG,IAAIC,cAAc,CAACL,cAAc,CAAC;IAC1C;IACAI,CAAC,CAACE,OAAO,CAACX,OAAO,CAAC;;IAElB;IACAK,cAAc,CAAC,CAAC;IAChB,OAAOI,CAAC;EACV,CAAC;;EAED;AACF;AACA;AACA;EACE,IAAIG,IAAI,GAAG,SAASA,IAAIA,CAACC,EAAE,EAAE;IAC3B,IAAI,CAACX,MAAM,EAAE;MACXA,MAAM,GAAGM,SAAS,CAAC,CAAC;IACtB;IACA,IAAIJ,SAAS,CAACU,OAAO,CAACD,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;MAChCT,SAAS,CAACW,IAAI,CAACF,EAAE,CAAC;IACpB;EACF,CAAC;;EAED;AACF;AACA;EACE,IAAIG,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG;IAC/Bd,MAAM,CAACe,UAAU,CAAC,CAAC;IACnBb,SAAS,GAAG,EAAE;IACdF,MAAM,GAAGC,SAAS;IAClBH,OAAO,CAACkB,eAAe,CAACxB,SAAS,CAACyB,YAAY,CAAC;IAC/ClB,WAAW,IAAIA,WAAW,CAAC,CAAC;EAC9B,CAAC;;EAED;AACF;AACA;AACA;EACE,IAAImB,MAAM,GAAG,SAASA,MAAMA,CAACP,EAAE,EAAE;IAC/B,IAAIQ,GAAG,GAAGjB,SAAS,CAACU,OAAO,CAACD,EAAE,CAAC;IAC/B,IAAIQ,GAAG,KAAK,CAAC,CAAC,EAAE;MACdjB,SAAS,CAACkB,MAAM,CAACD,GAAG,EAAE,CAAC,CAAC;IAC1B;;IAEA;IACA;IACA,IAAIjB,SAAS,CAACmB,MAAM,KAAK,CAAC,IAAIrB,MAAM,EAAE;MACpCc,OAAO,CAAC,CAAC;IACX;EACF,CAAC;EACD,OAAO;IACLhB,OAAO,EAAEA,OAAO;IAChBY,IAAI,EAAEA,IAAI;IACVI,OAAO,EAAEA,OAAO;IAChBI,MAAM,EAAEA;EACV,CAAC;AACH,CAAC;AACD7B,OAAO,CAACE,YAAY,GAAGA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}