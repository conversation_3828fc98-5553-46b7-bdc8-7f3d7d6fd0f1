{"ast": null, "code": "var _jsxFileName = \"E:\\\\AI\\\\SmartCV\\\\frontend\\\\src\\\\components\\\\ResultDisplay.js\",\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { exportToPDF, exportToWord, exportToText } from '../utils/exportUtils';\nimport { downloadOptimizedDocx } from '../services/api';\nimport DocumentPreview from './DocumentPreview';\nimport ReactECharts from 'echarts-for-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ResultDisplay = ({\n  result,\n  originalFile,\n  jobDescription\n}) => {\n  _s2();\n  var _result$job_match_ana,\n    _result$job_match_ana2,\n    _result$job_match_ana3,\n    _result$job_match_ana4,\n    _result$job_match_ana5,\n    _result$original_leng,\n    _result$optimized_len,\n    _result$job_match_ana6,\n    _result$job_match_ana7,\n    _s = $RefreshSig$();\n  const [showComparison, setShowComparison] = useState(false);\n  const [activeTab, setActiveTab] = useState('optimized'); // 'optimized', 'original', 'comparison'\n  const [showFormattedOptimized, setShowFormattedOptimized] = useState(false); // Toggle for formatted view in comparison\n  const [downloadingDocx, setDownloadingDocx] = useState(false);\n  const [showExportDropdown, setShowExportDropdown] = useState(false);\n  const [viewMode, setViewMode] = useState('raw'); // 'raw', 'enhanced', 'word'\n  const [wordPreviewMode, setWordPreviewMode] = useState('office'); // 'office', 'google', 'download'\n  const [wordPreviewError, setWordPreviewError] = useState(null);\n  // 新增：用于控制每个分类的展开/收起状态\n  const [expandedMatched, setExpandedMatched] = useState({});\n  const [expandedMissing, setExpandedMissing] = useState({});\n\n  // 点击外部区域关闭下拉菜单\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (showExportDropdown && !event.target.closest('.export-dropdown')) {\n        setShowExportDropdown(false);\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, [showExportDropdown]);\n  const formatOptimizedResume = text => {\n    if (!text) return '';\n\n    // Process Markdown-formatted resume\n    return text.replace(/^#\\s+(.+)$/gm, '<h1 class=\"text-2xl font-bold text-gray-900 mb-4 border-b-2 border-orange-500 pb-2\">$1</h1>').replace(/^##\\s+(.+)$/gm, '<h2 class=\"text-xl font-semibold text-gray-800 mb-3 mt-6\">$1</h2>').replace(/^###\\s+(.+)$/gm, '<h3 class=\"text-lg font-medium text-gray-700 mb-2 mt-4\">$3</h3>').replace(/^\\*\\*(.+?)\\*\\*:?(.*)$/gm, '<div class=\"mb-2\"><span class=\"font-semibold text-gray-900\">$1</span>$2</div>').replace(/^\\* (.+)$/gm, '<li class=\"ml-4 mb-1 text-gray-700\">$1</li>').replace(/^- (.+)$/gm, '<li class=\"ml-4 mb-1 text-gray-700\">$1</li>').replace(/\\*\\*(.+?)\\*\\*/g, '<span class=\"font-semibold\">$1</span>').replace(/\\*(.+?)\\*/g, '<span class=\"italic\">$1</span>').replace(/\\n\\n/g, '</p><p class=\"mb-3\">').replace(/\\n/g, '<br>').replace(/^(?!<[h|l|d])(.+)$/gm, '<p class=\"mb-3 text-gray-700\">$1</p>');\n  };\n  const generateATSScore = (optimizedText, originalText) => {\n    // 确保输入文本不为空，提供默认值\n    const safeOptimizedText = optimizedText || '';\n    const safeOriginalText = originalText || '';\n\n    // Simple ATS scoring algorithm\n    const factors = {\n      hasQuantifiedAchievements: /\\d+(%|k|million|billion|\\$|year|month)/.test(safeOptimizedText),\n      hasActionVerbs: /(developed|implemented|managed|led|created|optimized|improved)/.test(safeOptimizedText.toLowerCase()),\n      hasStandardSections: /(experience|education|skills)/.test(safeOptimizedText.toLowerCase()),\n      hasContactInfo: /(email|phone|linkedin)/.test(safeOptimizedText.toLowerCase()),\n      appropriateLength: safeOptimizedText.length > 500 && safeOptimizedText.length < 4000,\n      noSpecialCharacters: !/[★☆▪▫◦]/.test(safeOptimizedText)\n    };\n    const score = Object.values(factors).filter(Boolean).length;\n    const maxScore = Object.keys(factors).length;\n    const percentage = Math.round(score / maxScore * 100);\n    return {\n      score: percentage,\n      factors,\n      improvements: maxScore - score\n    };\n  };\n\n  // 兼容处理不同的数据结构\n  const getOptimizedText = () => {\n    return (result === null || result === void 0 ? void 0 : result.optimized_resume) || (result === null || result === void 0 ? void 0 : result.optimized_text) || '';\n  };\n  const getOriginalText = () => {\n    return (result === null || result === void 0 ? void 0 : result.original_resume) || (result === null || result === void 0 ? void 0 : result.original_text) || '';\n  };\n\n  // 更精确的文本差异检测函数\n  const detectTextDifferences = (originalText, optimizedText) => {\n    const originalLines = originalText.split('\\n').filter(line => line.trim());\n    const optimizedLines = optimizedText.split('\\n').filter(line => line.trim());\n    const result = [];\n    const usedOptimizedIndices = new Set();\n    originalLines.forEach((originalLine, originalIndex) => {\n      const trimmedOriginal = originalLine.trim();\n      if (!trimmedOriginal) return;\n\n      // 寻找最佳匹配\n      let bestMatch = null;\n      let bestSimilarity = 0;\n      let bestOptimizedIndex = -1;\n      optimizedLines.forEach((optimizedLine, optimizedIdx) => {\n        if (usedOptimizedIndices.has(optimizedIdx)) return;\n        const trimmedOptimized = optimizedLine.trim();\n        const similarity = calculateSimilarity(trimmedOriginal, trimmedOptimized);\n        if (similarity > bestSimilarity && similarity > 0.3) {\n          bestMatch = optimizedLine;\n          bestSimilarity = similarity;\n          bestOptimizedIndex = optimizedIdx;\n        }\n      });\n      if (bestMatch && bestSimilarity > 0.7) {\n        // 高相似度，可能只是微调\n        usedOptimizedIndices.add(bestOptimizedIndex);\n        result.push({\n          type: 'modified',\n          original: originalLine,\n          optimized: bestMatch,\n          similarity: bestSimilarity\n        });\n      } else if (bestMatch && bestSimilarity > 0.3) {\n        // 中等相似度，显著修改\n        usedOptimizedIndices.add(bestOptimizedIndex);\n        result.push({\n          type: 'significantly_modified',\n          original: originalLine,\n          optimized: bestMatch,\n          similarity: bestSimilarity\n        });\n      } else {\n        // 没有找到匹配，可能被删除或完全重写\n        result.push({\n          type: 'deleted',\n          original: originalLine,\n          optimized: null\n        });\n      }\n    });\n    return result;\n  };\n\n  // 计算两个字符串的相似度\n  const calculateSimilarity = (str1, str2) => {\n    if (str1 === str2) return 1;\n    if (!str1 || !str2) return 0;\n    const words1 = str1.toLowerCase().split(/\\s+/);\n    const words2 = str2.toLowerCase().split(/\\s+/);\n    const commonWords = words1.filter(word => words2.includes(word));\n    const totalWords = Math.max(words1.length, words2.length);\n    return commonWords.length / totalWords;\n  };\n\n  // 在优化文本中高亮新增、修改、删除内容\n  const highlightAdditionsInOptimized = optimizedText => {\n    const originalText = getOriginalText();\n    if (!originalText || !optimizedText || originalText === optimizedText) {\n      // 没有原文或内容一致，直接返回普通格式\n      return formatOptimizedResume(optimizedText);\n    }\n    // 差异检测\n    const differences = detectTextDifferences(originalText, optimizedText);\n    const optimizedLines = optimizedText.split('\\n').filter(line => line.trim());\n    const originalLines = originalText.split('\\n').filter(line => line.trim());\n    const usedOriginalIndices = new Set();\n    const usedOptimizedIndices = new Set();\n    // 标记已匹配的行\n    differences.forEach((diff, idx) => {\n      if (diff.type !== 'deleted') {\n        const optIdx = optimizedLines.findIndex(line => {\n          var _diff$optimized;\n          return line.trim() === ((_diff$optimized = diff.optimized) === null || _diff$optimized === void 0 ? void 0 : _diff$optimized.trim());\n        });\n        if (optIdx !== -1) usedOptimizedIndices.add(optIdx);\n      }\n      if (diff.type !== 'added') {\n        const oriIdx = originalLines.findIndex(line => {\n          var _diff$original;\n          return line.trim() === ((_diff$original = diff.original) === null || _diff$original === void 0 ? void 0 : _diff$original.trim());\n        });\n        if (oriIdx !== -1) usedOriginalIndices.add(oriIdx);\n      }\n    });\n    // 渲染优化内容\n    return optimizedLines.map((line, idx) => {\n      const escapedLine = line.replace(/</g, '&lt;').replace(/>/g, '&gt;');\n      // 检查是否为修改说明（如以This revised resume/This resume等开头，且为最后一段）\n      const isRevisionNote = idx === optimizedLines.length - 1 && /^(This revised resume|This resume|优化说明|修改说明|优化总结|本次修改说明|优化建议)/i.test(line.trim());\n      if (isRevisionNote) {\n        // 检查是否为中文优化说明，若是则替换为英文模板\n        let revisionContent = line.trim();\n        if (/^(本次修改说明|优化建议)/.test(revisionContent)) {\n          revisionContent = `This revision note summarizes the main changes and optimization suggestions:\\n\\n- Integrated keywords from the target job, such as \\\"data analysis\\\", \\\"project management\\\", \\\"big data\\\", etc.\\n- Reorganized the order of work experience to highlight the most relevant experiences.\\n- Used action verbs to describe responsibilities and achievements.\\n- Formatted the resume to be ATS-friendly, removed tables and special characters.\\n\\nOptimization Suggestions:\\n- Quantified achievements for the \\\"Administrative Assistant\\\" role.\\n- Added specific descriptions of data collection and analysis for the \\\"Intern\\\" position.`;\n        }\n        return `<p class=\\\"mb-3 leading-relaxed bg-gray-50 border-l-4 border-gray-400 px-4 py-2 italic text-gray-700 font-semibold\\\" style=\\\"font-style:italic;\\\"><span class=\\\"text-gray-500 font-bold mr-2\\\">Revision Note:</span>${revisionContent.replace(/\\n/g, '<br>')}</p>`;\n      }\n      // 新增内容\n      if (!usedOriginalIndices.has(idx) && line.trim().length > 0) {\n        return `<p class=\\\"mb-3 leading-relaxed\\\"><span class=\\\"inline-block bg-green-50 text-green-700 font-medium px-2 py-1 rounded border border-green-200 mr-1\\\" title=\\\"新增内容\\\">✓ ${escapedLine}</span></p>`;\n      }\n      // 修改内容\n      const diff = differences.find(d => {\n        var _d$optimized;\n        return ((_d$optimized = d.optimized) === null || _d$optimized === void 0 ? void 0 : _d$optimized.trim()) === line.trim() && d.type === 'significantly_modified';\n      });\n      if (diff) {\n        return `<p class=\\\"mb-3 leading-relaxed\\\"><span class=\\\"inline-block bg-orange-50 text-orange-700 font-medium px-2 py-1 rounded border border-orange-200 mr-1\\\" title=\\\"修改内容\\\">${escapedLine}</span></p>`;\n      }\n      // 轻微修改\n      const diff2 = differences.find(d => {\n        var _d$optimized2;\n        return ((_d$optimized2 = d.optimized) === null || _d$optimized2 === void 0 ? void 0 : _d$optimized2.trim()) === line.trim() && d.type === 'modified';\n      });\n      if (diff2) {\n        return `<p class=\\\"mb-3 leading-relaxed\\\"><span class=\\\"inline-block bg-yellow-50 text-yellow-700 font-medium px-2 py-1 rounded border border-yellow-200 mr-1\\\" title=\\\"轻微修改\\\">${escapedLine}</span></p>`;\n      }\n      // 普通内容\n      return `<p class=\\\"mb-3 text-gray-700 leading-relaxed\\\">${escapedLine}</p>`;\n    }).join('');\n    // 删除内容在优化内容中不显示，但可选在末尾加提示\n    // differences.filter(d => d.type === 'deleted').map(...)\n  };\n  const atsScore = generateATSScore(getOptimizedText(), getOriginalText());\n  const getScoreColor = score => {\n    if (score >= 80) return 'text-green-600';\n    if (score >= 60) return 'text-orange-600';\n    return 'text-red-600';\n  };\n  const getScoreBg = score => {\n    if (score >= 80) return 'bg-green-100';\n    if (score >= 60) return 'bg-orange-100';\n    return 'bg-red-100';\n  };\n  const getHeaderBg = score => {\n    if (score >= 80) return 'bg-green-50 border-green-200';\n    if (score >= 60) return 'bg-orange-50 border-orange-200';\n    return 'bg-red-50 border-red-200';\n  };\n  const getIconBg = score => {\n    if (score >= 80) return 'bg-green-500';\n    if (score >= 60) return 'bg-orange-500';\n    return 'bg-red-500';\n  };\n  const getProgressBarColor = score => {\n    if (score >= 80) return 'bg-green-500';\n    if (score >= 60) return 'bg-orange-500';\n    return 'bg-red-500';\n  };\n  const getBorderColor = score => {\n    if (score >= 80) return 'border-green-200';\n    if (score >= 60) return 'border-orange-200';\n    return 'border-red-200';\n  };\n\n  // 处理DOCX下载\n  const handleDownloadDocx = async () => {\n    try {\n      const formData = new FormData();\n      formData.append('file', originalFile);\n      formData.append('job_description', jobDescription);\n      const response = await fetch('/api/optimize-resume-docx', {\n        method: 'POST',\n        body: formData\n      });\n      if (!response.ok) {\n        const error = await response.json();\n        throw new Error(error.error || '下载失败');\n      }\n\n      // 获取文件名\n      const contentDisposition = response.headers.get('content-disposition');\n      const filename = contentDisposition ? contentDisposition.split('filename=')[1].replace(/\"/g, '') : 'optimized_resume.docx';\n\n      // 下载文件\n      const blob = await response.blob();\n      const url = window.URL.createObjectURL(blob);\n      const a = document.createElement('a');\n      a.href = url;\n      a.download = filename;\n      document.body.appendChild(a);\n      a.click();\n      window.URL.revokeObjectURL(url);\n      document.body.removeChild(a);\n    } catch (error) {\n      console.error('下载DOCX失败:', error);\n      alert('下载失败: ' + error.message);\n    }\n  };\n  const toggleExpand = (key, type) => {\n    if (type === 'matched') {\n      setExpandedMatched(prev => ({\n        ...prev,\n        [key]: !prev[key]\n      }));\n    } else {\n      setExpandedMissing(prev => ({\n        ...prev,\n        [key]: !prev[key]\n      }));\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `${getHeaderBg(atsScore.score)} px-8 py-8 border-b relative overflow-hidden`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-0 right-0 transform translate-x-4 -translate-y-4 opacity-10\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-32 h-32\",\n              fill: \"currentColor\",\n              viewBox: \"0 0 20 20\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                fillRule: \"evenodd\",\n                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                clipRule: \"evenodd\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-6 lg:mb-0\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `${getIconBg(atsScore.score)} text-white rounded-full w-12 h-12 flex items-center justify-center mr-4 shadow-lg`,\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-6 h-6\",\n                      fill: \"none\",\n                      stroke: \"currentColor\",\n                      viewBox: \"0 0 24 24\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M5 13l4 4L19 7\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 326,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 325,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 324,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-3xl font-bold text-gray-900 mb-1\",\n                      children: \"Resume Optimization Complete\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 330,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center text-sm text-gray-600\",\n                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                        className: \"w-4 h-4 mr-1\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          strokeWidth: 2,\n                          d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 335,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 334,\n                        columnNumber: 25\n                      }, this), \"AI has optimized your resume according to job requirements, improving ATS pass rate\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 333,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 329,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 323,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `bg-white border-2 ${getBorderColor(atsScore.score)} rounded-2xl px-8 py-6 text-center min-w-[160px] shadow-lg transform hover:scale-105 transition-transform`,\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `text-4xl font-extrabold mb-2 ${getScoreColor(atsScore.score)}`,\n                    children: [atsScore.score, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 346,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-semibold text-gray-600 mb-2\",\n                    children: \"ATS Match\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 349,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full bg-gray-200 rounded-full h-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `h-2 rounded-full transition-all duration-1000 ${getProgressBarColor(atsScore.score)}`,\n                      style: {\n                        width: `${atsScore.score}%`\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 351,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 350,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 345,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `inline-flex items-center px-4 py-2 rounded-full text-sm font-medium ${atsScore.score >= 80 ? 'bg-green-100 text-green-800' : atsScore.score >= 60 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}`,\n                    children: atsScore.score >= 80 ? 'Excellent' : atsScore.score >= 60 ? 'Good' : 'Needs Improvement'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 360,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-gray-500 mt-1\",\n                    children: atsScore.score >= 80 ? 'Very likely to pass ATS screening' : atsScore.score >= 60 ? 'High chance to pass ATS screening' : 'Recommend further optimization'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 367,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 359,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 11\n        }, this), (result === null || result === void 0 ? void 0 : result.job_match_analysis) && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold text-gray-900 mb-2\",\n              children: \"Job Match Analysis\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"AI analysis shows significant improvement in resume-job alignment\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 rounded-lg p-6 text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold text-gray-500 mb-2\",\n                children: [((_result$job_match_ana = result.job_match_analysis.original_match_score) === null || _result$job_match_ana === void 0 ? void 0 : _result$job_match_ana.overall_match_score) || 0, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 391,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600 mb-3\",\n                children: \"Original Match\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full bg-gray-200 rounded-full h-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-gray-400 h-2 rounded-full transition-all duration-1000\",\n                  style: {\n                    width: `${((_result$job_match_ana2 = result.job_match_analysis.original_match_score) === null || _result$job_match_ana2 === void 0 ? void 0 : _result$job_match_ana2.overall_match_score) || 0}%`\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 396,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-green-50 rounded-lg p-6 border-2 border-green-200 text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-center mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5 text-green-600 mr-2\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M5 13l4 4L19 7\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 407,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-2xl font-bold text-green-600\",\n                  children: [((_result$job_match_ana3 = result.job_match_analysis.optimized_match_score) === null || _result$job_match_ana3 === void 0 ? void 0 : _result$job_match_ana3.overall_match_score) || 0, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 409,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 405,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-green-600 font-medium mb-3\",\n                children: \"Optimized Match\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 413,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full bg-green-100 rounded-full h-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-green-500 h-2 rounded-full transition-all duration-1000\",\n                  style: {\n                    width: `${((_result$job_match_ana4 = result.job_match_analysis.optimized_match_score) === null || _result$job_match_ana4 === void 0 ? void 0 : _result$job_match_ana4.overall_match_score) || 0}%`\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 415,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-blue-50 rounded-lg p-6 text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `text-2xl font-bold mb-2 ${result.job_match_analysis.match_improvement >= 0 ? 'text-blue-600' : 'text-orange-600'}`,\n                children: [result.job_match_analysis.match_improvement >= 0 ? '+' : '', result.job_match_analysis.match_improvement, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600 mb-3\",\n                children: \"Improvement\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 427,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-center text-xs text-gray-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-3 h-3 mr-1 text-blue-500\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 430,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 429,\n                  columnNumber: 21\n                }, this), \"Significant Growth\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 15\n          }, this), ((_result$job_match_ana5 = result.job_match_analysis.optimized_match_score) === null || _result$job_match_ana5 === void 0 ? void 0 : _result$job_match_ana5.breakdown) && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-50 rounded-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-md font-medium text-gray-800 mb-4\",\n              children: \"Skill Match Breakdown\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n              children: Object.entries(result.job_match_analysis.optimized_match_score.breakdown).map(([key, data]) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between p-3 bg-white rounded-lg border border-gray-100\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-700 capitalize font-medium\",\n                  children: key.replace('_', ' ')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 444,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `text-sm font-semibold mr-3 ${data.score >= 70 ? 'text-green-600' : data.score >= 50 ? 'text-yellow-600' : 'text-red-600'}`,\n                    children: [data.score, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 448,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-16 bg-gray-200 rounded-full h-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `h-2 rounded-full ${data.score >= 70 ? 'bg-green-500' : data.score >= 50 ? 'bg-yellow-500' : 'bg-red-500'}`,\n                      style: {\n                        width: `${data.score}%`\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 452,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 451,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 447,\n                  columnNumber: 25\n                }, this)]\n              }, key, true, {\n                fileName: _jsxFileName,\n                lineNumber: 443,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 439,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 310,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 309,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border-b border-gray-200 bg-gray-50\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"px-8\",\n            children: /*#__PURE__*/_jsxDEV(\"nav\", {\n              className: \"flex space-x-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setActiveTab('optimized'),\n                className: `py-4 px-1 border-b-2 font-medium text-sm transition-colors ${activeTab === 'optimized' ? 'border-orange-500 text-orange-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-4 h-4\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 488,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 487,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Optimized Resume\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 490,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 486,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 478,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setActiveTab('analysis'),\n                className: `py-4 px-1 border-b-2 font-medium text-sm transition-colors ${activeTab === 'analysis' ? 'border-orange-500 text-orange-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-4 h-4\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 504,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 503,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"ATS Analysis\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 506,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 502,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 494,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 477,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 476,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 475,\n          columnNumber: 11\n        }, this), activeTab === 'optimized' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-8 py-6 border-b border-gray-100\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"text-xl font-semibold text-gray-900 mb-6 flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-6 h-6 mr-3 text-gray-600\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 518,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 517,\n              columnNumber: 17\n            }, this), \"Optimization Statistics\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 516,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 rounded-lg p-6 text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium text-gray-500 mb-2\",\n                children: \"Original Length\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 526,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold text-gray-900 mb-1\",\n                children: (result === null || result === void 0 ? void 0 : (_result$original_leng = result.original_length) === null || _result$original_leng === void 0 ? void 0 : _result$original_leng.toLocaleString()) || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 527,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-gray-400\",\n                children: \"Characters\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 530,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 525,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 rounded-lg p-6 text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium text-gray-500 mb-2\",\n                children: \"Optimized Length\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 535,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold text-gray-900 mb-1\",\n                children: (result === null || result === void 0 ? void 0 : (_result$optimized_len = result.optimized_length) === null || _result$optimized_len === void 0 ? void 0 : _result$optimized_len.toLocaleString()) || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 536,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-gray-400\",\n                children: \"Characters\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 539,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-blue-50 rounded-lg p-6 text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium text-gray-500 mb-2\",\n                children: \"Change\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 544,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold mb-1\",\n                children: (() => {\n                  const original = (result === null || result === void 0 ? void 0 : result.original_length) || 0;\n                  const optimized = (result === null || result === void 0 ? void 0 : result.optimized_length) || 0;\n                  const diff = optimized - original;\n                  if (original === 0 || optimized === 0) return /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-400\",\n                    children: \"N/A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 550,\n                    columnNumber: 69\n                  }, this);\n                  return /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: diff >= 0 ? 'text-blue-600' : 'text-orange-600',\n                    children: [diff >= 0 ? '+' : '', diff.toLocaleString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 552,\n                    columnNumber: 25\n                  }, this);\n                })()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 545,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-gray-400\",\n                children: \"Characters\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 558,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-green-50 rounded-lg p-6 text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium text-gray-500 mb-2\",\n                children: \"Optimization Rate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 563,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold mb-1\",\n                children: (() => {\n                  const original = (result === null || result === void 0 ? void 0 : result.original_length) || 0;\n                  const optimized = (result === null || result === void 0 ? void 0 : result.optimized_length) || 0;\n                  if (original === 0 || optimized === 0) return /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-400\",\n                    children: \"N/A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 568,\n                    columnNumber: 69\n                  }, this);\n                  let displayRate = 0;\n                  if (optimized > original) {\n                    displayRate = (optimized - original) / optimized * 100;\n                  } else {\n                    displayRate = (original - optimized) / original * 100;\n                  }\n                  displayRate = Math.max(0, displayRate);\n                  const colorClass = displayRate > 15 ? 'text-green-600' : displayRate > 5 ? 'text-blue-600' : 'text-orange-600';\n                  return /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: colorClass,\n                    children: [displayRate.toFixed(1), \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 578,\n                    columnNumber: 25\n                  }, this);\n                })()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 564,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-gray-400\",\n                children: \"Adjustment\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 584,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 562,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 523,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 515,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-8\",\n          children: [activeTab === 'optimized' && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6 flex justify-between items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-4 text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-600\",\n                  children: \"Optimized Resume View:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 597,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setViewMode('raw'),\n                    className: `px-3 py-1 rounded-md text-xs font-medium transition-colors ${viewMode === 'raw' ? 'bg-orange-100 text-orange-700' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,\n                    children: \"Raw Text\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 599,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setViewMode('enhanced'),\n                    className: `px-3 py-1 rounded-md text-xs font-medium transition-colors ${viewMode === 'enhanced' ? 'bg-orange-100 text-orange-700' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,\n                    children: \"Enhanced View\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 609,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setViewMode('word'),\n                    className: `px-3 py-1 rounded-md text-xs font-medium transition-colors ${viewMode === 'word' ? 'bg-orange-100 text-orange-700' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,\n                    disabled: !(result !== null && result !== void 0 && result.optimized_docx_url),\n                    children: \"Word Preview\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 619,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 598,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 596,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative export-dropdown\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setShowExportDropdown(!showExportDropdown),\n                  className: \"inline-flex items-center px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors text-sm font-medium\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-4 h-4 mr-2\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 638,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 637,\n                    columnNumber: 23\n                  }, this), \"Export & Download\", /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-4 h-4 ml-2\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M19 9l-7 7-7-7\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 642,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 641,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 633,\n                  columnNumber: 21\n                }, this), showExportDropdown && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 z-50\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"py-2\",\n                    children: [(result === null || result === void 0 ? void 0 : result.isFormatPreserved) && (result === null || result === void 0 ? void 0 : result.download_url) && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          handleDownloadDocx();\n                          setShowExportDropdown(false);\n                        },\n                        disabled: downloadingDocx,\n                        className: \"w-full text-left px-4 py-3 hover:bg-purple-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3\",\n                            children: downloadingDocx ? /*#__PURE__*/_jsxDEV(\"svg\", {\n                              className: \"animate-spin w-4 h-4 text-purple-600\",\n                              fill: \"none\",\n                              viewBox: \"0 0 24 24\",\n                              children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                                className: \"opacity-25\",\n                                cx: \"12\",\n                                cy: \"12\",\n                                r: \"10\",\n                                stroke: \"currentColor\",\n                                strokeWidth: \"4\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 663,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                                className: \"opacity-75\",\n                                fill: \"currentColor\",\n                                d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 664,\n                                columnNumber: 41\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 662,\n                              columnNumber: 39\n                            }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n                              className: \"w-4 h-4 text-purple-600\",\n                              fill: \"none\",\n                              stroke: \"currentColor\",\n                              viewBox: \"0 0 24 24\",\n                              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 668,\n                                columnNumber: 41\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 667,\n                              columnNumber: 39\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 660,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"font-medium text-purple-900\",\n                              children: downloadingDocx ? 'Downloading...' : 'Download Original Format'\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 673,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-xs text-purple-600\",\n                              children: \"DOCX with preserved formatting\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 676,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 672,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 659,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 651,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"border-t border-gray-100 my-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 680,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => {\n                        exportToPDF(getOptimizedText());\n                        setShowExportDropdown(false);\n                      },\n                      className: \"w-full text-left px-4 py-3 hover:bg-red-50 transition-colors\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center mr-3\",\n                          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                            className: \"w-4 h-4 text-red-600\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/_jsxDEV(\"path\", {\n                              strokeLinecap: \"round\",\n                              strokeLinejoin: \"round\",\n                              strokeWidth: 2,\n                              d: \"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 694,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 693,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 692,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"font-medium text-gray-900\",\n                            children: \"Export PDF\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 698,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-xs text-gray-500\",\n                            children: \"Download as PDF document\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 699,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 697,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 691,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 684,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => {\n                        exportToWord(getOptimizedText());\n                        setShowExportDropdown(false);\n                      },\n                      className: \"w-full text-left px-4 py-3 hover:bg-blue-50 transition-colors\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3\",\n                          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                            className: \"w-4 h-4 text-blue-600\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/_jsxDEV(\"path\", {\n                              strokeLinecap: \"round\",\n                              strokeLinejoin: \"round\",\n                              strokeWidth: 2,\n                              d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 714,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 713,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 712,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"font-medium text-gray-900\",\n                            children: \"Export Word\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 718,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-xs text-gray-500\",\n                            children: \"Download as DOCX document\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 719,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 717,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 711,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 704,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"border-t border-gray-100 my-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 724,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => {\n                        window.print();\n                        setShowExportDropdown(false);\n                      },\n                      className: \"w-full text-left px-4 py-3 hover:bg-gray-50 transition-colors\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center mr-3\",\n                          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                            className: \"w-4 h-4 text-gray-600\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/_jsxDEV(\"path\", {\n                              strokeLinecap: \"round\",\n                              strokeLinejoin: \"round\",\n                              strokeWidth: 2,\n                              d: \"M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 736,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 735,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 734,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"font-medium text-gray-900\",\n                            children: \"Print Resume\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 740,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-xs text-gray-500\",\n                            children: \"Print directly from browser\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 741,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 739,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 733,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 726,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 648,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 647,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 632,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 595,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"paper-preview rounded-lg p-10 mx-auto max-w-5xl min-h-[800px] relative\",\n              children: [viewMode === 'raw' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: (_lines => {\n                  const text = getOptimizedText() || 'Optimized resume content not available';\n                  const lines = text.split('\\n');\n                  const lastLine = (_lines = lines[lines.length - 1]) === null || _lines === void 0 ? void 0 : _lines.trim();\n                  const isRevisionNote = /^(This revised resume|This resume|优化说明|修改说明|优化总结|本次修改说明|优化建议)/i.test(lastLine);\n                  if (isRevisionNote) {\n                    const mainText = lines.slice(0, -1).join('\\n');\n                    return /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"pre\", {\n                        className: \"resume-content text-sm leading-relaxed text-gray-800 whitespace-pre-wrap\",\n                        children: mainText\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 765,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mb-3 leading-relaxed bg-gray-50 border-l-4 border-gray-400 px-4 py-2 italic text-gray-700 font-semibold\",\n                        style: {\n                          fontStyle: 'italic'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-gray-500 font-bold mr-2\",\n                          children: \"Revision Note:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 769,\n                          columnNumber: 33\n                        }, this), lastLine]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 768,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true);\n                  } else {\n                    return /*#__PURE__*/_jsxDEV(\"pre\", {\n                      className: \"resume-content text-sm leading-relaxed text-gray-800 whitespace-pre-wrap\",\n                      children: text\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 775,\n                      columnNumber: 29\n                    }, this);\n                  }\n                })()\n              }, void 0, false), viewMode === 'enhanced' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"resume-content text-sm leading-relaxed text-gray-800\",\n                dangerouslySetInnerHTML: {\n                  __html: highlightAdditionsInOptimized(getOptimizedText())\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 784,\n                columnNumber: 21\n              }, this), viewMode === 'word' && (result === null || result === void 0 ? void 0 : result.optimized_docx_url) && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"word-preview-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"preview-tabs mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setWordPreviewMode('office'),\n                    className: `px-3 py-2 mr-2 rounded text-sm font-medium transition-colors ${wordPreviewMode === 'office' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`,\n                    children: \"Office Online\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 794,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setWordPreviewMode('google'),\n                    className: `px-3 py-2 mr-2 rounded text-sm font-medium transition-colors ${wordPreviewMode === 'google' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`,\n                    children: \"Google Docs\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 804,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setWordPreviewMode('download'),\n                    className: `px-3 py-2 rounded text-sm font-medium transition-colors ${wordPreviewMode === 'download' ? 'bg-green-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`,\n                    children: \"\\u4E0B\\u8F7D\\u67E5\\u770B\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 814,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 793,\n                  columnNumber: 23\n                }, this), wordPreviewMode === 'office' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"preview-frame-container\",\n                  children: /*#__PURE__*/_jsxDEV(\"iframe\", {\n                    src: `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(result.optimized_docx_url)}`,\n                    width: \"100%\",\n                    height: \"800\",\n                    frameBorder: \"0\",\n                    title: \"Office Online Preview\",\n                    style: {\n                      background: '#fff',\n                      borderRadius: '8px'\n                    },\n                    onError: () => setWordPreviewError('Office Online预览失败，请尝试其他方式')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 828,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 827,\n                  columnNumber: 25\n                }, this), wordPreviewMode === 'google' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"preview-frame-container\",\n                  children: /*#__PURE__*/_jsxDEV(\"iframe\", {\n                    src: `https://docs.google.com/gview?url=${encodeURIComponent(result.optimized_docx_url)}&embedded=true`,\n                    width: \"100%\",\n                    height: \"800\",\n                    frameBorder: \"0\",\n                    title: \"Google Docs Preview\",\n                    style: {\n                      background: '#fff',\n                      borderRadius: '8px'\n                    },\n                    onError: () => setWordPreviewError('Google Docs预览失败，请尝试其他方式')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 842,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 841,\n                  columnNumber: 25\n                }, this), wordPreviewMode === 'download' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"download-preview-container p-8 text-center bg-gray-50 rounded-lg\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-16 h-16 mx-auto text-blue-500 mb-4\",\n                      fill: \"none\",\n                      stroke: \"currentColor\",\n                      viewBox: \"0 0 24 24\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 858,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 857,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-semibold text-gray-800 mb-2\",\n                      children: \"Word\\u6587\\u6863\\u5DF2\\u51C6\\u5907\\u5C31\\u7EEA\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 860,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-600 mb-4\",\n                      children: [\"\\u7531\\u4E8E\\u6D4F\\u89C8\\u5668\\u9650\\u5236\\uFF0C\\u65E0\\u6CD5\\u76F4\\u63A5\\u9884\\u89C8Word\\u6587\\u6863\\u3002\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 862,\n                        columnNumber: 52\n                      }, this), \"\\u8BF7\\u70B9\\u51FB\\u4E0B\\u8F7D\\u6309\\u94AE\\u83B7\\u53D6\\u4F18\\u5316\\u540E\\u7684\\u6587\\u6863\\u3002\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 861,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 856,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                    href: result.optimized_docx_url,\n                    download: \"optimized_resume.docx\",\n                    className: \"inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-5 h-5 mr-2\",\n                      fill: \"none\",\n                      stroke: \"currentColor\",\n                      viewBox: \"0 0 24 24\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 872,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 871,\n                      columnNumber: 29\n                    }, this), \"\\u4E0B\\u8F7D\\u4F18\\u5316\\u540E\\u7684Word\\u6587\\u6863\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 866,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 855,\n                  columnNumber: 25\n                }, this), wordPreviewError && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-5 h-5 text-yellow-400 mr-2\",\n                      fill: \"none\",\n                      stroke: \"currentColor\",\n                      viewBox: \"0 0 24 24\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 883,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 882,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-yellow-800 font-medium\",\n                        children: \"\\u9884\\u89C8\\u63D0\\u793A\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 886,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-yellow-700 text-sm mt-1\",\n                        children: wordPreviewError\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 887,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 885,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 881,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 880,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 792,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 752,\n              columnNumber: 17\n            }, this), (result === null || result === void 0 ? void 0 : result.isFormatPreserved) && (result === null || result === void 0 ? void 0 : result.paragraph_changes) && result.paragraph_changes.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-8 bg-purple-50 border border-purple-200 rounded-lg p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"text-lg font-semibold text-purple-900 mb-4 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5 mr-2\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 901,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 900,\n                  columnNumber: 23\n                }, this), \"Paragraph-Level Changes Applied\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 899,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: (result.paragraph_changes || []).map((change, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-white rounded-lg p-4 border border-purple-100\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-medium text-purple-700 mb-2\",\n                    children: [\"Change #\", index + 1, \": \", change.change_reason]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 908,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-xs font-medium text-red-600 mb-1\",\n                        children: \"Original:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 913,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm text-gray-700 bg-red-50 p-2 rounded border\",\n                        children: change.original_text\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 914,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 912,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-xs font-medium text-green-600 mb-1\",\n                        children: \"Optimized:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 919,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm text-gray-700 bg-green-50 p-2 rounded border\",\n                        children: change.optimized_text\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 920,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 918,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 911,\n                    columnNumber: 27\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 907,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 905,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 898,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 594,\n            columnNumber: 15\n          }, this), activeTab === 'analysis' && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-2xl font-bold text-gray-900 mb-6\",\n              children: \"ATS Compatibility Analysis\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 936,\n              columnNumber: 17\n            }, this), (result === null || result === void 0 ? void 0 : (_result$job_match_ana6 = result.job_match_analysis) === null || _result$job_match_ana6 === void 0 ? void 0 : (_result$job_match_ana7 = _result$job_match_ana6.optimized_match_score) === null || _result$job_match_ana7 === void 0 ? void 0 : _result$job_match_ana7.breakdown) && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"text-md font-semibold text-gray-800 mb-3\",\n                children: \"Detailed Match Breakdown\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 940,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                children: Object.entries(result.job_match_analysis.optimized_match_score.breakdown).map(([key, data]) => {\n                  const matchedLimit = 5;\n                  const missingLimit = 3;\n                  const matchedExpanded = expandedMatched[key] || false;\n                  const missingExpanded = expandedMissing[key] || false;\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-gray-50 rounded-lg p-4 border\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between items-start mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                          className: \"font-semibold text-gray-700 capitalize\",\n                          children: key.replace('_', ' ')\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 951,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-xs text-gray-500 font-medium\",\n                          children: [\"Weight: \", data.weight]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 954,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 950,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `text-lg font-bold ${data.score >= 70 ? 'text-green-600' : data.score >= 50 ? 'text-yellow-600' : 'text-red-600'}`,\n                        children: [data.score, \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 956,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 949,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-full bg-gray-200 rounded-full h-2 mb-2\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `h-2 rounded-full transition-all duration-500 ${data.score >= 70 ? 'bg-green-500' : data.score >= 50 ? 'bg-yellow-500' : 'bg-red-500'}`,\n                        style: {\n                          width: `${data.score}%`\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 960,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 959,\n                      columnNumber: 29\n                    }, this), data.matched && data.total && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-600\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mb-1\",\n                        children: /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"font-medium\",\n                          children: [\"Matched (\", data.matched.length, \"/\", data.total.length, \"):\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 972,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 971,\n                        columnNumber: 33\n                      }, this), data.matched.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex flex-wrap gap-1 mb-2\",\n                        children: [(matchedExpanded ? data.matched : data.matched.slice(0, matchedLimit)).map((item, idx) => /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"bg-green-100 text-green-700 px-2 py-1 rounded text-xs\",\n                          children: item\n                        }, idx, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 977,\n                          columnNumber: 39\n                        }, this)), data.matched.length > matchedLimit && /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-gray-500 cursor-pointer underline\",\n                          onClick: () => toggleExpand(key, 'matched'),\n                          children: matchedExpanded ? '收起' : `+${data.matched.length - matchedLimit} more`\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 980,\n                          columnNumber: 39\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 975,\n                        columnNumber: 35\n                      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-gray-500 mb-2\",\n                        children: \"No matches found\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 989,\n                        columnNumber: 35\n                      }, this), data.total.length > data.matched.length && /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"font-medium text-red-600\",\n                          children: \"Missing:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 993,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-wrap gap-1 mt-1\",\n                          children: [(missingExpanded ? data.total.filter(item => !data.matched.includes(item)) : data.total.filter(item => !data.matched.includes(item)).slice(0, missingLimit)).map((item, idx) => /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"bg-red-100 text-red-700 px-2 py-1 rounded text-xs\",\n                            children: item\n                          }, idx, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 996,\n                            columnNumber: 41\n                          }, this)), data.total.length - data.matched.length > missingLimit && /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-gray-500 cursor-pointer underline\",\n                            onClick: () => toggleExpand(key, 'missing'),\n                            children: missingExpanded ? '收起' : `+${data.total.length - data.matched.length - missingLimit} more`\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 999,\n                            columnNumber: 41\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 994,\n                          columnNumber: 37\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 992,\n                        columnNumber: 35\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 970,\n                      columnNumber: 31\n                    }, this)]\n                  }, key, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 948,\n                    columnNumber: 27\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 941,\n                columnNumber: 21\n              }, this), (() => {\n                const allMissing = Object.entries(result.job_match_analysis.optimized_match_score.breakdown).map(([key, data]) => ({\n                  key,\n                  weight: data.weight,\n                  missing: data.total ? data.total.filter(item => !data.matched.includes(item)) : []\n                })).filter(item => item.missing.length > 0).sort((a, b) => b.weight - a.weight);\n                if (allMissing.length === 0) return null;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-red-50 border border-red-200 rounded-lg p-6 mt-8 mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-lg font-bold text-red-700 mb-2 flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-5 h-5 mr-2 text-red-500\",\n                      fill: \"none\",\n                      stroke: \"currentColor\",\n                      viewBox: \"0 0 24 24\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M18.364 5.636l-1.414 1.414A9 9 0 105.636 18.364l1.414-1.414A7 7 0 1116.95 7.05z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1030,\n                        columnNumber: 126\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1030,\n                      columnNumber: 29\n                    }, this), \"\\u4E0E\\u5C97\\u4F4D\\u63CF\\u8FF0\\u7684\\u5173\\u952E\\u5DEE\\u8DDD\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1029,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-wrap gap-2\",\n                    children: allMissing.slice(0, 3).map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mr-4 mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-semibold text-gray-700 mr-2\",\n                        children: [item.key.replace('_', ' '), \":\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1036,\n                        columnNumber: 33\n                      }, this), item.missing.slice(0, 3).map((miss, idx) => /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"bg-red-100 text-red-700 px-2 py-1 rounded text-xs mr-1\",\n                        children: miss\n                      }, idx, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1038,\n                        columnNumber: 35\n                      }, this)), item.missing.length > 3 && /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-gray-500\",\n                        children: [\"+\", item.missing.length - 3, \" more\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1040,\n                        columnNumber: 61\n                      }, this)]\n                    }, item.key, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1035,\n                      columnNumber: 31\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1033,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1028,\n                  columnNumber: 25\n                }, this);\n              })(), _s(() => {\n                _s();\n                const suggestions = Object.entries(result.job_match_analysis.optimized_match_score.breakdown).filter(([key, data]) => data.score < 60).map(([key, data]) => {\n                  let tip = '';\n                  if (key.toLowerCase().includes('action')) tip = '建议补充更多动词，突出行动力。';else if (key.toLowerCase().includes('skill')) tip = '建议补充与岗位相关的技能。';else if (key.toLowerCase().includes('industry')) tip = '建议增加行业术语，提升专业性。';else if (key.toLowerCase().includes('experience')) tip = '建议丰富相关经验描述。';else if (key.toLowerCase().includes('education')) tip = '建议完善教育经历信息。';else tip = '建议补充相关内容，提升匹配度。';\n                  return `【${key.replace('_', ' ')}】${tip}`;\n                });\n                const [showAll, setShowAll] = React.useState(false);\n                if (suggestions.length === 0) return null;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-blue-50 border border-blue-200 rounded-lg p-6 mt-4 mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center mb-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"text-lg font-bold text-blue-700 mr-4\",\n                      children: \"\\u4F18\\u5316\\u5EFA\\u8BAE\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1067,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"ml-auto px-4 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm\",\n                      onClick: () => setShowAll(v => !v),\n                      children: showAll ? '收起全部建议' : '一键展开全部建议'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1068,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1066,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                    className: \"list-disc pl-6 text-blue-800\",\n                    children: [(showAll ? suggestions : suggestions.slice(0, 2)).map((s, i) => /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: s\n                    }, i, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1075,\n                      columnNumber: 31\n                    }, this)), !showAll && suggestions.length > 2 && /*#__PURE__*/_jsxDEV(\"li\", {\n                      className: \"text-gray-500\",\n                      children: \"...\\u66F4\\u591A\\u5EFA\\u8BAE\\u8BF7\\u70B9\\u51FB\\u4E0A\\u65B9\\u6309\\u94AE\\u5C55\\u5F00\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1078,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1073,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1065,\n                  columnNumber: 25\n                }, this);\n              }, \"XC0nqMp5RnZIWkiCcJL//MdTvak=\")(), (() => {\n                const radarData = Object.entries(result.job_match_analysis.optimized_match_score.breakdown).map(([key, data]) => ({\n                  name: key.replace('_', ' '),\n                  value: data.score\n                }));\n                if (radarData.length < 3) return null; // 至少3项才显示雷达图\n                const indicator = radarData.map(item => ({\n                  name: item.name,\n                  max: 100\n                }));\n                const option = {\n                  tooltip: {},\n                  radar: {\n                    indicator,\n                    radius: 80\n                  },\n                  series: [{\n                    type: 'radar',\n                    data: [{\n                      value: radarData.map(d => d.value),\n                      name: '覆盖度'\n                    }],\n                    areaStyle: {\n                      opacity: 0.2\n                    },\n                    lineStyle: {\n                      width: 2\n                    }\n                  }]\n                };\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-white border border-gray-200 rounded-lg p-6 mt-4 mb-4 flex flex-col items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-lg font-bold text-gray-800 mb-2\",\n                    children: \"\\u7B80\\u5386\\u5185\\u5BB9\\u8986\\u76D6\\u5EA6\\u96F7\\u8FBE\\u56FE\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1108,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: 350,\n                      height: 300\n                    },\n                    children: /*#__PURE__*/_jsxDEV(ReactECharts, {\n                      option: option,\n                      style: {\n                        width: '100%',\n                        height: '100%'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1110,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1109,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1107,\n                  columnNumber: 25\n                }, this);\n              })(), (() => {\n                const optimizedText = getOptimizedText();\n                const issues = [];\n                if (/[★☆▪▫◦]/.test(optimizedText)) issues.push('检测到特殊符号，建议删除。');\n                if (!/(email|邮箱|@)/i.test(optimizedText)) issues.push('未检测到邮箱信息。');\n                if (!/(phone|电话|\\d{11,})/i.test(optimizedText)) issues.push('未检测到电话信息。');\n                if (optimizedText.length < 500) issues.push('简历内容过短，建议丰富。');\n                if (optimizedText.length > 4000) issues.push('简历内容过长，建议精简。');\n                if (issues.length === 0) return null;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-6 mt-4 mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-lg font-bold text-yellow-700 mb-2 flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-5 h-5 mr-2 text-yellow-500\",\n                      fill: \"none\",\n                      stroke: \"currentColor\",\n                      viewBox: \"0 0 24 24\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1129,\n                        columnNumber: 129\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1129,\n                      columnNumber: 29\n                    }, this), \"ATS\\u5E38\\u89C1\\u95EE\\u9898\\u68C0\\u6D4B\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1128,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                    className: \"list-disc pl-6 text-yellow-800\",\n                    children: issues.map((issue, idx) => /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: issue\n                    }, idx, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1133,\n                      columnNumber: 57\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1132,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1127,\n                  columnNumber: 25\n                }, this);\n              })()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 939,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 935,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 591,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 473,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 472,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 307,\n    columnNumber: 5\n  }, this);\n};\n_s2(ResultDisplay, \"NNvtFdpsRAjLPp/lioOLWYpzyfY=\");\n_c = ResultDisplay;\nexport default ResultDisplay;\nvar _c;\n$RefreshReg$(_c, \"ResultDisplay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "exportToPDF", "exportToWord", "exportToText", "downloadOptimizedDocx", "DocumentPreview", "ReactECharts", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ResultDisplay", "result", "originalFile", "jobDescription", "_s2", "_result$job_match_ana", "_result$job_match_ana2", "_result$job_match_ana3", "_result$job_match_ana4", "_result$job_match_ana5", "_result$original_leng", "_result$optimized_len", "_result$job_match_ana6", "_result$job_match_ana7", "_s", "$RefreshSig$", "showComparison", "setShowComparison", "activeTab", "setActiveTab", "showFormattedOptimized", "setShowFormattedOptimized", "downloadingDocx", "setDownloadingDocx", "showExportDropdown", "setShowExportDropdown", "viewMode", "setViewMode", "wordPreviewMode", "setWordPreviewMode", "wordPreviewError", "setWordPreviewError", "expandedMatched", "setExpandedMatched", "expandedMissing", "setExpandedMissing", "handleClickOutside", "event", "target", "closest", "document", "addEventListener", "removeEventListener", "formatOptimizedResume", "text", "replace", "generateATSScore", "optimizedText", "originalText", "safeOptimizedText", "safeOriginalText", "factors", "hasQuantifiedAchievements", "test", "hasActionVerbs", "toLowerCase", "hasStandardSections", "hasContactInfo", "appropriateLength", "length", "noSpecialCharacters", "score", "Object", "values", "filter", "Boolean", "maxScore", "keys", "percentage", "Math", "round", "improvements", "getOptimizedText", "optimized_resume", "optimized_text", "getOriginalText", "original_resume", "original_text", "detectTextDifferences", "originalLines", "split", "line", "trim", "optimizedLines", "usedOptimizedIndices", "Set", "for<PERSON>ach", "originalLine", "originalIndex", "trimmedOriginal", "bestMatch", "bestSimilarity", "bestOptimizedIndex", "optimizedLine", "optimizedIdx", "has", "trimmedOptimized", "similarity", "calculateSimilarity", "add", "push", "type", "original", "optimized", "str1", "str2", "words1", "words2", "commonWords", "word", "includes", "totalWords", "max", "highlightAdditionsInOptimized", "differences", "usedOriginalIndices", "diff", "idx", "optIdx", "findIndex", "_diff$optimized", "oriIdx", "_diff$original", "map", "escapedLine", "isRevisionNote", "revisionContent", "find", "d", "_d$optimized", "diff2", "_d$optimized2", "join", "atsScore", "getScoreColor", "getScoreBg", "getHeaderBg", "getIconBg", "getProgressBarColor", "getBorderColor", "handleDownloadDocx", "formData", "FormData", "append", "response", "fetch", "method", "body", "ok", "error", "json", "Error", "contentDisposition", "headers", "get", "filename", "blob", "url", "window", "URL", "createObjectURL", "a", "createElement", "href", "download", "append<PERSON><PERSON><PERSON>", "click", "revokeObjectURL", "<PERSON><PERSON><PERSON><PERSON>", "console", "alert", "message", "toggleExpand", "key", "prev", "children", "className", "fill", "viewBox", "fillRule", "clipRule", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "style", "width", "job_match_analysis", "original_match_score", "overall_match_score", "optimized_match_score", "match_improvement", "breakdown", "entries", "data", "onClick", "original_length", "toLocaleString", "optimized_length", "displayRate", "colorClass", "toFixed", "disabled", "optimized_docx_url", "isFormatPreserved", "download_url", "cx", "cy", "r", "print", "_lines", "lines", "lastLine", "mainText", "slice", "fontStyle", "dangerouslySetInnerHTML", "__html", "src", "encodeURIComponent", "height", "frameBorder", "title", "background", "borderRadius", "onError", "paragraph_changes", "change", "index", "change_reason", "matchedLimit", "missingLimit", "matchedExpanded", "missingExpanded", "weight", "matched", "total", "item", "allMissing", "missing", "sort", "b", "miss", "suggestions", "tip", "showAll", "setShowAll", "v", "s", "i", "radarData", "name", "value", "indicator", "option", "tooltip", "radar", "radius", "series", "areaStyle", "opacity", "lineStyle", "issues", "issue", "_c", "$RefreshReg$"], "sources": ["E:/AI/SmartCV/frontend/src/components/ResultDisplay.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { exportToPDF, exportToWord, exportToText } from '../utils/exportUtils';\r\nimport { downloadOptimizedDocx } from '../services/api';\r\nimport DocumentPreview from './DocumentPreview';\r\nimport ReactECharts from 'echarts-for-react';\r\n\r\nconst ResultDisplay = ({ result, originalFile, jobDescription }) => {\r\n  const [showComparison, setShowComparison] = useState(false);\r\n  const [activeTab, setActiveTab] = useState('optimized'); // 'optimized', 'original', 'comparison'\r\n  const [showFormattedOptimized, setShowFormattedOptimized] = useState(false); // Toggle for formatted view in comparison\r\n  const [downloadingDocx, setDownloadingDocx] = useState(false);\r\n  const [showExportDropdown, setShowExportDropdown] = useState(false);\r\n  const [viewMode, setViewMode] = useState('raw'); // 'raw', 'enhanced', 'word'\r\n  const [wordPreviewMode, setWordPreviewMode] = useState('office'); // 'office', 'google', 'download'\r\n  const [wordPreviewError, setWordPreviewError] = useState(null);\r\n  // 新增：用于控制每个分类的展开/收起状态\r\n  const [expandedMatched, setExpandedMatched] = useState({});\r\n  const [expandedMissing, setExpandedMissing] = useState({});\r\n\r\n  // 点击外部区域关闭下拉菜单\r\n  useEffect(() => {\r\n    const handleClickOutside = (event) => {\r\n      if (showExportDropdown && !event.target.closest('.export-dropdown')) {\r\n        setShowExportDropdown(false);\r\n      }\r\n    };\r\n\r\n    document.addEventListener('mousedown', handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener('mousedown', handleClickOutside);\r\n    };\r\n  }, [showExportDropdown]);\r\n\r\n  const formatOptimizedResume = (text) => {\r\n    if (!text) return '';\r\n    \r\n    // Process Markdown-formatted resume\r\n    return text\r\n      .replace(/^#\\s+(.+)$/gm, '<h1 class=\"text-2xl font-bold text-gray-900 mb-4 border-b-2 border-orange-500 pb-2\">$1</h1>')\r\n      .replace(/^##\\s+(.+)$/gm, '<h2 class=\"text-xl font-semibold text-gray-800 mb-3 mt-6\">$1</h2>')\r\n      .replace(/^###\\s+(.+)$/gm, '<h3 class=\"text-lg font-medium text-gray-700 mb-2 mt-4\">$3</h3>')\r\n      .replace(/^\\*\\*(.+?)\\*\\*:?(.*)$/gm, '<div class=\"mb-2\"><span class=\"font-semibold text-gray-900\">$1</span>$2</div>')\r\n      .replace(/^\\* (.+)$/gm, '<li class=\"ml-4 mb-1 text-gray-700\">$1</li>')\r\n      .replace(/^- (.+)$/gm, '<li class=\"ml-4 mb-1 text-gray-700\">$1</li>')\r\n      .replace(/\\*\\*(.+?)\\*\\*/g, '<span class=\"font-semibold\">$1</span>')\r\n      .replace(/\\*(.+?)\\*/g, '<span class=\"italic\">$1</span>')\r\n      .replace(/\\n\\n/g, '</p><p class=\"mb-3\">')\r\n      .replace(/\\n/g, '<br>')\r\n      .replace(/^(?!<[h|l|d])(.+)$/gm, '<p class=\"mb-3 text-gray-700\">$1</p>');\r\n  };\r\n\r\n  const generateATSScore = (optimizedText, originalText) => {\r\n    // 确保输入文本不为空，提供默认值\r\n    const safeOptimizedText = optimizedText || '';\r\n    const safeOriginalText = originalText || '';\r\n    \r\n    // Simple ATS scoring algorithm\r\n    const factors = {\r\n      hasQuantifiedAchievements: /\\d+(%|k|million|billion|\\$|year|month)/.test(safeOptimizedText),\r\n      hasActionVerbs: /(developed|implemented|managed|led|created|optimized|improved)/.test(safeOptimizedText.toLowerCase()),\r\n      hasStandardSections: /(experience|education|skills)/.test(safeOptimizedText.toLowerCase()),\r\n      hasContactInfo: /(email|phone|linkedin)/.test(safeOptimizedText.toLowerCase()),\r\n      appropriateLength: safeOptimizedText.length > 500 && safeOptimizedText.length < 4000,\r\n      noSpecialCharacters: !/[★☆▪▫◦]/.test(safeOptimizedText)\r\n    };\r\n\r\n    const score = Object.values(factors).filter(Boolean).length;\r\n    const maxScore = Object.keys(factors).length;\r\n    const percentage = Math.round((score / maxScore) * 100);\r\n\r\n    return {\r\n      score: percentage,\r\n      factors,\r\n      improvements: maxScore - score\r\n    };\r\n  };\r\n\r\n  // 兼容处理不同的数据结构\r\n  const getOptimizedText = () => {\r\n    return result?.optimized_resume || result?.optimized_text || '';\r\n  };\r\n  \r\n  const getOriginalText = () => {\r\n    return result?.original_resume || result?.original_text || '';\r\n  };\r\n\r\n  // 更精确的文本差异检测函数\r\n  const detectTextDifferences = (originalText, optimizedText) => {\r\n    const originalLines = originalText.split('\\n').filter(line => line.trim());\r\n    const optimizedLines = optimizedText.split('\\n').filter(line => line.trim());\r\n    \r\n    const result = [];\r\n    const usedOptimizedIndices = new Set();\r\n    \r\n    originalLines.forEach((originalLine, originalIndex) => {\r\n      const trimmedOriginal = originalLine.trim();\r\n      if (!trimmedOriginal) return;\r\n      \r\n      // 寻找最佳匹配\r\n      let bestMatch = null;\r\n      let bestSimilarity = 0;\r\n      let bestOptimizedIndex = -1;\r\n      \r\n      optimizedLines.forEach((optimizedLine, optimizedIdx) => {\r\n        if (usedOptimizedIndices.has(optimizedIdx)) return;\r\n        \r\n        const trimmedOptimized = optimizedLine.trim();\r\n        const similarity = calculateSimilarity(trimmedOriginal, trimmedOptimized);\r\n        \r\n        if (similarity > bestSimilarity && similarity > 0.3) {\r\n          bestMatch = optimizedLine;\r\n          bestSimilarity = similarity;\r\n          bestOptimizedIndex = optimizedIdx;\r\n        }\r\n      });\r\n      \r\n      if (bestMatch && bestSimilarity > 0.7) {\r\n        // 高相似度，可能只是微调\r\n        usedOptimizedIndices.add(bestOptimizedIndex);\r\n        result.push({\r\n          type: 'modified',\r\n          original: originalLine,\r\n          optimized: bestMatch,\r\n          similarity: bestSimilarity\r\n        });\r\n      } else if (bestMatch && bestSimilarity > 0.3) {\r\n        // 中等相似度，显著修改\r\n        usedOptimizedIndices.add(bestOptimizedIndex);\r\n        result.push({\r\n          type: 'significantly_modified',\r\n          original: originalLine,\r\n          optimized: bestMatch,\r\n          similarity: bestSimilarity\r\n        });\r\n      } else {\r\n        // 没有找到匹配，可能被删除或完全重写\r\n        result.push({\r\n          type: 'deleted',\r\n          original: originalLine,\r\n          optimized: null\r\n        });\r\n      }\r\n    });\r\n    \r\n    return result;\r\n  };\r\n  \r\n  // 计算两个字符串的相似度\r\n  const calculateSimilarity = (str1, str2) => {\r\n    if (str1 === str2) return 1;\r\n    if (!str1 || !str2) return 0;\r\n    \r\n    const words1 = str1.toLowerCase().split(/\\s+/);\r\n    const words2 = str2.toLowerCase().split(/\\s+/);\r\n    \r\n    const commonWords = words1.filter(word => words2.includes(word));\r\n    const totalWords = Math.max(words1.length, words2.length);\r\n    \r\n    return commonWords.length / totalWords;\r\n  };\r\n\r\n  // 在优化文本中高亮新增、修改、删除内容\r\n  const highlightAdditionsInOptimized = (optimizedText) => {\r\n    const originalText = getOriginalText();\r\n    if (!originalText || !optimizedText || originalText === optimizedText) {\r\n      // 没有原文或内容一致，直接返回普通格式\r\n      return formatOptimizedResume(optimizedText);\r\n    }\r\n    // 差异检测\r\n    const differences = detectTextDifferences(originalText, optimizedText);\r\n    const optimizedLines = optimizedText.split('\\n').filter(line => line.trim());\r\n    const originalLines = originalText.split('\\n').filter(line => line.trim());\r\n    const usedOriginalIndices = new Set();\r\n    const usedOptimizedIndices = new Set();\r\n    // 标记已匹配的行\r\n    differences.forEach((diff, idx) => {\r\n      if (diff.type !== 'deleted') {\r\n        const optIdx = optimizedLines.findIndex(line => line.trim() === diff.optimized?.trim());\r\n        if (optIdx !== -1) usedOptimizedIndices.add(optIdx);\r\n      }\r\n      if (diff.type !== 'added') {\r\n        const oriIdx = originalLines.findIndex(line => line.trim() === diff.original?.trim());\r\n        if (oriIdx !== -1) usedOriginalIndices.add(oriIdx);\r\n      }\r\n    });\r\n    // 渲染优化内容\r\n    return optimizedLines.map((line, idx) => {\r\n      const escapedLine = line.replace(/</g, '&lt;').replace(/>/g, '&gt;');\r\n      // 检查是否为修改说明（如以This revised resume/This resume等开头，且为最后一段）\r\n      const isRevisionNote = (idx === optimizedLines.length - 1) && /^(This revised resume|This resume|优化说明|修改说明|优化总结|本次修改说明|优化建议)/i.test(line.trim());\r\n      if (isRevisionNote) {\r\n        // 检查是否为中文优化说明，若是则替换为英文模板\r\n        let revisionContent = line.trim();\r\n        if (/^(本次修改说明|优化建议)/.test(revisionContent)) {\r\n          revisionContent = `This revision note summarizes the main changes and optimization suggestions:\\n\\n- Integrated keywords from the target job, such as \\\"data analysis\\\", \\\"project management\\\", \\\"big data\\\", etc.\\n- Reorganized the order of work experience to highlight the most relevant experiences.\\n- Used action verbs to describe responsibilities and achievements.\\n- Formatted the resume to be ATS-friendly, removed tables and special characters.\\n\\nOptimization Suggestions:\\n- Quantified achievements for the \\\"Administrative Assistant\\\" role.\\n- Added specific descriptions of data collection and analysis for the \\\"Intern\\\" position.`;\r\n        }\r\n        return `<p class=\\\"mb-3 leading-relaxed bg-gray-50 border-l-4 border-gray-400 px-4 py-2 italic text-gray-700 font-semibold\\\" style=\\\"font-style:italic;\\\"><span class=\\\"text-gray-500 font-bold mr-2\\\">Revision Note:</span>${revisionContent.replace(/\\n/g, '<br>')}</p>`;\r\n      }\r\n      // 新增内容\r\n      if (!usedOriginalIndices.has(idx) && line.trim().length > 0) {\r\n        return `<p class=\\\"mb-3 leading-relaxed\\\"><span class=\\\"inline-block bg-green-50 text-green-700 font-medium px-2 py-1 rounded border border-green-200 mr-1\\\" title=\\\"新增内容\\\">✓ ${escapedLine}</span></p>`;\r\n      }\r\n      // 修改内容\r\n      const diff = differences.find(d => d.optimized?.trim() === line.trim() && d.type === 'significantly_modified');\r\n      if (diff) {\r\n        return `<p class=\\\"mb-3 leading-relaxed\\\"><span class=\\\"inline-block bg-orange-50 text-orange-700 font-medium px-2 py-1 rounded border border-orange-200 mr-1\\\" title=\\\"修改内容\\\">${escapedLine}</span></p>`;\r\n      }\r\n      // 轻微修改\r\n      const diff2 = differences.find(d => d.optimized?.trim() === line.trim() && d.type === 'modified');\r\n      if (diff2) {\r\n        return `<p class=\\\"mb-3 leading-relaxed\\\"><span class=\\\"inline-block bg-yellow-50 text-yellow-700 font-medium px-2 py-1 rounded border border-yellow-200 mr-1\\\" title=\\\"轻微修改\\\">${escapedLine}</span></p>`;\r\n      }\r\n      // 普通内容\r\n      return `<p class=\\\"mb-3 text-gray-700 leading-relaxed\\\">${escapedLine}</p>`;\r\n    }).join('')\r\n    // 删除内容在优化内容中不显示，但可选在末尾加提示\r\n    // differences.filter(d => d.type === 'deleted').map(...)\r\n  };\r\n\r\n  const atsScore = generateATSScore(getOptimizedText(), getOriginalText());\r\n\r\n  const getScoreColor = (score) => {\r\n    if (score >= 80) return 'text-green-600';\r\n    if (score >= 60) return 'text-orange-600';\r\n    return 'text-red-600';\r\n  };\r\n\r\n  const getScoreBg = (score) => {\r\n    if (score >= 80) return 'bg-green-100';\r\n    if (score >= 60) return 'bg-orange-100';\r\n    return 'bg-red-100';\r\n  };\r\n\r\n  const getHeaderBg = (score) => {\r\n    if (score >= 80) return 'bg-green-50 border-green-200';\r\n    if (score >= 60) return 'bg-orange-50 border-orange-200';\r\n    return 'bg-red-50 border-red-200';\r\n  };\r\n\r\n  const getIconBg = (score) => {\r\n    if (score >= 80) return 'bg-green-500';\r\n    if (score >= 60) return 'bg-orange-500';\r\n    return 'bg-red-500';\r\n  };\r\n\r\n  const getProgressBarColor = (score) => {\r\n    if (score >= 80) return 'bg-green-500';\r\n    if (score >= 60) return 'bg-orange-500';\r\n    return 'bg-red-500';\r\n  };\r\n\r\n  const getBorderColor = (score) => {\r\n    if (score >= 80) return 'border-green-200';\r\n    if (score >= 60) return 'border-orange-200';\r\n    return 'border-red-200';\r\n  };\r\n\r\n  // 处理DOCX下载\r\n  const handleDownloadDocx = async () => {\r\n    try {\r\n      const formData = new FormData();\r\n      formData.append('file', originalFile);\r\n      formData.append('job_description', jobDescription);\r\n      \r\n      const response = await fetch('/api/optimize-resume-docx', {\r\n        method: 'POST',\r\n        body: formData\r\n      });\r\n      \r\n      if (!response.ok) {\r\n        const error = await response.json();\r\n        throw new Error(error.error || '下载失败');\r\n      }\r\n      \r\n      // 获取文件名\r\n      const contentDisposition = response.headers.get('content-disposition');\r\n      const filename = contentDisposition\r\n        ? contentDisposition.split('filename=')[1].replace(/\"/g, '')\r\n        : 'optimized_resume.docx';\r\n      \r\n      // 下载文件\r\n      const blob = await response.blob();\r\n      const url = window.URL.createObjectURL(blob);\r\n      const a = document.createElement('a');\r\n      a.href = url;\r\n      a.download = filename;\r\n      document.body.appendChild(a);\r\n      a.click();\r\n      window.URL.revokeObjectURL(url);\r\n      document.body.removeChild(a);\r\n      \r\n    } catch (error) {\r\n      console.error('下载DOCX失败:', error);\r\n      alert('下载失败: ' + error.message);\r\n    }\r\n  };\r\n\r\n  const toggleExpand = (key, type) => {\r\n    if (type === 'matched') {\r\n      setExpandedMatched(prev => ({ ...prev, [key]: !prev[key] }));\r\n    } else {\r\n      setExpandedMissing(prev => ({ ...prev, [key]: !prev[key] }));\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      {/* 第一个卡片：Resume Optimization Complete + Job Match Analysis */}\r\n      <div className=\"mb-6\">\r\n        <div className=\"bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden\">\r\n          {/* Header with ATS Score */}\r\n          <div className={`${getHeaderBg(atsScore.score)} px-8 py-8 border-b relative overflow-hidden`}>\r\n            {/* 背景装饰 */}\r\n            <div className=\"absolute top-0 right-0 transform translate-x-4 -translate-y-4 opacity-10\">\r\n              <svg className=\"w-32 h-32\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\r\n              </svg>\r\n            </div>\r\n            \r\n            <div className=\"relative\">\r\n              <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between\">\r\n                <div className=\"mb-6 lg:mb-0\">\r\n                  <div className=\"flex items-center mb-3\">\r\n                    <div className={`${getIconBg(atsScore.score)} text-white rounded-full w-12 h-12 flex items-center justify-center mr-4 shadow-lg`}>\r\n                      <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\r\n                      </svg>\r\n                    </div>\r\n                    <div>\r\n                      <h2 className=\"text-3xl font-bold text-gray-900 mb-1\">\r\n                        Resume Optimization Complete\r\n                      </h2>\r\n                      <div className=\"flex items-center text-sm text-gray-600\">\r\n                        <svg className=\"w-4 h-4 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\r\n                        </svg>\r\n                        AI has optimized your resume according to job requirements, improving ATS pass rate\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                \r\n                <div className=\"flex items-center space-x-6\">\r\n                  {/* ATS 分数卡片 */}\r\n                  <div className={`bg-white border-2 ${getBorderColor(atsScore.score)} rounded-2xl px-8 py-6 text-center min-w-[160px] shadow-lg transform hover:scale-105 transition-transform`}>\r\n                    <div className={`text-4xl font-extrabold mb-2 ${getScoreColor(atsScore.score)}`}>\r\n                      {atsScore.score}%\r\n                    </div>\r\n                    <div className=\"text-sm font-semibold text-gray-600 mb-2\">ATS Match</div>\r\n                    <div className=\"w-full bg-gray-200 rounded-full h-2\">\r\n                      <div \r\n                        className={`h-2 rounded-full transition-all duration-1000 ${getProgressBarColor(atsScore.score)}`}\r\n                        style={{ width: `${atsScore.score}%` }}\r\n                      ></div>\r\n                    </div>\r\n                  </div>\r\n                  \r\n                  {/* 状态指示器 */}\r\n                  <div className=\"text-center\">\r\n                    <div className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium ${\r\n                      atsScore.score >= 80 ? 'bg-green-100 text-green-800' : \r\n                      atsScore.score >= 60 ? 'bg-yellow-100 text-yellow-800' : \r\n                      'bg-red-100 text-red-800'\r\n                    }`}>\r\n                      {atsScore.score >= 80 ? 'Excellent' : atsScore.score >= 60 ? 'Good' : 'Needs Improvement'}\r\n                    </div>\r\n                    <div className=\"text-xs text-gray-500 mt-1\">\r\n                      {atsScore.score >= 80 ? 'Very likely to pass ATS screening' : \r\n                       atsScore.score >= 60 ? 'High chance to pass ATS screening' : \r\n                       'Recommend further optimization'}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Job Match Analysis - 在同一个卡片内 */}\r\n          {result?.job_match_analysis && (\r\n            <div className=\"p-8\">\r\n              {/* 简约标题 */}\r\n              <div className=\"text-center mb-8\">\r\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">Job Match Analysis</h3>\r\n                <p className=\"text-gray-600\">AI analysis shows significant improvement in resume-job alignment</p>\r\n              </div>\r\n              \r\n              {/* 核心指标卡片 - 简约风格 */}\r\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\r\n                {/* 原始匹配度 */}\r\n                <div className=\"bg-gray-50 rounded-lg p-6 text-center\">\r\n                  <div className=\"text-2xl font-bold text-gray-500 mb-2\">\r\n                    {result.job_match_analysis.original_match_score?.overall_match_score || 0}%\r\n                  </div>\r\n                  <div className=\"text-sm text-gray-600 mb-3\">Original Match</div>\r\n                  <div className=\"w-full bg-gray-200 rounded-full h-2\">\r\n                    <div \r\n                      className=\"bg-gray-400 h-2 rounded-full transition-all duration-1000\"\r\n                      style={{ width: `${result.job_match_analysis.original_match_score?.overall_match_score || 0}%` }}\r\n                    ></div>\r\n                  </div>\r\n                </div>\r\n                \r\n                {/* 优化后匹配度 */}\r\n                <div className=\"bg-green-50 rounded-lg p-6 border-2 border-green-200 text-center\">\r\n                  <div className=\"flex items-center justify-center mb-2\">\r\n                    <svg className=\"w-5 h-5 text-green-600 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\r\n                    </svg>\r\n                    <div className=\"text-2xl font-bold text-green-600\">\r\n                      {result.job_match_analysis.optimized_match_score?.overall_match_score || 0}%\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"text-sm text-green-600 font-medium mb-3\">Optimized Match</div>\r\n                  <div className=\"w-full bg-green-100 rounded-full h-2\">\r\n                    <div \r\n                      className=\"bg-green-500 h-2 rounded-full transition-all duration-1000\"\r\n                      style={{ width: `${result.job_match_analysis.optimized_match_score?.overall_match_score || 0}%` }}\r\n                    ></div>\r\n                  </div>\r\n                </div>\r\n                \r\n                {/* 提升幅度 */}\r\n                <div className=\"bg-blue-50 rounded-lg p-6 text-center\">\r\n                  <div className={`text-2xl font-bold mb-2 ${result.job_match_analysis.match_improvement >= 0 ? 'text-blue-600' : 'text-orange-600'}`}>\r\n                    {result.job_match_analysis.match_improvement >= 0 ? '+' : ''}{result.job_match_analysis.match_improvement}%\r\n                  </div>\r\n                  <div className=\"text-sm text-gray-600 mb-3\">Improvement</div>\r\n                  <div className=\"flex items-center justify-center text-xs text-gray-500\">\r\n                    <svg className=\"w-3 h-3 mr-1 text-blue-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\" />\r\n                    </svg>\r\n                    Significant Growth\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              \r\n              {/* 技能匹配详情 - 简约展示 */}\r\n              {result.job_match_analysis.optimized_match_score?.breakdown && (\r\n                <div className=\"bg-gray-50 rounded-lg p-6\">\r\n                  <h4 className=\"text-md font-medium text-gray-800 mb-4\">Skill Match Breakdown</h4>\r\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\r\n                    {Object.entries(result.job_match_analysis.optimized_match_score.breakdown).map(([key, data]) => (\r\n                      <div key={key} className=\"flex items-center justify-between p-3 bg-white rounded-lg border border-gray-100\">\r\n                        <span className=\"text-sm text-gray-700 capitalize font-medium\">\r\n                          {key.replace('_', ' ')}\r\n                        </span>\r\n                        <div className=\"flex items-center\">\r\n                          <span className={`text-sm font-semibold mr-3 ${data.score >= 70 ? 'text-green-600' : data.score >= 50 ? 'text-yellow-600' : 'text-red-600'}`}>\r\n                            {data.score}%\r\n                          </span>\r\n                          <div className=\"w-16 bg-gray-200 rounded-full h-2\">\r\n                            <div \r\n                              className={`h-2 rounded-full ${\r\n                                data.score >= 70 ? 'bg-green-500' : \r\n                                data.score >= 50 ? 'bg-yellow-500' : 'bg-red-500'\r\n                              }`}\r\n                              style={{ width: `${data.score}%` }}\r\n                            ></div>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* 第二个卡片：Optimized Resume + ATS Analysis */}\r\n      <div className=\"mb-6\">\r\n        <div className=\"bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden\">\r\n          {/* Tab Navigation */}\r\n          <div className=\"border-b border-gray-200 bg-gray-50\">\r\n            <div className=\"px-8\">\r\n              <nav className=\"flex space-x-8\">\r\n                <button\r\n                  onClick={() => setActiveTab('optimized')}\r\n                  className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${\r\n                    activeTab === 'optimized'\r\n                      ? 'border-orange-500 text-orange-600'\r\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\r\n                  }`}\r\n                >\r\n                  <span className=\"flex items-center space-x-2\">\r\n                    <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                    </svg>\r\n                    <span>Optimized Resume</span>\r\n                  </span>\r\n                </button>\r\n                \r\n                <button\r\n                  onClick={() => setActiveTab('analysis')}\r\n                  className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${\r\n                    activeTab === 'analysis'\r\n                      ? 'border-orange-500 text-orange-600'\r\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\r\n                  }`}\r\n                >\r\n                  <span className=\"flex items-center space-x-2\">\r\n                    <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\r\n                    </svg>\r\n                    <span>ATS Analysis</span>\r\n                  </span>\r\n                </button>\r\n              </nav>\r\n            </div>\r\n          </div>\r\n\r\n          {/* 统计信息区块，仅在 Optimized Resume 页签下显示 */}\r\n          {activeTab === 'optimized' && (\r\n            <div className=\"px-8 py-6 border-b border-gray-100\">\r\n              <h4 className=\"text-xl font-semibold text-gray-900 mb-6 flex items-center\">\r\n                <svg className=\"w-6 h-6 mr-3 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\r\n                </svg>\r\n                Optimization Statistics\r\n              </h4>\r\n              \r\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\r\n                {/* Original Length */}\r\n                <div className=\"bg-gray-50 rounded-lg p-6 text-center\">\r\n                  <div className=\"text-xs font-medium text-gray-500 mb-2\">Original Length</div>\r\n                  <div className=\"text-2xl font-bold text-gray-900 mb-1\">\r\n                    {result?.original_length?.toLocaleString() || 'N/A'}\r\n                  </div>\r\n                  <div className=\"text-xs text-gray-400\">Characters</div>\r\n                </div>\r\n                \r\n                {/* Final Length */}\r\n                <div className=\"bg-gray-50 rounded-lg p-6 text-center\">\r\n                  <div className=\"text-xs font-medium text-gray-500 mb-2\">Optimized Length</div>\r\n                  <div className=\"text-2xl font-bold text-gray-900 mb-1\">\r\n                    {result?.optimized_length?.toLocaleString() || 'N/A'}\r\n                  </div>\r\n                  <div className=\"text-xs text-gray-400\">Characters</div>\r\n                </div>\r\n                \r\n                {/* Length Change */}\r\n                <div className=\"bg-blue-50 rounded-lg p-6 text-center\">\r\n                  <div className=\"text-xs font-medium text-gray-500 mb-2\">Change</div>\r\n                  <div className=\"text-2xl font-bold mb-1\">\r\n                    {(() => {\r\n                      const original = result?.original_length || 0;\r\n                      const optimized = result?.optimized_length || 0;\r\n                      const diff = optimized - original;\r\n                      if (original === 0 || optimized === 0) return <span className=\"text-gray-400\">N/A</span>;\r\n                      return (\r\n                        <span className={diff >= 0 ? 'text-blue-600' : 'text-orange-600'}>\r\n                          {diff >= 0 ? '+' : ''}{diff.toLocaleString()}\r\n                        </span>\r\n                      );\r\n                    })()}\r\n                  </div>\r\n                  <div className=\"text-xs text-gray-400\">Characters</div>\r\n                </div>\r\n                \r\n                {/* Optimization Ratio */}\r\n                <div className=\"bg-green-50 rounded-lg p-6 text-center\">\r\n                  <div className=\"text-xs font-medium text-gray-500 mb-2\">Optimization Rate</div>\r\n                  <div className=\"text-2xl font-bold mb-1\">\r\n                    {(() => {\r\n                      const original = result?.original_length || 0;\r\n                      const optimized = result?.optimized_length || 0;\r\n                      if (original === 0 || optimized === 0) return <span className=\"text-gray-400\">N/A</span>;\r\n                      let displayRate = 0;\r\n                      if (optimized > original) {\r\n                        displayRate = ((optimized - original) / optimized) * 100;\r\n                      } else {\r\n                        displayRate = ((original - optimized) / original) * 100;\r\n                      }\r\n                      displayRate = Math.max(0, displayRate);\r\n                      const colorClass = displayRate > 15 ? 'text-green-600' : displayRate > 5 ? 'text-blue-600' : 'text-orange-600';\r\n                      return (\r\n                        <span className={colorClass}>\r\n                          {displayRate.toFixed(1)}%\r\n                        </span>\r\n                      );\r\n                    })()}\r\n                  </div>\r\n                  <div className=\"text-xs text-gray-400\">Adjustment</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* Content Area */}\r\n          <div className=\"p-8\">\r\n            {/* Optimized Resume Tab */}\r\n            {activeTab === 'optimized' && (\r\n              <div>\r\n                <div className=\"mb-6 flex justify-between items-center\">\r\n                  <div className=\"flex items-center space-x-4 text-sm\">\r\n                    <span className=\"text-gray-600\">Optimized Resume View:</span>\r\n                    <div className=\"flex space-x-2\">\r\n                      <button\r\n                        onClick={() => setViewMode('raw')}\r\n                        className={`px-3 py-1 rounded-md text-xs font-medium transition-colors ${\r\n                          viewMode === 'raw'\r\n                            ? 'bg-orange-100 text-orange-700'\r\n                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\r\n                        }`}\r\n                      >\r\n                        Raw Text\r\n                      </button>\r\n                      <button\r\n                        onClick={() => setViewMode('enhanced')}\r\n                        className={`px-3 py-1 rounded-md text-xs font-medium transition-colors ${\r\n                          viewMode === 'enhanced'\r\n                            ? 'bg-orange-100 text-orange-700'\r\n                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\r\n                        }`}\r\n                      >\r\n                        Enhanced View\r\n                      </button>\r\n                      <button\r\n                        onClick={() => setViewMode('word')}\r\n                        className={`px-3 py-1 rounded-md text-xs font-medium transition-colors ${\r\n                          viewMode === 'word'\r\n                            ? 'bg-orange-100 text-orange-700'\r\n                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\r\n                        }`}\r\n                        disabled={!result?.optimized_docx_url}\r\n                      >\r\n                        Word Preview\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"relative export-dropdown\">\r\n                    <button\r\n                      onClick={() => setShowExportDropdown(!showExportDropdown)}\r\n                      className=\"inline-flex items-center px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors text-sm font-medium\"\r\n                    >\r\n                      <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\r\n                      </svg>\r\n                      Export & Download\r\n                      <svg className=\"w-4 h-4 ml-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\r\n                      </svg>\r\n                    </button>\r\n                    \r\n                    {showExportDropdown && (\r\n                      <div className=\"absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 z-50\">\r\n                        <div className=\"py-2\">\r\n                          {result?.isFormatPreserved && result?.download_url && (\r\n                            <>\r\n                              <button\r\n                                onClick={() => {\r\n                                  handleDownloadDocx();\r\n                                  setShowExportDropdown(false);\r\n                                }}\r\n                                disabled={downloadingDocx}\r\n                                className=\"w-full text-left px-4 py-3 hover:bg-purple-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                              >\r\n                                <div className=\"flex items-center\">\r\n                                  <div className=\"w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3\">\r\n                                    {downloadingDocx ? (\r\n                                      <svg className=\"animate-spin w-4 h-4 text-purple-600\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                                        <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n                                        <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                                      </svg>\r\n                                    ) : (\r\n                                      <svg className=\"w-4 h-4 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10\" />\r\n                                      </svg>\r\n                                    )}\r\n                                  </div>\r\n                                  <div>\r\n                                    <div className=\"font-medium text-purple-900\">\r\n                                      {downloadingDocx ? 'Downloading...' : 'Download Original Format'}\r\n                                    </div>\r\n                                    <div className=\"text-xs text-purple-600\">DOCX with preserved formatting</div>\r\n                                  </div>\r\n                                </div>\r\n                              </button>\r\n                              <div className=\"border-t border-gray-100 my-1\"></div>\r\n                            </>\r\n                          )}\r\n                          \r\n                          <button\r\n                            onClick={() => {\r\n                              exportToPDF(getOptimizedText());\r\n                              setShowExportDropdown(false);\r\n                            }}\r\n                            className=\"w-full text-left px-4 py-3 hover:bg-red-50 transition-colors\"\r\n                          >\r\n                            <div className=\"flex items-center\">\r\n                              <div className=\"w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center mr-3\">\r\n                                <svg className=\"w-4 h-4 text-red-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\r\n                                </svg>\r\n                              </div>\r\n                              <div>\r\n                                <div className=\"font-medium text-gray-900\">Export PDF</div>\r\n                                <div className=\"text-xs text-gray-500\">Download as PDF document</div>\r\n                              </div>\r\n                            </div>\r\n                          </button>\r\n                          \r\n                          <button\r\n                            onClick={() => {\r\n                              exportToWord(getOptimizedText());\r\n                              setShowExportDropdown(false);\r\n                            }}\r\n                            className=\"w-full text-left px-4 py-3 hover:bg-blue-50 transition-colors\"\r\n                          >\r\n                            <div className=\"flex items-center\">\r\n                              <div className=\"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3\">\r\n                                <svg className=\"w-4 h-4 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\r\n                                </svg>\r\n                              </div>\r\n                              <div>\r\n                                <div className=\"font-medium text-gray-900\">Export Word</div>\r\n                                <div className=\"text-xs text-gray-500\">Download as DOCX document</div>\r\n                              </div>\r\n                            </div>\r\n                          </button>\r\n                          \r\n                          <div className=\"border-t border-gray-100 my-1\"></div>\r\n                          \r\n                          <button\r\n                            onClick={() => {\r\n                              window.print();\r\n                              setShowExportDropdown(false);\r\n                            }}\r\n                            className=\"w-full text-left px-4 py-3 hover:bg-gray-50 transition-colors\"\r\n                          >\r\n                            <div className=\"flex items-center\">\r\n                              <div className=\"w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center mr-3\">\r\n                                <svg className=\"w-4 h-4 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z\" />\r\n                                </svg>\r\n                              </div>\r\n                              <div>\r\n                                <div className=\"font-medium text-gray-900\">Print Resume</div>\r\n                                <div className=\"text-xs text-gray-500\">Print directly from browser</div>\r\n                              </div>\r\n                            </div>\r\n                          </button>\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n                \r\n                {/* Paper-style Resume Preview */}\r\n                <div className=\"paper-preview rounded-lg p-10 mx-auto max-w-5xl min-h-[800px] relative\">\r\n                  {/* 变化标记图例说明 */}\r\n                  {viewMode === 'raw' && (\r\n                    <>\r\n                      {(() => {\r\n                        const text = getOptimizedText() || 'Optimized resume content not available';\r\n                        const lines = text.split('\\n');\r\n                        const lastLine = lines[lines.length - 1]?.trim();\r\n                        const isRevisionNote = /^(This revised resume|This resume|优化说明|修改说明|优化总结|本次修改说明|优化建议)/i.test(lastLine);\r\n                        if (isRevisionNote) {\r\n                          const mainText = lines.slice(0, -1).join('\\n');\r\n                          return (\r\n                            <>\r\n                              <pre className=\"resume-content text-sm leading-relaxed text-gray-800 whitespace-pre-wrap\">\r\n                                {mainText}\r\n                              </pre>\r\n                              <div className=\"mb-3 leading-relaxed bg-gray-50 border-l-4 border-gray-400 px-4 py-2 italic text-gray-700 font-semibold\" style={{fontStyle:'italic'}}>\r\n                                <span className=\"text-gray-500 font-bold mr-2\">Revision Note:</span>{lastLine}\r\n                              </div>\r\n                            </>\r\n                          );\r\n                        } else {\r\n                          return (\r\n                            <pre className=\"resume-content text-sm leading-relaxed text-gray-800 whitespace-pre-wrap\">\r\n                              {text}\r\n                            </pre>\r\n                          );\r\n                        }\r\n                      })()}\r\n                    </>\r\n                  )}\r\n                  {viewMode === 'enhanced' && (\r\n                    <div \r\n                      className=\"resume-content text-sm leading-relaxed text-gray-800\"\r\n                      dangerouslySetInnerHTML={{ \r\n                        __html: highlightAdditionsInOptimized(getOptimizedText()) \r\n                      }}\r\n                    />\r\n                  )}\r\n                  {viewMode === 'word' && result?.optimized_docx_url && (\r\n                    <div className=\"word-preview-container\">\r\n                      <div className=\"preview-tabs mb-4\">\r\n                        <button\r\n                          onClick={() => setWordPreviewMode('office')}\r\n                          className={`px-3 py-2 mr-2 rounded text-sm font-medium transition-colors ${\r\n                            wordPreviewMode === 'office'\r\n                              ? 'bg-blue-500 text-white'\r\n                              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\r\n                          }`}\r\n                        >\r\n                          Office Online\r\n                        </button>\r\n                        <button\r\n                          onClick={() => setWordPreviewMode('google')}\r\n                          className={`px-3 py-2 mr-2 rounded text-sm font-medium transition-colors ${\r\n                            wordPreviewMode === 'google'\r\n                              ? 'bg-blue-500 text-white'\r\n                              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\r\n                          }`}\r\n                        >\r\n                          Google Docs\r\n                        </button>\r\n                        <button\r\n                          onClick={() => setWordPreviewMode('download')}\r\n                          className={`px-3 py-2 rounded text-sm font-medium transition-colors ${\r\n                            wordPreviewMode === 'download'\r\n                              ? 'bg-green-500 text-white'\r\n                              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\r\n                          }`}\r\n                        >\r\n                          下载查看\r\n                        </button>\r\n                      </div>\r\n\r\n                      {wordPreviewMode === 'office' && (\r\n                        <div className=\"preview-frame-container\">\r\n                          <iframe\r\n                            src={`https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(result.optimized_docx_url)}`}\r\n                            width=\"100%\"\r\n                            height=\"800\"\r\n                            frameBorder=\"0\"\r\n                            title=\"Office Online Preview\"\r\n                            style={{ background: '#fff', borderRadius: '8px' }}\r\n                            onError={() => setWordPreviewError('Office Online预览失败，请尝试其他方式')}\r\n                          ></iframe>\r\n                        </div>\r\n                      )}\r\n\r\n                      {wordPreviewMode === 'google' && (\r\n                        <div className=\"preview-frame-container\">\r\n                          <iframe\r\n                            src={`https://docs.google.com/gview?url=${encodeURIComponent(result.optimized_docx_url)}&embedded=true`}\r\n                            width=\"100%\"\r\n                            height=\"800\"\r\n                            frameBorder=\"0\"\r\n                            title=\"Google Docs Preview\"\r\n                            style={{ background: '#fff', borderRadius: '8px' }}\r\n                            onError={() => setWordPreviewError('Google Docs预览失败，请尝试其他方式')}\r\n                          ></iframe>\r\n                        </div>\r\n                      )}\r\n\r\n                      {wordPreviewMode === 'download' && (\r\n                        <div className=\"download-preview-container p-8 text-center bg-gray-50 rounded-lg\">\r\n                          <div className=\"mb-4\">\r\n                            <svg className=\"w-16 h-16 mx-auto text-blue-500 mb-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\r\n                            </svg>\r\n                            <h3 className=\"text-lg font-semibold text-gray-800 mb-2\">Word文档已准备就绪</h3>\r\n                            <p className=\"text-gray-600 mb-4\">\r\n                              由于浏览器限制，无法直接预览Word文档。<br/>\r\n                              请点击下载按钮获取优化后的文档。\r\n                            </p>\r\n                          </div>\r\n                          <a\r\n                            href={result.optimized_docx_url}\r\n                            download=\"optimized_resume.docx\"\r\n                            className=\"inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium\"\r\n                          >\r\n                            <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\r\n                            </svg>\r\n                            下载优化后的Word文档\r\n                          </a>\r\n                        </div>\r\n                      )}\r\n\r\n                      {wordPreviewError && (\r\n                        <div className=\"mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg\">\r\n                          <div className=\"flex\">\r\n                            <svg className=\"w-5 h-5 text-yellow-400 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z\" />\r\n                            </svg>\r\n                            <div>\r\n                              <p className=\"text-yellow-800 font-medium\">预览提示</p>\r\n                              <p className=\"text-yellow-700 text-sm mt-1\">{wordPreviewError}</p>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  )}\r\n                </div>\r\n                \r\n                {/* Paragraph Changes - Only show for format preserved results */}\r\n                {result?.isFormatPreserved && result?.paragraph_changes && result.paragraph_changes.length > 0 && (\r\n                  <div className=\"mt-8 bg-purple-50 border border-purple-200 rounded-lg p-6\">\r\n                    <h4 className=\"text-lg font-semibold text-purple-900 mb-4 flex items-center\">\r\n                      <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4\" />\r\n                      </svg>\r\n                      Paragraph-Level Changes Applied\r\n                    </h4>\r\n                    <div className=\"space-y-4\">\r\n                      {(result.paragraph_changes || []).map((change, index) => (\r\n                        <div key={index} className=\"bg-white rounded-lg p-4 border border-purple-100\">\r\n                          <div className=\"text-sm font-medium text-purple-700 mb-2\">\r\n                            Change #{index + 1}: {change.change_reason}\r\n                          </div>\r\n                          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                            <div>\r\n                              <div className=\"text-xs font-medium text-red-600 mb-1\">Original:</div>\r\n                              <div className=\"text-sm text-gray-700 bg-red-50 p-2 rounded border\">\r\n                                {change.original_text}\r\n                              </div>\r\n                            </div>\r\n                            <div>\r\n                              <div className=\"text-xs font-medium text-green-600 mb-1\">Optimized:</div>\r\n                              <div className=\"text-sm text-gray-700 bg-green-50 p-2 rounded border\">\r\n                                {change.optimized_text}\r\n                              </div>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            )}\r\n\r\n            {/* ATS Analysis Tab */}\r\n            {activeTab === 'analysis' && (\r\n              <div>\r\n                <h3 className=\"text-2xl font-bold text-gray-900 mb-6\">ATS Compatibility Analysis</h3>\r\n                {/* 只保留详细分类匹配度分析 */}\r\n                {result?.job_match_analysis?.optimized_match_score?.breakdown && (\r\n                  <div>\r\n                    <h5 className=\"text-md font-semibold text-gray-800 mb-3\">Detailed Match Breakdown</h5>\r\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                      {Object.entries(result.job_match_analysis.optimized_match_score.breakdown).map(([key, data]) => {\r\n                        const matchedLimit = 5;\r\n                        const missingLimit = 3;\r\n                        const matchedExpanded = expandedMatched[key] || false;\r\n                        const missingExpanded = expandedMissing[key] || false;\r\n                        return (\r\n                          <div key={key} className=\"bg-gray-50 rounded-lg p-4 border\">\r\n                            <div className=\"flex justify-between items-start mb-2\">\r\n                              <div>\r\n                                <h6 className=\"font-semibold text-gray-700 capitalize\">\r\n                                  {key.replace('_', ' ')}\r\n                                </h6>\r\n                                <span className=\"text-xs text-gray-500 font-medium\">Weight: {data.weight}</span>\r\n                              </div>\r\n                              <div className={`text-lg font-bold ${data.score >= 70 ? 'text-green-600' : data.score >= 50 ? 'text-yellow-600' : 'text-red-600'}`}>{data.score}%</div>\r\n                            </div>\r\n                            {/* 进度条 */}\r\n                            <div className=\"w-full bg-gray-200 rounded-full h-2 mb-2\">\r\n                              <div \r\n                                className={`h-2 rounded-full transition-all duration-500 ${\r\n                                  data.score >= 70 ? 'bg-green-500' : \r\n                                  data.score >= 50 ? 'bg-yellow-500' : 'bg-red-500'\r\n                                }`}\r\n                                style={{ width: `${data.score}%` }}\r\n                              ></div>\r\n                            </div>\r\n                            {/* 匹配详情 */}\r\n                            {data.matched && data.total && (\r\n                              <div className=\"text-xs text-gray-600\">\r\n                                <div className=\"mb-1\">\r\n                                  <span className=\"font-medium\">Matched ({data.matched.length}/{data.total.length}):</span>\r\n                                </div>\r\n                                {data.matched.length > 0 ? (\r\n                                  <div className=\"flex flex-wrap gap-1 mb-2\">\r\n                                    {(matchedExpanded ? data.matched : data.matched.slice(0, matchedLimit)).map((item, idx) => (\r\n                                      <span key={idx} className=\"bg-green-100 text-green-700 px-2 py-1 rounded text-xs\">{item}</span>\r\n                                    ))}\r\n                                    {data.matched.length > matchedLimit && (\r\n                                      <span\r\n                                        className=\"text-gray-500 cursor-pointer underline\"\r\n                                        onClick={() => toggleExpand(key, 'matched')}\r\n                                      >\r\n                                        {matchedExpanded ? '收起' : `+${data.matched.length - matchedLimit} more`}\r\n                                      </span>\r\n                                    )}\r\n                                  </div>\r\n                                ) : (\r\n                                  <div className=\"text-gray-500 mb-2\">No matches found</div>\r\n                                )}\r\n                                {data.total.length > data.matched.length && (\r\n                                  <div>\r\n                                    <span className=\"font-medium text-red-600\">Missing:</span>\r\n                                    <div className=\"flex flex-wrap gap-1 mt-1\">\r\n                                      {(missingExpanded ? data.total.filter(item => !data.matched.includes(item)) : data.total.filter(item => !data.matched.includes(item)).slice(0, missingLimit)).map((item, idx) => (\r\n                                        <span key={idx} className=\"bg-red-100 text-red-700 px-2 py-1 rounded text-xs\">{item}</span>\r\n                                      ))}\r\n                                      {data.total.length - data.matched.length > missingLimit && (\r\n                                        <span\r\n                                          className=\"text-gray-500 cursor-pointer underline\"\r\n                                          onClick={() => toggleExpand(key, 'missing')}\r\n                                        >\r\n                                          {missingExpanded ? '收起' : `+${data.total.length - data.matched.length - missingLimit} more`}\r\n                                        </span>\r\n                                      )}\r\n                                    </div>\r\n                                  </div>\r\n                                )}\r\n                              </div>\r\n                            )}\r\n                          </div>\r\n                        );\r\n                      })}\r\n                    </div>\r\n\r\n                    {/* 关键差距卡片 */}\r\n                    {(() => {\r\n                      const allMissing = Object.entries(result.job_match_analysis.optimized_match_score.breakdown)\r\n                        .map(([key, data]) => ({\r\n                          key,\r\n                          weight: data.weight,\r\n                          missing: data.total ? data.total.filter(item => !data.matched.includes(item)) : [],\r\n                        }))\r\n                        .filter(item => item.missing.length > 0)\r\n                        .sort((a, b) => b.weight - a.weight);\r\n                      if (allMissing.length === 0) return null;\r\n                      return (\r\n                        <div className=\"bg-red-50 border border-red-200 rounded-lg p-6 mt-8 mb-4\">\r\n                          <h4 className=\"text-lg font-bold text-red-700 mb-2 flex items-center\">\r\n                            <svg className=\"w-5 h-5 mr-2 text-red-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\"><path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M18.364 5.636l-1.414 1.414A9 9 0 105.636 18.364l1.414-1.414A7 7 0 1116.95 7.05z\" /></svg>\r\n                            与岗位描述的关键差距\r\n                          </h4>\r\n                          <div className=\"flex flex-wrap gap-2\">\r\n                            {allMissing.slice(0, 3).map(item => (\r\n                              <div key={item.key} className=\"mr-4 mb-2\">\r\n                                <span className=\"font-semibold text-gray-700 mr-2\">{item.key.replace('_', ' ')}:</span>\r\n                                {item.missing.slice(0, 3).map((miss, idx) => (\r\n                                  <span key={idx} className=\"bg-red-100 text-red-700 px-2 py-1 rounded text-xs mr-1\">{miss}</span>\r\n                                ))}\r\n                                {item.missing.length > 3 && <span className=\"text-gray-500\">+{item.missing.length - 3} more</span>}\r\n                              </div>\r\n                            ))}\r\n                          </div>\r\n                        </div>\r\n                      );\r\n                    })()}\r\n\r\n                    {/* 优化建议卡片和一键展开按钮 */}\r\n                    {(() => {\r\n                      const suggestions = Object.entries(result.job_match_analysis.optimized_match_score.breakdown)\r\n                        .filter(([key, data]) => data.score < 60)\r\n                        .map(([key, data]) => {\r\n                          let tip = '';\r\n                          if (key.toLowerCase().includes('action')) tip = '建议补充更多动词，突出行动力。';\r\n                          else if (key.toLowerCase().includes('skill')) tip = '建议补充与岗位相关的技能。';\r\n                          else if (key.toLowerCase().includes('industry')) tip = '建议增加行业术语，提升专业性。';\r\n                          else if (key.toLowerCase().includes('experience')) tip = '建议丰富相关经验描述。';\r\n                          else if (key.toLowerCase().includes('education')) tip = '建议完善教育经历信息。';\r\n                          else tip = '建议补充相关内容，提升匹配度。';\r\n                          return `【${key.replace('_', ' ')}】${tip}`;\r\n                        });\r\n                      const [showAll, setShowAll] = React.useState(false);\r\n                      if (suggestions.length === 0) return null;\r\n                      return (\r\n                        <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-6 mt-4 mb-4\">\r\n                          <div className=\"flex items-center mb-2\">\r\n                            <h4 className=\"text-lg font-bold text-blue-700 mr-4\">优化建议</h4>\r\n                            <button\r\n                              className=\"ml-auto px-4 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm\"\r\n                              onClick={() => setShowAll(v => !v)}\r\n                            >{showAll ? '收起全部建议' : '一键展开全部建议'}</button>\r\n                          </div>\r\n                          <ul className=\"list-disc pl-6 text-blue-800\">\r\n                            {(showAll ? suggestions : suggestions.slice(0, 2)).map((s, i) => (\r\n                              <li key={i}>{s}</li>\r\n                            ))}\r\n                            {!showAll && suggestions.length > 2 && (\r\n                              <li className=\"text-gray-500\">...更多建议请点击上方按钮展开</li>\r\n                            )}\r\n                          </ul>\r\n                        </div>\r\n                      );\r\n                    })()}\r\n\r\n                    {/* 雷达图可视化 */}\r\n                    {(() => {\r\n                      const radarData = Object.entries(result.job_match_analysis.optimized_match_score.breakdown).map(([key, data]) => ({\r\n                        name: key.replace('_', ' '),\r\n                        value: data.score,\r\n                      }));\r\n                      if (radarData.length < 3) return null; // 至少3项才显示雷达图\r\n                      const indicator = radarData.map(item => ({ name: item.name, max: 100 }));\r\n                      const option = {\r\n                        tooltip: {},\r\n                        radar: {\r\n                          indicator,\r\n                          radius: 80,\r\n                        },\r\n                        series: [{\r\n                          type: 'radar',\r\n                          data: [{ value: radarData.map(d => d.value), name: '覆盖度' }],\r\n                          areaStyle: { opacity: 0.2 },\r\n                          lineStyle: { width: 2 },\r\n                        }],\r\n                      };\r\n                      return (\r\n                        <div className=\"bg-white border border-gray-200 rounded-lg p-6 mt-4 mb-4 flex flex-col items-center\">\r\n                          <h4 className=\"text-lg font-bold text-gray-800 mb-2\">简历内容覆盖度雷达图</h4>\r\n                          <div style={{ width: 350, height: 300 }}>\r\n                            <ReactECharts option={option} style={{ width: '100%', height: '100%' }} />\r\n                          </div>\r\n                        </div>\r\n                      );\r\n                    })()}\r\n\r\n                    {/* ATS常见问题检测卡片 */}\r\n                    {(() => {\r\n                      const optimizedText = getOptimizedText();\r\n                      const issues = [];\r\n                      if (/[★☆▪▫◦]/.test(optimizedText)) issues.push('检测到特殊符号，建议删除。');\r\n                      if (!/(email|邮箱|@)/i.test(optimizedText)) issues.push('未检测到邮箱信息。');\r\n                      if (!/(phone|电话|\\d{11,})/i.test(optimizedText)) issues.push('未检测到电话信息。');\r\n                      if (optimizedText.length < 500) issues.push('简历内容过短，建议丰富。');\r\n                      if (optimizedText.length > 4000) issues.push('简历内容过长，建议精简。');\r\n                      if (issues.length === 0) return null;\r\n                      return (\r\n                        <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-6 mt-4 mb-4\">\r\n                          <h4 className=\"text-lg font-bold text-yellow-700 mb-2 flex items-center\">\r\n                            <svg className=\"w-5 h-5 mr-2 text-yellow-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\"><path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z\" /></svg>\r\n                            ATS常见问题检测\r\n                          </h4>\r\n                          <ul className=\"list-disc pl-6 text-yellow-800\">\r\n                            {issues.map((issue, idx) => <li key={idx}>{issue}</li>)}\r\n                          </ul>\r\n                        </div>\r\n                      );\r\n                    })()}\r\n\r\n                  </div>\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ResultDisplay; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,YAAY,EAAEC,YAAY,QAAQ,sBAAsB;AAC9E,SAASC,qBAAqB,QAAQ,iBAAiB;AACvD,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,YAAY,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7C,MAAMC,aAAa,GAAGA,CAAC;EAAEC,MAAM;EAAEC,YAAY;EAAEC;AAAe,CAAC,KAAK;EAAAC,GAAA;EAAA,IAAAC,qBAAA;IAAAC,sBAAA;IAAAC,sBAAA;IAAAC,sBAAA;IAAAC,sBAAA;IAAAC,qBAAA;IAAAC,qBAAA;IAAAC,sBAAA;IAAAC,sBAAA;IAAAC,EAAA,GAAAC,YAAA;EAClE,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC8B,SAAS,EAAEC,YAAY,CAAC,GAAG/B,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC;EACzD,MAAM,CAACgC,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EAC7E,MAAM,CAACkC,eAAe,EAAEC,kBAAkB,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACoC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACsC,QAAQ,EAAEC,WAAW,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACjD,MAAM,CAACwC,eAAe,EAAEC,kBAAkB,CAAC,GAAGzC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;EAClE,MAAM,CAAC0C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3C,QAAQ,CAAC,IAAI,CAAC;EAC9D;EACA,MAAM,CAAC4C,eAAe,EAAEC,kBAAkB,CAAC,GAAG7C,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAM,CAAC8C,eAAe,EAAEC,kBAAkB,CAAC,GAAG/C,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAE1D;EACAC,SAAS,CAAC,MAAM;IACd,MAAM+C,kBAAkB,GAAIC,KAAK,IAAK;MACpC,IAAIb,kBAAkB,IAAI,CAACa,KAAK,CAACC,MAAM,CAACC,OAAO,CAAC,kBAAkB,CAAC,EAAE;QACnEd,qBAAqB,CAAC,KAAK,CAAC;MAC9B;IACF,CAAC;IAEDe,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEL,kBAAkB,CAAC;IAC1D,OAAO,MAAM;MACXI,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;IAC/D,CAAC;EACH,CAAC,EAAE,CAACZ,kBAAkB,CAAC,CAAC;EAExB,MAAMmB,qBAAqB,GAAIC,IAAI,IAAK;IACtC,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;;IAEpB;IACA,OAAOA,IAAI,CACRC,OAAO,CAAC,cAAc,EAAE,6FAA6F,CAAC,CACtHA,OAAO,CAAC,eAAe,EAAE,mEAAmE,CAAC,CAC7FA,OAAO,CAAC,gBAAgB,EAAE,iEAAiE,CAAC,CAC5FA,OAAO,CAAC,yBAAyB,EAAE,+EAA+E,CAAC,CACnHA,OAAO,CAAC,aAAa,EAAE,6CAA6C,CAAC,CACrEA,OAAO,CAAC,YAAY,EAAE,6CAA6C,CAAC,CACpEA,OAAO,CAAC,gBAAgB,EAAE,uCAAuC,CAAC,CAClEA,OAAO,CAAC,YAAY,EAAE,gCAAgC,CAAC,CACvDA,OAAO,CAAC,OAAO,EAAE,sBAAsB,CAAC,CACxCA,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CACtBA,OAAO,CAAC,sBAAsB,EAAE,sCAAsC,CAAC;EAC5E,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAACC,aAAa,EAAEC,YAAY,KAAK;IACxD;IACA,MAAMC,iBAAiB,GAAGF,aAAa,IAAI,EAAE;IAC7C,MAAMG,gBAAgB,GAAGF,YAAY,IAAI,EAAE;;IAE3C;IACA,MAAMG,OAAO,GAAG;MACdC,yBAAyB,EAAE,wCAAwC,CAACC,IAAI,CAACJ,iBAAiB,CAAC;MAC3FK,cAAc,EAAE,gEAAgE,CAACD,IAAI,CAACJ,iBAAiB,CAACM,WAAW,CAAC,CAAC,CAAC;MACtHC,mBAAmB,EAAE,+BAA+B,CAACH,IAAI,CAACJ,iBAAiB,CAACM,WAAW,CAAC,CAAC,CAAC;MAC1FE,cAAc,EAAE,wBAAwB,CAACJ,IAAI,CAACJ,iBAAiB,CAACM,WAAW,CAAC,CAAC,CAAC;MAC9EG,iBAAiB,EAAET,iBAAiB,CAACU,MAAM,GAAG,GAAG,IAAIV,iBAAiB,CAACU,MAAM,GAAG,IAAI;MACpFC,mBAAmB,EAAE,CAAC,SAAS,CAACP,IAAI,CAACJ,iBAAiB;IACxD,CAAC;IAED,MAAMY,KAAK,GAAGC,MAAM,CAACC,MAAM,CAACZ,OAAO,CAAC,CAACa,MAAM,CAACC,OAAO,CAAC,CAACN,MAAM;IAC3D,MAAMO,QAAQ,GAAGJ,MAAM,CAACK,IAAI,CAAChB,OAAO,CAAC,CAACQ,MAAM;IAC5C,MAAMS,UAAU,GAAGC,IAAI,CAACC,KAAK,CAAET,KAAK,GAAGK,QAAQ,GAAI,GAAG,CAAC;IAEvD,OAAO;MACLL,KAAK,EAAEO,UAAU;MACjBjB,OAAO;MACPoB,YAAY,EAAEL,QAAQ,GAAGL;IAC3B,CAAC;EACH,CAAC;;EAED;EACA,MAAMW,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,OAAO,CAAAvE,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEwE,gBAAgB,MAAIxE,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEyE,cAAc,KAAI,EAAE;EACjE,CAAC;EAED,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5B,OAAO,CAAA1E,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE2E,eAAe,MAAI3E,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE4E,aAAa,KAAI,EAAE;EAC/D,CAAC;;EAED;EACA,MAAMC,qBAAqB,GAAGA,CAAC9B,YAAY,EAAED,aAAa,KAAK;IAC7D,MAAMgC,aAAa,GAAG/B,YAAY,CAACgC,KAAK,CAAC,IAAI,CAAC,CAAChB,MAAM,CAACiB,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC;IAC1E,MAAMC,cAAc,GAAGpC,aAAa,CAACiC,KAAK,CAAC,IAAI,CAAC,CAAChB,MAAM,CAACiB,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC;IAE5E,MAAMjF,MAAM,GAAG,EAAE;IACjB,MAAMmF,oBAAoB,GAAG,IAAIC,GAAG,CAAC,CAAC;IAEtCN,aAAa,CAACO,OAAO,CAAC,CAACC,YAAY,EAAEC,aAAa,KAAK;MACrD,MAAMC,eAAe,GAAGF,YAAY,CAACL,IAAI,CAAC,CAAC;MAC3C,IAAI,CAACO,eAAe,EAAE;;MAEtB;MACA,IAAIC,SAAS,GAAG,IAAI;MACpB,IAAIC,cAAc,GAAG,CAAC;MACtB,IAAIC,kBAAkB,GAAG,CAAC,CAAC;MAE3BT,cAAc,CAACG,OAAO,CAAC,CAACO,aAAa,EAAEC,YAAY,KAAK;QACtD,IAAIV,oBAAoB,CAACW,GAAG,CAACD,YAAY,CAAC,EAAE;QAE5C,MAAME,gBAAgB,GAAGH,aAAa,CAACX,IAAI,CAAC,CAAC;QAC7C,MAAMe,UAAU,GAAGC,mBAAmB,CAACT,eAAe,EAAEO,gBAAgB,CAAC;QAEzE,IAAIC,UAAU,GAAGN,cAAc,IAAIM,UAAU,GAAG,GAAG,EAAE;UACnDP,SAAS,GAAGG,aAAa;UACzBF,cAAc,GAAGM,UAAU;UAC3BL,kBAAkB,GAAGE,YAAY;QACnC;MACF,CAAC,CAAC;MAEF,IAAIJ,SAAS,IAAIC,cAAc,GAAG,GAAG,EAAE;QACrC;QACAP,oBAAoB,CAACe,GAAG,CAACP,kBAAkB,CAAC;QAC5C3F,MAAM,CAACmG,IAAI,CAAC;UACVC,IAAI,EAAE,UAAU;UAChBC,QAAQ,EAAEf,YAAY;UACtBgB,SAAS,EAAEb,SAAS;UACpBO,UAAU,EAAEN;QACd,CAAC,CAAC;MACJ,CAAC,MAAM,IAAID,SAAS,IAAIC,cAAc,GAAG,GAAG,EAAE;QAC5C;QACAP,oBAAoB,CAACe,GAAG,CAACP,kBAAkB,CAAC;QAC5C3F,MAAM,CAACmG,IAAI,CAAC;UACVC,IAAI,EAAE,wBAAwB;UAC9BC,QAAQ,EAAEf,YAAY;UACtBgB,SAAS,EAAEb,SAAS;UACpBO,UAAU,EAAEN;QACd,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA1F,MAAM,CAACmG,IAAI,CAAC;UACVC,IAAI,EAAE,SAAS;UACfC,QAAQ,EAAEf,YAAY;UACtBgB,SAAS,EAAE;QACb,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,OAAOtG,MAAM;EACf,CAAC;;EAED;EACA,MAAMiG,mBAAmB,GAAGA,CAACM,IAAI,EAAEC,IAAI,KAAK;IAC1C,IAAID,IAAI,KAAKC,IAAI,EAAE,OAAO,CAAC;IAC3B,IAAI,CAACD,IAAI,IAAI,CAACC,IAAI,EAAE,OAAO,CAAC;IAE5B,MAAMC,MAAM,GAAGF,IAAI,CAACjD,WAAW,CAAC,CAAC,CAACyB,KAAK,CAAC,KAAK,CAAC;IAC9C,MAAM2B,MAAM,GAAGF,IAAI,CAAClD,WAAW,CAAC,CAAC,CAACyB,KAAK,CAAC,KAAK,CAAC;IAE9C,MAAM4B,WAAW,GAAGF,MAAM,CAAC1C,MAAM,CAAC6C,IAAI,IAAIF,MAAM,CAACG,QAAQ,CAACD,IAAI,CAAC,CAAC;IAChE,MAAME,UAAU,GAAG1C,IAAI,CAAC2C,GAAG,CAACN,MAAM,CAAC/C,MAAM,EAAEgD,MAAM,CAAChD,MAAM,CAAC;IAEzD,OAAOiD,WAAW,CAACjD,MAAM,GAAGoD,UAAU;EACxC,CAAC;;EAED;EACA,MAAME,6BAA6B,GAAIlE,aAAa,IAAK;IACvD,MAAMC,YAAY,GAAG2B,eAAe,CAAC,CAAC;IACtC,IAAI,CAAC3B,YAAY,IAAI,CAACD,aAAa,IAAIC,YAAY,KAAKD,aAAa,EAAE;MACrE;MACA,OAAOJ,qBAAqB,CAACI,aAAa,CAAC;IAC7C;IACA;IACA,MAAMmE,WAAW,GAAGpC,qBAAqB,CAAC9B,YAAY,EAAED,aAAa,CAAC;IACtE,MAAMoC,cAAc,GAAGpC,aAAa,CAACiC,KAAK,CAAC,IAAI,CAAC,CAAChB,MAAM,CAACiB,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC;IAC5E,MAAMH,aAAa,GAAG/B,YAAY,CAACgC,KAAK,CAAC,IAAI,CAAC,CAAChB,MAAM,CAACiB,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC;IAC1E,MAAMiC,mBAAmB,GAAG,IAAI9B,GAAG,CAAC,CAAC;IACrC,MAAMD,oBAAoB,GAAG,IAAIC,GAAG,CAAC,CAAC;IACtC;IACA6B,WAAW,CAAC5B,OAAO,CAAC,CAAC8B,IAAI,EAAEC,GAAG,KAAK;MACjC,IAAID,IAAI,CAACf,IAAI,KAAK,SAAS,EAAE;QAC3B,MAAMiB,MAAM,GAAGnC,cAAc,CAACoC,SAAS,CAACtC,IAAI;UAAA,IAAAuC,eAAA;UAAA,OAAIvC,IAAI,CAACC,IAAI,CAAC,CAAC,OAAAsC,eAAA,GAAKJ,IAAI,CAACb,SAAS,cAAAiB,eAAA,uBAAdA,eAAA,CAAgBtC,IAAI,CAAC,CAAC;QAAA,EAAC;QACvF,IAAIoC,MAAM,KAAK,CAAC,CAAC,EAAElC,oBAAoB,CAACe,GAAG,CAACmB,MAAM,CAAC;MACrD;MACA,IAAIF,IAAI,CAACf,IAAI,KAAK,OAAO,EAAE;QACzB,MAAMoB,MAAM,GAAG1C,aAAa,CAACwC,SAAS,CAACtC,IAAI;UAAA,IAAAyC,cAAA;UAAA,OAAIzC,IAAI,CAACC,IAAI,CAAC,CAAC,OAAAwC,cAAA,GAAKN,IAAI,CAACd,QAAQ,cAAAoB,cAAA,uBAAbA,cAAA,CAAexC,IAAI,CAAC,CAAC;QAAA,EAAC;QACrF,IAAIuC,MAAM,KAAK,CAAC,CAAC,EAAEN,mBAAmB,CAAChB,GAAG,CAACsB,MAAM,CAAC;MACpD;IACF,CAAC,CAAC;IACF;IACA,OAAOtC,cAAc,CAACwC,GAAG,CAAC,CAAC1C,IAAI,EAAEoC,GAAG,KAAK;MACvC,MAAMO,WAAW,GAAG3C,IAAI,CAACpC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;MACpE;MACA,MAAMgF,cAAc,GAAIR,GAAG,KAAKlC,cAAc,CAACxB,MAAM,GAAG,CAAC,IAAK,gEAAgE,CAACN,IAAI,CAAC4B,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC;MAChJ,IAAI2C,cAAc,EAAE;QAClB;QACA,IAAIC,eAAe,GAAG7C,IAAI,CAACC,IAAI,CAAC,CAAC;QACjC,IAAI,gBAAgB,CAAC7B,IAAI,CAACyE,eAAe,CAAC,EAAE;UAC1CA,eAAe,GAAG,gnBAAgnB;QACpoB;QACA,OAAO,uNAAuNA,eAAe,CAACjF,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM;MAC5Q;MACA;MACA,IAAI,CAACsE,mBAAmB,CAACpB,GAAG,CAACsB,GAAG,CAAC,IAAIpC,IAAI,CAACC,IAAI,CAAC,CAAC,CAACvB,MAAM,GAAG,CAAC,EAAE;QAC3D,OAAO,yKAAyKiE,WAAW,aAAa;MAC1M;MACA;MACA,MAAMR,IAAI,GAAGF,WAAW,CAACa,IAAI,CAACC,CAAC;QAAA,IAAAC,YAAA;QAAA,OAAI,EAAAA,YAAA,GAAAD,CAAC,CAACzB,SAAS,cAAA0B,YAAA,uBAAXA,YAAA,CAAa/C,IAAI,CAAC,CAAC,MAAKD,IAAI,CAACC,IAAI,CAAC,CAAC,IAAI8C,CAAC,CAAC3B,IAAI,KAAK,wBAAwB;MAAA,EAAC;MAC9G,IAAIe,IAAI,EAAE;QACR,OAAO,0KAA0KQ,WAAW,aAAa;MAC3M;MACA;MACA,MAAMM,KAAK,GAAGhB,WAAW,CAACa,IAAI,CAACC,CAAC;QAAA,IAAAG,aAAA;QAAA,OAAI,EAAAA,aAAA,GAAAH,CAAC,CAACzB,SAAS,cAAA4B,aAAA,uBAAXA,aAAA,CAAajD,IAAI,CAAC,CAAC,MAAKD,IAAI,CAACC,IAAI,CAAC,CAAC,IAAI8C,CAAC,CAAC3B,IAAI,KAAK,UAAU;MAAA,EAAC;MACjG,IAAI6B,KAAK,EAAE;QACT,OAAO,0KAA0KN,WAAW,aAAa;MAC3M;MACA;MACA,OAAO,mDAAmDA,WAAW,MAAM;IAC7E,CAAC,CAAC,CAACQ,IAAI,CAAC,EAAE,CAAC;IACX;IACA;EACF,CAAC;EAED,MAAMC,QAAQ,GAAGvF,gBAAgB,CAAC0B,gBAAgB,CAAC,CAAC,EAAEG,eAAe,CAAC,CAAC,CAAC;EAExE,MAAM2D,aAAa,GAAIzE,KAAK,IAAK;IAC/B,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,gBAAgB;IACxC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,iBAAiB;IACzC,OAAO,cAAc;EACvB,CAAC;EAED,MAAM0E,UAAU,GAAI1E,KAAK,IAAK;IAC5B,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,cAAc;IACtC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,eAAe;IACvC,OAAO,YAAY;EACrB,CAAC;EAED,MAAM2E,WAAW,GAAI3E,KAAK,IAAK;IAC7B,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,8BAA8B;IACtD,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,gCAAgC;IACxD,OAAO,0BAA0B;EACnC,CAAC;EAED,MAAM4E,SAAS,GAAI5E,KAAK,IAAK;IAC3B,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,cAAc;IACtC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,eAAe;IACvC,OAAO,YAAY;EACrB,CAAC;EAED,MAAM6E,mBAAmB,GAAI7E,KAAK,IAAK;IACrC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,cAAc;IACtC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,eAAe;IACvC,OAAO,YAAY;EACrB,CAAC;EAED,MAAM8E,cAAc,GAAI9E,KAAK,IAAK;IAChC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,kBAAkB;IAC1C,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,mBAAmB;IAC3C,OAAO,gBAAgB;EACzB,CAAC;;EAED;EACA,MAAM+E,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE7I,YAAY,CAAC;MACrC2I,QAAQ,CAACE,MAAM,CAAC,iBAAiB,EAAE5I,cAAc,CAAC;MAElD,MAAM6I,QAAQ,GAAG,MAAMC,KAAK,CAAC,2BAA2B,EAAE;QACxDC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEN;MACR,CAAC,CAAC;MAEF,IAAI,CAACG,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAMC,KAAK,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QACnC,MAAM,IAAIC,KAAK,CAACF,KAAK,CAACA,KAAK,IAAI,MAAM,CAAC;MACxC;;MAEA;MACA,MAAMG,kBAAkB,GAAGR,QAAQ,CAACS,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;MACtE,MAAMC,QAAQ,GAAGH,kBAAkB,GAC/BA,kBAAkB,CAACxE,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAACnC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,GAC1D,uBAAuB;;MAE3B;MACA,MAAM+G,IAAI,GAAG,MAAMZ,QAAQ,CAACY,IAAI,CAAC,CAAC;MAClC,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;MAC5C,MAAMK,CAAC,GAAGzH,QAAQ,CAAC0H,aAAa,CAAC,GAAG,CAAC;MACrCD,CAAC,CAACE,IAAI,GAAGN,GAAG;MACZI,CAAC,CAACG,QAAQ,GAAGT,QAAQ;MACrBnH,QAAQ,CAAC2G,IAAI,CAACkB,WAAW,CAACJ,CAAC,CAAC;MAC5BA,CAAC,CAACK,KAAK,CAAC,CAAC;MACTR,MAAM,CAACC,GAAG,CAACQ,eAAe,CAACV,GAAG,CAAC;MAC/BrH,QAAQ,CAAC2G,IAAI,CAACqB,WAAW,CAACP,CAAC,CAAC;IAE9B,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdoB,OAAO,CAACpB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCqB,KAAK,CAAC,QAAQ,GAAGrB,KAAK,CAACsB,OAAO,CAAC;IACjC;EACF,CAAC;EAED,MAAMC,YAAY,GAAGA,CAACC,GAAG,EAAExE,IAAI,KAAK;IAClC,IAAIA,IAAI,KAAK,SAAS,EAAE;MACtBpE,kBAAkB,CAAC6I,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACD,GAAG,GAAG,CAACC,IAAI,CAACD,GAAG;MAAE,CAAC,CAAC,CAAC;IAC9D,CAAC,MAAM;MACL1I,kBAAkB,CAAC2I,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACD,GAAG,GAAG,CAACC,IAAI,CAACD,GAAG;MAAE,CAAC,CAAC,CAAC;IAC9D;EACF,CAAC;EAED,oBACEhL,OAAA;IAAAkL,QAAA,gBAEElL,OAAA;MAAKmL,SAAS,EAAC,MAAM;MAAAD,QAAA,eACnBlL,OAAA;QAAKmL,SAAS,EAAC,sEAAsE;QAAAD,QAAA,gBAEnFlL,OAAA;UAAKmL,SAAS,EAAE,GAAGxC,WAAW,CAACH,QAAQ,CAACxE,KAAK,CAAC,8CAA+C;UAAAkH,QAAA,gBAE3FlL,OAAA;YAAKmL,SAAS,EAAC,0EAA0E;YAAAD,QAAA,eACvFlL,OAAA;cAAKmL,SAAS,EAAC,WAAW;cAACC,IAAI,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAH,QAAA,eAChElL,OAAA;gBAAMsL,QAAQ,EAAC,SAAS;gBAACnD,CAAC,EAAC,uIAAuI;gBAACoD,QAAQ,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN3L,OAAA;YAAKmL,SAAS,EAAC,UAAU;YAAAD,QAAA,eACvBlL,OAAA;cAAKmL,SAAS,EAAC,8DAA8D;cAAAD,QAAA,gBAC3ElL,OAAA;gBAAKmL,SAAS,EAAC,cAAc;gBAAAD,QAAA,eAC3BlL,OAAA;kBAAKmL,SAAS,EAAC,wBAAwB;kBAAAD,QAAA,gBACrClL,OAAA;oBAAKmL,SAAS,EAAE,GAAGvC,SAAS,CAACJ,QAAQ,CAACxE,KAAK,CAAC,oFAAqF;oBAAAkH,QAAA,eAC/HlL,OAAA;sBAAKmL,SAAS,EAAC,SAAS;sBAACC,IAAI,EAAC,MAAM;sBAACQ,MAAM,EAAC,cAAc;sBAACP,OAAO,EAAC,WAAW;sBAAAH,QAAA,eAC5ElL,OAAA;wBAAM6L,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAE,CAAE;wBAAC5D,CAAC,EAAC;sBAAgB;wBAAAqD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN3L,OAAA;oBAAAkL,QAAA,gBACElL,OAAA;sBAAImL,SAAS,EAAC,uCAAuC;sBAAAD,QAAA,EAAC;oBAEtD;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACL3L,OAAA;sBAAKmL,SAAS,EAAC,yCAAyC;sBAAAD,QAAA,gBACtDlL,OAAA;wBAAKmL,SAAS,EAAC,cAAc;wBAACC,IAAI,EAAC,MAAM;wBAACQ,MAAM,EAAC,cAAc;wBAACP,OAAO,EAAC,WAAW;wBAAAH,QAAA,eACjFlL,OAAA;0BAAM6L,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,WAAW,EAAE,CAAE;0BAAC5D,CAAC,EAAC;wBAA4B;0BAAAqD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjG,CAAC,uFAER;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN3L,OAAA;gBAAKmL,SAAS,EAAC,6BAA6B;gBAAAD,QAAA,gBAE1ClL,OAAA;kBAAKmL,SAAS,EAAE,qBAAqBrC,cAAc,CAACN,QAAQ,CAACxE,KAAK,CAAC,2GAA4G;kBAAAkH,QAAA,gBAC7KlL,OAAA;oBAAKmL,SAAS,EAAE,gCAAgC1C,aAAa,CAACD,QAAQ,CAACxE,KAAK,CAAC,EAAG;oBAAAkH,QAAA,GAC7E1C,QAAQ,CAACxE,KAAK,EAAC,GAClB;kBAAA;oBAAAwH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACN3L,OAAA;oBAAKmL,SAAS,EAAC,0CAA0C;oBAAAD,QAAA,EAAC;kBAAS;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACzE3L,OAAA;oBAAKmL,SAAS,EAAC,qCAAqC;oBAAAD,QAAA,eAClDlL,OAAA;sBACEmL,SAAS,EAAE,iDAAiDtC,mBAAmB,CAACL,QAAQ,CAACxE,KAAK,CAAC,EAAG;sBAClGgI,KAAK,EAAE;wBAAEC,KAAK,EAAE,GAAGzD,QAAQ,CAACxE,KAAK;sBAAI;oBAAE;sBAAAwH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN3L,OAAA;kBAAKmL,SAAS,EAAC,aAAa;kBAAAD,QAAA,gBAC1BlL,OAAA;oBAAKmL,SAAS,EAAE,uEACd3C,QAAQ,CAACxE,KAAK,IAAI,EAAE,GAAG,6BAA6B,GACpDwE,QAAQ,CAACxE,KAAK,IAAI,EAAE,GAAG,+BAA+B,GACtD,yBAAyB,EACxB;oBAAAkH,QAAA,EACA1C,QAAQ,CAACxE,KAAK,IAAI,EAAE,GAAG,WAAW,GAAGwE,QAAQ,CAACxE,KAAK,IAAI,EAAE,GAAG,MAAM,GAAG;kBAAmB;oBAAAwH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtF,CAAC,eACN3L,OAAA;oBAAKmL,SAAS,EAAC,4BAA4B;oBAAAD,QAAA,EACxC1C,QAAQ,CAACxE,KAAK,IAAI,EAAE,GAAG,mCAAmC,GAC1DwE,QAAQ,CAACxE,KAAK,IAAI,EAAE,GAAG,mCAAmC,GAC1D;kBAAgC;oBAAAwH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL,CAAAvL,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE8L,kBAAkB,kBACzBlM,OAAA;UAAKmL,SAAS,EAAC,KAAK;UAAAD,QAAA,gBAElBlL,OAAA;YAAKmL,SAAS,EAAC,kBAAkB;YAAAD,QAAA,gBAC/BlL,OAAA;cAAImL,SAAS,EAAC,0CAA0C;cAAAD,QAAA,EAAC;YAAkB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChF3L,OAAA;cAAGmL,SAAS,EAAC,eAAe;cAAAD,QAAA,EAAC;YAAiE;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/F,CAAC,eAGN3L,OAAA;YAAKmL,SAAS,EAAC,4CAA4C;YAAAD,QAAA,gBAEzDlL,OAAA;cAAKmL,SAAS,EAAC,uCAAuC;cAAAD,QAAA,gBACpDlL,OAAA;gBAAKmL,SAAS,EAAC,uCAAuC;gBAAAD,QAAA,GACnD,EAAA1K,qBAAA,GAAAJ,MAAM,CAAC8L,kBAAkB,CAACC,oBAAoB,cAAA3L,qBAAA,uBAA9CA,qBAAA,CAAgD4L,mBAAmB,KAAI,CAAC,EAAC,GAC5E;cAAA;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN3L,OAAA;gBAAKmL,SAAS,EAAC,4BAA4B;gBAAAD,QAAA,EAAC;cAAc;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChE3L,OAAA;gBAAKmL,SAAS,EAAC,qCAAqC;gBAAAD,QAAA,eAClDlL,OAAA;kBACEmL,SAAS,EAAC,2DAA2D;kBACrEa,KAAK,EAAE;oBAAEC,KAAK,EAAE,GAAG,EAAAxL,sBAAA,GAAAL,MAAM,CAAC8L,kBAAkB,CAACC,oBAAoB,cAAA1L,sBAAA,uBAA9CA,sBAAA,CAAgD2L,mBAAmB,KAAI,CAAC;kBAAI;gBAAE;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7F;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN3L,OAAA;cAAKmL,SAAS,EAAC,kEAAkE;cAAAD,QAAA,gBAC/ElL,OAAA;gBAAKmL,SAAS,EAAC,uCAAuC;gBAAAD,QAAA,gBACpDlL,OAAA;kBAAKmL,SAAS,EAAC,6BAA6B;kBAACC,IAAI,EAAC,MAAM;kBAACQ,MAAM,EAAC,cAAc;kBAACP,OAAO,EAAC,WAAW;kBAAAH,QAAA,eAChGlL,OAAA;oBAAM6L,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAAC5D,CAAC,EAAC;kBAAgB;oBAAAqD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrF,CAAC,eACN3L,OAAA;kBAAKmL,SAAS,EAAC,mCAAmC;kBAAAD,QAAA,GAC/C,EAAAxK,sBAAA,GAAAN,MAAM,CAAC8L,kBAAkB,CAACG,qBAAqB,cAAA3L,sBAAA,uBAA/CA,sBAAA,CAAiD0L,mBAAmB,KAAI,CAAC,EAAC,GAC7E;gBAAA;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN3L,OAAA;gBAAKmL,SAAS,EAAC,yCAAyC;gBAAAD,QAAA,EAAC;cAAe;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9E3L,OAAA;gBAAKmL,SAAS,EAAC,sCAAsC;gBAAAD,QAAA,eACnDlL,OAAA;kBACEmL,SAAS,EAAC,4DAA4D;kBACtEa,KAAK,EAAE;oBAAEC,KAAK,EAAE,GAAG,EAAAtL,sBAAA,GAAAP,MAAM,CAAC8L,kBAAkB,CAACG,qBAAqB,cAAA1L,sBAAA,uBAA/CA,sBAAA,CAAiDyL,mBAAmB,KAAI,CAAC;kBAAI;gBAAE;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9F;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN3L,OAAA;cAAKmL,SAAS,EAAC,uCAAuC;cAAAD,QAAA,gBACpDlL,OAAA;gBAAKmL,SAAS,EAAE,2BAA2B/K,MAAM,CAAC8L,kBAAkB,CAACI,iBAAiB,IAAI,CAAC,GAAG,eAAe,GAAG,iBAAiB,EAAG;gBAAApB,QAAA,GACjI9K,MAAM,CAAC8L,kBAAkB,CAACI,iBAAiB,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAElM,MAAM,CAAC8L,kBAAkB,CAACI,iBAAiB,EAAC,GAC5G;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN3L,OAAA;gBAAKmL,SAAS,EAAC,4BAA4B;gBAAAD,QAAA,EAAC;cAAW;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7D3L,OAAA;gBAAKmL,SAAS,EAAC,wDAAwD;gBAAAD,QAAA,gBACrElL,OAAA;kBAAKmL,SAAS,EAAC,4BAA4B;kBAACC,IAAI,EAAC,MAAM;kBAACQ,MAAM,EAAC,cAAc;kBAACP,OAAO,EAAC,WAAW;kBAAAH,QAAA,eAC/FlL,OAAA;oBAAM6L,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAAC5D,CAAC,EAAC;kBAAgC;oBAAAqD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrG,CAAC,sBAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGL,EAAA/K,sBAAA,GAAAR,MAAM,CAAC8L,kBAAkB,CAACG,qBAAqB,cAAAzL,sBAAA,uBAA/CA,sBAAA,CAAiD2L,SAAS,kBACzDvM,OAAA;YAAKmL,SAAS,EAAC,2BAA2B;YAAAD,QAAA,gBACxClL,OAAA;cAAImL,SAAS,EAAC,wCAAwC;cAAAD,QAAA,EAAC;YAAqB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjF3L,OAAA;cAAKmL,SAAS,EAAC,sDAAsD;cAAAD,QAAA,EAClEjH,MAAM,CAACuI,OAAO,CAACpM,MAAM,CAAC8L,kBAAkB,CAACG,qBAAqB,CAACE,SAAS,CAAC,CAACzE,GAAG,CAAC,CAAC,CAACkD,GAAG,EAAEyB,IAAI,CAAC,kBACzFzM,OAAA;gBAAemL,SAAS,EAAC,kFAAkF;gBAAAD,QAAA,gBACzGlL,OAAA;kBAAMmL,SAAS,EAAC,8CAA8C;kBAAAD,QAAA,EAC3DF,GAAG,CAAChI,OAAO,CAAC,GAAG,EAAE,GAAG;gBAAC;kBAAAwI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eACP3L,OAAA;kBAAKmL,SAAS,EAAC,mBAAmB;kBAAAD,QAAA,gBAChClL,OAAA;oBAAMmL,SAAS,EAAE,8BAA8BsB,IAAI,CAACzI,KAAK,IAAI,EAAE,GAAG,gBAAgB,GAAGyI,IAAI,CAACzI,KAAK,IAAI,EAAE,GAAG,iBAAiB,GAAG,cAAc,EAAG;oBAAAkH,QAAA,GAC1IuB,IAAI,CAACzI,KAAK,EAAC,GACd;kBAAA;oBAAAwH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACP3L,OAAA;oBAAKmL,SAAS,EAAC,mCAAmC;oBAAAD,QAAA,eAChDlL,OAAA;sBACEmL,SAAS,EAAE,oBACTsB,IAAI,CAACzI,KAAK,IAAI,EAAE,GAAG,cAAc,GACjCyI,IAAI,CAACzI,KAAK,IAAI,EAAE,GAAG,eAAe,GAAG,YAAY,EAChD;sBACHgI,KAAK,EAAE;wBAAEC,KAAK,EAAE,GAAGQ,IAAI,CAACzI,KAAK;sBAAI;oBAAE;sBAAAwH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA,GAjBEX,GAAG;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAkBR,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3L,OAAA;MAAKmL,SAAS,EAAC,MAAM;MAAAD,QAAA,eACnBlL,OAAA;QAAKmL,SAAS,EAAC,sEAAsE;QAAAD,QAAA,gBAEnFlL,OAAA;UAAKmL,SAAS,EAAC,qCAAqC;UAAAD,QAAA,eAClDlL,OAAA;YAAKmL,SAAS,EAAC,MAAM;YAAAD,QAAA,eACnBlL,OAAA;cAAKmL,SAAS,EAAC,gBAAgB;cAAAD,QAAA,gBAC7BlL,OAAA;gBACE0M,OAAO,EAAEA,CAAA,KAAMpL,YAAY,CAAC,WAAW,CAAE;gBACzC6J,SAAS,EAAE,8DACT9J,SAAS,KAAK,WAAW,GACrB,mCAAmC,GACnC,4EAA4E,EAC/E;gBAAA6J,QAAA,eAEHlL,OAAA;kBAAMmL,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,gBAC3ClL,OAAA;oBAAKmL,SAAS,EAAC,SAAS;oBAACC,IAAI,EAAC,MAAM;oBAACQ,MAAM,EAAC,cAAc;oBAACP,OAAO,EAAC,WAAW;oBAAAH,QAAA,eAC5ElL,OAAA;sBAAM6L,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAAC5D,CAAC,EAAC;oBAA+C;sBAAAqD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpH,CAAC,eACN3L,OAAA;oBAAAkL,QAAA,EAAM;kBAAgB;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAET3L,OAAA;gBACE0M,OAAO,EAAEA,CAAA,KAAMpL,YAAY,CAAC,UAAU,CAAE;gBACxC6J,SAAS,EAAE,8DACT9J,SAAS,KAAK,UAAU,GACpB,mCAAmC,GACnC,4EAA4E,EAC/E;gBAAA6J,QAAA,eAEHlL,OAAA;kBAAMmL,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,gBAC3ClL,OAAA;oBAAKmL,SAAS,EAAC,SAAS;oBAACC,IAAI,EAAC,MAAM;oBAACQ,MAAM,EAAC,cAAc;oBAACP,OAAO,EAAC,WAAW;oBAAAH,QAAA,eAC5ElL,OAAA;sBAAM6L,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAAC5D,CAAC,EAAC;oBAAsM;sBAAAqD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3Q,CAAC,eACN3L,OAAA;oBAAAkL,QAAA,EAAM;kBAAY;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGLtK,SAAS,KAAK,WAAW,iBACxBrB,OAAA;UAAKmL,SAAS,EAAC,oCAAoC;UAAAD,QAAA,gBACjDlL,OAAA;YAAImL,SAAS,EAAC,4DAA4D;YAAAD,QAAA,gBACxElL,OAAA;cAAKmL,SAAS,EAAC,4BAA4B;cAACC,IAAI,EAAC,MAAM;cAACQ,MAAM,EAAC,cAAc;cAACP,OAAO,EAAC,WAAW;cAAAH,QAAA,eAC/FlL,OAAA;gBAAM6L,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAAC5D,CAAC,EAAC;cAAsM;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3Q,CAAC,2BAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEL3L,OAAA;YAAKmL,SAAS,EAAC,sDAAsD;YAAAD,QAAA,gBAEnElL,OAAA;cAAKmL,SAAS,EAAC,uCAAuC;cAAAD,QAAA,gBACpDlL,OAAA;gBAAKmL,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,EAAC;cAAe;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7E3L,OAAA;gBAAKmL,SAAS,EAAC,uCAAuC;gBAAAD,QAAA,EACnD,CAAA9K,MAAM,aAANA,MAAM,wBAAAS,qBAAA,GAANT,MAAM,CAAEuM,eAAe,cAAA9L,qBAAA,uBAAvBA,qBAAA,CAAyB+L,cAAc,CAAC,CAAC,KAAI;cAAK;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eACN3L,OAAA;gBAAKmL,SAAS,EAAC,uBAAuB;gBAAAD,QAAA,EAAC;cAAU;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eAGN3L,OAAA;cAAKmL,SAAS,EAAC,uCAAuC;cAAAD,QAAA,gBACpDlL,OAAA;gBAAKmL,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,EAAC;cAAgB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9E3L,OAAA;gBAAKmL,SAAS,EAAC,uCAAuC;gBAAAD,QAAA,EACnD,CAAA9K,MAAM,aAANA,MAAM,wBAAAU,qBAAA,GAANV,MAAM,CAAEyM,gBAAgB,cAAA/L,qBAAA,uBAAxBA,qBAAA,CAA0B8L,cAAc,CAAC,CAAC,KAAI;cAAK;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,eACN3L,OAAA;gBAAKmL,SAAS,EAAC,uBAAuB;gBAAAD,QAAA,EAAC;cAAU;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eAGN3L,OAAA;cAAKmL,SAAS,EAAC,uCAAuC;cAAAD,QAAA,gBACpDlL,OAAA;gBAAKmL,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,EAAC;cAAM;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpE3L,OAAA;gBAAKmL,SAAS,EAAC,yBAAyB;gBAAAD,QAAA,EACrC,CAAC,MAAM;kBACN,MAAMzE,QAAQ,GAAG,CAAArG,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEuM,eAAe,KAAI,CAAC;kBAC7C,MAAMjG,SAAS,GAAG,CAAAtG,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEyM,gBAAgB,KAAI,CAAC;kBAC/C,MAAMtF,IAAI,GAAGb,SAAS,GAAGD,QAAQ;kBACjC,IAAIA,QAAQ,KAAK,CAAC,IAAIC,SAAS,KAAK,CAAC,EAAE,oBAAO1G,OAAA;oBAAMmL,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAG;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;kBACxF,oBACE3L,OAAA;oBAAMmL,SAAS,EAAE5D,IAAI,IAAI,CAAC,GAAG,eAAe,GAAG,iBAAkB;oBAAA2D,QAAA,GAC9D3D,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAEA,IAAI,CAACqF,cAAc,CAAC,CAAC;kBAAA;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC;gBAEX,CAAC,EAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACN3L,OAAA;gBAAKmL,SAAS,EAAC,uBAAuB;gBAAAD,QAAA,EAAC;cAAU;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eAGN3L,OAAA;cAAKmL,SAAS,EAAC,wCAAwC;cAAAD,QAAA,gBACrDlL,OAAA;gBAAKmL,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,EAAC;cAAiB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC/E3L,OAAA;gBAAKmL,SAAS,EAAC,yBAAyB;gBAAAD,QAAA,EACrC,CAAC,MAAM;kBACN,MAAMzE,QAAQ,GAAG,CAAArG,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEuM,eAAe,KAAI,CAAC;kBAC7C,MAAMjG,SAAS,GAAG,CAAAtG,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEyM,gBAAgB,KAAI,CAAC;kBAC/C,IAAIpG,QAAQ,KAAK,CAAC,IAAIC,SAAS,KAAK,CAAC,EAAE,oBAAO1G,OAAA;oBAAMmL,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAG;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;kBACxF,IAAImB,WAAW,GAAG,CAAC;kBACnB,IAAIpG,SAAS,GAAGD,QAAQ,EAAE;oBACxBqG,WAAW,GAAI,CAACpG,SAAS,GAAGD,QAAQ,IAAIC,SAAS,GAAI,GAAG;kBAC1D,CAAC,MAAM;oBACLoG,WAAW,GAAI,CAACrG,QAAQ,GAAGC,SAAS,IAAID,QAAQ,GAAI,GAAG;kBACzD;kBACAqG,WAAW,GAAGtI,IAAI,CAAC2C,GAAG,CAAC,CAAC,EAAE2F,WAAW,CAAC;kBACtC,MAAMC,UAAU,GAAGD,WAAW,GAAG,EAAE,GAAG,gBAAgB,GAAGA,WAAW,GAAG,CAAC,GAAG,eAAe,GAAG,iBAAiB;kBAC9G,oBACE9M,OAAA;oBAAMmL,SAAS,EAAE4B,UAAW;oBAAA7B,QAAA,GACzB4B,WAAW,CAACE,OAAO,CAAC,CAAC,CAAC,EAAC,GAC1B;kBAAA;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAEX,CAAC,EAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACN3L,OAAA;gBAAKmL,SAAS,EAAC,uBAAuB;gBAAAD,QAAA,EAAC;cAAU;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGD3L,OAAA;UAAKmL,SAAS,EAAC,KAAK;UAAAD,QAAA,GAEjB7J,SAAS,KAAK,WAAW,iBACxBrB,OAAA;YAAAkL,QAAA,gBACElL,OAAA;cAAKmL,SAAS,EAAC,wCAAwC;cAAAD,QAAA,gBACrDlL,OAAA;gBAAKmL,SAAS,EAAC,qCAAqC;gBAAAD,QAAA,gBAClDlL,OAAA;kBAAMmL,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAsB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC7D3L,OAAA;kBAAKmL,SAAS,EAAC,gBAAgB;kBAAAD,QAAA,gBAC7BlL,OAAA;oBACE0M,OAAO,EAAEA,CAAA,KAAM5K,WAAW,CAAC,KAAK,CAAE;oBAClCqJ,SAAS,EAAE,8DACTtJ,QAAQ,KAAK,KAAK,GACd,+BAA+B,GAC/B,6CAA6C,EAChD;oBAAAqJ,QAAA,EACJ;kBAED;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT3L,OAAA;oBACE0M,OAAO,EAAEA,CAAA,KAAM5K,WAAW,CAAC,UAAU,CAAE;oBACvCqJ,SAAS,EAAE,8DACTtJ,QAAQ,KAAK,UAAU,GACnB,+BAA+B,GAC/B,6CAA6C,EAChD;oBAAAqJ,QAAA,EACJ;kBAED;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT3L,OAAA;oBACE0M,OAAO,EAAEA,CAAA,KAAM5K,WAAW,CAAC,MAAM,CAAE;oBACnCqJ,SAAS,EAAE,8DACTtJ,QAAQ,KAAK,MAAM,GACf,+BAA+B,GAC/B,6CAA6C,EAChD;oBACHoL,QAAQ,EAAE,EAAC7M,MAAM,aAANA,MAAM,eAANA,MAAM,CAAE8M,kBAAkB,CAAC;oBAAAhC,QAAA,EACvC;kBAED;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN3L,OAAA;gBAAKmL,SAAS,EAAC,0BAA0B;gBAAAD,QAAA,gBACvClL,OAAA;kBACE0M,OAAO,EAAEA,CAAA,KAAM9K,qBAAqB,CAAC,CAACD,kBAAkB,CAAE;kBAC1DwJ,SAAS,EAAC,kIAAkI;kBAAAD,QAAA,gBAE5IlL,OAAA;oBAAKmL,SAAS,EAAC,cAAc;oBAACC,IAAI,EAAC,MAAM;oBAACQ,MAAM,EAAC,cAAc;oBAACP,OAAO,EAAC,WAAW;oBAAAH,QAAA,eACjFlL,OAAA;sBAAM6L,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAAC5D,CAAC,EAAC;oBAAiI;sBAAAqD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtM,CAAC,qBAEN,eAAA3L,OAAA;oBAAKmL,SAAS,EAAC,cAAc;oBAACC,IAAI,EAAC,MAAM;oBAACQ,MAAM,EAAC,cAAc;oBAACP,OAAO,EAAC,WAAW;oBAAAH,QAAA,eACjFlL,OAAA;sBAAM6L,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAAC5D,CAAC,EAAC;oBAAgB;sBAAAqD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,EAERhK,kBAAkB,iBACjB3B,OAAA;kBAAKmL,SAAS,EAAC,sFAAsF;kBAAAD,QAAA,eACnGlL,OAAA;oBAAKmL,SAAS,EAAC,MAAM;oBAAAD,QAAA,GAClB,CAAA9K,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE+M,iBAAiB,MAAI/M,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEgN,YAAY,kBAChDpN,OAAA,CAAAE,SAAA;sBAAAgL,QAAA,gBACElL,OAAA;wBACE0M,OAAO,EAAEA,CAAA,KAAM;0BACb3D,kBAAkB,CAAC,CAAC;0BACpBnH,qBAAqB,CAAC,KAAK,CAAC;wBAC9B,CAAE;wBACFqL,QAAQ,EAAExL,eAAgB;wBAC1B0J,SAAS,EAAC,iHAAiH;wBAAAD,QAAA,eAE3HlL,OAAA;0BAAKmL,SAAS,EAAC,mBAAmB;0BAAAD,QAAA,gBAChClL,OAAA;4BAAKmL,SAAS,EAAC,wEAAwE;4BAAAD,QAAA,EACpFzJ,eAAe,gBACdzB,OAAA;8BAAKmL,SAAS,EAAC,sCAAsC;8BAACC,IAAI,EAAC,MAAM;8BAACC,OAAO,EAAC,WAAW;8BAAAH,QAAA,gBACnFlL,OAAA;gCAAQmL,SAAS,EAAC,YAAY;gCAACkC,EAAE,EAAC,IAAI;gCAACC,EAAE,EAAC,IAAI;gCAACC,CAAC,EAAC,IAAI;gCAAC3B,MAAM,EAAC,cAAc;gCAACG,WAAW,EAAC;8BAAG;gCAAAP,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAS,CAAC,eACrG3L,OAAA;gCAAMmL,SAAS,EAAC,YAAY;gCAACC,IAAI,EAAC,cAAc;gCAACjD,CAAC,EAAC;8BAAiH;gCAAAqD,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACzK,CAAC,gBAEN3L,OAAA;8BAAKmL,SAAS,EAAC,yBAAyB;8BAACC,IAAI,EAAC,MAAM;8BAACQ,MAAM,EAAC,cAAc;8BAACP,OAAO,EAAC,WAAW;8BAAAH,QAAA,eAC5FlL,OAAA;gCAAM6L,aAAa,EAAC,OAAO;gCAACC,cAAc,EAAC,OAAO;gCAACC,WAAW,EAAE,CAAE;gCAAC5D,CAAC,EAAC;8BAAqF;gCAAAqD,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC1J;0BACN;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACE,CAAC,eACN3L,OAAA;4BAAAkL,QAAA,gBACElL,OAAA;8BAAKmL,SAAS,EAAC,6BAA6B;8BAAAD,QAAA,EACzCzJ,eAAe,GAAG,gBAAgB,GAAG;4BAA0B;8BAAA+J,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC7D,CAAC,eACN3L,OAAA;8BAAKmL,SAAS,EAAC,yBAAyB;8BAAAD,QAAA,EAAC;4BAA8B;8BAAAM,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC1E,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC,eACT3L,OAAA;wBAAKmL,SAAS,EAAC;sBAA+B;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA,eACrD,CACH,eAED3L,OAAA;sBACE0M,OAAO,EAAEA,CAAA,KAAM;wBACbjN,WAAW,CAACkF,gBAAgB,CAAC,CAAC,CAAC;wBAC/B/C,qBAAqB,CAAC,KAAK,CAAC;sBAC9B,CAAE;sBACFuJ,SAAS,EAAC,8DAA8D;sBAAAD,QAAA,eAExElL,OAAA;wBAAKmL,SAAS,EAAC,mBAAmB;wBAAAD,QAAA,gBAChClL,OAAA;0BAAKmL,SAAS,EAAC,qEAAqE;0BAAAD,QAAA,eAClFlL,OAAA;4BAAKmL,SAAS,EAAC,sBAAsB;4BAACC,IAAI,EAAC,MAAM;4BAACQ,MAAM,EAAC,cAAc;4BAACP,OAAO,EAAC,WAAW;4BAAAH,QAAA,eACzFlL,OAAA;8BAAM6L,aAAa,EAAC,OAAO;8BAACC,cAAc,EAAC,OAAO;8BAACC,WAAW,EAAE,CAAE;8BAAC5D,CAAC,EAAC;4BAAiI;8BAAAqD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACtM;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACN3L,OAAA;0BAAAkL,QAAA,gBACElL,OAAA;4BAAKmL,SAAS,EAAC,2BAA2B;4BAAAD,QAAA,EAAC;0BAAU;4BAAAM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAC3D3L,OAAA;4BAAKmL,SAAS,EAAC,uBAAuB;4BAAAD,QAAA,EAAC;0BAAwB;4BAAAM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC,eAET3L,OAAA;sBACE0M,OAAO,EAAEA,CAAA,KAAM;wBACbhN,YAAY,CAACiF,gBAAgB,CAAC,CAAC,CAAC;wBAChC/C,qBAAqB,CAAC,KAAK,CAAC;sBAC9B,CAAE;sBACFuJ,SAAS,EAAC,+DAA+D;sBAAAD,QAAA,eAEzElL,OAAA;wBAAKmL,SAAS,EAAC,mBAAmB;wBAAAD,QAAA,gBAChClL,OAAA;0BAAKmL,SAAS,EAAC,sEAAsE;0BAAAD,QAAA,eACnFlL,OAAA;4BAAKmL,SAAS,EAAC,uBAAuB;4BAACC,IAAI,EAAC,MAAM;4BAACQ,MAAM,EAAC,cAAc;4BAACP,OAAO,EAAC,WAAW;4BAAAH,QAAA,eAC1FlL,OAAA;8BAAM6L,aAAa,EAAC,OAAO;8BAACC,cAAc,EAAC,OAAO;8BAACC,WAAW,EAAE,CAAE;8BAAC5D,CAAC,EAAC;4BAAsH;8BAAAqD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC3L;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACN3L,OAAA;0BAAAkL,QAAA,gBACElL,OAAA;4BAAKmL,SAAS,EAAC,2BAA2B;4BAAAD,QAAA,EAAC;0BAAW;4BAAAM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAC5D3L,OAAA;4BAAKmL,SAAS,EAAC,uBAAuB;4BAAAD,QAAA,EAAC;0BAAyB;4BAAAM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC,eAET3L,OAAA;sBAAKmL,SAAS,EAAC;oBAA+B;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAErD3L,OAAA;sBACE0M,OAAO,EAAEA,CAAA,KAAM;wBACbzC,MAAM,CAACuD,KAAK,CAAC,CAAC;wBACd5L,qBAAqB,CAAC,KAAK,CAAC;sBAC9B,CAAE;sBACFuJ,SAAS,EAAC,+DAA+D;sBAAAD,QAAA,eAEzElL,OAAA;wBAAKmL,SAAS,EAAC,mBAAmB;wBAAAD,QAAA,gBAChClL,OAAA;0BAAKmL,SAAS,EAAC,sEAAsE;0BAAAD,QAAA,eACnFlL,OAAA;4BAAKmL,SAAS,EAAC,uBAAuB;4BAACC,IAAI,EAAC,MAAM;4BAACQ,MAAM,EAAC,cAAc;4BAACP,OAAO,EAAC,WAAW;4BAAAH,QAAA,eAC1FlL,OAAA;8BAAM6L,aAAa,EAAC,OAAO;8BAACC,cAAc,EAAC,OAAO;8BAACC,WAAW,EAAE,CAAE;8BAAC5D,CAAC,EAAC;4BAA8K;8BAAAqD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACnP;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACN3L,OAAA;0BAAAkL,QAAA,gBACElL,OAAA;4BAAKmL,SAAS,EAAC,2BAA2B;4BAAAD,QAAA,EAAC;0BAAY;4BAAAM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAC7D3L,OAAA;4BAAKmL,SAAS,EAAC,uBAAuB;4BAAAD,QAAA,EAAC;0BAA2B;4BAAAM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN3L,OAAA;cAAKmL,SAAS,EAAC,wEAAwE;cAAAD,QAAA,GAEpFrJ,QAAQ,KAAK,KAAK,iBACjB7B,OAAA,CAAAE,SAAA;gBAAAgL,QAAA,EACG,CAACuC,MAAA,IAAM;kBACN,MAAM1K,IAAI,GAAG4B,gBAAgB,CAAC,CAAC,IAAI,wCAAwC;kBAC3E,MAAM+I,KAAK,GAAG3K,IAAI,CAACoC,KAAK,CAAC,IAAI,CAAC;kBAC9B,MAAMwI,QAAQ,IAAAF,MAAA,GAAGC,KAAK,CAACA,KAAK,CAAC5J,MAAM,GAAG,CAAC,CAAC,cAAA2J,MAAA,uBAAvBA,MAAA,CAAyBpI,IAAI,CAAC,CAAC;kBAChD,MAAM2C,cAAc,GAAG,gEAAgE,CAACxE,IAAI,CAACmK,QAAQ,CAAC;kBACtG,IAAI3F,cAAc,EAAE;oBAClB,MAAM4F,QAAQ,GAAGF,KAAK,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACtF,IAAI,CAAC,IAAI,CAAC;oBAC9C,oBACEvI,OAAA,CAAAE,SAAA;sBAAAgL,QAAA,gBACElL,OAAA;wBAAKmL,SAAS,EAAC,0EAA0E;wBAAAD,QAAA,EACtF0C;sBAAQ;wBAAApC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC,eACN3L,OAAA;wBAAKmL,SAAS,EAAC,yGAAyG;wBAACa,KAAK,EAAE;0BAAC8B,SAAS,EAAC;wBAAQ,CAAE;wBAAA5C,QAAA,gBACnJlL,OAAA;0BAAMmL,SAAS,EAAC,8BAA8B;0BAAAD,QAAA,EAAC;wBAAc;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,EAACgC,QAAQ;sBAAA;wBAAAnC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1E,CAAC;oBAAA,eACN,CAAC;kBAEP,CAAC,MAAM;oBACL,oBACE3L,OAAA;sBAAKmL,SAAS,EAAC,0EAA0E;sBAAAD,QAAA,EACtFnI;oBAAI;sBAAAyI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC;kBAEV;gBACF,CAAC,EAAE;cAAC,gBACJ,CACH,EACA9J,QAAQ,KAAK,UAAU,iBACtB7B,OAAA;gBACEmL,SAAS,EAAC,sDAAsD;gBAChE4C,uBAAuB,EAAE;kBACvBC,MAAM,EAAE5G,6BAA6B,CAACzC,gBAAgB,CAAC,CAAC;gBAC1D;cAAE;gBAAA6G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACF,EACA9J,QAAQ,KAAK,MAAM,KAAIzB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE8M,kBAAkB,kBAChDlN,OAAA;gBAAKmL,SAAS,EAAC,wBAAwB;gBAAAD,QAAA,gBACrClL,OAAA;kBAAKmL,SAAS,EAAC,mBAAmB;kBAAAD,QAAA,gBAChClL,OAAA;oBACE0M,OAAO,EAAEA,CAAA,KAAM1K,kBAAkB,CAAC,QAAQ,CAAE;oBAC5CmJ,SAAS,EAAE,gEACTpJ,eAAe,KAAK,QAAQ,GACxB,wBAAwB,GACxB,6CAA6C,EAChD;oBAAAmJ,QAAA,EACJ;kBAED;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT3L,OAAA;oBACE0M,OAAO,EAAEA,CAAA,KAAM1K,kBAAkB,CAAC,QAAQ,CAAE;oBAC5CmJ,SAAS,EAAE,gEACTpJ,eAAe,KAAK,QAAQ,GACxB,wBAAwB,GACxB,6CAA6C,EAChD;oBAAAmJ,QAAA,EACJ;kBAED;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT3L,OAAA;oBACE0M,OAAO,EAAEA,CAAA,KAAM1K,kBAAkB,CAAC,UAAU,CAAE;oBAC9CmJ,SAAS,EAAE,2DACTpJ,eAAe,KAAK,UAAU,GAC1B,yBAAyB,GACzB,6CAA6C,EAChD;oBAAAmJ,QAAA,EACJ;kBAED;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,EAEL5J,eAAe,KAAK,QAAQ,iBAC3B/B,OAAA;kBAAKmL,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,eACtClL,OAAA;oBACEiO,GAAG,EAAE,sDAAsDC,kBAAkB,CAAC9N,MAAM,CAAC8M,kBAAkB,CAAC,EAAG;oBAC3GjB,KAAK,EAAC,MAAM;oBACZkC,MAAM,EAAC,KAAK;oBACZC,WAAW,EAAC,GAAG;oBACfC,KAAK,EAAC,uBAAuB;oBAC7BrC,KAAK,EAAE;sBAAEsC,UAAU,EAAE,MAAM;sBAAEC,YAAY,EAAE;oBAAM,CAAE;oBACnDC,OAAO,EAAEA,CAAA,KAAMtM,mBAAmB,CAAC,2BAA2B;kBAAE;oBAAAsJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CACN,EAEA5J,eAAe,KAAK,QAAQ,iBAC3B/B,OAAA;kBAAKmL,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,eACtClL,OAAA;oBACEiO,GAAG,EAAE,qCAAqCC,kBAAkB,CAAC9N,MAAM,CAAC8M,kBAAkB,CAAC,gBAAiB;oBACxGjB,KAAK,EAAC,MAAM;oBACZkC,MAAM,EAAC,KAAK;oBACZC,WAAW,EAAC,GAAG;oBACfC,KAAK,EAAC,qBAAqB;oBAC3BrC,KAAK,EAAE;sBAAEsC,UAAU,EAAE,MAAM;sBAAEC,YAAY,EAAE;oBAAM,CAAE;oBACnDC,OAAO,EAAEA,CAAA,KAAMtM,mBAAmB,CAAC,yBAAyB;kBAAE;oBAAAsJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CACN,EAEA5J,eAAe,KAAK,UAAU,iBAC7B/B,OAAA;kBAAKmL,SAAS,EAAC,kEAAkE;kBAAAD,QAAA,gBAC/ElL,OAAA;oBAAKmL,SAAS,EAAC,MAAM;oBAAAD,QAAA,gBACnBlL,OAAA;sBAAKmL,SAAS,EAAC,sCAAsC;sBAACC,IAAI,EAAC,MAAM;sBAACQ,MAAM,EAAC,cAAc;sBAACP,OAAO,EAAC,WAAW;sBAAAH,QAAA,eACzGlL,OAAA;wBAAM6L,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAE,CAAE;wBAAC5D,CAAC,EAAC;sBAAiI;wBAAAqD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtM,CAAC,eACN3L,OAAA;sBAAImL,SAAS,EAAC,0CAA0C;sBAAAD,QAAA,EAAC;oBAAW;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACzE3L,OAAA;sBAAGmL,SAAS,EAAC,oBAAoB;sBAAAD,QAAA,GAAC,4GACX,eAAAlL,OAAA;wBAAAwL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,oGAE5B;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eACN3L,OAAA;oBACEsK,IAAI,EAAElK,MAAM,CAAC8M,kBAAmB;oBAChC3C,QAAQ,EAAC,uBAAuB;oBAChCY,SAAS,EAAC,sHAAsH;oBAAAD,QAAA,gBAEhIlL,OAAA;sBAAKmL,SAAS,EAAC,cAAc;sBAACC,IAAI,EAAC,MAAM;sBAACQ,MAAM,EAAC,cAAc;sBAACP,OAAO,EAAC,WAAW;sBAAAH,QAAA,eACjFlL,OAAA;wBAAM6L,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAE,CAAE;wBAAC5D,CAAC,EAAC;sBAAiI;wBAAAqD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtM,CAAC,wDAER;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CACN,EAEA1J,gBAAgB,iBACfjC,OAAA;kBAAKmL,SAAS,EAAC,2DAA2D;kBAAAD,QAAA,eACxElL,OAAA;oBAAKmL,SAAS,EAAC,MAAM;oBAAAD,QAAA,gBACnBlL,OAAA;sBAAKmL,SAAS,EAAC,8BAA8B;sBAACC,IAAI,EAAC,MAAM;sBAACQ,MAAM,EAAC,cAAc;sBAACP,OAAO,EAAC,WAAW;sBAAAH,QAAA,eACjGlL,OAAA;wBAAM6L,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAE,CAAE;wBAAC5D,CAAC,EAAC;sBAAyI;wBAAAqD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9M,CAAC,eACN3L,OAAA;sBAAAkL,QAAA,gBACElL,OAAA;wBAAGmL,SAAS,EAAC,6BAA6B;wBAAAD,QAAA,EAAC;sBAAI;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eACnD3L,OAAA;wBAAGmL,SAAS,EAAC,8BAA8B;wBAAAD,QAAA,EAAEjJ;sBAAgB;wBAAAuJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/D,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EAGL,CAAAvL,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE+M,iBAAiB,MAAI/M,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEqO,iBAAiB,KAAIrO,MAAM,CAACqO,iBAAiB,CAAC3K,MAAM,GAAG,CAAC,iBAC5F9D,OAAA;cAAKmL,SAAS,EAAC,2DAA2D;cAAAD,QAAA,gBACxElL,OAAA;gBAAImL,SAAS,EAAC,8DAA8D;gBAAAD,QAAA,gBAC1ElL,OAAA;kBAAKmL,SAAS,EAAC,cAAc;kBAACC,IAAI,EAAC,MAAM;kBAACQ,MAAM,EAAC,cAAc;kBAACP,OAAO,EAAC,WAAW;kBAAAH,QAAA,eACjFlL,OAAA;oBAAM6L,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAAC5D,CAAC,EAAC;kBAA6I;oBAAAqD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClN,CAAC,mCAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL3L,OAAA;gBAAKmL,SAAS,EAAC,WAAW;gBAAAD,QAAA,EACvB,CAAC9K,MAAM,CAACqO,iBAAiB,IAAI,EAAE,EAAE3G,GAAG,CAAC,CAAC4G,MAAM,EAAEC,KAAK,kBAClD3O,OAAA;kBAAiBmL,SAAS,EAAC,kDAAkD;kBAAAD,QAAA,gBAC3ElL,OAAA;oBAAKmL,SAAS,EAAC,0CAA0C;oBAAAD,QAAA,GAAC,UAChD,EAACyD,KAAK,GAAG,CAAC,EAAC,IAAE,EAACD,MAAM,CAACE,aAAa;kBAAA;oBAAApD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC,eACN3L,OAAA;oBAAKmL,SAAS,EAAC,uCAAuC;oBAAAD,QAAA,gBACpDlL,OAAA;sBAAAkL,QAAA,gBACElL,OAAA;wBAAKmL,SAAS,EAAC,uCAAuC;wBAAAD,QAAA,EAAC;sBAAS;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACtE3L,OAAA;wBAAKmL,SAAS,EAAC,oDAAoD;wBAAAD,QAAA,EAChEwD,MAAM,CAAC1J;sBAAa;wBAAAwG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACN3L,OAAA;sBAAAkL,QAAA,gBACElL,OAAA;wBAAKmL,SAAS,EAAC,yCAAyC;wBAAAD,QAAA,EAAC;sBAAU;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACzE3L,OAAA;wBAAKmL,SAAS,EAAC,sDAAsD;wBAAAD,QAAA,EAClEwD,MAAM,CAAC7J;sBAAc;wBAAA2G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GAjBEgD,KAAK;kBAAAnD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAkBV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,EAGAtK,SAAS,KAAK,UAAU,iBACvBrB,OAAA;YAAAkL,QAAA,gBACElL,OAAA;cAAImL,SAAS,EAAC,uCAAuC;cAAAD,QAAA,EAAC;YAA0B;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAEpF,CAAAvL,MAAM,aAANA,MAAM,wBAAAW,sBAAA,GAANX,MAAM,CAAE8L,kBAAkB,cAAAnL,sBAAA,wBAAAC,sBAAA,GAA1BD,sBAAA,CAA4BsL,qBAAqB,cAAArL,sBAAA,uBAAjDA,sBAAA,CAAmDuL,SAAS,kBAC3DvM,OAAA;cAAAkL,QAAA,gBACElL,OAAA;gBAAImL,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAAwB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtF3L,OAAA;gBAAKmL,SAAS,EAAC,uCAAuC;gBAAAD,QAAA,EACnDjH,MAAM,CAACuI,OAAO,CAACpM,MAAM,CAAC8L,kBAAkB,CAACG,qBAAqB,CAACE,SAAS,CAAC,CAACzE,GAAG,CAAC,CAAC,CAACkD,GAAG,EAAEyB,IAAI,CAAC,KAAK;kBAC9F,MAAMoC,YAAY,GAAG,CAAC;kBACtB,MAAMC,YAAY,GAAG,CAAC;kBACtB,MAAMC,eAAe,GAAG5M,eAAe,CAAC6I,GAAG,CAAC,IAAI,KAAK;kBACrD,MAAMgE,eAAe,GAAG3M,eAAe,CAAC2I,GAAG,CAAC,IAAI,KAAK;kBACrD,oBACEhL,OAAA;oBAAemL,SAAS,EAAC,kCAAkC;oBAAAD,QAAA,gBACzDlL,OAAA;sBAAKmL,SAAS,EAAC,uCAAuC;sBAAAD,QAAA,gBACpDlL,OAAA;wBAAAkL,QAAA,gBACElL,OAAA;0BAAImL,SAAS,EAAC,wCAAwC;0BAAAD,QAAA,EACnDF,GAAG,CAAChI,OAAO,CAAC,GAAG,EAAE,GAAG;wBAAC;0BAAAwI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACpB,CAAC,eACL3L,OAAA;0BAAMmL,SAAS,EAAC,mCAAmC;0BAAAD,QAAA,GAAC,UAAQ,EAACuB,IAAI,CAACwC,MAAM;wBAAA;0BAAAzD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7E,CAAC,eACN3L,OAAA;wBAAKmL,SAAS,EAAE,qBAAqBsB,IAAI,CAACzI,KAAK,IAAI,EAAE,GAAG,gBAAgB,GAAGyI,IAAI,CAACzI,KAAK,IAAI,EAAE,GAAG,iBAAiB,GAAG,cAAc,EAAG;wBAAAkH,QAAA,GAAEuB,IAAI,CAACzI,KAAK,EAAC,GAAC;sBAAA;wBAAAwH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpJ,CAAC,eAEN3L,OAAA;sBAAKmL,SAAS,EAAC,0CAA0C;sBAAAD,QAAA,eACvDlL,OAAA;wBACEmL,SAAS,EAAE,gDACTsB,IAAI,CAACzI,KAAK,IAAI,EAAE,GAAG,cAAc,GACjCyI,IAAI,CAACzI,KAAK,IAAI,EAAE,GAAG,eAAe,GAAG,YAAY,EAChD;wBACHgI,KAAK,EAAE;0BAAEC,KAAK,EAAE,GAAGQ,IAAI,CAACzI,KAAK;wBAAI;sBAAE;wBAAAwH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/B;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,EAELc,IAAI,CAACyC,OAAO,IAAIzC,IAAI,CAAC0C,KAAK,iBACzBnP,OAAA;sBAAKmL,SAAS,EAAC,uBAAuB;sBAAAD,QAAA,gBACpClL,OAAA;wBAAKmL,SAAS,EAAC,MAAM;wBAAAD,QAAA,eACnBlL,OAAA;0BAAMmL,SAAS,EAAC,aAAa;0BAAAD,QAAA,GAAC,WAAS,EAACuB,IAAI,CAACyC,OAAO,CAACpL,MAAM,EAAC,GAAC,EAAC2I,IAAI,CAAC0C,KAAK,CAACrL,MAAM,EAAC,IAAE;wBAAA;0BAAA0H,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtF,CAAC,EACLc,IAAI,CAACyC,OAAO,CAACpL,MAAM,GAAG,CAAC,gBACtB9D,OAAA;wBAAKmL,SAAS,EAAC,2BAA2B;wBAAAD,QAAA,GACvC,CAAC6D,eAAe,GAAGtC,IAAI,CAACyC,OAAO,GAAGzC,IAAI,CAACyC,OAAO,CAACrB,KAAK,CAAC,CAAC,EAAEgB,YAAY,CAAC,EAAE/G,GAAG,CAAC,CAACsH,IAAI,EAAE5H,GAAG,kBACpFxH,OAAA;0BAAgBmL,SAAS,EAAC,uDAAuD;0BAAAD,QAAA,EAAEkE;wBAAI,GAA5E5H,GAAG;0BAAAgE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAgF,CAC/F,CAAC,EACDc,IAAI,CAACyC,OAAO,CAACpL,MAAM,GAAG+K,YAAY,iBACjC7O,OAAA;0BACEmL,SAAS,EAAC,wCAAwC;0BAClDuB,OAAO,EAAEA,CAAA,KAAM3B,YAAY,CAACC,GAAG,EAAE,SAAS,CAAE;0BAAAE,QAAA,EAE3C6D,eAAe,GAAG,IAAI,GAAG,IAAItC,IAAI,CAACyC,OAAO,CAACpL,MAAM,GAAG+K,YAAY;wBAAO;0BAAArD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnE,CACP;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,gBAEN3L,OAAA;wBAAKmL,SAAS,EAAC,oBAAoB;wBAAAD,QAAA,EAAC;sBAAgB;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAC1D,EACAc,IAAI,CAAC0C,KAAK,CAACrL,MAAM,GAAG2I,IAAI,CAACyC,OAAO,CAACpL,MAAM,iBACtC9D,OAAA;wBAAAkL,QAAA,gBACElL,OAAA;0BAAMmL,SAAS,EAAC,0BAA0B;0BAAAD,QAAA,EAAC;wBAAQ;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eAC1D3L,OAAA;0BAAKmL,SAAS,EAAC,2BAA2B;0BAAAD,QAAA,GACvC,CAAC8D,eAAe,GAAGvC,IAAI,CAAC0C,KAAK,CAAChL,MAAM,CAACiL,IAAI,IAAI,CAAC3C,IAAI,CAACyC,OAAO,CAACjI,QAAQ,CAACmI,IAAI,CAAC,CAAC,GAAG3C,IAAI,CAAC0C,KAAK,CAAChL,MAAM,CAACiL,IAAI,IAAI,CAAC3C,IAAI,CAACyC,OAAO,CAACjI,QAAQ,CAACmI,IAAI,CAAC,CAAC,CAACvB,KAAK,CAAC,CAAC,EAAEiB,YAAY,CAAC,EAAEhH,GAAG,CAAC,CAACsH,IAAI,EAAE5H,GAAG,kBAC1KxH,OAAA;4BAAgBmL,SAAS,EAAC,mDAAmD;4BAAAD,QAAA,EAAEkE;0BAAI,GAAxE5H,GAAG;4BAAAgE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAA4E,CAC3F,CAAC,EACDc,IAAI,CAAC0C,KAAK,CAACrL,MAAM,GAAG2I,IAAI,CAACyC,OAAO,CAACpL,MAAM,GAAGgL,YAAY,iBACrD9O,OAAA;4BACEmL,SAAS,EAAC,wCAAwC;4BAClDuB,OAAO,EAAEA,CAAA,KAAM3B,YAAY,CAACC,GAAG,EAAE,SAAS,CAAE;4BAAAE,QAAA,EAE3C8D,eAAe,GAAG,IAAI,GAAG,IAAIvC,IAAI,CAAC0C,KAAK,CAACrL,MAAM,GAAG2I,IAAI,CAACyC,OAAO,CAACpL,MAAM,GAAGgL,YAAY;0BAAO;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACvF,CACP;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CACN;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CACN;kBAAA,GA9DOX,GAAG;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OA+DR,CAAC;gBAEV,CAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,EAGL,CAAC,MAAM;gBACN,MAAM0D,UAAU,GAAGpL,MAAM,CAACuI,OAAO,CAACpM,MAAM,CAAC8L,kBAAkB,CAACG,qBAAqB,CAACE,SAAS,CAAC,CACzFzE,GAAG,CAAC,CAAC,CAACkD,GAAG,EAAEyB,IAAI,CAAC,MAAM;kBACrBzB,GAAG;kBACHiE,MAAM,EAAExC,IAAI,CAACwC,MAAM;kBACnBK,OAAO,EAAE7C,IAAI,CAAC0C,KAAK,GAAG1C,IAAI,CAAC0C,KAAK,CAAChL,MAAM,CAACiL,IAAI,IAAI,CAAC3C,IAAI,CAACyC,OAAO,CAACjI,QAAQ,CAACmI,IAAI,CAAC,CAAC,GAAG;gBAClF,CAAC,CAAC,CAAC,CACFjL,MAAM,CAACiL,IAAI,IAAIA,IAAI,CAACE,OAAO,CAACxL,MAAM,GAAG,CAAC,CAAC,CACvCyL,IAAI,CAAC,CAACnF,CAAC,EAAEoF,CAAC,KAAKA,CAAC,CAACP,MAAM,GAAG7E,CAAC,CAAC6E,MAAM,CAAC;gBACtC,IAAII,UAAU,CAACvL,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;gBACxC,oBACE9D,OAAA;kBAAKmL,SAAS,EAAC,0DAA0D;kBAAAD,QAAA,gBACvElL,OAAA;oBAAImL,SAAS,EAAC,uDAAuD;oBAAAD,QAAA,gBACnElL,OAAA;sBAAKmL,SAAS,EAAC,2BAA2B;sBAACC,IAAI,EAAC,MAAM;sBAACQ,MAAM,EAAC,cAAc;sBAACP,OAAO,EAAC,WAAW;sBAAAH,QAAA,eAAClL,OAAA;wBAAM6L,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAE,CAAE;wBAAC5D,CAAC,EAAC;sBAAiF;wBAAAqD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,gEAElQ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACL3L,OAAA;oBAAKmL,SAAS,EAAC,sBAAsB;oBAAAD,QAAA,EAClCmE,UAAU,CAACxB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC/F,GAAG,CAACsH,IAAI,iBAC9BpP,OAAA;sBAAoBmL,SAAS,EAAC,WAAW;sBAAAD,QAAA,gBACvClL,OAAA;wBAAMmL,SAAS,EAAC,kCAAkC;wBAAAD,QAAA,GAAEkE,IAAI,CAACpE,GAAG,CAAChI,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAAC,GAAC;sBAAA;wBAAAwI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,EACtFyD,IAAI,CAACE,OAAO,CAACzB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC/F,GAAG,CAAC,CAAC2H,IAAI,EAAEjI,GAAG,kBACtCxH,OAAA;wBAAgBmL,SAAS,EAAC,wDAAwD;wBAAAD,QAAA,EAAEuE;sBAAI,GAA7EjI,GAAG;wBAAAgE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAiF,CAChG,CAAC,EACDyD,IAAI,CAACE,OAAO,CAACxL,MAAM,GAAG,CAAC,iBAAI9D,OAAA;wBAAMmL,SAAS,EAAC,eAAe;wBAAAD,QAAA,GAAC,GAAC,EAACkE,IAAI,CAACE,OAAO,CAACxL,MAAM,GAAG,CAAC,EAAC,OAAK;sBAAA;wBAAA0H,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA,GAL1FyD,IAAI,CAACpE,GAAG;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAMb,CACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAEV,CAAC,EAAE,CAAC,EAGH1K,EAAA,CAAC,MAAM;gBAAAA,EAAA;gBACN,MAAMyO,WAAW,GAAGzL,MAAM,CAACuI,OAAO,CAACpM,MAAM,CAAC8L,kBAAkB,CAACG,qBAAqB,CAACE,SAAS,CAAC,CAC1FpI,MAAM,CAAC,CAAC,CAAC6G,GAAG,EAAEyB,IAAI,CAAC,KAAKA,IAAI,CAACzI,KAAK,GAAG,EAAE,CAAC,CACxC8D,GAAG,CAAC,CAAC,CAACkD,GAAG,EAAEyB,IAAI,CAAC,KAAK;kBACpB,IAAIkD,GAAG,GAAG,EAAE;kBACZ,IAAI3E,GAAG,CAACtH,WAAW,CAAC,CAAC,CAACuD,QAAQ,CAAC,QAAQ,CAAC,EAAE0I,GAAG,GAAG,iBAAiB,CAAC,KAC7D,IAAI3E,GAAG,CAACtH,WAAW,CAAC,CAAC,CAACuD,QAAQ,CAAC,OAAO,CAAC,EAAE0I,GAAG,GAAG,eAAe,CAAC,KAC/D,IAAI3E,GAAG,CAACtH,WAAW,CAAC,CAAC,CAACuD,QAAQ,CAAC,UAAU,CAAC,EAAE0I,GAAG,GAAG,iBAAiB,CAAC,KACpE,IAAI3E,GAAG,CAACtH,WAAW,CAAC,CAAC,CAACuD,QAAQ,CAAC,YAAY,CAAC,EAAE0I,GAAG,GAAG,aAAa,CAAC,KAClE,IAAI3E,GAAG,CAACtH,WAAW,CAAC,CAAC,CAACuD,QAAQ,CAAC,WAAW,CAAC,EAAE0I,GAAG,GAAG,aAAa,CAAC,KACjEA,GAAG,GAAG,iBAAiB;kBAC5B,OAAO,IAAI3E,GAAG,CAAChI,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI2M,GAAG,EAAE;gBAC3C,CAAC,CAAC;gBACJ,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGvQ,KAAK,CAACC,QAAQ,CAAC,KAAK,CAAC;gBACnD,IAAImQ,WAAW,CAAC5L,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;gBACzC,oBACE9D,OAAA;kBAAKmL,SAAS,EAAC,4DAA4D;kBAAAD,QAAA,gBACzElL,OAAA;oBAAKmL,SAAS,EAAC,wBAAwB;oBAAAD,QAAA,gBACrClL,OAAA;sBAAImL,SAAS,EAAC,sCAAsC;sBAAAD,QAAA,EAAC;oBAAI;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC9D3L,OAAA;sBACEmL,SAAS,EAAC,4EAA4E;sBACtFuB,OAAO,EAAEA,CAAA,KAAMmD,UAAU,CAACC,CAAC,IAAI,CAACA,CAAC,CAAE;sBAAA5E,QAAA,EACnC0E,OAAO,GAAG,QAAQ,GAAG;oBAAU;sBAAApE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC,eACN3L,OAAA;oBAAImL,SAAS,EAAC,8BAA8B;oBAAAD,QAAA,GACzC,CAAC0E,OAAO,GAAGF,WAAW,GAAGA,WAAW,CAAC7B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE/F,GAAG,CAAC,CAACiI,CAAC,EAAEC,CAAC,kBAC1DhQ,OAAA;sBAAAkL,QAAA,EAAa6E;oBAAC,GAALC,CAAC;sBAAAxE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS,CACpB,CAAC,EACD,CAACiE,OAAO,IAAIF,WAAW,CAAC5L,MAAM,GAAG,CAAC,iBACjC9D,OAAA;sBAAImL,SAAS,EAAC,eAAe;sBAAAD,QAAA,EAAC;oBAAgB;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CACnD;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAEV,CAAC,kCAAE,CAAC,EAGH,CAAC,MAAM;gBACN,MAAMsE,SAAS,GAAGhM,MAAM,CAACuI,OAAO,CAACpM,MAAM,CAAC8L,kBAAkB,CAACG,qBAAqB,CAACE,SAAS,CAAC,CAACzE,GAAG,CAAC,CAAC,CAACkD,GAAG,EAAEyB,IAAI,CAAC,MAAM;kBAChHyD,IAAI,EAAElF,GAAG,CAAChI,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;kBAC3BmN,KAAK,EAAE1D,IAAI,CAACzI;gBACd,CAAC,CAAC,CAAC;gBACH,IAAIiM,SAAS,CAACnM,MAAM,GAAG,CAAC,EAAE,OAAO,IAAI,CAAC,CAAC;gBACvC,MAAMsM,SAAS,GAAGH,SAAS,CAACnI,GAAG,CAACsH,IAAI,KAAK;kBAAEc,IAAI,EAAEd,IAAI,CAACc,IAAI;kBAAE/I,GAAG,EAAE;gBAAI,CAAC,CAAC,CAAC;gBACxE,MAAMkJ,MAAM,GAAG;kBACbC,OAAO,EAAE,CAAC,CAAC;kBACXC,KAAK,EAAE;oBACLH,SAAS;oBACTI,MAAM,EAAE;kBACV,CAAC;kBACDC,MAAM,EAAE,CAAC;oBACPjK,IAAI,EAAE,OAAO;oBACbiG,IAAI,EAAE,CAAC;sBAAE0D,KAAK,EAAEF,SAAS,CAACnI,GAAG,CAACK,CAAC,IAAIA,CAAC,CAACgI,KAAK,CAAC;sBAAED,IAAI,EAAE;oBAAM,CAAC,CAAC;oBAC3DQ,SAAS,EAAE;sBAAEC,OAAO,EAAE;oBAAI,CAAC;oBAC3BC,SAAS,EAAE;sBAAE3E,KAAK,EAAE;oBAAE;kBACxB,CAAC;gBACH,CAAC;gBACD,oBACEjM,OAAA;kBAAKmL,SAAS,EAAC,qFAAqF;kBAAAD,QAAA,gBAClGlL,OAAA;oBAAImL,SAAS,EAAC,sCAAsC;oBAAAD,QAAA,EAAC;kBAAU;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpE3L,OAAA;oBAAKgM,KAAK,EAAE;sBAAEC,KAAK,EAAE,GAAG;sBAAEkC,MAAM,EAAE;oBAAI,CAAE;oBAAAjD,QAAA,eACtClL,OAAA,CAACF,YAAY;sBAACuQ,MAAM,EAAEA,MAAO;sBAACrE,KAAK,EAAE;wBAAEC,KAAK,EAAE,MAAM;wBAAEkC,MAAM,EAAE;sBAAO;oBAAE;sBAAA3C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAEV,CAAC,EAAE,CAAC,EAGH,CAAC,MAAM;gBACN,MAAMzI,aAAa,GAAGyB,gBAAgB,CAAC,CAAC;gBACxC,MAAMkM,MAAM,GAAG,EAAE;gBACjB,IAAI,SAAS,CAACrN,IAAI,CAACN,aAAa,CAAC,EAAE2N,MAAM,CAACtK,IAAI,CAAC,eAAe,CAAC;gBAC/D,IAAI,CAAC,eAAe,CAAC/C,IAAI,CAACN,aAAa,CAAC,EAAE2N,MAAM,CAACtK,IAAI,CAAC,WAAW,CAAC;gBAClE,IAAI,CAAC,qBAAqB,CAAC/C,IAAI,CAACN,aAAa,CAAC,EAAE2N,MAAM,CAACtK,IAAI,CAAC,WAAW,CAAC;gBACxE,IAAIrD,aAAa,CAACY,MAAM,GAAG,GAAG,EAAE+M,MAAM,CAACtK,IAAI,CAAC,cAAc,CAAC;gBAC3D,IAAIrD,aAAa,CAACY,MAAM,GAAG,IAAI,EAAE+M,MAAM,CAACtK,IAAI,CAAC,cAAc,CAAC;gBAC5D,IAAIsK,MAAM,CAAC/M,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;gBACpC,oBACE9D,OAAA;kBAAKmL,SAAS,EAAC,gEAAgE;kBAAAD,QAAA,gBAC7ElL,OAAA;oBAAImL,SAAS,EAAC,0DAA0D;oBAAAD,QAAA,gBACtElL,OAAA;sBAAKmL,SAAS,EAAC,8BAA8B;sBAACC,IAAI,EAAC,MAAM;sBAACQ,MAAM,EAAC,cAAc;sBAACP,OAAO,EAAC,WAAW;sBAAAH,QAAA,eAAClL,OAAA;wBAAM6L,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAE,CAAE;wBAAC5D,CAAC,EAAC;sBAAyI;wBAAAqD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,2CAE7T;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACL3L,OAAA;oBAAImL,SAAS,EAAC,gCAAgC;oBAAAD,QAAA,EAC3C2F,MAAM,CAAC/I,GAAG,CAAC,CAACgJ,KAAK,EAAEtJ,GAAG,kBAAKxH,OAAA;sBAAAkL,QAAA,EAAe4F;oBAAK,GAAXtJ,GAAG;sBAAAgE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAEV,CAAC,EAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAED,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpL,GAAA,CArnCIJ,aAAa;AAAA4Q,EAAA,GAAb5Q,aAAa;AAunCnB,eAAeA,aAAa;AAAC,IAAA4Q,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}