{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport { Path } from '../graphic.js';\n/**\r\n * Sausage: similar to sector, but have half circle on both sides\r\n */\nvar SausageShape = /** @class */function () {\n  function SausageShape() {\n    this.cx = 0;\n    this.cy = 0;\n    this.r0 = 0;\n    this.r = 0;\n    this.startAngle = 0;\n    this.endAngle = Math.PI * 2;\n    this.clockwise = true;\n  }\n  return SausageShape;\n}();\nvar SausagePath = /** @class */function (_super) {\n  __extends(SausagePath, _super);\n  function SausagePath(opts) {\n    var _this = _super.call(this, opts) || this;\n    _this.type = 'sausage';\n    return _this;\n  }\n  SausagePath.prototype.getDefaultShape = function () {\n    return new SausageShape();\n  };\n  SausagePath.prototype.buildPath = function (ctx, shape) {\n    var cx = shape.cx;\n    var cy = shape.cy;\n    var r0 = Math.max(shape.r0 || 0, 0);\n    var r = Math.max(shape.r, 0);\n    var dr = (r - r0) * 0.5;\n    var rCenter = r0 + dr;\n    var startAngle = shape.startAngle;\n    var endAngle = shape.endAngle;\n    var clockwise = shape.clockwise;\n    var PI2 = Math.PI * 2;\n    var lessThanCircle = clockwise ? endAngle - startAngle < PI2 : startAngle - endAngle < PI2;\n    if (!lessThanCircle) {\n      // Normalize angles\n      startAngle = endAngle - (clockwise ? PI2 : -PI2);\n    }\n    var unitStartX = Math.cos(startAngle);\n    var unitStartY = Math.sin(startAngle);\n    var unitEndX = Math.cos(endAngle);\n    var unitEndY = Math.sin(endAngle);\n    if (lessThanCircle) {\n      ctx.moveTo(unitStartX * r0 + cx, unitStartY * r0 + cy);\n      ctx.arc(unitStartX * rCenter + cx, unitStartY * rCenter + cy, dr, -Math.PI + startAngle, startAngle, !clockwise);\n    } else {\n      ctx.moveTo(unitStartX * r + cx, unitStartY * r + cy);\n    }\n    ctx.arc(cx, cy, r, startAngle, endAngle, !clockwise);\n    ctx.arc(unitEndX * rCenter + cx, unitEndY * rCenter + cy, dr, endAngle - Math.PI * 2, endAngle - Math.PI, !clockwise);\n    if (r0 !== 0) {\n      ctx.arc(cx, cy, r0, endAngle, startAngle, clockwise);\n    }\n    // ctx.closePath();\n  };\n  return SausagePath;\n}(Path);\nexport default SausagePath;", "map": {"version": 3, "names": ["__extends", "Path", "SausageShape", "cx", "cy", "r0", "r", "startAngle", "endAngle", "Math", "PI", "clockwise", "SausagePath", "_super", "opts", "_this", "call", "type", "prototype", "getDefaultShape", "buildPath", "ctx", "shape", "max", "dr", "rCenter", "PI2", "lessThanCircle", "unitStartX", "cos", "unitStartY", "sin", "unitEndX", "unitEndY", "moveTo", "arc"], "sources": ["E:/AI/SmartCV/node_modules/echarts/lib/util/shape/sausage.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport { Path } from '../graphic.js';\n/**\r\n * Sausage: similar to sector, but have half circle on both sides\r\n */\nvar SausageShape = /** @class */function () {\n  function SausageShape() {\n    this.cx = 0;\n    this.cy = 0;\n    this.r0 = 0;\n    this.r = 0;\n    this.startAngle = 0;\n    this.endAngle = Math.PI * 2;\n    this.clockwise = true;\n  }\n  return SausageShape;\n}();\nvar SausagePath = /** @class */function (_super) {\n  __extends(SausagePath, _super);\n  function SausagePath(opts) {\n    var _this = _super.call(this, opts) || this;\n    _this.type = 'sausage';\n    return _this;\n  }\n  SausagePath.prototype.getDefaultShape = function () {\n    return new SausageShape();\n  };\n  SausagePath.prototype.buildPath = function (ctx, shape) {\n    var cx = shape.cx;\n    var cy = shape.cy;\n    var r0 = Math.max(shape.r0 || 0, 0);\n    var r = Math.max(shape.r, 0);\n    var dr = (r - r0) * 0.5;\n    var rCenter = r0 + dr;\n    var startAngle = shape.startAngle;\n    var endAngle = shape.endAngle;\n    var clockwise = shape.clockwise;\n    var PI2 = Math.PI * 2;\n    var lessThanCircle = clockwise ? endAngle - startAngle < PI2 : startAngle - endAngle < PI2;\n    if (!lessThanCircle) {\n      // Normalize angles\n      startAngle = endAngle - (clockwise ? PI2 : -PI2);\n    }\n    var unitStartX = Math.cos(startAngle);\n    var unitStartY = Math.sin(startAngle);\n    var unitEndX = Math.cos(endAngle);\n    var unitEndY = Math.sin(endAngle);\n    if (lessThanCircle) {\n      ctx.moveTo(unitStartX * r0 + cx, unitStartY * r0 + cy);\n      ctx.arc(unitStartX * rCenter + cx, unitStartY * rCenter + cy, dr, -Math.PI + startAngle, startAngle, !clockwise);\n    } else {\n      ctx.moveTo(unitStartX * r + cx, unitStartY * r + cy);\n    }\n    ctx.arc(cx, cy, r, startAngle, endAngle, !clockwise);\n    ctx.arc(unitEndX * rCenter + cx, unitEndY * rCenter + cy, dr, endAngle - Math.PI * 2, endAngle - Math.PI, !clockwise);\n    if (r0 !== 0) {\n      ctx.arc(cx, cy, r0, endAngle, startAngle, clockwise);\n    }\n    // ctx.closePath();\n  };\n  return SausagePath;\n}(Path);\nexport default SausagePath;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,IAAI,QAAQ,eAAe;AACpC;AACA;AACA;AACA,IAAIC,YAAY,GAAG,aAAa,YAAY;EAC1C,SAASA,YAAYA,CAAA,EAAG;IACtB,IAAI,CAACC,EAAE,GAAG,CAAC;IACX,IAAI,CAACC,EAAE,GAAG,CAAC;IACX,IAAI,CAACC,EAAE,GAAG,CAAC;IACX,IAAI,CAACC,CAAC,GAAG,CAAC;IACV,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,QAAQ,GAAGC,IAAI,CAACC,EAAE,GAAG,CAAC;IAC3B,IAAI,CAACC,SAAS,GAAG,IAAI;EACvB;EACA,OAAOT,YAAY;AACrB,CAAC,CAAC,CAAC;AACH,IAAIU,WAAW,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC/Cb,SAAS,CAACY,WAAW,EAAEC,MAAM,CAAC;EAC9B,SAASD,WAAWA,CAACE,IAAI,EAAE;IACzB,IAAIC,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,EAAEF,IAAI,CAAC,IAAI,IAAI;IAC3CC,KAAK,CAACE,IAAI,GAAG,SAAS;IACtB,OAAOF,KAAK;EACd;EACAH,WAAW,CAACM,SAAS,CAACC,eAAe,GAAG,YAAY;IAClD,OAAO,IAAIjB,YAAY,CAAC,CAAC;EAC3B,CAAC;EACDU,WAAW,CAACM,SAAS,CAACE,SAAS,GAAG,UAAUC,GAAG,EAAEC,KAAK,EAAE;IACtD,IAAInB,EAAE,GAAGmB,KAAK,CAACnB,EAAE;IACjB,IAAIC,EAAE,GAAGkB,KAAK,CAAClB,EAAE;IACjB,IAAIC,EAAE,GAAGI,IAAI,CAACc,GAAG,CAACD,KAAK,CAACjB,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;IACnC,IAAIC,CAAC,GAAGG,IAAI,CAACc,GAAG,CAACD,KAAK,CAAChB,CAAC,EAAE,CAAC,CAAC;IAC5B,IAAIkB,EAAE,GAAG,CAAClB,CAAC,GAAGD,EAAE,IAAI,GAAG;IACvB,IAAIoB,OAAO,GAAGpB,EAAE,GAAGmB,EAAE;IACrB,IAAIjB,UAAU,GAAGe,KAAK,CAACf,UAAU;IACjC,IAAIC,QAAQ,GAAGc,KAAK,CAACd,QAAQ;IAC7B,IAAIG,SAAS,GAAGW,KAAK,CAACX,SAAS;IAC/B,IAAIe,GAAG,GAAGjB,IAAI,CAACC,EAAE,GAAG,CAAC;IACrB,IAAIiB,cAAc,GAAGhB,SAAS,GAAGH,QAAQ,GAAGD,UAAU,GAAGmB,GAAG,GAAGnB,UAAU,GAAGC,QAAQ,GAAGkB,GAAG;IAC1F,IAAI,CAACC,cAAc,EAAE;MACnB;MACApB,UAAU,GAAGC,QAAQ,IAAIG,SAAS,GAAGe,GAAG,GAAG,CAACA,GAAG,CAAC;IAClD;IACA,IAAIE,UAAU,GAAGnB,IAAI,CAACoB,GAAG,CAACtB,UAAU,CAAC;IACrC,IAAIuB,UAAU,GAAGrB,IAAI,CAACsB,GAAG,CAACxB,UAAU,CAAC;IACrC,IAAIyB,QAAQ,GAAGvB,IAAI,CAACoB,GAAG,CAACrB,QAAQ,CAAC;IACjC,IAAIyB,QAAQ,GAAGxB,IAAI,CAACsB,GAAG,CAACvB,QAAQ,CAAC;IACjC,IAAImB,cAAc,EAAE;MAClBN,GAAG,CAACa,MAAM,CAACN,UAAU,GAAGvB,EAAE,GAAGF,EAAE,EAAE2B,UAAU,GAAGzB,EAAE,GAAGD,EAAE,CAAC;MACtDiB,GAAG,CAACc,GAAG,CAACP,UAAU,GAAGH,OAAO,GAAGtB,EAAE,EAAE2B,UAAU,GAAGL,OAAO,GAAGrB,EAAE,EAAEoB,EAAE,EAAE,CAACf,IAAI,CAACC,EAAE,GAAGH,UAAU,EAAEA,UAAU,EAAE,CAACI,SAAS,CAAC;IAClH,CAAC,MAAM;MACLU,GAAG,CAACa,MAAM,CAACN,UAAU,GAAGtB,CAAC,GAAGH,EAAE,EAAE2B,UAAU,GAAGxB,CAAC,GAAGF,EAAE,CAAC;IACtD;IACAiB,GAAG,CAACc,GAAG,CAAChC,EAAE,EAAEC,EAAE,EAAEE,CAAC,EAAEC,UAAU,EAAEC,QAAQ,EAAE,CAACG,SAAS,CAAC;IACpDU,GAAG,CAACc,GAAG,CAACH,QAAQ,GAAGP,OAAO,GAAGtB,EAAE,EAAE8B,QAAQ,GAAGR,OAAO,GAAGrB,EAAE,EAAEoB,EAAE,EAAEhB,QAAQ,GAAGC,IAAI,CAACC,EAAE,GAAG,CAAC,EAAEF,QAAQ,GAAGC,IAAI,CAACC,EAAE,EAAE,CAACC,SAAS,CAAC;IACrH,IAAIN,EAAE,KAAK,CAAC,EAAE;MACZgB,GAAG,CAACc,GAAG,CAAChC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEG,QAAQ,EAAED,UAAU,EAAEI,SAAS,CAAC;IACtD;IACA;EACF,CAAC;EACD,OAAOC,WAAW;AACpB,CAAC,CAACX,IAAI,CAAC;AACP,eAAeW,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}