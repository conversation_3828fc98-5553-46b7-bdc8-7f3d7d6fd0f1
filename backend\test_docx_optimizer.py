#!/usr/bin/env python3
"""
测试DOCX格式保持优化功能
Test DOCX format preservation optimization functionality
"""

import os
import sys
import tempfile
from openai import OpenAI
from docx_optimizer import DocxOptimizer

def test_docx_optimizer():
    """测试DOCX优化器"""
    
    # 检查环境变量
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("错误：请设置OPENAI_API_KEY环境变量")
        return False
    
    # 初始化OpenAI客户端
    client = OpenAI(api_key=api_key)
    
    # 创建优化器
    optimizer = DocxOptimizer(client)
    
    # 测试数据
    job_description = """
    Software Engineer Position
    
    Requirements:
    - 3+ years experience in Python development
    - Experience with web frameworks like Django or Flask
    - Knowledge of databases (MySQL, PostgreSQL)
    - Experience with cloud platforms (AWS, Azure)
    - Strong problem-solving skills
    - Team collaboration abilities
    
    Responsibilities:
    - Develop and maintain web applications
    - Design database schemas
    - Implement API endpoints
    - Write unit tests
    - Code reviews and documentation
    """
    
    print("测试DOCX优化器...")
    print("职位描述:", job_description[:100] + "...")
    
    # 这里需要一个实际的DOCX文件来测试
    # 在实际测试中，您需要提供一个DOCX文件路径
    test_docx_path = "test_resume.docx"
    
    if not os.path.exists(test_docx_path):
        print(f"警告：测试文件 {test_docx_path} 不存在")
        print("请创建一个测试简历文件来运行完整测试")
        return True
    
    try:
        # 运行优化
        result = optimizer.optimize_docx_resume(test_docx_path, job_description)
        
        if result['success']:
            print("✅ DOCX优化成功!")
            print(f"输出文件: {result['output_file_path']}")
            print(f"原始文本长度: {len(result['original_text'])}")
            print(f"优化文本长度: {len(result['optimized_text'])}")
            print(f"段落修改数量: {len(result['paragraph_changes'])}")
            
            # 显示前几个段落修改
            for i, change in enumerate(result['paragraph_changes'][:3]):
                print(f"\n修改 {i+1}:")
                print(f"原文: {change.get('original_text', '')[:100]}...")
                print(f"优化后: {change.get('optimized_text', '')[:100]}...")
                print(f"原因: {change.get('change_reason', '')}")
        else:
            print("❌ DOCX优化失败:")
            print(result.get('error', '未知错误'))
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    # 加载环境变量
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        print("警告：python-dotenv未安装，请手动设置环境变量")
    
    success = test_docx_optimizer()
    sys.exit(0 if success else 1) 