{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as graphic from '../util/graphic.js';\nvar PI = Math.PI;\n/**\r\n * @param {module:echarts/ExtensionAPI} api\r\n * @param {Object} [opts]\r\n * @param {string} [opts.text]\r\n * @param {string} [opts.color]\r\n * @param {string} [opts.textColor]\r\n * @return {module:zrender/Element}\r\n */\nexport default function defaultLoading(api, opts) {\n  opts = opts || {};\n  zrUtil.defaults(opts, {\n    text: 'loading',\n    textColor: '#000',\n    fontSize: 12,\n    fontWeight: 'normal',\n    fontStyle: 'normal',\n    fontFamily: 'sans-serif',\n    maskColor: 'rgba(255, 255, 255, 0.8)',\n    showSpinner: true,\n    color: '#5470c6',\n    spinnerRadius: 10,\n    lineWidth: 5,\n    zlevel: 0\n  });\n  var group = new graphic.Group();\n  var mask = new graphic.Rect({\n    style: {\n      fill: opts.maskColor\n    },\n    zlevel: opts.zlevel,\n    z: 10000\n  });\n  group.add(mask);\n  var textContent = new graphic.Text({\n    style: {\n      text: opts.text,\n      fill: opts.textColor,\n      fontSize: opts.fontSize,\n      fontWeight: opts.fontWeight,\n      fontStyle: opts.fontStyle,\n      fontFamily: opts.fontFamily\n    },\n    zlevel: opts.zlevel,\n    z: 10001\n  });\n  var labelRect = new graphic.Rect({\n    style: {\n      fill: 'none'\n    },\n    textContent: textContent,\n    textConfig: {\n      position: 'right',\n      distance: 10\n    },\n    zlevel: opts.zlevel,\n    z: 10001\n  });\n  group.add(labelRect);\n  var arc;\n  if (opts.showSpinner) {\n    arc = new graphic.Arc({\n      shape: {\n        startAngle: -PI / 2,\n        endAngle: -PI / 2 + 0.1,\n        r: opts.spinnerRadius\n      },\n      style: {\n        stroke: opts.color,\n        lineCap: 'round',\n        lineWidth: opts.lineWidth\n      },\n      zlevel: opts.zlevel,\n      z: 10001\n    });\n    arc.animateShape(true).when(1000, {\n      endAngle: PI * 3 / 2\n    }).start('circularInOut');\n    arc.animateShape(true).when(1000, {\n      startAngle: PI * 3 / 2\n    }).delay(300).start('circularInOut');\n    group.add(arc);\n  }\n  // Inject resize\n  group.resize = function () {\n    var textWidth = textContent.getBoundingRect().width;\n    var r = opts.showSpinner ? opts.spinnerRadius : 0;\n    // cx = (containerWidth - arcDiameter - textDistance - textWidth) / 2\n    // textDistance needs to be calculated when both animation and text exist\n    var cx = (api.getWidth() - r * 2 - (opts.showSpinner && textWidth ? 10 : 0) - textWidth) / 2 - (opts.showSpinner && textWidth ? 0 : 5 + textWidth / 2)\n    // only show the text\n    + (opts.showSpinner ? 0 : textWidth / 2)\n    // only show the spinner\n    + (textWidth ? 0 : r);\n    var cy = api.getHeight() / 2;\n    opts.showSpinner && arc.setShape({\n      cx: cx,\n      cy: cy\n    });\n    labelRect.setShape({\n      x: cx - r,\n      y: cy - r,\n      width: r * 2,\n      height: r * 2\n    });\n    mask.setShape({\n      x: 0,\n      y: 0,\n      width: api.getWidth(),\n      height: api.getHeight()\n    });\n  };\n  group.resize();\n  return group;\n}", "map": {"version": 3, "names": ["zrUtil", "graphic", "PI", "Math", "defaultLoading", "api", "opts", "defaults", "text", "textColor", "fontSize", "fontWeight", "fontStyle", "fontFamily", "maskColor", "showSpinner", "color", "spinnerRadius", "lineWidth", "zlevel", "group", "Group", "mask", "Rect", "style", "fill", "z", "add", "textContent", "Text", "labelRect", "textConfig", "position", "distance", "arc", "Arc", "shape", "startAngle", "endAngle", "r", "stroke", "lineCap", "animateShape", "when", "start", "delay", "resize", "textWidth", "getBoundingRect", "width", "cx", "getWidth", "cy", "getHeight", "setShape", "x", "y", "height"], "sources": ["E:/AI/SmartCV/node_modules/echarts/lib/loading/default.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as graphic from '../util/graphic.js';\nvar PI = Math.PI;\n/**\r\n * @param {module:echarts/ExtensionAPI} api\r\n * @param {Object} [opts]\r\n * @param {string} [opts.text]\r\n * @param {string} [opts.color]\r\n * @param {string} [opts.textColor]\r\n * @return {module:zrender/Element}\r\n */\nexport default function defaultLoading(api, opts) {\n  opts = opts || {};\n  zrUtil.defaults(opts, {\n    text: 'loading',\n    textColor: '#000',\n    fontSize: 12,\n    fontWeight: 'normal',\n    fontStyle: 'normal',\n    fontFamily: 'sans-serif',\n    maskColor: 'rgba(255, 255, 255, 0.8)',\n    showSpinner: true,\n    color: '#5470c6',\n    spinnerRadius: 10,\n    lineWidth: 5,\n    zlevel: 0\n  });\n  var group = new graphic.Group();\n  var mask = new graphic.Rect({\n    style: {\n      fill: opts.maskColor\n    },\n    zlevel: opts.zlevel,\n    z: 10000\n  });\n  group.add(mask);\n  var textContent = new graphic.Text({\n    style: {\n      text: opts.text,\n      fill: opts.textColor,\n      fontSize: opts.fontSize,\n      fontWeight: opts.fontWeight,\n      fontStyle: opts.fontStyle,\n      fontFamily: opts.fontFamily\n    },\n    zlevel: opts.zlevel,\n    z: 10001\n  });\n  var labelRect = new graphic.Rect({\n    style: {\n      fill: 'none'\n    },\n    textContent: textContent,\n    textConfig: {\n      position: 'right',\n      distance: 10\n    },\n    zlevel: opts.zlevel,\n    z: 10001\n  });\n  group.add(labelRect);\n  var arc;\n  if (opts.showSpinner) {\n    arc = new graphic.Arc({\n      shape: {\n        startAngle: -PI / 2,\n        endAngle: -PI / 2 + 0.1,\n        r: opts.spinnerRadius\n      },\n      style: {\n        stroke: opts.color,\n        lineCap: 'round',\n        lineWidth: opts.lineWidth\n      },\n      zlevel: opts.zlevel,\n      z: 10001\n    });\n    arc.animateShape(true).when(1000, {\n      endAngle: PI * 3 / 2\n    }).start('circularInOut');\n    arc.animateShape(true).when(1000, {\n      startAngle: PI * 3 / 2\n    }).delay(300).start('circularInOut');\n    group.add(arc);\n  }\n  // Inject resize\n  group.resize = function () {\n    var textWidth = textContent.getBoundingRect().width;\n    var r = opts.showSpinner ? opts.spinnerRadius : 0;\n    // cx = (containerWidth - arcDiameter - textDistance - textWidth) / 2\n    // textDistance needs to be calculated when both animation and text exist\n    var cx = (api.getWidth() - r * 2 - (opts.showSpinner && textWidth ? 10 : 0) - textWidth) / 2 - (opts.showSpinner && textWidth ? 0 : 5 + textWidth / 2)\n    // only show the text\n    + (opts.showSpinner ? 0 : textWidth / 2)\n    // only show the spinner\n    + (textWidth ? 0 : r);\n    var cy = api.getHeight() / 2;\n    opts.showSpinner && arc.setShape({\n      cx: cx,\n      cy: cy\n    });\n    labelRect.setShape({\n      x: cx - r,\n      y: cy - r,\n      width: r * 2,\n      height: r * 2\n    });\n    mask.setShape({\n      x: 0,\n      y: 0,\n      width: api.getWidth(),\n      height: api.getHeight()\n    });\n  };\n  group.resize();\n  return group;\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,OAAO,KAAKC,OAAO,MAAM,oBAAoB;AAC7C,IAAIC,EAAE,GAAGC,IAAI,CAACD,EAAE;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASE,cAAcA,CAACC,GAAG,EAAEC,IAAI,EAAE;EAChDA,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;EACjBN,MAAM,CAACO,QAAQ,CAACD,IAAI,EAAE;IACpBE,IAAI,EAAE,SAAS;IACfC,SAAS,EAAE,MAAM;IACjBC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,QAAQ;IACpBC,SAAS,EAAE,QAAQ;IACnBC,UAAU,EAAE,YAAY;IACxBC,SAAS,EAAE,0BAA0B;IACrCC,WAAW,EAAE,IAAI;IACjBC,KAAK,EAAE,SAAS;IAChBC,aAAa,EAAE,EAAE;IACjBC,SAAS,EAAE,CAAC;IACZC,MAAM,EAAE;EACV,CAAC,CAAC;EACF,IAAIC,KAAK,GAAG,IAAInB,OAAO,CAACoB,KAAK,CAAC,CAAC;EAC/B,IAAIC,IAAI,GAAG,IAAIrB,OAAO,CAACsB,IAAI,CAAC;IAC1BC,KAAK,EAAE;MACLC,IAAI,EAAEnB,IAAI,CAACQ;IACb,CAAC;IACDK,MAAM,EAAEb,IAAI,CAACa,MAAM;IACnBO,CAAC,EAAE;EACL,CAAC,CAAC;EACFN,KAAK,CAACO,GAAG,CAACL,IAAI,CAAC;EACf,IAAIM,WAAW,GAAG,IAAI3B,OAAO,CAAC4B,IAAI,CAAC;IACjCL,KAAK,EAAE;MACLhB,IAAI,EAAEF,IAAI,CAACE,IAAI;MACfiB,IAAI,EAAEnB,IAAI,CAACG,SAAS;MACpBC,QAAQ,EAAEJ,IAAI,CAACI,QAAQ;MACvBC,UAAU,EAAEL,IAAI,CAACK,UAAU;MAC3BC,SAAS,EAAEN,IAAI,CAACM,SAAS;MACzBC,UAAU,EAAEP,IAAI,CAACO;IACnB,CAAC;IACDM,MAAM,EAAEb,IAAI,CAACa,MAAM;IACnBO,CAAC,EAAE;EACL,CAAC,CAAC;EACF,IAAII,SAAS,GAAG,IAAI7B,OAAO,CAACsB,IAAI,CAAC;IAC/BC,KAAK,EAAE;MACLC,IAAI,EAAE;IACR,CAAC;IACDG,WAAW,EAAEA,WAAW;IACxBG,UAAU,EAAE;MACVC,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE;IACZ,CAAC;IACDd,MAAM,EAAEb,IAAI,CAACa,MAAM;IACnBO,CAAC,EAAE;EACL,CAAC,CAAC;EACFN,KAAK,CAACO,GAAG,CAACG,SAAS,CAAC;EACpB,IAAII,GAAG;EACP,IAAI5B,IAAI,CAACS,WAAW,EAAE;IACpBmB,GAAG,GAAG,IAAIjC,OAAO,CAACkC,GAAG,CAAC;MACpBC,KAAK,EAAE;QACLC,UAAU,EAAE,CAACnC,EAAE,GAAG,CAAC;QACnBoC,QAAQ,EAAE,CAACpC,EAAE,GAAG,CAAC,GAAG,GAAG;QACvBqC,CAAC,EAAEjC,IAAI,CAACW;MACV,CAAC;MACDO,KAAK,EAAE;QACLgB,MAAM,EAAElC,IAAI,CAACU,KAAK;QAClByB,OAAO,EAAE,OAAO;QAChBvB,SAAS,EAAEZ,IAAI,CAACY;MAClB,CAAC;MACDC,MAAM,EAAEb,IAAI,CAACa,MAAM;MACnBO,CAAC,EAAE;IACL,CAAC,CAAC;IACFQ,GAAG,CAACQ,YAAY,CAAC,IAAI,CAAC,CAACC,IAAI,CAAC,IAAI,EAAE;MAChCL,QAAQ,EAAEpC,EAAE,GAAG,CAAC,GAAG;IACrB,CAAC,CAAC,CAAC0C,KAAK,CAAC,eAAe,CAAC;IACzBV,GAAG,CAACQ,YAAY,CAAC,IAAI,CAAC,CAACC,IAAI,CAAC,IAAI,EAAE;MAChCN,UAAU,EAAEnC,EAAE,GAAG,CAAC,GAAG;IACvB,CAAC,CAAC,CAAC2C,KAAK,CAAC,GAAG,CAAC,CAACD,KAAK,CAAC,eAAe,CAAC;IACpCxB,KAAK,CAACO,GAAG,CAACO,GAAG,CAAC;EAChB;EACA;EACAd,KAAK,CAAC0B,MAAM,GAAG,YAAY;IACzB,IAAIC,SAAS,GAAGnB,WAAW,CAACoB,eAAe,CAAC,CAAC,CAACC,KAAK;IACnD,IAAIV,CAAC,GAAGjC,IAAI,CAACS,WAAW,GAAGT,IAAI,CAACW,aAAa,GAAG,CAAC;IACjD;IACA;IACA,IAAIiC,EAAE,GAAG,CAAC7C,GAAG,CAAC8C,QAAQ,CAAC,CAAC,GAAGZ,CAAC,GAAG,CAAC,IAAIjC,IAAI,CAACS,WAAW,IAAIgC,SAAS,GAAG,EAAE,GAAG,CAAC,CAAC,GAAGA,SAAS,IAAI,CAAC,IAAIzC,IAAI,CAACS,WAAW,IAAIgC,SAAS,GAAG,CAAC,GAAG,CAAC,GAAGA,SAAS,GAAG,CAAC;IACrJ;IAAA,GACGzC,IAAI,CAACS,WAAW,GAAG,CAAC,GAAGgC,SAAS,GAAG,CAAC;IACvC;IAAA,GACGA,SAAS,GAAG,CAAC,GAAGR,CAAC,CAAC;IACrB,IAAIa,EAAE,GAAG/C,GAAG,CAACgD,SAAS,CAAC,CAAC,GAAG,CAAC;IAC5B/C,IAAI,CAACS,WAAW,IAAImB,GAAG,CAACoB,QAAQ,CAAC;MAC/BJ,EAAE,EAAEA,EAAE;MACNE,EAAE,EAAEA;IACN,CAAC,CAAC;IACFtB,SAAS,CAACwB,QAAQ,CAAC;MACjBC,CAAC,EAAEL,EAAE,GAAGX,CAAC;MACTiB,CAAC,EAAEJ,EAAE,GAAGb,CAAC;MACTU,KAAK,EAAEV,CAAC,GAAG,CAAC;MACZkB,MAAM,EAAElB,CAAC,GAAG;IACd,CAAC,CAAC;IACFjB,IAAI,CAACgC,QAAQ,CAAC;MACZC,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE,CAAC;MACJP,KAAK,EAAE5C,GAAG,CAAC8C,QAAQ,CAAC,CAAC;MACrBM,MAAM,EAAEpD,GAAG,CAACgD,SAAS,CAAC;IACxB,CAAC,CAAC;EACJ,CAAC;EACDjC,KAAK,CAAC0B,MAAM,CAAC,CAAC;EACd,OAAO1B,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}