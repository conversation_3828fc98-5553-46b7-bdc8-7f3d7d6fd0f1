{"ast": null, "code": "import Transformable, { TRANSFORMABLE_PROPS } from './core/Transformable.js';\nimport Animator, { cloneValue } from './animation/Animator.js';\nimport BoundingRect from './core/BoundingRect.js';\nimport Eventful from './core/Eventful.js';\nimport { calculateTextPosition, parsePercent } from './contain/text.js';\nimport { guid, isObject, keys, extend, indexOf, logError, mixin, isArrayLike, isTypedArray, isGradientObject, filter, reduce } from './core/util.js';\nimport { LIGHT_LABEL_COLOR, DARK_LABEL_COLOR } from './config.js';\nimport { parse, stringify } from './tool/color.js';\nimport { REDRAW_BIT } from './graphic/constants.js';\nexport var PRESERVED_NORMAL_STATE = '__zr_normal__';\nvar PRIMARY_STATES_KEYS = TRANSFORMABLE_PROPS.concat(['ignore']);\nvar DEFAULT_ANIMATABLE_MAP = reduce(TRANSFORMABLE_PROPS, function (obj, key) {\n  obj[key] = true;\n  return obj;\n}, {\n  ignore: false\n});\nvar tmpTextPosCalcRes = {};\nvar tmpBoundingRect = new BoundingRect(0, 0, 0, 0);\nvar Element = function () {\n  function Element(props) {\n    this.id = guid();\n    this.animators = [];\n    this.currentStates = [];\n    this.states = {};\n    this._init(props);\n  }\n  Element.prototype._init = function (props) {\n    this.attr(props);\n  };\n  Element.prototype.drift = function (dx, dy, e) {\n    switch (this.draggable) {\n      case 'horizontal':\n        dy = 0;\n        break;\n      case 'vertical':\n        dx = 0;\n        break;\n    }\n    var m = this.transform;\n    if (!m) {\n      m = this.transform = [1, 0, 0, 1, 0, 0];\n    }\n    m[4] += dx;\n    m[5] += dy;\n    this.decomposeTransform();\n    this.markRedraw();\n  };\n  Element.prototype.beforeUpdate = function () {};\n  Element.prototype.afterUpdate = function () {};\n  Element.prototype.update = function () {\n    this.updateTransform();\n    if (this.__dirty) {\n      this.updateInnerText();\n    }\n  };\n  Element.prototype.updateInnerText = function (forceUpdate) {\n    var textEl = this._textContent;\n    if (textEl && (!textEl.ignore || forceUpdate)) {\n      if (!this.textConfig) {\n        this.textConfig = {};\n      }\n      var textConfig = this.textConfig;\n      var isLocal = textConfig.local;\n      var innerTransformable = textEl.innerTransformable;\n      var textAlign = void 0;\n      var textVerticalAlign = void 0;\n      var textStyleChanged = false;\n      innerTransformable.parent = isLocal ? this : null;\n      var innerOrigin = false;\n      innerTransformable.copyTransform(textEl);\n      if (textConfig.position != null) {\n        var layoutRect = tmpBoundingRect;\n        if (textConfig.layoutRect) {\n          layoutRect.copy(textConfig.layoutRect);\n        } else {\n          layoutRect.copy(this.getBoundingRect());\n        }\n        if (!isLocal) {\n          layoutRect.applyTransform(this.transform);\n        }\n        if (this.calculateTextPosition) {\n          this.calculateTextPosition(tmpTextPosCalcRes, textConfig, layoutRect);\n        } else {\n          calculateTextPosition(tmpTextPosCalcRes, textConfig, layoutRect);\n        }\n        innerTransformable.x = tmpTextPosCalcRes.x;\n        innerTransformable.y = tmpTextPosCalcRes.y;\n        textAlign = tmpTextPosCalcRes.align;\n        textVerticalAlign = tmpTextPosCalcRes.verticalAlign;\n        var textOrigin = textConfig.origin;\n        if (textOrigin && textConfig.rotation != null) {\n          var relOriginX = void 0;\n          var relOriginY = void 0;\n          if (textOrigin === 'center') {\n            relOriginX = layoutRect.width * 0.5;\n            relOriginY = layoutRect.height * 0.5;\n          } else {\n            relOriginX = parsePercent(textOrigin[0], layoutRect.width);\n            relOriginY = parsePercent(textOrigin[1], layoutRect.height);\n          }\n          innerOrigin = true;\n          innerTransformable.originX = -innerTransformable.x + relOriginX + (isLocal ? 0 : layoutRect.x);\n          innerTransformable.originY = -innerTransformable.y + relOriginY + (isLocal ? 0 : layoutRect.y);\n        }\n      }\n      if (textConfig.rotation != null) {\n        innerTransformable.rotation = textConfig.rotation;\n      }\n      var textOffset = textConfig.offset;\n      if (textOffset) {\n        innerTransformable.x += textOffset[0];\n        innerTransformable.y += textOffset[1];\n        if (!innerOrigin) {\n          innerTransformable.originX = -textOffset[0];\n          innerTransformable.originY = -textOffset[1];\n        }\n      }\n      var isInside = textConfig.inside == null ? typeof textConfig.position === 'string' && textConfig.position.indexOf('inside') >= 0 : textConfig.inside;\n      var innerTextDefaultStyle = this._innerTextDefaultStyle || (this._innerTextDefaultStyle = {});\n      var textFill = void 0;\n      var textStroke = void 0;\n      var autoStroke = void 0;\n      if (isInside && this.canBeInsideText()) {\n        textFill = textConfig.insideFill;\n        textStroke = textConfig.insideStroke;\n        if (textFill == null || textFill === 'auto') {\n          textFill = this.getInsideTextFill();\n        }\n        if (textStroke == null || textStroke === 'auto') {\n          textStroke = this.getInsideTextStroke(textFill);\n          autoStroke = true;\n        }\n      } else {\n        textFill = textConfig.outsideFill;\n        textStroke = textConfig.outsideStroke;\n        if (textFill == null || textFill === 'auto') {\n          textFill = this.getOutsideFill();\n        }\n        if (textStroke == null || textStroke === 'auto') {\n          textStroke = this.getOutsideStroke(textFill);\n          autoStroke = true;\n        }\n      }\n      textFill = textFill || '#000';\n      if (textFill !== innerTextDefaultStyle.fill || textStroke !== innerTextDefaultStyle.stroke || autoStroke !== innerTextDefaultStyle.autoStroke || textAlign !== innerTextDefaultStyle.align || textVerticalAlign !== innerTextDefaultStyle.verticalAlign) {\n        textStyleChanged = true;\n        innerTextDefaultStyle.fill = textFill;\n        innerTextDefaultStyle.stroke = textStroke;\n        innerTextDefaultStyle.autoStroke = autoStroke;\n        innerTextDefaultStyle.align = textAlign;\n        innerTextDefaultStyle.verticalAlign = textVerticalAlign;\n        textEl.setDefaultTextStyle(innerTextDefaultStyle);\n      }\n      textEl.__dirty |= REDRAW_BIT;\n      if (textStyleChanged) {\n        textEl.dirtyStyle(true);\n      }\n    }\n  };\n  Element.prototype.canBeInsideText = function () {\n    return true;\n  };\n  Element.prototype.getInsideTextFill = function () {\n    return '#fff';\n  };\n  Element.prototype.getInsideTextStroke = function (textFill) {\n    return '#000';\n  };\n  Element.prototype.getOutsideFill = function () {\n    return this.__zr && this.__zr.isDarkMode() ? LIGHT_LABEL_COLOR : DARK_LABEL_COLOR;\n  };\n  Element.prototype.getOutsideStroke = function (textFill) {\n    var backgroundColor = this.__zr && this.__zr.getBackgroundColor();\n    var colorArr = typeof backgroundColor === 'string' && parse(backgroundColor);\n    if (!colorArr) {\n      colorArr = [255, 255, 255, 1];\n    }\n    var alpha = colorArr[3];\n    var isDark = this.__zr.isDarkMode();\n    for (var i = 0; i < 3; i++) {\n      colorArr[i] = colorArr[i] * alpha + (isDark ? 0 : 255) * (1 - alpha);\n    }\n    colorArr[3] = 1;\n    return stringify(colorArr, 'rgba');\n  };\n  Element.prototype.traverse = function (cb, context) {};\n  Element.prototype.attrKV = function (key, value) {\n    if (key === 'textConfig') {\n      this.setTextConfig(value);\n    } else if (key === 'textContent') {\n      this.setTextContent(value);\n    } else if (key === 'clipPath') {\n      this.setClipPath(value);\n    } else if (key === 'extra') {\n      this.extra = this.extra || {};\n      extend(this.extra, value);\n    } else {\n      this[key] = value;\n    }\n  };\n  Element.prototype.hide = function () {\n    this.ignore = true;\n    this.markRedraw();\n  };\n  Element.prototype.show = function () {\n    this.ignore = false;\n    this.markRedraw();\n  };\n  Element.prototype.attr = function (keyOrObj, value) {\n    if (typeof keyOrObj === 'string') {\n      this.attrKV(keyOrObj, value);\n    } else if (isObject(keyOrObj)) {\n      var obj = keyOrObj;\n      var keysArr = keys(obj);\n      for (var i = 0; i < keysArr.length; i++) {\n        var key = keysArr[i];\n        this.attrKV(key, keyOrObj[key]);\n      }\n    }\n    this.markRedraw();\n    return this;\n  };\n  Element.prototype.saveCurrentToNormalState = function (toState) {\n    this._innerSaveToNormal(toState);\n    var normalState = this._normalState;\n    for (var i = 0; i < this.animators.length; i++) {\n      var animator = this.animators[i];\n      var fromStateTransition = animator.__fromStateTransition;\n      if (animator.getLoop() || fromStateTransition && fromStateTransition !== PRESERVED_NORMAL_STATE) {\n        continue;\n      }\n      var targetName = animator.targetName;\n      var target = targetName ? normalState[targetName] : normalState;\n      animator.saveTo(target);\n    }\n  };\n  Element.prototype._innerSaveToNormal = function (toState) {\n    var normalState = this._normalState;\n    if (!normalState) {\n      normalState = this._normalState = {};\n    }\n    if (toState.textConfig && !normalState.textConfig) {\n      normalState.textConfig = this.textConfig;\n    }\n    this._savePrimaryToNormal(toState, normalState, PRIMARY_STATES_KEYS);\n  };\n  Element.prototype._savePrimaryToNormal = function (toState, normalState, primaryKeys) {\n    for (var i = 0; i < primaryKeys.length; i++) {\n      var key = primaryKeys[i];\n      if (toState[key] != null && !(key in normalState)) {\n        normalState[key] = this[key];\n      }\n    }\n  };\n  Element.prototype.hasState = function () {\n    return this.currentStates.length > 0;\n  };\n  Element.prototype.getState = function (name) {\n    return this.states[name];\n  };\n  Element.prototype.ensureState = function (name) {\n    var states = this.states;\n    if (!states[name]) {\n      states[name] = {};\n    }\n    return states[name];\n  };\n  Element.prototype.clearStates = function (noAnimation) {\n    this.useState(PRESERVED_NORMAL_STATE, false, noAnimation);\n  };\n  Element.prototype.useState = function (stateName, keepCurrentStates, noAnimation, forceUseHoverLayer) {\n    var toNormalState = stateName === PRESERVED_NORMAL_STATE;\n    var hasStates = this.hasState();\n    if (!hasStates && toNormalState) {\n      return;\n    }\n    var currentStates = this.currentStates;\n    var animationCfg = this.stateTransition;\n    if (indexOf(currentStates, stateName) >= 0 && (keepCurrentStates || currentStates.length === 1)) {\n      return;\n    }\n    var state;\n    if (this.stateProxy && !toNormalState) {\n      state = this.stateProxy(stateName);\n    }\n    if (!state) {\n      state = this.states && this.states[stateName];\n    }\n    if (!state && !toNormalState) {\n      logError(\"State \" + stateName + \" not exists.\");\n      return;\n    }\n    if (!toNormalState) {\n      this.saveCurrentToNormalState(state);\n    }\n    var useHoverLayer = !!(state && state.hoverLayer || forceUseHoverLayer);\n    if (useHoverLayer) {\n      this._toggleHoverLayerFlag(true);\n    }\n    this._applyStateObj(stateName, state, this._normalState, keepCurrentStates, !noAnimation && !this.__inHover && animationCfg && animationCfg.duration > 0, animationCfg);\n    var textContent = this._textContent;\n    var textGuide = this._textGuide;\n    if (textContent) {\n      textContent.useState(stateName, keepCurrentStates, noAnimation, useHoverLayer);\n    }\n    if (textGuide) {\n      textGuide.useState(stateName, keepCurrentStates, noAnimation, useHoverLayer);\n    }\n    if (toNormalState) {\n      this.currentStates = [];\n      this._normalState = {};\n    } else {\n      if (!keepCurrentStates) {\n        this.currentStates = [stateName];\n      } else {\n        this.currentStates.push(stateName);\n      }\n    }\n    this._updateAnimationTargets();\n    this.markRedraw();\n    if (!useHoverLayer && this.__inHover) {\n      this._toggleHoverLayerFlag(false);\n      this.__dirty &= ~REDRAW_BIT;\n    }\n    return state;\n  };\n  Element.prototype.useStates = function (states, noAnimation, forceUseHoverLayer) {\n    if (!states.length) {\n      this.clearStates();\n    } else {\n      var stateObjects = [];\n      var currentStates = this.currentStates;\n      var len = states.length;\n      var notChange = len === currentStates.length;\n      if (notChange) {\n        for (var i = 0; i < len; i++) {\n          if (states[i] !== currentStates[i]) {\n            notChange = false;\n            break;\n          }\n        }\n      }\n      if (notChange) {\n        return;\n      }\n      for (var i = 0; i < len; i++) {\n        var stateName = states[i];\n        var stateObj = void 0;\n        if (this.stateProxy) {\n          stateObj = this.stateProxy(stateName, states);\n        }\n        if (!stateObj) {\n          stateObj = this.states[stateName];\n        }\n        if (stateObj) {\n          stateObjects.push(stateObj);\n        }\n      }\n      var lastStateObj = stateObjects[len - 1];\n      var useHoverLayer = !!(lastStateObj && lastStateObj.hoverLayer || forceUseHoverLayer);\n      if (useHoverLayer) {\n        this._toggleHoverLayerFlag(true);\n      }\n      var mergedState = this._mergeStates(stateObjects);\n      var animationCfg = this.stateTransition;\n      this.saveCurrentToNormalState(mergedState);\n      this._applyStateObj(states.join(','), mergedState, this._normalState, false, !noAnimation && !this.__inHover && animationCfg && animationCfg.duration > 0, animationCfg);\n      var textContent = this._textContent;\n      var textGuide = this._textGuide;\n      if (textContent) {\n        textContent.useStates(states, noAnimation, useHoverLayer);\n      }\n      if (textGuide) {\n        textGuide.useStates(states, noAnimation, useHoverLayer);\n      }\n      this._updateAnimationTargets();\n      this.currentStates = states.slice();\n      this.markRedraw();\n      if (!useHoverLayer && this.__inHover) {\n        this._toggleHoverLayerFlag(false);\n        this.__dirty &= ~REDRAW_BIT;\n      }\n    }\n  };\n  Element.prototype.isSilent = function () {\n    var isSilent = this.silent;\n    var ancestor = this.parent;\n    while (!isSilent && ancestor) {\n      if (ancestor.silent) {\n        isSilent = true;\n        break;\n      }\n      ancestor = ancestor.parent;\n    }\n    return isSilent;\n  };\n  Element.prototype._updateAnimationTargets = function () {\n    for (var i = 0; i < this.animators.length; i++) {\n      var animator = this.animators[i];\n      if (animator.targetName) {\n        animator.changeTarget(this[animator.targetName]);\n      }\n    }\n  };\n  Element.prototype.removeState = function (state) {\n    var idx = indexOf(this.currentStates, state);\n    if (idx >= 0) {\n      var currentStates = this.currentStates.slice();\n      currentStates.splice(idx, 1);\n      this.useStates(currentStates);\n    }\n  };\n  Element.prototype.replaceState = function (oldState, newState, forceAdd) {\n    var currentStates = this.currentStates.slice();\n    var idx = indexOf(currentStates, oldState);\n    var newStateExists = indexOf(currentStates, newState) >= 0;\n    if (idx >= 0) {\n      if (!newStateExists) {\n        currentStates[idx] = newState;\n      } else {\n        currentStates.splice(idx, 1);\n      }\n    } else if (forceAdd && !newStateExists) {\n      currentStates.push(newState);\n    }\n    this.useStates(currentStates);\n  };\n  Element.prototype.toggleState = function (state, enable) {\n    if (enable) {\n      this.useState(state, true);\n    } else {\n      this.removeState(state);\n    }\n  };\n  Element.prototype._mergeStates = function (states) {\n    var mergedState = {};\n    var mergedTextConfig;\n    for (var i = 0; i < states.length; i++) {\n      var state = states[i];\n      extend(mergedState, state);\n      if (state.textConfig) {\n        mergedTextConfig = mergedTextConfig || {};\n        extend(mergedTextConfig, state.textConfig);\n      }\n    }\n    if (mergedTextConfig) {\n      mergedState.textConfig = mergedTextConfig;\n    }\n    return mergedState;\n  };\n  Element.prototype._applyStateObj = function (stateName, state, normalState, keepCurrentStates, transition, animationCfg) {\n    var needsRestoreToNormal = !(state && keepCurrentStates);\n    if (state && state.textConfig) {\n      this.textConfig = extend({}, keepCurrentStates ? this.textConfig : normalState.textConfig);\n      extend(this.textConfig, state.textConfig);\n    } else if (needsRestoreToNormal) {\n      if (normalState.textConfig) {\n        this.textConfig = normalState.textConfig;\n      }\n    }\n    var transitionTarget = {};\n    var hasTransition = false;\n    for (var i = 0; i < PRIMARY_STATES_KEYS.length; i++) {\n      var key = PRIMARY_STATES_KEYS[i];\n      var propNeedsTransition = transition && DEFAULT_ANIMATABLE_MAP[key];\n      if (state && state[key] != null) {\n        if (propNeedsTransition) {\n          hasTransition = true;\n          transitionTarget[key] = state[key];\n        } else {\n          this[key] = state[key];\n        }\n      } else if (needsRestoreToNormal) {\n        if (normalState[key] != null) {\n          if (propNeedsTransition) {\n            hasTransition = true;\n            transitionTarget[key] = normalState[key];\n          } else {\n            this[key] = normalState[key];\n          }\n        }\n      }\n    }\n    if (!transition) {\n      for (var i = 0; i < this.animators.length; i++) {\n        var animator = this.animators[i];\n        var targetName = animator.targetName;\n        if (!animator.getLoop()) {\n          animator.__changeFinalValue(targetName ? (state || normalState)[targetName] : state || normalState);\n        }\n      }\n    }\n    if (hasTransition) {\n      this._transitionState(stateName, transitionTarget, animationCfg);\n    }\n  };\n  Element.prototype._attachComponent = function (componentEl) {\n    if (componentEl.__zr && !componentEl.__hostTarget) {\n      if (process.env.NODE_ENV !== 'production') {\n        throw new Error('Text element has been added to zrender.');\n      }\n      return;\n    }\n    if (componentEl === this) {\n      if (process.env.NODE_ENV !== 'production') {\n        throw new Error('Recursive component attachment.');\n      }\n      return;\n    }\n    var zr = this.__zr;\n    if (zr) {\n      componentEl.addSelfToZr(zr);\n    }\n    componentEl.__zr = zr;\n    componentEl.__hostTarget = this;\n  };\n  Element.prototype._detachComponent = function (componentEl) {\n    if (componentEl.__zr) {\n      componentEl.removeSelfFromZr(componentEl.__zr);\n    }\n    componentEl.__zr = null;\n    componentEl.__hostTarget = null;\n  };\n  Element.prototype.getClipPath = function () {\n    return this._clipPath;\n  };\n  Element.prototype.setClipPath = function (clipPath) {\n    if (this._clipPath && this._clipPath !== clipPath) {\n      this.removeClipPath();\n    }\n    this._attachComponent(clipPath);\n    this._clipPath = clipPath;\n    this.markRedraw();\n  };\n  Element.prototype.removeClipPath = function () {\n    var clipPath = this._clipPath;\n    if (clipPath) {\n      this._detachComponent(clipPath);\n      this._clipPath = null;\n      this.markRedraw();\n    }\n  };\n  Element.prototype.getTextContent = function () {\n    return this._textContent;\n  };\n  Element.prototype.setTextContent = function (textEl) {\n    var previousTextContent = this._textContent;\n    if (previousTextContent === textEl) {\n      return;\n    }\n    if (previousTextContent && previousTextContent !== textEl) {\n      this.removeTextContent();\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (textEl.__zr && !textEl.__hostTarget) {\n        throw new Error('Text element has been added to zrender.');\n      }\n    }\n    textEl.innerTransformable = new Transformable();\n    this._attachComponent(textEl);\n    this._textContent = textEl;\n    this.markRedraw();\n  };\n  Element.prototype.setTextConfig = function (cfg) {\n    if (!this.textConfig) {\n      this.textConfig = {};\n    }\n    extend(this.textConfig, cfg);\n    this.markRedraw();\n  };\n  Element.prototype.removeTextConfig = function () {\n    this.textConfig = null;\n    this.markRedraw();\n  };\n  Element.prototype.removeTextContent = function () {\n    var textEl = this._textContent;\n    if (textEl) {\n      textEl.innerTransformable = null;\n      this._detachComponent(textEl);\n      this._textContent = null;\n      this._innerTextDefaultStyle = null;\n      this.markRedraw();\n    }\n  };\n  Element.prototype.getTextGuideLine = function () {\n    return this._textGuide;\n  };\n  Element.prototype.setTextGuideLine = function (guideLine) {\n    if (this._textGuide && this._textGuide !== guideLine) {\n      this.removeTextGuideLine();\n    }\n    this._attachComponent(guideLine);\n    this._textGuide = guideLine;\n    this.markRedraw();\n  };\n  Element.prototype.removeTextGuideLine = function () {\n    var textGuide = this._textGuide;\n    if (textGuide) {\n      this._detachComponent(textGuide);\n      this._textGuide = null;\n      this.markRedraw();\n    }\n  };\n  Element.prototype.markRedraw = function () {\n    this.__dirty |= REDRAW_BIT;\n    var zr = this.__zr;\n    if (zr) {\n      if (this.__inHover) {\n        zr.refreshHover();\n      } else {\n        zr.refresh();\n      }\n    }\n    if (this.__hostTarget) {\n      this.__hostTarget.markRedraw();\n    }\n  };\n  Element.prototype.dirty = function () {\n    this.markRedraw();\n  };\n  Element.prototype._toggleHoverLayerFlag = function (inHover) {\n    this.__inHover = inHover;\n    var textContent = this._textContent;\n    var textGuide = this._textGuide;\n    if (textContent) {\n      textContent.__inHover = inHover;\n    }\n    if (textGuide) {\n      textGuide.__inHover = inHover;\n    }\n  };\n  Element.prototype.addSelfToZr = function (zr) {\n    if (this.__zr === zr) {\n      return;\n    }\n    this.__zr = zr;\n    var animators = this.animators;\n    if (animators) {\n      for (var i = 0; i < animators.length; i++) {\n        zr.animation.addAnimator(animators[i]);\n      }\n    }\n    if (this._clipPath) {\n      this._clipPath.addSelfToZr(zr);\n    }\n    if (this._textContent) {\n      this._textContent.addSelfToZr(zr);\n    }\n    if (this._textGuide) {\n      this._textGuide.addSelfToZr(zr);\n    }\n  };\n  Element.prototype.removeSelfFromZr = function (zr) {\n    if (!this.__zr) {\n      return;\n    }\n    this.__zr = null;\n    var animators = this.animators;\n    if (animators) {\n      for (var i = 0; i < animators.length; i++) {\n        zr.animation.removeAnimator(animators[i]);\n      }\n    }\n    if (this._clipPath) {\n      this._clipPath.removeSelfFromZr(zr);\n    }\n    if (this._textContent) {\n      this._textContent.removeSelfFromZr(zr);\n    }\n    if (this._textGuide) {\n      this._textGuide.removeSelfFromZr(zr);\n    }\n  };\n  Element.prototype.animate = function (key, loop, allowDiscreteAnimation) {\n    var target = key ? this[key] : this;\n    if (process.env.NODE_ENV !== 'production') {\n      if (!target) {\n        logError('Property \"' + key + '\" is not existed in element ' + this.id);\n        return;\n      }\n    }\n    var animator = new Animator(target, loop, allowDiscreteAnimation);\n    key && (animator.targetName = key);\n    this.addAnimator(animator, key);\n    return animator;\n  };\n  Element.prototype.addAnimator = function (animator, key) {\n    var zr = this.__zr;\n    var el = this;\n    animator.during(function () {\n      el.updateDuringAnimation(key);\n    }).done(function () {\n      var animators = el.animators;\n      var idx = indexOf(animators, animator);\n      if (idx >= 0) {\n        animators.splice(idx, 1);\n      }\n    });\n    this.animators.push(animator);\n    if (zr) {\n      zr.animation.addAnimator(animator);\n    }\n    zr && zr.wakeUp();\n  };\n  Element.prototype.updateDuringAnimation = function (key) {\n    this.markRedraw();\n  };\n  Element.prototype.stopAnimation = function (scope, forwardToLast) {\n    var animators = this.animators;\n    var len = animators.length;\n    var leftAnimators = [];\n    for (var i = 0; i < len; i++) {\n      var animator = animators[i];\n      if (!scope || scope === animator.scope) {\n        animator.stop(forwardToLast);\n      } else {\n        leftAnimators.push(animator);\n      }\n    }\n    this.animators = leftAnimators;\n    return this;\n  };\n  Element.prototype.animateTo = function (target, cfg, animationProps) {\n    animateTo(this, target, cfg, animationProps);\n  };\n  Element.prototype.animateFrom = function (target, cfg, animationProps) {\n    animateTo(this, target, cfg, animationProps, true);\n  };\n  Element.prototype._transitionState = function (stateName, target, cfg, animationProps) {\n    var animators = animateTo(this, target, cfg, animationProps);\n    for (var i = 0; i < animators.length; i++) {\n      animators[i].__fromStateTransition = stateName;\n    }\n  };\n  Element.prototype.getBoundingRect = function () {\n    return null;\n  };\n  Element.prototype.getPaintRect = function () {\n    return null;\n  };\n  Element.initDefaultProps = function () {\n    var elProto = Element.prototype;\n    elProto.type = 'element';\n    elProto.name = '';\n    elProto.ignore = elProto.silent = elProto.isGroup = elProto.draggable = elProto.dragging = elProto.ignoreClip = elProto.__inHover = false;\n    elProto.__dirty = REDRAW_BIT;\n    var logs = {};\n    function logDeprecatedError(key, xKey, yKey) {\n      if (!logs[key + xKey + yKey]) {\n        console.warn(\"DEPRECATED: '\" + key + \"' has been deprecated. use '\" + xKey + \"', '\" + yKey + \"' instead\");\n        logs[key + xKey + yKey] = true;\n      }\n    }\n    function createLegacyProperty(key, privateKey, xKey, yKey) {\n      Object.defineProperty(elProto, key, {\n        get: function () {\n          if (process.env.NODE_ENV !== 'production') {\n            logDeprecatedError(key, xKey, yKey);\n          }\n          if (!this[privateKey]) {\n            var pos = this[privateKey] = [];\n            enhanceArray(this, pos);\n          }\n          return this[privateKey];\n        },\n        set: function (pos) {\n          if (process.env.NODE_ENV !== 'production') {\n            logDeprecatedError(key, xKey, yKey);\n          }\n          this[xKey] = pos[0];\n          this[yKey] = pos[1];\n          this[privateKey] = pos;\n          enhanceArray(this, pos);\n        }\n      });\n      function enhanceArray(self, pos) {\n        Object.defineProperty(pos, 0, {\n          get: function () {\n            return self[xKey];\n          },\n          set: function (val) {\n            self[xKey] = val;\n          }\n        });\n        Object.defineProperty(pos, 1, {\n          get: function () {\n            return self[yKey];\n          },\n          set: function (val) {\n            self[yKey] = val;\n          }\n        });\n      }\n    }\n    if (Object.defineProperty) {\n      createLegacyProperty('position', '_legacyPos', 'x', 'y');\n      createLegacyProperty('scale', '_legacyScale', 'scaleX', 'scaleY');\n      createLegacyProperty('origin', '_legacyOrigin', 'originX', 'originY');\n    }\n  }();\n  return Element;\n}();\nmixin(Element, Eventful);\nmixin(Element, Transformable);\nfunction animateTo(animatable, target, cfg, animationProps, reverse) {\n  cfg = cfg || {};\n  var animators = [];\n  animateToShallow(animatable, '', animatable, target, cfg, animationProps, animators, reverse);\n  var finishCount = animators.length;\n  var doneHappened = false;\n  var cfgDone = cfg.done;\n  var cfgAborted = cfg.aborted;\n  var doneCb = function () {\n    doneHappened = true;\n    finishCount--;\n    if (finishCount <= 0) {\n      doneHappened ? cfgDone && cfgDone() : cfgAborted && cfgAborted();\n    }\n  };\n  var abortedCb = function () {\n    finishCount--;\n    if (finishCount <= 0) {\n      doneHappened ? cfgDone && cfgDone() : cfgAborted && cfgAborted();\n    }\n  };\n  if (!finishCount) {\n    cfgDone && cfgDone();\n  }\n  if (animators.length > 0 && cfg.during) {\n    animators[0].during(function (target, percent) {\n      cfg.during(percent);\n    });\n  }\n  for (var i = 0; i < animators.length; i++) {\n    var animator = animators[i];\n    if (doneCb) {\n      animator.done(doneCb);\n    }\n    if (abortedCb) {\n      animator.aborted(abortedCb);\n    }\n    if (cfg.force) {\n      animator.duration(cfg.duration);\n    }\n    animator.start(cfg.easing);\n  }\n  return animators;\n}\nfunction copyArrShallow(source, target, len) {\n  for (var i = 0; i < len; i++) {\n    source[i] = target[i];\n  }\n}\nfunction is2DArray(value) {\n  return isArrayLike(value[0]);\n}\nfunction copyValue(target, source, key) {\n  if (isArrayLike(source[key])) {\n    if (!isArrayLike(target[key])) {\n      target[key] = [];\n    }\n    if (isTypedArray(source[key])) {\n      var len = source[key].length;\n      if (target[key].length !== len) {\n        target[key] = new source[key].constructor(len);\n        copyArrShallow(target[key], source[key], len);\n      }\n    } else {\n      var sourceArr = source[key];\n      var targetArr = target[key];\n      var len0 = sourceArr.length;\n      if (is2DArray(sourceArr)) {\n        var len1 = sourceArr[0].length;\n        for (var i = 0; i < len0; i++) {\n          if (!targetArr[i]) {\n            targetArr[i] = Array.prototype.slice.call(sourceArr[i]);\n          } else {\n            copyArrShallow(targetArr[i], sourceArr[i], len1);\n          }\n        }\n      } else {\n        copyArrShallow(targetArr, sourceArr, len0);\n      }\n      targetArr.length = sourceArr.length;\n    }\n  } else {\n    target[key] = source[key];\n  }\n}\nfunction isValueSame(val1, val2) {\n  return val1 === val2 || isArrayLike(val1) && isArrayLike(val2) && is1DArraySame(val1, val2);\n}\nfunction is1DArraySame(arr0, arr1) {\n  var len = arr0.length;\n  if (len !== arr1.length) {\n    return false;\n  }\n  for (var i = 0; i < len; i++) {\n    if (arr0[i] !== arr1[i]) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction animateToShallow(animatable, topKey, animateObj, target, cfg, animationProps, animators, reverse) {\n  var targetKeys = keys(target);\n  var duration = cfg.duration;\n  var delay = cfg.delay;\n  var additive = cfg.additive;\n  var setToFinal = cfg.setToFinal;\n  var animateAll = !isObject(animationProps);\n  var existsAnimators = animatable.animators;\n  var animationKeys = [];\n  for (var k = 0; k < targetKeys.length; k++) {\n    var innerKey = targetKeys[k];\n    var targetVal = target[innerKey];\n    if (targetVal != null && animateObj[innerKey] != null && (animateAll || animationProps[innerKey])) {\n      if (isObject(targetVal) && !isArrayLike(targetVal) && !isGradientObject(targetVal)) {\n        if (topKey) {\n          if (!reverse) {\n            animateObj[innerKey] = targetVal;\n            animatable.updateDuringAnimation(topKey);\n          }\n          continue;\n        }\n        animateToShallow(animatable, innerKey, animateObj[innerKey], targetVal, cfg, animationProps && animationProps[innerKey], animators, reverse);\n      } else {\n        animationKeys.push(innerKey);\n      }\n    } else if (!reverse) {\n      animateObj[innerKey] = targetVal;\n      animatable.updateDuringAnimation(topKey);\n      animationKeys.push(innerKey);\n    }\n  }\n  var keyLen = animationKeys.length;\n  if (!additive && keyLen) {\n    for (var i = 0; i < existsAnimators.length; i++) {\n      var animator = existsAnimators[i];\n      if (animator.targetName === topKey) {\n        var allAborted = animator.stopTracks(animationKeys);\n        if (allAborted) {\n          var idx = indexOf(existsAnimators, animator);\n          existsAnimators.splice(idx, 1);\n        }\n      }\n    }\n  }\n  if (!cfg.force) {\n    animationKeys = filter(animationKeys, function (key) {\n      return !isValueSame(target[key], animateObj[key]);\n    });\n    keyLen = animationKeys.length;\n  }\n  if (keyLen > 0 || cfg.force && !animators.length) {\n    var revertedSource = void 0;\n    var reversedTarget = void 0;\n    var sourceClone = void 0;\n    if (reverse) {\n      reversedTarget = {};\n      if (setToFinal) {\n        revertedSource = {};\n      }\n      for (var i = 0; i < keyLen; i++) {\n        var innerKey = animationKeys[i];\n        reversedTarget[innerKey] = animateObj[innerKey];\n        if (setToFinal) {\n          revertedSource[innerKey] = target[innerKey];\n        } else {\n          animateObj[innerKey] = target[innerKey];\n        }\n      }\n    } else if (setToFinal) {\n      sourceClone = {};\n      for (var i = 0; i < keyLen; i++) {\n        var innerKey = animationKeys[i];\n        sourceClone[innerKey] = cloneValue(animateObj[innerKey]);\n        copyValue(animateObj, target, innerKey);\n      }\n    }\n    var animator = new Animator(animateObj, false, false, additive ? filter(existsAnimators, function (animator) {\n      return animator.targetName === topKey;\n    }) : null);\n    animator.targetName = topKey;\n    if (cfg.scope) {\n      animator.scope = cfg.scope;\n    }\n    if (setToFinal && revertedSource) {\n      animator.whenWithKeys(0, revertedSource, animationKeys);\n    }\n    if (sourceClone) {\n      animator.whenWithKeys(0, sourceClone, animationKeys);\n    }\n    animator.whenWithKeys(duration == null ? 500 : duration, reverse ? reversedTarget : target, animationKeys).delay(delay || 0);\n    animatable.addAnimator(animator, topKey);\n    animators.push(animator);\n  }\n}\nexport default Element;", "map": {"version": 3, "names": ["Transformable", "TRANSFORMABLE_PROPS", "Animator", "cloneValue", "BoundingRect", "Eventful", "calculateTextPosition", "parsePercent", "guid", "isObject", "keys", "extend", "indexOf", "logError", "mixin", "isArrayLike", "isTypedArray", "isGradientObject", "filter", "reduce", "LIGHT_LABEL_COLOR", "DARK_LABEL_COLOR", "parse", "stringify", "REDRAW_BIT", "PRESERVED_NORMAL_STATE", "PRIMARY_STATES_KEYS", "concat", "DEFAULT_ANIMATABLE_MAP", "obj", "key", "ignore", "tmpTextPosCalcRes", "tmpBoundingRect", "Element", "props", "id", "animators", "currentStates", "states", "_init", "prototype", "attr", "drift", "dx", "dy", "e", "draggable", "m", "transform", "decomposeTransform", "mark<PERSON><PERSON><PERSON>", "beforeUpdate", "afterUpdate", "update", "updateTransform", "__dirty", "updateInnerText", "forceUpdate", "textEl", "_textContent", "textConfig", "isLocal", "local", "innerTransformable", "textAlign", "textVerticalAlign", "textStyleChanged", "parent", "innerOrigin", "copyTransform", "position", "layoutRect", "copy", "getBoundingRect", "applyTransform", "x", "y", "align", "verticalAlign", "<PERSON><PERSON><PERSON><PERSON>", "origin", "rotation", "relOriginX", "relOriginY", "width", "height", "originX", "originY", "textOffset", "offset", "isInside", "inside", "innerTextDefaultStyle", "_innerTextDefaultStyle", "textFill", "textStroke", "autoStroke", "canBeInsideText", "insideFill", "insideStroke", "getInsideTextFill", "getInsideTextStroke", "outsideFill", "outsideStroke", "getOutsideFill", "getOutsideStroke", "fill", "stroke", "setDefaultTextStyle", "dirtyStyle", "__zr", "isDarkMode", "backgroundColor", "getBackgroundColor", "colorArr", "alpha", "isDark", "i", "traverse", "cb", "context", "attrKV", "value", "setTextConfig", "setTextContent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "extra", "hide", "show", "key<PERSON>r<PERSON><PERSON><PERSON>", "keysArr", "length", "saveCurrentToNormalState", "toState", "_innerSaveToNormal", "normalState", "_normalState", "animator", "fromStateTransition", "__fromStateTransition", "getLoop", "targetName", "target", "saveTo", "_savePrimaryToNormal", "primaryKeys", "hasState", "getState", "name", "ensureState", "clearStates", "noAnimation", "useState", "stateName", "keepCurrentStates", "forceUseHoverLayer", "toNormalState", "hasStates", "animationCfg", "stateTransition", "state", "stateProxy", "useHoverLayer", "hoverLayer", "_toggleHoverLayerFlag", "_applyStateObj", "__inHover", "duration", "textContent", "textGuide", "_textGuide", "push", "_updateAnimationTargets", "useStates", "stateObjects", "len", "notChange", "stateObj", "lastStateObj", "mergedState", "_mergeStates", "join", "slice", "isSilent", "silent", "ancestor", "change<PERSON>arget", "removeState", "idx", "splice", "replaceState", "oldState", "newState", "forceAdd", "newStateExists", "toggleState", "enable", "mergedTextConfig", "transition", "needsRestoreToNormal", "<PERSON><PERSON><PERSON><PERSON>", "hasTransition", "propNeedsTransition", "__changeFinalValue", "_transitionState", "_attachComponent", "componentEl", "__host<PERSON><PERSON>get", "process", "env", "NODE_ENV", "Error", "zr", "addSelfToZr", "_detachComponent", "removeSelfFromZr", "getClipPath", "_clipPath", "clipPath", "removeClip<PERSON>ath", "getTextContent", "previousTextContent", "removeTextContent", "cfg", "removeTextConfig", "getTextGuideLine", "setTextGuideLine", "guideLine", "removeTextGuideLine", "refreshHover", "refresh", "dirty", "inHover", "animation", "addAnimator", "removeAnimator", "animate", "loop", "allowDiscreteAnimation", "el", "during", "updateDuringAnimation", "done", "wakeUp", "stopAnimation", "scope", "forwardToLast", "leftAnimators", "stop", "animateTo", "animationProps", "animateFrom", "getPaintRect", "initDefaultProps", "elProto", "type", "isGroup", "dragging", "ignoreClip", "logs", "logDeprecatedError", "xKey", "y<PERSON><PERSON>", "console", "warn", "createLegacyProperty", "privateKey", "Object", "defineProperty", "get", "pos", "enhanceArray", "set", "self", "val", "animatable", "reverse", "animateToShallow", "finishCount", "<PERSON><PERSON><PERSON><PERSON>ed", "cfgDone", "cfgAborted", "aborted", "doneCb", "abortedCb", "percent", "force", "start", "easing", "copyArrShallow", "source", "is2DArray", "copyValue", "constructor", "sourceArr", "targetArr", "len0", "len1", "Array", "call", "isValueSame", "val1", "val2", "is1DArraySame", "arr0", "arr1", "top<PERSON>ey", "animateObj", "targetKeys", "delay", "additive", "setToFinal", "animateAll", "existsAnimators", "animationKeys", "k", "innerKey", "targetVal", "keyLen", "allAborted", "stopTracks", "revertedSource", "<PERSON><PERSON><PERSON><PERSON>", "sourceClone", "when<PERSON>ith<PERSON>eys"], "sources": ["E:/AI/SmartCV/node_modules/zrender/lib/Element.js"], "sourcesContent": ["import Transformable, { TRANSFORMABLE_PROPS } from './core/Transformable.js';\nimport Animator, { cloneValue } from './animation/Animator.js';\nimport BoundingRect from './core/BoundingRect.js';\nimport Eventful from './core/Eventful.js';\nimport { calculateTextPosition, parsePercent } from './contain/text.js';\nimport { guid, isObject, keys, extend, indexOf, logError, mixin, isArrayLike, isTypedArray, isGradientObject, filter, reduce } from './core/util.js';\nimport { LIGHT_LABEL_COLOR, DARK_LABEL_COLOR } from './config.js';\nimport { parse, stringify } from './tool/color.js';\nimport { REDRAW_BIT } from './graphic/constants.js';\nexport var PRESERVED_NORMAL_STATE = '__zr_normal__';\nvar PRIMARY_STATES_KEYS = TRANSFORMABLE_PROPS.concat(['ignore']);\nvar DEFAULT_ANIMATABLE_MAP = reduce(TRANSFORMABLE_PROPS, function (obj, key) {\n    obj[key] = true;\n    return obj;\n}, { ignore: false });\nvar tmpTextPosCalcRes = {};\nvar tmpBoundingRect = new BoundingRect(0, 0, 0, 0);\nvar Element = (function () {\n    function Element(props) {\n        this.id = guid();\n        this.animators = [];\n        this.currentStates = [];\n        this.states = {};\n        this._init(props);\n    }\n    Element.prototype._init = function (props) {\n        this.attr(props);\n    };\n    Element.prototype.drift = function (dx, dy, e) {\n        switch (this.draggable) {\n            case 'horizontal':\n                dy = 0;\n                break;\n            case 'vertical':\n                dx = 0;\n                break;\n        }\n        var m = this.transform;\n        if (!m) {\n            m = this.transform = [1, 0, 0, 1, 0, 0];\n        }\n        m[4] += dx;\n        m[5] += dy;\n        this.decomposeTransform();\n        this.markRedraw();\n    };\n    Element.prototype.beforeUpdate = function () { };\n    Element.prototype.afterUpdate = function () { };\n    Element.prototype.update = function () {\n        this.updateTransform();\n        if (this.__dirty) {\n            this.updateInnerText();\n        }\n    };\n    Element.prototype.updateInnerText = function (forceUpdate) {\n        var textEl = this._textContent;\n        if (textEl && (!textEl.ignore || forceUpdate)) {\n            if (!this.textConfig) {\n                this.textConfig = {};\n            }\n            var textConfig = this.textConfig;\n            var isLocal = textConfig.local;\n            var innerTransformable = textEl.innerTransformable;\n            var textAlign = void 0;\n            var textVerticalAlign = void 0;\n            var textStyleChanged = false;\n            innerTransformable.parent = isLocal ? this : null;\n            var innerOrigin = false;\n            innerTransformable.copyTransform(textEl);\n            if (textConfig.position != null) {\n                var layoutRect = tmpBoundingRect;\n                if (textConfig.layoutRect) {\n                    layoutRect.copy(textConfig.layoutRect);\n                }\n                else {\n                    layoutRect.copy(this.getBoundingRect());\n                }\n                if (!isLocal) {\n                    layoutRect.applyTransform(this.transform);\n                }\n                if (this.calculateTextPosition) {\n                    this.calculateTextPosition(tmpTextPosCalcRes, textConfig, layoutRect);\n                }\n                else {\n                    calculateTextPosition(tmpTextPosCalcRes, textConfig, layoutRect);\n                }\n                innerTransformable.x = tmpTextPosCalcRes.x;\n                innerTransformable.y = tmpTextPosCalcRes.y;\n                textAlign = tmpTextPosCalcRes.align;\n                textVerticalAlign = tmpTextPosCalcRes.verticalAlign;\n                var textOrigin = textConfig.origin;\n                if (textOrigin && textConfig.rotation != null) {\n                    var relOriginX = void 0;\n                    var relOriginY = void 0;\n                    if (textOrigin === 'center') {\n                        relOriginX = layoutRect.width * 0.5;\n                        relOriginY = layoutRect.height * 0.5;\n                    }\n                    else {\n                        relOriginX = parsePercent(textOrigin[0], layoutRect.width);\n                        relOriginY = parsePercent(textOrigin[1], layoutRect.height);\n                    }\n                    innerOrigin = true;\n                    innerTransformable.originX = -innerTransformable.x + relOriginX + (isLocal ? 0 : layoutRect.x);\n                    innerTransformable.originY = -innerTransformable.y + relOriginY + (isLocal ? 0 : layoutRect.y);\n                }\n            }\n            if (textConfig.rotation != null) {\n                innerTransformable.rotation = textConfig.rotation;\n            }\n            var textOffset = textConfig.offset;\n            if (textOffset) {\n                innerTransformable.x += textOffset[0];\n                innerTransformable.y += textOffset[1];\n                if (!innerOrigin) {\n                    innerTransformable.originX = -textOffset[0];\n                    innerTransformable.originY = -textOffset[1];\n                }\n            }\n            var isInside = textConfig.inside == null\n                ? (typeof textConfig.position === 'string' && textConfig.position.indexOf('inside') >= 0)\n                : textConfig.inside;\n            var innerTextDefaultStyle = this._innerTextDefaultStyle || (this._innerTextDefaultStyle = {});\n            var textFill = void 0;\n            var textStroke = void 0;\n            var autoStroke = void 0;\n            if (isInside && this.canBeInsideText()) {\n                textFill = textConfig.insideFill;\n                textStroke = textConfig.insideStroke;\n                if (textFill == null || textFill === 'auto') {\n                    textFill = this.getInsideTextFill();\n                }\n                if (textStroke == null || textStroke === 'auto') {\n                    textStroke = this.getInsideTextStroke(textFill);\n                    autoStroke = true;\n                }\n            }\n            else {\n                textFill = textConfig.outsideFill;\n                textStroke = textConfig.outsideStroke;\n                if (textFill == null || textFill === 'auto') {\n                    textFill = this.getOutsideFill();\n                }\n                if (textStroke == null || textStroke === 'auto') {\n                    textStroke = this.getOutsideStroke(textFill);\n                    autoStroke = true;\n                }\n            }\n            textFill = textFill || '#000';\n            if (textFill !== innerTextDefaultStyle.fill\n                || textStroke !== innerTextDefaultStyle.stroke\n                || autoStroke !== innerTextDefaultStyle.autoStroke\n                || textAlign !== innerTextDefaultStyle.align\n                || textVerticalAlign !== innerTextDefaultStyle.verticalAlign) {\n                textStyleChanged = true;\n                innerTextDefaultStyle.fill = textFill;\n                innerTextDefaultStyle.stroke = textStroke;\n                innerTextDefaultStyle.autoStroke = autoStroke;\n                innerTextDefaultStyle.align = textAlign;\n                innerTextDefaultStyle.verticalAlign = textVerticalAlign;\n                textEl.setDefaultTextStyle(innerTextDefaultStyle);\n            }\n            textEl.__dirty |= REDRAW_BIT;\n            if (textStyleChanged) {\n                textEl.dirtyStyle(true);\n            }\n        }\n    };\n    Element.prototype.canBeInsideText = function () {\n        return true;\n    };\n    Element.prototype.getInsideTextFill = function () {\n        return '#fff';\n    };\n    Element.prototype.getInsideTextStroke = function (textFill) {\n        return '#000';\n    };\n    Element.prototype.getOutsideFill = function () {\n        return this.__zr && this.__zr.isDarkMode() ? LIGHT_LABEL_COLOR : DARK_LABEL_COLOR;\n    };\n    Element.prototype.getOutsideStroke = function (textFill) {\n        var backgroundColor = this.__zr && this.__zr.getBackgroundColor();\n        var colorArr = typeof backgroundColor === 'string' && parse(backgroundColor);\n        if (!colorArr) {\n            colorArr = [255, 255, 255, 1];\n        }\n        var alpha = colorArr[3];\n        var isDark = this.__zr.isDarkMode();\n        for (var i = 0; i < 3; i++) {\n            colorArr[i] = colorArr[i] * alpha + (isDark ? 0 : 255) * (1 - alpha);\n        }\n        colorArr[3] = 1;\n        return stringify(colorArr, 'rgba');\n    };\n    Element.prototype.traverse = function (cb, context) { };\n    Element.prototype.attrKV = function (key, value) {\n        if (key === 'textConfig') {\n            this.setTextConfig(value);\n        }\n        else if (key === 'textContent') {\n            this.setTextContent(value);\n        }\n        else if (key === 'clipPath') {\n            this.setClipPath(value);\n        }\n        else if (key === 'extra') {\n            this.extra = this.extra || {};\n            extend(this.extra, value);\n        }\n        else {\n            this[key] = value;\n        }\n    };\n    Element.prototype.hide = function () {\n        this.ignore = true;\n        this.markRedraw();\n    };\n    Element.prototype.show = function () {\n        this.ignore = false;\n        this.markRedraw();\n    };\n    Element.prototype.attr = function (keyOrObj, value) {\n        if (typeof keyOrObj === 'string') {\n            this.attrKV(keyOrObj, value);\n        }\n        else if (isObject(keyOrObj)) {\n            var obj = keyOrObj;\n            var keysArr = keys(obj);\n            for (var i = 0; i < keysArr.length; i++) {\n                var key = keysArr[i];\n                this.attrKV(key, keyOrObj[key]);\n            }\n        }\n        this.markRedraw();\n        return this;\n    };\n    Element.prototype.saveCurrentToNormalState = function (toState) {\n        this._innerSaveToNormal(toState);\n        var normalState = this._normalState;\n        for (var i = 0; i < this.animators.length; i++) {\n            var animator = this.animators[i];\n            var fromStateTransition = animator.__fromStateTransition;\n            if (animator.getLoop() || fromStateTransition && fromStateTransition !== PRESERVED_NORMAL_STATE) {\n                continue;\n            }\n            var targetName = animator.targetName;\n            var target = targetName\n                ? normalState[targetName] : normalState;\n            animator.saveTo(target);\n        }\n    };\n    Element.prototype._innerSaveToNormal = function (toState) {\n        var normalState = this._normalState;\n        if (!normalState) {\n            normalState = this._normalState = {};\n        }\n        if (toState.textConfig && !normalState.textConfig) {\n            normalState.textConfig = this.textConfig;\n        }\n        this._savePrimaryToNormal(toState, normalState, PRIMARY_STATES_KEYS);\n    };\n    Element.prototype._savePrimaryToNormal = function (toState, normalState, primaryKeys) {\n        for (var i = 0; i < primaryKeys.length; i++) {\n            var key = primaryKeys[i];\n            if (toState[key] != null && !(key in normalState)) {\n                normalState[key] = this[key];\n            }\n        }\n    };\n    Element.prototype.hasState = function () {\n        return this.currentStates.length > 0;\n    };\n    Element.prototype.getState = function (name) {\n        return this.states[name];\n    };\n    Element.prototype.ensureState = function (name) {\n        var states = this.states;\n        if (!states[name]) {\n            states[name] = {};\n        }\n        return states[name];\n    };\n    Element.prototype.clearStates = function (noAnimation) {\n        this.useState(PRESERVED_NORMAL_STATE, false, noAnimation);\n    };\n    Element.prototype.useState = function (stateName, keepCurrentStates, noAnimation, forceUseHoverLayer) {\n        var toNormalState = stateName === PRESERVED_NORMAL_STATE;\n        var hasStates = this.hasState();\n        if (!hasStates && toNormalState) {\n            return;\n        }\n        var currentStates = this.currentStates;\n        var animationCfg = this.stateTransition;\n        if (indexOf(currentStates, stateName) >= 0 && (keepCurrentStates || currentStates.length === 1)) {\n            return;\n        }\n        var state;\n        if (this.stateProxy && !toNormalState) {\n            state = this.stateProxy(stateName);\n        }\n        if (!state) {\n            state = (this.states && this.states[stateName]);\n        }\n        if (!state && !toNormalState) {\n            logError(\"State \" + stateName + \" not exists.\");\n            return;\n        }\n        if (!toNormalState) {\n            this.saveCurrentToNormalState(state);\n        }\n        var useHoverLayer = !!((state && state.hoverLayer) || forceUseHoverLayer);\n        if (useHoverLayer) {\n            this._toggleHoverLayerFlag(true);\n        }\n        this._applyStateObj(stateName, state, this._normalState, keepCurrentStates, !noAnimation && !this.__inHover && animationCfg && animationCfg.duration > 0, animationCfg);\n        var textContent = this._textContent;\n        var textGuide = this._textGuide;\n        if (textContent) {\n            textContent.useState(stateName, keepCurrentStates, noAnimation, useHoverLayer);\n        }\n        if (textGuide) {\n            textGuide.useState(stateName, keepCurrentStates, noAnimation, useHoverLayer);\n        }\n        if (toNormalState) {\n            this.currentStates = [];\n            this._normalState = {};\n        }\n        else {\n            if (!keepCurrentStates) {\n                this.currentStates = [stateName];\n            }\n            else {\n                this.currentStates.push(stateName);\n            }\n        }\n        this._updateAnimationTargets();\n        this.markRedraw();\n        if (!useHoverLayer && this.__inHover) {\n            this._toggleHoverLayerFlag(false);\n            this.__dirty &= ~REDRAW_BIT;\n        }\n        return state;\n    };\n    Element.prototype.useStates = function (states, noAnimation, forceUseHoverLayer) {\n        if (!states.length) {\n            this.clearStates();\n        }\n        else {\n            var stateObjects = [];\n            var currentStates = this.currentStates;\n            var len = states.length;\n            var notChange = len === currentStates.length;\n            if (notChange) {\n                for (var i = 0; i < len; i++) {\n                    if (states[i] !== currentStates[i]) {\n                        notChange = false;\n                        break;\n                    }\n                }\n            }\n            if (notChange) {\n                return;\n            }\n            for (var i = 0; i < len; i++) {\n                var stateName = states[i];\n                var stateObj = void 0;\n                if (this.stateProxy) {\n                    stateObj = this.stateProxy(stateName, states);\n                }\n                if (!stateObj) {\n                    stateObj = this.states[stateName];\n                }\n                if (stateObj) {\n                    stateObjects.push(stateObj);\n                }\n            }\n            var lastStateObj = stateObjects[len - 1];\n            var useHoverLayer = !!((lastStateObj && lastStateObj.hoverLayer) || forceUseHoverLayer);\n            if (useHoverLayer) {\n                this._toggleHoverLayerFlag(true);\n            }\n            var mergedState = this._mergeStates(stateObjects);\n            var animationCfg = this.stateTransition;\n            this.saveCurrentToNormalState(mergedState);\n            this._applyStateObj(states.join(','), mergedState, this._normalState, false, !noAnimation && !this.__inHover && animationCfg && animationCfg.duration > 0, animationCfg);\n            var textContent = this._textContent;\n            var textGuide = this._textGuide;\n            if (textContent) {\n                textContent.useStates(states, noAnimation, useHoverLayer);\n            }\n            if (textGuide) {\n                textGuide.useStates(states, noAnimation, useHoverLayer);\n            }\n            this._updateAnimationTargets();\n            this.currentStates = states.slice();\n            this.markRedraw();\n            if (!useHoverLayer && this.__inHover) {\n                this._toggleHoverLayerFlag(false);\n                this.__dirty &= ~REDRAW_BIT;\n            }\n        }\n    };\n    Element.prototype.isSilent = function () {\n        var isSilent = this.silent;\n        var ancestor = this.parent;\n        while (!isSilent && ancestor) {\n            if (ancestor.silent) {\n                isSilent = true;\n                break;\n            }\n            ancestor = ancestor.parent;\n        }\n        return isSilent;\n    };\n    Element.prototype._updateAnimationTargets = function () {\n        for (var i = 0; i < this.animators.length; i++) {\n            var animator = this.animators[i];\n            if (animator.targetName) {\n                animator.changeTarget(this[animator.targetName]);\n            }\n        }\n    };\n    Element.prototype.removeState = function (state) {\n        var idx = indexOf(this.currentStates, state);\n        if (idx >= 0) {\n            var currentStates = this.currentStates.slice();\n            currentStates.splice(idx, 1);\n            this.useStates(currentStates);\n        }\n    };\n    Element.prototype.replaceState = function (oldState, newState, forceAdd) {\n        var currentStates = this.currentStates.slice();\n        var idx = indexOf(currentStates, oldState);\n        var newStateExists = indexOf(currentStates, newState) >= 0;\n        if (idx >= 0) {\n            if (!newStateExists) {\n                currentStates[idx] = newState;\n            }\n            else {\n                currentStates.splice(idx, 1);\n            }\n        }\n        else if (forceAdd && !newStateExists) {\n            currentStates.push(newState);\n        }\n        this.useStates(currentStates);\n    };\n    Element.prototype.toggleState = function (state, enable) {\n        if (enable) {\n            this.useState(state, true);\n        }\n        else {\n            this.removeState(state);\n        }\n    };\n    Element.prototype._mergeStates = function (states) {\n        var mergedState = {};\n        var mergedTextConfig;\n        for (var i = 0; i < states.length; i++) {\n            var state = states[i];\n            extend(mergedState, state);\n            if (state.textConfig) {\n                mergedTextConfig = mergedTextConfig || {};\n                extend(mergedTextConfig, state.textConfig);\n            }\n        }\n        if (mergedTextConfig) {\n            mergedState.textConfig = mergedTextConfig;\n        }\n        return mergedState;\n    };\n    Element.prototype._applyStateObj = function (stateName, state, normalState, keepCurrentStates, transition, animationCfg) {\n        var needsRestoreToNormal = !(state && keepCurrentStates);\n        if (state && state.textConfig) {\n            this.textConfig = extend({}, keepCurrentStates ? this.textConfig : normalState.textConfig);\n            extend(this.textConfig, state.textConfig);\n        }\n        else if (needsRestoreToNormal) {\n            if (normalState.textConfig) {\n                this.textConfig = normalState.textConfig;\n            }\n        }\n        var transitionTarget = {};\n        var hasTransition = false;\n        for (var i = 0; i < PRIMARY_STATES_KEYS.length; i++) {\n            var key = PRIMARY_STATES_KEYS[i];\n            var propNeedsTransition = transition && DEFAULT_ANIMATABLE_MAP[key];\n            if (state && state[key] != null) {\n                if (propNeedsTransition) {\n                    hasTransition = true;\n                    transitionTarget[key] = state[key];\n                }\n                else {\n                    this[key] = state[key];\n                }\n            }\n            else if (needsRestoreToNormal) {\n                if (normalState[key] != null) {\n                    if (propNeedsTransition) {\n                        hasTransition = true;\n                        transitionTarget[key] = normalState[key];\n                    }\n                    else {\n                        this[key] = normalState[key];\n                    }\n                }\n            }\n        }\n        if (!transition) {\n            for (var i = 0; i < this.animators.length; i++) {\n                var animator = this.animators[i];\n                var targetName = animator.targetName;\n                if (!animator.getLoop()) {\n                    animator.__changeFinalValue(targetName\n                        ? (state || normalState)[targetName]\n                        : (state || normalState));\n                }\n            }\n        }\n        if (hasTransition) {\n            this._transitionState(stateName, transitionTarget, animationCfg);\n        }\n    };\n    Element.prototype._attachComponent = function (componentEl) {\n        if (componentEl.__zr && !componentEl.__hostTarget) {\n            if (process.env.NODE_ENV !== 'production') {\n                throw new Error('Text element has been added to zrender.');\n            }\n            return;\n        }\n        if (componentEl === this) {\n            if (process.env.NODE_ENV !== 'production') {\n                throw new Error('Recursive component attachment.');\n            }\n            return;\n        }\n        var zr = this.__zr;\n        if (zr) {\n            componentEl.addSelfToZr(zr);\n        }\n        componentEl.__zr = zr;\n        componentEl.__hostTarget = this;\n    };\n    Element.prototype._detachComponent = function (componentEl) {\n        if (componentEl.__zr) {\n            componentEl.removeSelfFromZr(componentEl.__zr);\n        }\n        componentEl.__zr = null;\n        componentEl.__hostTarget = null;\n    };\n    Element.prototype.getClipPath = function () {\n        return this._clipPath;\n    };\n    Element.prototype.setClipPath = function (clipPath) {\n        if (this._clipPath && this._clipPath !== clipPath) {\n            this.removeClipPath();\n        }\n        this._attachComponent(clipPath);\n        this._clipPath = clipPath;\n        this.markRedraw();\n    };\n    Element.prototype.removeClipPath = function () {\n        var clipPath = this._clipPath;\n        if (clipPath) {\n            this._detachComponent(clipPath);\n            this._clipPath = null;\n            this.markRedraw();\n        }\n    };\n    Element.prototype.getTextContent = function () {\n        return this._textContent;\n    };\n    Element.prototype.setTextContent = function (textEl) {\n        var previousTextContent = this._textContent;\n        if (previousTextContent === textEl) {\n            return;\n        }\n        if (previousTextContent && previousTextContent !== textEl) {\n            this.removeTextContent();\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            if (textEl.__zr && !textEl.__hostTarget) {\n                throw new Error('Text element has been added to zrender.');\n            }\n        }\n        textEl.innerTransformable = new Transformable();\n        this._attachComponent(textEl);\n        this._textContent = textEl;\n        this.markRedraw();\n    };\n    Element.prototype.setTextConfig = function (cfg) {\n        if (!this.textConfig) {\n            this.textConfig = {};\n        }\n        extend(this.textConfig, cfg);\n        this.markRedraw();\n    };\n    Element.prototype.removeTextConfig = function () {\n        this.textConfig = null;\n        this.markRedraw();\n    };\n    Element.prototype.removeTextContent = function () {\n        var textEl = this._textContent;\n        if (textEl) {\n            textEl.innerTransformable = null;\n            this._detachComponent(textEl);\n            this._textContent = null;\n            this._innerTextDefaultStyle = null;\n            this.markRedraw();\n        }\n    };\n    Element.prototype.getTextGuideLine = function () {\n        return this._textGuide;\n    };\n    Element.prototype.setTextGuideLine = function (guideLine) {\n        if (this._textGuide && this._textGuide !== guideLine) {\n            this.removeTextGuideLine();\n        }\n        this._attachComponent(guideLine);\n        this._textGuide = guideLine;\n        this.markRedraw();\n    };\n    Element.prototype.removeTextGuideLine = function () {\n        var textGuide = this._textGuide;\n        if (textGuide) {\n            this._detachComponent(textGuide);\n            this._textGuide = null;\n            this.markRedraw();\n        }\n    };\n    Element.prototype.markRedraw = function () {\n        this.__dirty |= REDRAW_BIT;\n        var zr = this.__zr;\n        if (zr) {\n            if (this.__inHover) {\n                zr.refreshHover();\n            }\n            else {\n                zr.refresh();\n            }\n        }\n        if (this.__hostTarget) {\n            this.__hostTarget.markRedraw();\n        }\n    };\n    Element.prototype.dirty = function () {\n        this.markRedraw();\n    };\n    Element.prototype._toggleHoverLayerFlag = function (inHover) {\n        this.__inHover = inHover;\n        var textContent = this._textContent;\n        var textGuide = this._textGuide;\n        if (textContent) {\n            textContent.__inHover = inHover;\n        }\n        if (textGuide) {\n            textGuide.__inHover = inHover;\n        }\n    };\n    Element.prototype.addSelfToZr = function (zr) {\n        if (this.__zr === zr) {\n            return;\n        }\n        this.__zr = zr;\n        var animators = this.animators;\n        if (animators) {\n            for (var i = 0; i < animators.length; i++) {\n                zr.animation.addAnimator(animators[i]);\n            }\n        }\n        if (this._clipPath) {\n            this._clipPath.addSelfToZr(zr);\n        }\n        if (this._textContent) {\n            this._textContent.addSelfToZr(zr);\n        }\n        if (this._textGuide) {\n            this._textGuide.addSelfToZr(zr);\n        }\n    };\n    Element.prototype.removeSelfFromZr = function (zr) {\n        if (!this.__zr) {\n            return;\n        }\n        this.__zr = null;\n        var animators = this.animators;\n        if (animators) {\n            for (var i = 0; i < animators.length; i++) {\n                zr.animation.removeAnimator(animators[i]);\n            }\n        }\n        if (this._clipPath) {\n            this._clipPath.removeSelfFromZr(zr);\n        }\n        if (this._textContent) {\n            this._textContent.removeSelfFromZr(zr);\n        }\n        if (this._textGuide) {\n            this._textGuide.removeSelfFromZr(zr);\n        }\n    };\n    Element.prototype.animate = function (key, loop, allowDiscreteAnimation) {\n        var target = key ? this[key] : this;\n        if (process.env.NODE_ENV !== 'production') {\n            if (!target) {\n                logError('Property \"'\n                    + key\n                    + '\" is not existed in element '\n                    + this.id);\n                return;\n            }\n        }\n        var animator = new Animator(target, loop, allowDiscreteAnimation);\n        key && (animator.targetName = key);\n        this.addAnimator(animator, key);\n        return animator;\n    };\n    Element.prototype.addAnimator = function (animator, key) {\n        var zr = this.__zr;\n        var el = this;\n        animator.during(function () {\n            el.updateDuringAnimation(key);\n        }).done(function () {\n            var animators = el.animators;\n            var idx = indexOf(animators, animator);\n            if (idx >= 0) {\n                animators.splice(idx, 1);\n            }\n        });\n        this.animators.push(animator);\n        if (zr) {\n            zr.animation.addAnimator(animator);\n        }\n        zr && zr.wakeUp();\n    };\n    Element.prototype.updateDuringAnimation = function (key) {\n        this.markRedraw();\n    };\n    Element.prototype.stopAnimation = function (scope, forwardToLast) {\n        var animators = this.animators;\n        var len = animators.length;\n        var leftAnimators = [];\n        for (var i = 0; i < len; i++) {\n            var animator = animators[i];\n            if (!scope || scope === animator.scope) {\n                animator.stop(forwardToLast);\n            }\n            else {\n                leftAnimators.push(animator);\n            }\n        }\n        this.animators = leftAnimators;\n        return this;\n    };\n    Element.prototype.animateTo = function (target, cfg, animationProps) {\n        animateTo(this, target, cfg, animationProps);\n    };\n    Element.prototype.animateFrom = function (target, cfg, animationProps) {\n        animateTo(this, target, cfg, animationProps, true);\n    };\n    Element.prototype._transitionState = function (stateName, target, cfg, animationProps) {\n        var animators = animateTo(this, target, cfg, animationProps);\n        for (var i = 0; i < animators.length; i++) {\n            animators[i].__fromStateTransition = stateName;\n        }\n    };\n    Element.prototype.getBoundingRect = function () {\n        return null;\n    };\n    Element.prototype.getPaintRect = function () {\n        return null;\n    };\n    Element.initDefaultProps = (function () {\n        var elProto = Element.prototype;\n        elProto.type = 'element';\n        elProto.name = '';\n        elProto.ignore =\n            elProto.silent =\n                elProto.isGroup =\n                    elProto.draggable =\n                        elProto.dragging =\n                            elProto.ignoreClip =\n                                elProto.__inHover = false;\n        elProto.__dirty = REDRAW_BIT;\n        var logs = {};\n        function logDeprecatedError(key, xKey, yKey) {\n            if (!logs[key + xKey + yKey]) {\n                console.warn(\"DEPRECATED: '\" + key + \"' has been deprecated. use '\" + xKey + \"', '\" + yKey + \"' instead\");\n                logs[key + xKey + yKey] = true;\n            }\n        }\n        function createLegacyProperty(key, privateKey, xKey, yKey) {\n            Object.defineProperty(elProto, key, {\n                get: function () {\n                    if (process.env.NODE_ENV !== 'production') {\n                        logDeprecatedError(key, xKey, yKey);\n                    }\n                    if (!this[privateKey]) {\n                        var pos = this[privateKey] = [];\n                        enhanceArray(this, pos);\n                    }\n                    return this[privateKey];\n                },\n                set: function (pos) {\n                    if (process.env.NODE_ENV !== 'production') {\n                        logDeprecatedError(key, xKey, yKey);\n                    }\n                    this[xKey] = pos[0];\n                    this[yKey] = pos[1];\n                    this[privateKey] = pos;\n                    enhanceArray(this, pos);\n                }\n            });\n            function enhanceArray(self, pos) {\n                Object.defineProperty(pos, 0, {\n                    get: function () {\n                        return self[xKey];\n                    },\n                    set: function (val) {\n                        self[xKey] = val;\n                    }\n                });\n                Object.defineProperty(pos, 1, {\n                    get: function () {\n                        return self[yKey];\n                    },\n                    set: function (val) {\n                        self[yKey] = val;\n                    }\n                });\n            }\n        }\n        if (Object.defineProperty) {\n            createLegacyProperty('position', '_legacyPos', 'x', 'y');\n            createLegacyProperty('scale', '_legacyScale', 'scaleX', 'scaleY');\n            createLegacyProperty('origin', '_legacyOrigin', 'originX', 'originY');\n        }\n    })();\n    return Element;\n}());\nmixin(Element, Eventful);\nmixin(Element, Transformable);\nfunction animateTo(animatable, target, cfg, animationProps, reverse) {\n    cfg = cfg || {};\n    var animators = [];\n    animateToShallow(animatable, '', animatable, target, cfg, animationProps, animators, reverse);\n    var finishCount = animators.length;\n    var doneHappened = false;\n    var cfgDone = cfg.done;\n    var cfgAborted = cfg.aborted;\n    var doneCb = function () {\n        doneHappened = true;\n        finishCount--;\n        if (finishCount <= 0) {\n            doneHappened\n                ? (cfgDone && cfgDone())\n                : (cfgAborted && cfgAborted());\n        }\n    };\n    var abortedCb = function () {\n        finishCount--;\n        if (finishCount <= 0) {\n            doneHappened\n                ? (cfgDone && cfgDone())\n                : (cfgAborted && cfgAborted());\n        }\n    };\n    if (!finishCount) {\n        cfgDone && cfgDone();\n    }\n    if (animators.length > 0 && cfg.during) {\n        animators[0].during(function (target, percent) {\n            cfg.during(percent);\n        });\n    }\n    for (var i = 0; i < animators.length; i++) {\n        var animator = animators[i];\n        if (doneCb) {\n            animator.done(doneCb);\n        }\n        if (abortedCb) {\n            animator.aborted(abortedCb);\n        }\n        if (cfg.force) {\n            animator.duration(cfg.duration);\n        }\n        animator.start(cfg.easing);\n    }\n    return animators;\n}\nfunction copyArrShallow(source, target, len) {\n    for (var i = 0; i < len; i++) {\n        source[i] = target[i];\n    }\n}\nfunction is2DArray(value) {\n    return isArrayLike(value[0]);\n}\nfunction copyValue(target, source, key) {\n    if (isArrayLike(source[key])) {\n        if (!isArrayLike(target[key])) {\n            target[key] = [];\n        }\n        if (isTypedArray(source[key])) {\n            var len = source[key].length;\n            if (target[key].length !== len) {\n                target[key] = new (source[key].constructor)(len);\n                copyArrShallow(target[key], source[key], len);\n            }\n        }\n        else {\n            var sourceArr = source[key];\n            var targetArr = target[key];\n            var len0 = sourceArr.length;\n            if (is2DArray(sourceArr)) {\n                var len1 = sourceArr[0].length;\n                for (var i = 0; i < len0; i++) {\n                    if (!targetArr[i]) {\n                        targetArr[i] = Array.prototype.slice.call(sourceArr[i]);\n                    }\n                    else {\n                        copyArrShallow(targetArr[i], sourceArr[i], len1);\n                    }\n                }\n            }\n            else {\n                copyArrShallow(targetArr, sourceArr, len0);\n            }\n            targetArr.length = sourceArr.length;\n        }\n    }\n    else {\n        target[key] = source[key];\n    }\n}\nfunction isValueSame(val1, val2) {\n    return val1 === val2\n        || isArrayLike(val1) && isArrayLike(val2) && is1DArraySame(val1, val2);\n}\nfunction is1DArraySame(arr0, arr1) {\n    var len = arr0.length;\n    if (len !== arr1.length) {\n        return false;\n    }\n    for (var i = 0; i < len; i++) {\n        if (arr0[i] !== arr1[i]) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction animateToShallow(animatable, topKey, animateObj, target, cfg, animationProps, animators, reverse) {\n    var targetKeys = keys(target);\n    var duration = cfg.duration;\n    var delay = cfg.delay;\n    var additive = cfg.additive;\n    var setToFinal = cfg.setToFinal;\n    var animateAll = !isObject(animationProps);\n    var existsAnimators = animatable.animators;\n    var animationKeys = [];\n    for (var k = 0; k < targetKeys.length; k++) {\n        var innerKey = targetKeys[k];\n        var targetVal = target[innerKey];\n        if (targetVal != null && animateObj[innerKey] != null\n            && (animateAll || animationProps[innerKey])) {\n            if (isObject(targetVal)\n                && !isArrayLike(targetVal)\n                && !isGradientObject(targetVal)) {\n                if (topKey) {\n                    if (!reverse) {\n                        animateObj[innerKey] = targetVal;\n                        animatable.updateDuringAnimation(topKey);\n                    }\n                    continue;\n                }\n                animateToShallow(animatable, innerKey, animateObj[innerKey], targetVal, cfg, animationProps && animationProps[innerKey], animators, reverse);\n            }\n            else {\n                animationKeys.push(innerKey);\n            }\n        }\n        else if (!reverse) {\n            animateObj[innerKey] = targetVal;\n            animatable.updateDuringAnimation(topKey);\n            animationKeys.push(innerKey);\n        }\n    }\n    var keyLen = animationKeys.length;\n    if (!additive && keyLen) {\n        for (var i = 0; i < existsAnimators.length; i++) {\n            var animator = existsAnimators[i];\n            if (animator.targetName === topKey) {\n                var allAborted = animator.stopTracks(animationKeys);\n                if (allAborted) {\n                    var idx = indexOf(existsAnimators, animator);\n                    existsAnimators.splice(idx, 1);\n                }\n            }\n        }\n    }\n    if (!cfg.force) {\n        animationKeys = filter(animationKeys, function (key) { return !isValueSame(target[key], animateObj[key]); });\n        keyLen = animationKeys.length;\n    }\n    if (keyLen > 0\n        || (cfg.force && !animators.length)) {\n        var revertedSource = void 0;\n        var reversedTarget = void 0;\n        var sourceClone = void 0;\n        if (reverse) {\n            reversedTarget = {};\n            if (setToFinal) {\n                revertedSource = {};\n            }\n            for (var i = 0; i < keyLen; i++) {\n                var innerKey = animationKeys[i];\n                reversedTarget[innerKey] = animateObj[innerKey];\n                if (setToFinal) {\n                    revertedSource[innerKey] = target[innerKey];\n                }\n                else {\n                    animateObj[innerKey] = target[innerKey];\n                }\n            }\n        }\n        else if (setToFinal) {\n            sourceClone = {};\n            for (var i = 0; i < keyLen; i++) {\n                var innerKey = animationKeys[i];\n                sourceClone[innerKey] = cloneValue(animateObj[innerKey]);\n                copyValue(animateObj, target, innerKey);\n            }\n        }\n        var animator = new Animator(animateObj, false, false, additive ? filter(existsAnimators, function (animator) { return animator.targetName === topKey; }) : null);\n        animator.targetName = topKey;\n        if (cfg.scope) {\n            animator.scope = cfg.scope;\n        }\n        if (setToFinal && revertedSource) {\n            animator.whenWithKeys(0, revertedSource, animationKeys);\n        }\n        if (sourceClone) {\n            animator.whenWithKeys(0, sourceClone, animationKeys);\n        }\n        animator.whenWithKeys(duration == null ? 500 : duration, reverse ? reversedTarget : target, animationKeys).delay(delay || 0);\n        animatable.addAnimator(animator, topKey);\n        animators.push(animator);\n    }\n}\nexport default Element;\n"], "mappings": "AAAA,OAAOA,aAAa,IAAIC,mBAAmB,QAAQ,yBAAyB;AAC5E,OAAOC,QAAQ,IAAIC,UAAU,QAAQ,yBAAyB;AAC9D,OAAOC,YAAY,MAAM,wBAAwB;AACjD,OAAOC,QAAQ,MAAM,oBAAoB;AACzC,SAASC,qBAAqB,EAAEC,YAAY,QAAQ,mBAAmB;AACvE,SAASC,IAAI,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,WAAW,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,MAAM,QAAQ,gBAAgB;AACpJ,SAASC,iBAAiB,EAAEC,gBAAgB,QAAQ,aAAa;AACjE,SAASC,KAAK,EAAEC,SAAS,QAAQ,iBAAiB;AAClD,SAASC,UAAU,QAAQ,wBAAwB;AACnD,OAAO,IAAIC,sBAAsB,GAAG,eAAe;AACnD,IAAIC,mBAAmB,GAAGzB,mBAAmB,CAAC0B,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC;AAChE,IAAIC,sBAAsB,GAAGT,MAAM,CAAClB,mBAAmB,EAAE,UAAU4B,GAAG,EAAEC,GAAG,EAAE;EACzED,GAAG,CAACC,GAAG,CAAC,GAAG,IAAI;EACf,OAAOD,GAAG;AACd,CAAC,EAAE;EAAEE,MAAM,EAAE;AAAM,CAAC,CAAC;AACrB,IAAIC,iBAAiB,GAAG,CAAC,CAAC;AAC1B,IAAIC,eAAe,GAAG,IAAI7B,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAClD,IAAI8B,OAAO,GAAI,YAAY;EACvB,SAASA,OAAOA,CAACC,KAAK,EAAE;IACpB,IAAI,CAACC,EAAE,GAAG5B,IAAI,CAAC,CAAC;IAChB,IAAI,CAAC6B,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC;IAChB,IAAI,CAACC,KAAK,CAACL,KAAK,CAAC;EACrB;EACAD,OAAO,CAACO,SAAS,CAACD,KAAK,GAAG,UAAUL,KAAK,EAAE;IACvC,IAAI,CAACO,IAAI,CAACP,KAAK,CAAC;EACpB,CAAC;EACDD,OAAO,CAACO,SAAS,CAACE,KAAK,GAAG,UAAUC,EAAE,EAAEC,EAAE,EAAEC,CAAC,EAAE;IAC3C,QAAQ,IAAI,CAACC,SAAS;MAClB,KAAK,YAAY;QACbF,EAAE,GAAG,CAAC;QACN;MACJ,KAAK,UAAU;QACXD,EAAE,GAAG,CAAC;QACN;IACR;IACA,IAAII,CAAC,GAAG,IAAI,CAACC,SAAS;IACtB,IAAI,CAACD,CAAC,EAAE;MACJA,CAAC,GAAG,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC3C;IACAD,CAAC,CAAC,CAAC,CAAC,IAAIJ,EAAE;IACVI,CAAC,CAAC,CAAC,CAAC,IAAIH,EAAE;IACV,IAAI,CAACK,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAACC,UAAU,CAAC,CAAC;EACrB,CAAC;EACDjB,OAAO,CAACO,SAAS,CAACW,YAAY,GAAG,YAAY,CAAE,CAAC;EAChDlB,OAAO,CAACO,SAAS,CAACY,WAAW,GAAG,YAAY,CAAE,CAAC;EAC/CnB,OAAO,CAACO,SAAS,CAACa,MAAM,GAAG,YAAY;IACnC,IAAI,CAACC,eAAe,CAAC,CAAC;IACtB,IAAI,IAAI,CAACC,OAAO,EAAE;MACd,IAAI,CAACC,eAAe,CAAC,CAAC;IAC1B;EACJ,CAAC;EACDvB,OAAO,CAACO,SAAS,CAACgB,eAAe,GAAG,UAAUC,WAAW,EAAE;IACvD,IAAIC,MAAM,GAAG,IAAI,CAACC,YAAY;IAC9B,IAAID,MAAM,KAAK,CAACA,MAAM,CAAC5B,MAAM,IAAI2B,WAAW,CAAC,EAAE;MAC3C,IAAI,CAAC,IAAI,CAACG,UAAU,EAAE;QAClB,IAAI,CAACA,UAAU,GAAG,CAAC,CAAC;MACxB;MACA,IAAIA,UAAU,GAAG,IAAI,CAACA,UAAU;MAChC,IAAIC,OAAO,GAAGD,UAAU,CAACE,KAAK;MAC9B,IAAIC,kBAAkB,GAAGL,MAAM,CAACK,kBAAkB;MAClD,IAAIC,SAAS,GAAG,KAAK,CAAC;MACtB,IAAIC,iBAAiB,GAAG,KAAK,CAAC;MAC9B,IAAIC,gBAAgB,GAAG,KAAK;MAC5BH,kBAAkB,CAACI,MAAM,GAAGN,OAAO,GAAG,IAAI,GAAG,IAAI;MACjD,IAAIO,WAAW,GAAG,KAAK;MACvBL,kBAAkB,CAACM,aAAa,CAACX,MAAM,CAAC;MACxC,IAAIE,UAAU,CAACU,QAAQ,IAAI,IAAI,EAAE;QAC7B,IAAIC,UAAU,GAAGvC,eAAe;QAChC,IAAI4B,UAAU,CAACW,UAAU,EAAE;UACvBA,UAAU,CAACC,IAAI,CAACZ,UAAU,CAACW,UAAU,CAAC;QAC1C,CAAC,MACI;UACDA,UAAU,CAACC,IAAI,CAAC,IAAI,CAACC,eAAe,CAAC,CAAC,CAAC;QAC3C;QACA,IAAI,CAACZ,OAAO,EAAE;UACVU,UAAU,CAACG,cAAc,CAAC,IAAI,CAAC1B,SAAS,CAAC;QAC7C;QACA,IAAI,IAAI,CAAC3C,qBAAqB,EAAE;UAC5B,IAAI,CAACA,qBAAqB,CAAC0B,iBAAiB,EAAE6B,UAAU,EAAEW,UAAU,CAAC;QACzE,CAAC,MACI;UACDlE,qBAAqB,CAAC0B,iBAAiB,EAAE6B,UAAU,EAAEW,UAAU,CAAC;QACpE;QACAR,kBAAkB,CAACY,CAAC,GAAG5C,iBAAiB,CAAC4C,CAAC;QAC1CZ,kBAAkB,CAACa,CAAC,GAAG7C,iBAAiB,CAAC6C,CAAC;QAC1CZ,SAAS,GAAGjC,iBAAiB,CAAC8C,KAAK;QACnCZ,iBAAiB,GAAGlC,iBAAiB,CAAC+C,aAAa;QACnD,IAAIC,UAAU,GAAGnB,UAAU,CAACoB,MAAM;QAClC,IAAID,UAAU,IAAInB,UAAU,CAACqB,QAAQ,IAAI,IAAI,EAAE;UAC3C,IAAIC,UAAU,GAAG,KAAK,CAAC;UACvB,IAAIC,UAAU,GAAG,KAAK,CAAC;UACvB,IAAIJ,UAAU,KAAK,QAAQ,EAAE;YACzBG,UAAU,GAAGX,UAAU,CAACa,KAAK,GAAG,GAAG;YACnCD,UAAU,GAAGZ,UAAU,CAACc,MAAM,GAAG,GAAG;UACxC,CAAC,MACI;YACDH,UAAU,GAAG5E,YAAY,CAACyE,UAAU,CAAC,CAAC,CAAC,EAAER,UAAU,CAACa,KAAK,CAAC;YAC1DD,UAAU,GAAG7E,YAAY,CAACyE,UAAU,CAAC,CAAC,CAAC,EAAER,UAAU,CAACc,MAAM,CAAC;UAC/D;UACAjB,WAAW,GAAG,IAAI;UAClBL,kBAAkB,CAACuB,OAAO,GAAG,CAACvB,kBAAkB,CAACY,CAAC,GAAGO,UAAU,IAAIrB,OAAO,GAAG,CAAC,GAAGU,UAAU,CAACI,CAAC,CAAC;UAC9FZ,kBAAkB,CAACwB,OAAO,GAAG,CAACxB,kBAAkB,CAACa,CAAC,GAAGO,UAAU,IAAItB,OAAO,GAAG,CAAC,GAAGU,UAAU,CAACK,CAAC,CAAC;QAClG;MACJ;MACA,IAAIhB,UAAU,CAACqB,QAAQ,IAAI,IAAI,EAAE;QAC7BlB,kBAAkB,CAACkB,QAAQ,GAAGrB,UAAU,CAACqB,QAAQ;MACrD;MACA,IAAIO,UAAU,GAAG5B,UAAU,CAAC6B,MAAM;MAClC,IAAID,UAAU,EAAE;QACZzB,kBAAkB,CAACY,CAAC,IAAIa,UAAU,CAAC,CAAC,CAAC;QACrCzB,kBAAkB,CAACa,CAAC,IAAIY,UAAU,CAAC,CAAC,CAAC;QACrC,IAAI,CAACpB,WAAW,EAAE;UACdL,kBAAkB,CAACuB,OAAO,GAAG,CAACE,UAAU,CAAC,CAAC,CAAC;UAC3CzB,kBAAkB,CAACwB,OAAO,GAAG,CAACC,UAAU,CAAC,CAAC,CAAC;QAC/C;MACJ;MACA,IAAIE,QAAQ,GAAG9B,UAAU,CAAC+B,MAAM,IAAI,IAAI,GACjC,OAAO/B,UAAU,CAACU,QAAQ,KAAK,QAAQ,IAAIV,UAAU,CAACU,QAAQ,CAAC3D,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,GACtFiD,UAAU,CAAC+B,MAAM;MACvB,IAAIC,qBAAqB,GAAG,IAAI,CAACC,sBAAsB,KAAK,IAAI,CAACA,sBAAsB,GAAG,CAAC,CAAC,CAAC;MAC7F,IAAIC,QAAQ,GAAG,KAAK,CAAC;MACrB,IAAIC,UAAU,GAAG,KAAK,CAAC;MACvB,IAAIC,UAAU,GAAG,KAAK,CAAC;MACvB,IAAIN,QAAQ,IAAI,IAAI,CAACO,eAAe,CAAC,CAAC,EAAE;QACpCH,QAAQ,GAAGlC,UAAU,CAACsC,UAAU;QAChCH,UAAU,GAAGnC,UAAU,CAACuC,YAAY;QACpC,IAAIL,QAAQ,IAAI,IAAI,IAAIA,QAAQ,KAAK,MAAM,EAAE;UACzCA,QAAQ,GAAG,IAAI,CAACM,iBAAiB,CAAC,CAAC;QACvC;QACA,IAAIL,UAAU,IAAI,IAAI,IAAIA,UAAU,KAAK,MAAM,EAAE;UAC7CA,UAAU,GAAG,IAAI,CAACM,mBAAmB,CAACP,QAAQ,CAAC;UAC/CE,UAAU,GAAG,IAAI;QACrB;MACJ,CAAC,MACI;QACDF,QAAQ,GAAGlC,UAAU,CAAC0C,WAAW;QACjCP,UAAU,GAAGnC,UAAU,CAAC2C,aAAa;QACrC,IAAIT,QAAQ,IAAI,IAAI,IAAIA,QAAQ,KAAK,MAAM,EAAE;UACzCA,QAAQ,GAAG,IAAI,CAACU,cAAc,CAAC,CAAC;QACpC;QACA,IAAIT,UAAU,IAAI,IAAI,IAAIA,UAAU,KAAK,MAAM,EAAE;UAC7CA,UAAU,GAAG,IAAI,CAACU,gBAAgB,CAACX,QAAQ,CAAC;UAC5CE,UAAU,GAAG,IAAI;QACrB;MACJ;MACAF,QAAQ,GAAGA,QAAQ,IAAI,MAAM;MAC7B,IAAIA,QAAQ,KAAKF,qBAAqB,CAACc,IAAI,IACpCX,UAAU,KAAKH,qBAAqB,CAACe,MAAM,IAC3CX,UAAU,KAAKJ,qBAAqB,CAACI,UAAU,IAC/ChC,SAAS,KAAK4B,qBAAqB,CAACf,KAAK,IACzCZ,iBAAiB,KAAK2B,qBAAqB,CAACd,aAAa,EAAE;QAC9DZ,gBAAgB,GAAG,IAAI;QACvB0B,qBAAqB,CAACc,IAAI,GAAGZ,QAAQ;QACrCF,qBAAqB,CAACe,MAAM,GAAGZ,UAAU;QACzCH,qBAAqB,CAACI,UAAU,GAAGA,UAAU;QAC7CJ,qBAAqB,CAACf,KAAK,GAAGb,SAAS;QACvC4B,qBAAqB,CAACd,aAAa,GAAGb,iBAAiB;QACvDP,MAAM,CAACkD,mBAAmB,CAAChB,qBAAqB,CAAC;MACrD;MACAlC,MAAM,CAACH,OAAO,IAAIhC,UAAU;MAC5B,IAAI2C,gBAAgB,EAAE;QAClBR,MAAM,CAACmD,UAAU,CAAC,IAAI,CAAC;MAC3B;IACJ;EACJ,CAAC;EACD5E,OAAO,CAACO,SAAS,CAACyD,eAAe,GAAG,YAAY;IAC5C,OAAO,IAAI;EACf,CAAC;EACDhE,OAAO,CAACO,SAAS,CAAC4D,iBAAiB,GAAG,YAAY;IAC9C,OAAO,MAAM;EACjB,CAAC;EACDnE,OAAO,CAACO,SAAS,CAAC6D,mBAAmB,GAAG,UAAUP,QAAQ,EAAE;IACxD,OAAO,MAAM;EACjB,CAAC;EACD7D,OAAO,CAACO,SAAS,CAACgE,cAAc,GAAG,YAAY;IAC3C,OAAO,IAAI,CAACM,IAAI,IAAI,IAAI,CAACA,IAAI,CAACC,UAAU,CAAC,CAAC,GAAG5F,iBAAiB,GAAGC,gBAAgB;EACrF,CAAC;EACDa,OAAO,CAACO,SAAS,CAACiE,gBAAgB,GAAG,UAAUX,QAAQ,EAAE;IACrD,IAAIkB,eAAe,GAAG,IAAI,CAACF,IAAI,IAAI,IAAI,CAACA,IAAI,CAACG,kBAAkB,CAAC,CAAC;IACjE,IAAIC,QAAQ,GAAG,OAAOF,eAAe,KAAK,QAAQ,IAAI3F,KAAK,CAAC2F,eAAe,CAAC;IAC5E,IAAI,CAACE,QAAQ,EAAE;MACXA,QAAQ,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IACjC;IACA,IAAIC,KAAK,GAAGD,QAAQ,CAAC,CAAC,CAAC;IACvB,IAAIE,MAAM,GAAG,IAAI,CAACN,IAAI,CAACC,UAAU,CAAC,CAAC;IACnC,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACxBH,QAAQ,CAACG,CAAC,CAAC,GAAGH,QAAQ,CAACG,CAAC,CAAC,GAAGF,KAAK,GAAG,CAACC,MAAM,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,GAAGD,KAAK,CAAC;IACxE;IACAD,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;IACf,OAAO5F,SAAS,CAAC4F,QAAQ,EAAE,MAAM,CAAC;EACtC,CAAC;EACDjF,OAAO,CAACO,SAAS,CAAC8E,QAAQ,GAAG,UAAUC,EAAE,EAAEC,OAAO,EAAE,CAAE,CAAC;EACvDvF,OAAO,CAACO,SAAS,CAACiF,MAAM,GAAG,UAAU5F,GAAG,EAAE6F,KAAK,EAAE;IAC7C,IAAI7F,GAAG,KAAK,YAAY,EAAE;MACtB,IAAI,CAAC8F,aAAa,CAACD,KAAK,CAAC;IAC7B,CAAC,MACI,IAAI7F,GAAG,KAAK,aAAa,EAAE;MAC5B,IAAI,CAAC+F,cAAc,CAACF,KAAK,CAAC;IAC9B,CAAC,MACI,IAAI7F,GAAG,KAAK,UAAU,EAAE;MACzB,IAAI,CAACgG,WAAW,CAACH,KAAK,CAAC;IAC3B,CAAC,MACI,IAAI7F,GAAG,KAAK,OAAO,EAAE;MACtB,IAAI,CAACiG,KAAK,GAAG,IAAI,CAACA,KAAK,IAAI,CAAC,CAAC;MAC7BpH,MAAM,CAAC,IAAI,CAACoH,KAAK,EAAEJ,KAAK,CAAC;IAC7B,CAAC,MACI;MACD,IAAI,CAAC7F,GAAG,CAAC,GAAG6F,KAAK;IACrB;EACJ,CAAC;EACDzF,OAAO,CAACO,SAAS,CAACuF,IAAI,GAAG,YAAY;IACjC,IAAI,CAACjG,MAAM,GAAG,IAAI;IAClB,IAAI,CAACoB,UAAU,CAAC,CAAC;EACrB,CAAC;EACDjB,OAAO,CAACO,SAAS,CAACwF,IAAI,GAAG,YAAY;IACjC,IAAI,CAAClG,MAAM,GAAG,KAAK;IACnB,IAAI,CAACoB,UAAU,CAAC,CAAC;EACrB,CAAC;EACDjB,OAAO,CAACO,SAAS,CAACC,IAAI,GAAG,UAAUwF,QAAQ,EAAEP,KAAK,EAAE;IAChD,IAAI,OAAOO,QAAQ,KAAK,QAAQ,EAAE;MAC9B,IAAI,CAACR,MAAM,CAACQ,QAAQ,EAAEP,KAAK,CAAC;IAChC,CAAC,MACI,IAAIlH,QAAQ,CAACyH,QAAQ,CAAC,EAAE;MACzB,IAAIrG,GAAG,GAAGqG,QAAQ;MAClB,IAAIC,OAAO,GAAGzH,IAAI,CAACmB,GAAG,CAAC;MACvB,KAAK,IAAIyF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGa,OAAO,CAACC,MAAM,EAAEd,CAAC,EAAE,EAAE;QACrC,IAAIxF,GAAG,GAAGqG,OAAO,CAACb,CAAC,CAAC;QACpB,IAAI,CAACI,MAAM,CAAC5F,GAAG,EAAEoG,QAAQ,CAACpG,GAAG,CAAC,CAAC;MACnC;IACJ;IACA,IAAI,CAACqB,UAAU,CAAC,CAAC;IACjB,OAAO,IAAI;EACf,CAAC;EACDjB,OAAO,CAACO,SAAS,CAAC4F,wBAAwB,GAAG,UAAUC,OAAO,EAAE;IAC5D,IAAI,CAACC,kBAAkB,CAACD,OAAO,CAAC;IAChC,IAAIE,WAAW,GAAG,IAAI,CAACC,YAAY;IACnC,KAAK,IAAInB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACjF,SAAS,CAAC+F,MAAM,EAAEd,CAAC,EAAE,EAAE;MAC5C,IAAIoB,QAAQ,GAAG,IAAI,CAACrG,SAAS,CAACiF,CAAC,CAAC;MAChC,IAAIqB,mBAAmB,GAAGD,QAAQ,CAACE,qBAAqB;MACxD,IAAIF,QAAQ,CAACG,OAAO,CAAC,CAAC,IAAIF,mBAAmB,IAAIA,mBAAmB,KAAKlH,sBAAsB,EAAE;QAC7F;MACJ;MACA,IAAIqH,UAAU,GAAGJ,QAAQ,CAACI,UAAU;MACpC,IAAIC,MAAM,GAAGD,UAAU,GACjBN,WAAW,CAACM,UAAU,CAAC,GAAGN,WAAW;MAC3CE,QAAQ,CAACM,MAAM,CAACD,MAAM,CAAC;IAC3B;EACJ,CAAC;EACD7G,OAAO,CAACO,SAAS,CAAC8F,kBAAkB,GAAG,UAAUD,OAAO,EAAE;IACtD,IAAIE,WAAW,GAAG,IAAI,CAACC,YAAY;IACnC,IAAI,CAACD,WAAW,EAAE;MACdA,WAAW,GAAG,IAAI,CAACC,YAAY,GAAG,CAAC,CAAC;IACxC;IACA,IAAIH,OAAO,CAACzE,UAAU,IAAI,CAAC2E,WAAW,CAAC3E,UAAU,EAAE;MAC/C2E,WAAW,CAAC3E,UAAU,GAAG,IAAI,CAACA,UAAU;IAC5C;IACA,IAAI,CAACoF,oBAAoB,CAACX,OAAO,EAAEE,WAAW,EAAE9G,mBAAmB,CAAC;EACxE,CAAC;EACDQ,OAAO,CAACO,SAAS,CAACwG,oBAAoB,GAAG,UAAUX,OAAO,EAAEE,WAAW,EAAEU,WAAW,EAAE;IAClF,KAAK,IAAI5B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4B,WAAW,CAACd,MAAM,EAAEd,CAAC,EAAE,EAAE;MACzC,IAAIxF,GAAG,GAAGoH,WAAW,CAAC5B,CAAC,CAAC;MACxB,IAAIgB,OAAO,CAACxG,GAAG,CAAC,IAAI,IAAI,IAAI,EAAEA,GAAG,IAAI0G,WAAW,CAAC,EAAE;QAC/CA,WAAW,CAAC1G,GAAG,CAAC,GAAG,IAAI,CAACA,GAAG,CAAC;MAChC;IACJ;EACJ,CAAC;EACDI,OAAO,CAACO,SAAS,CAAC0G,QAAQ,GAAG,YAAY;IACrC,OAAO,IAAI,CAAC7G,aAAa,CAAC8F,MAAM,GAAG,CAAC;EACxC,CAAC;EACDlG,OAAO,CAACO,SAAS,CAAC2G,QAAQ,GAAG,UAAUC,IAAI,EAAE;IACzC,OAAO,IAAI,CAAC9G,MAAM,CAAC8G,IAAI,CAAC;EAC5B,CAAC;EACDnH,OAAO,CAACO,SAAS,CAAC6G,WAAW,GAAG,UAAUD,IAAI,EAAE;IAC5C,IAAI9G,MAAM,GAAG,IAAI,CAACA,MAAM;IACxB,IAAI,CAACA,MAAM,CAAC8G,IAAI,CAAC,EAAE;MACf9G,MAAM,CAAC8G,IAAI,CAAC,GAAG,CAAC,CAAC;IACrB;IACA,OAAO9G,MAAM,CAAC8G,IAAI,CAAC;EACvB,CAAC;EACDnH,OAAO,CAACO,SAAS,CAAC8G,WAAW,GAAG,UAAUC,WAAW,EAAE;IACnD,IAAI,CAACC,QAAQ,CAAChI,sBAAsB,EAAE,KAAK,EAAE+H,WAAW,CAAC;EAC7D,CAAC;EACDtH,OAAO,CAACO,SAAS,CAACgH,QAAQ,GAAG,UAAUC,SAAS,EAAEC,iBAAiB,EAAEH,WAAW,EAAEI,kBAAkB,EAAE;IAClG,IAAIC,aAAa,GAAGH,SAAS,KAAKjI,sBAAsB;IACxD,IAAIqI,SAAS,GAAG,IAAI,CAACX,QAAQ,CAAC,CAAC;IAC/B,IAAI,CAACW,SAAS,IAAID,aAAa,EAAE;MAC7B;IACJ;IACA,IAAIvH,aAAa,GAAG,IAAI,CAACA,aAAa;IACtC,IAAIyH,YAAY,GAAG,IAAI,CAACC,eAAe;IACvC,IAAIpJ,OAAO,CAAC0B,aAAa,EAAEoH,SAAS,CAAC,IAAI,CAAC,KAAKC,iBAAiB,IAAIrH,aAAa,CAAC8F,MAAM,KAAK,CAAC,CAAC,EAAE;MAC7F;IACJ;IACA,IAAI6B,KAAK;IACT,IAAI,IAAI,CAACC,UAAU,IAAI,CAACL,aAAa,EAAE;MACnCI,KAAK,GAAG,IAAI,CAACC,UAAU,CAACR,SAAS,CAAC;IACtC;IACA,IAAI,CAACO,KAAK,EAAE;MACRA,KAAK,GAAI,IAAI,CAAC1H,MAAM,IAAI,IAAI,CAACA,MAAM,CAACmH,SAAS,CAAE;IACnD;IACA,IAAI,CAACO,KAAK,IAAI,CAACJ,aAAa,EAAE;MAC1BhJ,QAAQ,CAAC,QAAQ,GAAG6I,SAAS,GAAG,cAAc,CAAC;MAC/C;IACJ;IACA,IAAI,CAACG,aAAa,EAAE;MAChB,IAAI,CAACxB,wBAAwB,CAAC4B,KAAK,CAAC;IACxC;IACA,IAAIE,aAAa,GAAG,CAAC,EAAGF,KAAK,IAAIA,KAAK,CAACG,UAAU,IAAKR,kBAAkB,CAAC;IACzE,IAAIO,aAAa,EAAE;MACf,IAAI,CAACE,qBAAqB,CAAC,IAAI,CAAC;IACpC;IACA,IAAI,CAACC,cAAc,CAACZ,SAAS,EAAEO,KAAK,EAAE,IAAI,CAACxB,YAAY,EAAEkB,iBAAiB,EAAE,CAACH,WAAW,IAAI,CAAC,IAAI,CAACe,SAAS,IAAIR,YAAY,IAAIA,YAAY,CAACS,QAAQ,GAAG,CAAC,EAAET,YAAY,CAAC;IACvK,IAAIU,WAAW,GAAG,IAAI,CAAC7G,YAAY;IACnC,IAAI8G,SAAS,GAAG,IAAI,CAACC,UAAU;IAC/B,IAAIF,WAAW,EAAE;MACbA,WAAW,CAAChB,QAAQ,CAACC,SAAS,EAAEC,iBAAiB,EAAEH,WAAW,EAAEW,aAAa,CAAC;IAClF;IACA,IAAIO,SAAS,EAAE;MACXA,SAAS,CAACjB,QAAQ,CAACC,SAAS,EAAEC,iBAAiB,EAAEH,WAAW,EAAEW,aAAa,CAAC;IAChF;IACA,IAAIN,aAAa,EAAE;MACf,IAAI,CAACvH,aAAa,GAAG,EAAE;MACvB,IAAI,CAACmG,YAAY,GAAG,CAAC,CAAC;IAC1B,CAAC,MACI;MACD,IAAI,CAACkB,iBAAiB,EAAE;QACpB,IAAI,CAACrH,aAAa,GAAG,CAACoH,SAAS,CAAC;MACpC,CAAC,MACI;QACD,IAAI,CAACpH,aAAa,CAACsI,IAAI,CAAClB,SAAS,CAAC;MACtC;IACJ;IACA,IAAI,CAACmB,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAAC1H,UAAU,CAAC,CAAC;IACjB,IAAI,CAACgH,aAAa,IAAI,IAAI,CAACI,SAAS,EAAE;MAClC,IAAI,CAACF,qBAAqB,CAAC,KAAK,CAAC;MACjC,IAAI,CAAC7G,OAAO,IAAI,CAAChC,UAAU;IAC/B;IACA,OAAOyI,KAAK;EAChB,CAAC;EACD/H,OAAO,CAACO,SAAS,CAACqI,SAAS,GAAG,UAAUvI,MAAM,EAAEiH,WAAW,EAAEI,kBAAkB,EAAE;IAC7E,IAAI,CAACrH,MAAM,CAAC6F,MAAM,EAAE;MAChB,IAAI,CAACmB,WAAW,CAAC,CAAC;IACtB,CAAC,MACI;MACD,IAAIwB,YAAY,GAAG,EAAE;MACrB,IAAIzI,aAAa,GAAG,IAAI,CAACA,aAAa;MACtC,IAAI0I,GAAG,GAAGzI,MAAM,CAAC6F,MAAM;MACvB,IAAI6C,SAAS,GAAGD,GAAG,KAAK1I,aAAa,CAAC8F,MAAM;MAC5C,IAAI6C,SAAS,EAAE;QACX,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0D,GAAG,EAAE1D,CAAC,EAAE,EAAE;UAC1B,IAAI/E,MAAM,CAAC+E,CAAC,CAAC,KAAKhF,aAAa,CAACgF,CAAC,CAAC,EAAE;YAChC2D,SAAS,GAAG,KAAK;YACjB;UACJ;QACJ;MACJ;MACA,IAAIA,SAAS,EAAE;QACX;MACJ;MACA,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0D,GAAG,EAAE1D,CAAC,EAAE,EAAE;QAC1B,IAAIoC,SAAS,GAAGnH,MAAM,CAAC+E,CAAC,CAAC;QACzB,IAAI4D,QAAQ,GAAG,KAAK,CAAC;QACrB,IAAI,IAAI,CAAChB,UAAU,EAAE;UACjBgB,QAAQ,GAAG,IAAI,CAAChB,UAAU,CAACR,SAAS,EAAEnH,MAAM,CAAC;QACjD;QACA,IAAI,CAAC2I,QAAQ,EAAE;UACXA,QAAQ,GAAG,IAAI,CAAC3I,MAAM,CAACmH,SAAS,CAAC;QACrC;QACA,IAAIwB,QAAQ,EAAE;UACVH,YAAY,CAACH,IAAI,CAACM,QAAQ,CAAC;QAC/B;MACJ;MACA,IAAIC,YAAY,GAAGJ,YAAY,CAACC,GAAG,GAAG,CAAC,CAAC;MACxC,IAAIb,aAAa,GAAG,CAAC,EAAGgB,YAAY,IAAIA,YAAY,CAACf,UAAU,IAAKR,kBAAkB,CAAC;MACvF,IAAIO,aAAa,EAAE;QACf,IAAI,CAACE,qBAAqB,CAAC,IAAI,CAAC;MACpC;MACA,IAAIe,WAAW,GAAG,IAAI,CAACC,YAAY,CAACN,YAAY,CAAC;MACjD,IAAIhB,YAAY,GAAG,IAAI,CAACC,eAAe;MACvC,IAAI,CAAC3B,wBAAwB,CAAC+C,WAAW,CAAC;MAC1C,IAAI,CAACd,cAAc,CAAC/H,MAAM,CAAC+I,IAAI,CAAC,GAAG,CAAC,EAAEF,WAAW,EAAE,IAAI,CAAC3C,YAAY,EAAE,KAAK,EAAE,CAACe,WAAW,IAAI,CAAC,IAAI,CAACe,SAAS,IAAIR,YAAY,IAAIA,YAAY,CAACS,QAAQ,GAAG,CAAC,EAAET,YAAY,CAAC;MACxK,IAAIU,WAAW,GAAG,IAAI,CAAC7G,YAAY;MACnC,IAAI8G,SAAS,GAAG,IAAI,CAACC,UAAU;MAC/B,IAAIF,WAAW,EAAE;QACbA,WAAW,CAACK,SAAS,CAACvI,MAAM,EAAEiH,WAAW,EAAEW,aAAa,CAAC;MAC7D;MACA,IAAIO,SAAS,EAAE;QACXA,SAAS,CAACI,SAAS,CAACvI,MAAM,EAAEiH,WAAW,EAAEW,aAAa,CAAC;MAC3D;MACA,IAAI,CAACU,uBAAuB,CAAC,CAAC;MAC9B,IAAI,CAACvI,aAAa,GAAGC,MAAM,CAACgJ,KAAK,CAAC,CAAC;MACnC,IAAI,CAACpI,UAAU,CAAC,CAAC;MACjB,IAAI,CAACgH,aAAa,IAAI,IAAI,CAACI,SAAS,EAAE;QAClC,IAAI,CAACF,qBAAqB,CAAC,KAAK,CAAC;QACjC,IAAI,CAAC7G,OAAO,IAAI,CAAChC,UAAU;MAC/B;IACJ;EACJ,CAAC;EACDU,OAAO,CAACO,SAAS,CAAC+I,QAAQ,GAAG,YAAY;IACrC,IAAIA,QAAQ,GAAG,IAAI,CAACC,MAAM;IAC1B,IAAIC,QAAQ,GAAG,IAAI,CAACtH,MAAM;IAC1B,OAAO,CAACoH,QAAQ,IAAIE,QAAQ,EAAE;MAC1B,IAAIA,QAAQ,CAACD,MAAM,EAAE;QACjBD,QAAQ,GAAG,IAAI;QACf;MACJ;MACAE,QAAQ,GAAGA,QAAQ,CAACtH,MAAM;IAC9B;IACA,OAAOoH,QAAQ;EACnB,CAAC;EACDtJ,OAAO,CAACO,SAAS,CAACoI,uBAAuB,GAAG,YAAY;IACpD,KAAK,IAAIvD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACjF,SAAS,CAAC+F,MAAM,EAAEd,CAAC,EAAE,EAAE;MAC5C,IAAIoB,QAAQ,GAAG,IAAI,CAACrG,SAAS,CAACiF,CAAC,CAAC;MAChC,IAAIoB,QAAQ,CAACI,UAAU,EAAE;QACrBJ,QAAQ,CAACiD,YAAY,CAAC,IAAI,CAACjD,QAAQ,CAACI,UAAU,CAAC,CAAC;MACpD;IACJ;EACJ,CAAC;EACD5G,OAAO,CAACO,SAAS,CAACmJ,WAAW,GAAG,UAAU3B,KAAK,EAAE;IAC7C,IAAI4B,GAAG,GAAGjL,OAAO,CAAC,IAAI,CAAC0B,aAAa,EAAE2H,KAAK,CAAC;IAC5C,IAAI4B,GAAG,IAAI,CAAC,EAAE;MACV,IAAIvJ,aAAa,GAAG,IAAI,CAACA,aAAa,CAACiJ,KAAK,CAAC,CAAC;MAC9CjJ,aAAa,CAACwJ,MAAM,CAACD,GAAG,EAAE,CAAC,CAAC;MAC5B,IAAI,CAACf,SAAS,CAACxI,aAAa,CAAC;IACjC;EACJ,CAAC;EACDJ,OAAO,CAACO,SAAS,CAACsJ,YAAY,GAAG,UAAUC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;IACrE,IAAI5J,aAAa,GAAG,IAAI,CAACA,aAAa,CAACiJ,KAAK,CAAC,CAAC;IAC9C,IAAIM,GAAG,GAAGjL,OAAO,CAAC0B,aAAa,EAAE0J,QAAQ,CAAC;IAC1C,IAAIG,cAAc,GAAGvL,OAAO,CAAC0B,aAAa,EAAE2J,QAAQ,CAAC,IAAI,CAAC;IAC1D,IAAIJ,GAAG,IAAI,CAAC,EAAE;MACV,IAAI,CAACM,cAAc,EAAE;QACjB7J,aAAa,CAACuJ,GAAG,CAAC,GAAGI,QAAQ;MACjC,CAAC,MACI;QACD3J,aAAa,CAACwJ,MAAM,CAACD,GAAG,EAAE,CAAC,CAAC;MAChC;IACJ,CAAC,MACI,IAAIK,QAAQ,IAAI,CAACC,cAAc,EAAE;MAClC7J,aAAa,CAACsI,IAAI,CAACqB,QAAQ,CAAC;IAChC;IACA,IAAI,CAACnB,SAAS,CAACxI,aAAa,CAAC;EACjC,CAAC;EACDJ,OAAO,CAACO,SAAS,CAAC2J,WAAW,GAAG,UAAUnC,KAAK,EAAEoC,MAAM,EAAE;IACrD,IAAIA,MAAM,EAAE;MACR,IAAI,CAAC5C,QAAQ,CAACQ,KAAK,EAAE,IAAI,CAAC;IAC9B,CAAC,MACI;MACD,IAAI,CAAC2B,WAAW,CAAC3B,KAAK,CAAC;IAC3B;EACJ,CAAC;EACD/H,OAAO,CAACO,SAAS,CAAC4I,YAAY,GAAG,UAAU9I,MAAM,EAAE;IAC/C,IAAI6I,WAAW,GAAG,CAAC,CAAC;IACpB,IAAIkB,gBAAgB;IACpB,KAAK,IAAIhF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/E,MAAM,CAAC6F,MAAM,EAAEd,CAAC,EAAE,EAAE;MACpC,IAAI2C,KAAK,GAAG1H,MAAM,CAAC+E,CAAC,CAAC;MACrB3G,MAAM,CAACyK,WAAW,EAAEnB,KAAK,CAAC;MAC1B,IAAIA,KAAK,CAACpG,UAAU,EAAE;QAClByI,gBAAgB,GAAGA,gBAAgB,IAAI,CAAC,CAAC;QACzC3L,MAAM,CAAC2L,gBAAgB,EAAErC,KAAK,CAACpG,UAAU,CAAC;MAC9C;IACJ;IACA,IAAIyI,gBAAgB,EAAE;MAClBlB,WAAW,CAACvH,UAAU,GAAGyI,gBAAgB;IAC7C;IACA,OAAOlB,WAAW;EACtB,CAAC;EACDlJ,OAAO,CAACO,SAAS,CAAC6H,cAAc,GAAG,UAAUZ,SAAS,EAAEO,KAAK,EAAEzB,WAAW,EAAEmB,iBAAiB,EAAE4C,UAAU,EAAExC,YAAY,EAAE;IACrH,IAAIyC,oBAAoB,GAAG,EAAEvC,KAAK,IAAIN,iBAAiB,CAAC;IACxD,IAAIM,KAAK,IAAIA,KAAK,CAACpG,UAAU,EAAE;MAC3B,IAAI,CAACA,UAAU,GAAGlD,MAAM,CAAC,CAAC,CAAC,EAAEgJ,iBAAiB,GAAG,IAAI,CAAC9F,UAAU,GAAG2E,WAAW,CAAC3E,UAAU,CAAC;MAC1FlD,MAAM,CAAC,IAAI,CAACkD,UAAU,EAAEoG,KAAK,CAACpG,UAAU,CAAC;IAC7C,CAAC,MACI,IAAI2I,oBAAoB,EAAE;MAC3B,IAAIhE,WAAW,CAAC3E,UAAU,EAAE;QACxB,IAAI,CAACA,UAAU,GAAG2E,WAAW,CAAC3E,UAAU;MAC5C;IACJ;IACA,IAAI4I,gBAAgB,GAAG,CAAC,CAAC;IACzB,IAAIC,aAAa,GAAG,KAAK;IACzB,KAAK,IAAIpF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5F,mBAAmB,CAAC0G,MAAM,EAAEd,CAAC,EAAE,EAAE;MACjD,IAAIxF,GAAG,GAAGJ,mBAAmB,CAAC4F,CAAC,CAAC;MAChC,IAAIqF,mBAAmB,GAAGJ,UAAU,IAAI3K,sBAAsB,CAACE,GAAG,CAAC;MACnE,IAAImI,KAAK,IAAIA,KAAK,CAACnI,GAAG,CAAC,IAAI,IAAI,EAAE;QAC7B,IAAI6K,mBAAmB,EAAE;UACrBD,aAAa,GAAG,IAAI;UACpBD,gBAAgB,CAAC3K,GAAG,CAAC,GAAGmI,KAAK,CAACnI,GAAG,CAAC;QACtC,CAAC,MACI;UACD,IAAI,CAACA,GAAG,CAAC,GAAGmI,KAAK,CAACnI,GAAG,CAAC;QAC1B;MACJ,CAAC,MACI,IAAI0K,oBAAoB,EAAE;QAC3B,IAAIhE,WAAW,CAAC1G,GAAG,CAAC,IAAI,IAAI,EAAE;UAC1B,IAAI6K,mBAAmB,EAAE;YACrBD,aAAa,GAAG,IAAI;YACpBD,gBAAgB,CAAC3K,GAAG,CAAC,GAAG0G,WAAW,CAAC1G,GAAG,CAAC;UAC5C,CAAC,MACI;YACD,IAAI,CAACA,GAAG,CAAC,GAAG0G,WAAW,CAAC1G,GAAG,CAAC;UAChC;QACJ;MACJ;IACJ;IACA,IAAI,CAACyK,UAAU,EAAE;MACb,KAAK,IAAIjF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACjF,SAAS,CAAC+F,MAAM,EAAEd,CAAC,EAAE,EAAE;QAC5C,IAAIoB,QAAQ,GAAG,IAAI,CAACrG,SAAS,CAACiF,CAAC,CAAC;QAChC,IAAIwB,UAAU,GAAGJ,QAAQ,CAACI,UAAU;QACpC,IAAI,CAACJ,QAAQ,CAACG,OAAO,CAAC,CAAC,EAAE;UACrBH,QAAQ,CAACkE,kBAAkB,CAAC9D,UAAU,GAChC,CAACmB,KAAK,IAAIzB,WAAW,EAAEM,UAAU,CAAC,GACjCmB,KAAK,IAAIzB,WAAY,CAAC;QACjC;MACJ;IACJ;IACA,IAAIkE,aAAa,EAAE;MACf,IAAI,CAACG,gBAAgB,CAACnD,SAAS,EAAE+C,gBAAgB,EAAE1C,YAAY,CAAC;IACpE;EACJ,CAAC;EACD7H,OAAO,CAACO,SAAS,CAACqK,gBAAgB,GAAG,UAAUC,WAAW,EAAE;IACxD,IAAIA,WAAW,CAAChG,IAAI,IAAI,CAACgG,WAAW,CAACC,YAAY,EAAE;MAC/C,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACvC,MAAM,IAAIC,KAAK,CAAC,yCAAyC,CAAC;MAC9D;MACA;IACJ;IACA,IAAIL,WAAW,KAAK,IAAI,EAAE;MACtB,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACvC,MAAM,IAAIC,KAAK,CAAC,iCAAiC,CAAC;MACtD;MACA;IACJ;IACA,IAAIC,EAAE,GAAG,IAAI,CAACtG,IAAI;IAClB,IAAIsG,EAAE,EAAE;MACJN,WAAW,CAACO,WAAW,CAACD,EAAE,CAAC;IAC/B;IACAN,WAAW,CAAChG,IAAI,GAAGsG,EAAE;IACrBN,WAAW,CAACC,YAAY,GAAG,IAAI;EACnC,CAAC;EACD9K,OAAO,CAACO,SAAS,CAAC8K,gBAAgB,GAAG,UAAUR,WAAW,EAAE;IACxD,IAAIA,WAAW,CAAChG,IAAI,EAAE;MAClBgG,WAAW,CAACS,gBAAgB,CAACT,WAAW,CAAChG,IAAI,CAAC;IAClD;IACAgG,WAAW,CAAChG,IAAI,GAAG,IAAI;IACvBgG,WAAW,CAACC,YAAY,GAAG,IAAI;EACnC,CAAC;EACD9K,OAAO,CAACO,SAAS,CAACgL,WAAW,GAAG,YAAY;IACxC,OAAO,IAAI,CAACC,SAAS;EACzB,CAAC;EACDxL,OAAO,CAACO,SAAS,CAACqF,WAAW,GAAG,UAAU6F,QAAQ,EAAE;IAChD,IAAI,IAAI,CAACD,SAAS,IAAI,IAAI,CAACA,SAAS,KAAKC,QAAQ,EAAE;MAC/C,IAAI,CAACC,cAAc,CAAC,CAAC;IACzB;IACA,IAAI,CAACd,gBAAgB,CAACa,QAAQ,CAAC;IAC/B,IAAI,CAACD,SAAS,GAAGC,QAAQ;IACzB,IAAI,CAACxK,UAAU,CAAC,CAAC;EACrB,CAAC;EACDjB,OAAO,CAACO,SAAS,CAACmL,cAAc,GAAG,YAAY;IAC3C,IAAID,QAAQ,GAAG,IAAI,CAACD,SAAS;IAC7B,IAAIC,QAAQ,EAAE;MACV,IAAI,CAACJ,gBAAgB,CAACI,QAAQ,CAAC;MAC/B,IAAI,CAACD,SAAS,GAAG,IAAI;MACrB,IAAI,CAACvK,UAAU,CAAC,CAAC;IACrB;EACJ,CAAC;EACDjB,OAAO,CAACO,SAAS,CAACoL,cAAc,GAAG,YAAY;IAC3C,OAAO,IAAI,CAACjK,YAAY;EAC5B,CAAC;EACD1B,OAAO,CAACO,SAAS,CAACoF,cAAc,GAAG,UAAUlE,MAAM,EAAE;IACjD,IAAImK,mBAAmB,GAAG,IAAI,CAAClK,YAAY;IAC3C,IAAIkK,mBAAmB,KAAKnK,MAAM,EAAE;MAChC;IACJ;IACA,IAAImK,mBAAmB,IAAIA,mBAAmB,KAAKnK,MAAM,EAAE;MACvD,IAAI,CAACoK,iBAAiB,CAAC,CAAC;IAC5B;IACA,IAAId,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACvC,IAAIxJ,MAAM,CAACoD,IAAI,IAAI,CAACpD,MAAM,CAACqJ,YAAY,EAAE;QACrC,MAAM,IAAII,KAAK,CAAC,yCAAyC,CAAC;MAC9D;IACJ;IACAzJ,MAAM,CAACK,kBAAkB,GAAG,IAAIhE,aAAa,CAAC,CAAC;IAC/C,IAAI,CAAC8M,gBAAgB,CAACnJ,MAAM,CAAC;IAC7B,IAAI,CAACC,YAAY,GAAGD,MAAM;IAC1B,IAAI,CAACR,UAAU,CAAC,CAAC;EACrB,CAAC;EACDjB,OAAO,CAACO,SAAS,CAACmF,aAAa,GAAG,UAAUoG,GAAG,EAAE;IAC7C,IAAI,CAAC,IAAI,CAACnK,UAAU,EAAE;MAClB,IAAI,CAACA,UAAU,GAAG,CAAC,CAAC;IACxB;IACAlD,MAAM,CAAC,IAAI,CAACkD,UAAU,EAAEmK,GAAG,CAAC;IAC5B,IAAI,CAAC7K,UAAU,CAAC,CAAC;EACrB,CAAC;EACDjB,OAAO,CAACO,SAAS,CAACwL,gBAAgB,GAAG,YAAY;IAC7C,IAAI,CAACpK,UAAU,GAAG,IAAI;IACtB,IAAI,CAACV,UAAU,CAAC,CAAC;EACrB,CAAC;EACDjB,OAAO,CAACO,SAAS,CAACsL,iBAAiB,GAAG,YAAY;IAC9C,IAAIpK,MAAM,GAAG,IAAI,CAACC,YAAY;IAC9B,IAAID,MAAM,EAAE;MACRA,MAAM,CAACK,kBAAkB,GAAG,IAAI;MAChC,IAAI,CAACuJ,gBAAgB,CAAC5J,MAAM,CAAC;MAC7B,IAAI,CAACC,YAAY,GAAG,IAAI;MACxB,IAAI,CAACkC,sBAAsB,GAAG,IAAI;MAClC,IAAI,CAAC3C,UAAU,CAAC,CAAC;IACrB;EACJ,CAAC;EACDjB,OAAO,CAACO,SAAS,CAACyL,gBAAgB,GAAG,YAAY;IAC7C,OAAO,IAAI,CAACvD,UAAU;EAC1B,CAAC;EACDzI,OAAO,CAACO,SAAS,CAAC0L,gBAAgB,GAAG,UAAUC,SAAS,EAAE;IACtD,IAAI,IAAI,CAACzD,UAAU,IAAI,IAAI,CAACA,UAAU,KAAKyD,SAAS,EAAE;MAClD,IAAI,CAACC,mBAAmB,CAAC,CAAC;IAC9B;IACA,IAAI,CAACvB,gBAAgB,CAACsB,SAAS,CAAC;IAChC,IAAI,CAACzD,UAAU,GAAGyD,SAAS;IAC3B,IAAI,CAACjL,UAAU,CAAC,CAAC;EACrB,CAAC;EACDjB,OAAO,CAACO,SAAS,CAAC4L,mBAAmB,GAAG,YAAY;IAChD,IAAI3D,SAAS,GAAG,IAAI,CAACC,UAAU;IAC/B,IAAID,SAAS,EAAE;MACX,IAAI,CAAC6C,gBAAgB,CAAC7C,SAAS,CAAC;MAChC,IAAI,CAACC,UAAU,GAAG,IAAI;MACtB,IAAI,CAACxH,UAAU,CAAC,CAAC;IACrB;EACJ,CAAC;EACDjB,OAAO,CAACO,SAAS,CAACU,UAAU,GAAG,YAAY;IACvC,IAAI,CAACK,OAAO,IAAIhC,UAAU;IAC1B,IAAI6L,EAAE,GAAG,IAAI,CAACtG,IAAI;IAClB,IAAIsG,EAAE,EAAE;MACJ,IAAI,IAAI,CAAC9C,SAAS,EAAE;QAChB8C,EAAE,CAACiB,YAAY,CAAC,CAAC;MACrB,CAAC,MACI;QACDjB,EAAE,CAACkB,OAAO,CAAC,CAAC;MAChB;IACJ;IACA,IAAI,IAAI,CAACvB,YAAY,EAAE;MACnB,IAAI,CAACA,YAAY,CAAC7J,UAAU,CAAC,CAAC;IAClC;EACJ,CAAC;EACDjB,OAAO,CAACO,SAAS,CAAC+L,KAAK,GAAG,YAAY;IAClC,IAAI,CAACrL,UAAU,CAAC,CAAC;EACrB,CAAC;EACDjB,OAAO,CAACO,SAAS,CAAC4H,qBAAqB,GAAG,UAAUoE,OAAO,EAAE;IACzD,IAAI,CAAClE,SAAS,GAAGkE,OAAO;IACxB,IAAIhE,WAAW,GAAG,IAAI,CAAC7G,YAAY;IACnC,IAAI8G,SAAS,GAAG,IAAI,CAACC,UAAU;IAC/B,IAAIF,WAAW,EAAE;MACbA,WAAW,CAACF,SAAS,GAAGkE,OAAO;IACnC;IACA,IAAI/D,SAAS,EAAE;MACXA,SAAS,CAACH,SAAS,GAAGkE,OAAO;IACjC;EACJ,CAAC;EACDvM,OAAO,CAACO,SAAS,CAAC6K,WAAW,GAAG,UAAUD,EAAE,EAAE;IAC1C,IAAI,IAAI,CAACtG,IAAI,KAAKsG,EAAE,EAAE;MAClB;IACJ;IACA,IAAI,CAACtG,IAAI,GAAGsG,EAAE;IACd,IAAIhL,SAAS,GAAG,IAAI,CAACA,SAAS;IAC9B,IAAIA,SAAS,EAAE;MACX,KAAK,IAAIiF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjF,SAAS,CAAC+F,MAAM,EAAEd,CAAC,EAAE,EAAE;QACvC+F,EAAE,CAACqB,SAAS,CAACC,WAAW,CAACtM,SAAS,CAACiF,CAAC,CAAC,CAAC;MAC1C;IACJ;IACA,IAAI,IAAI,CAACoG,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAACJ,WAAW,CAACD,EAAE,CAAC;IAClC;IACA,IAAI,IAAI,CAACzJ,YAAY,EAAE;MACnB,IAAI,CAACA,YAAY,CAAC0J,WAAW,CAACD,EAAE,CAAC;IACrC;IACA,IAAI,IAAI,CAAC1C,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAAC2C,WAAW,CAACD,EAAE,CAAC;IACnC;EACJ,CAAC;EACDnL,OAAO,CAACO,SAAS,CAAC+K,gBAAgB,GAAG,UAAUH,EAAE,EAAE;IAC/C,IAAI,CAAC,IAAI,CAACtG,IAAI,EAAE;MACZ;IACJ;IACA,IAAI,CAACA,IAAI,GAAG,IAAI;IAChB,IAAI1E,SAAS,GAAG,IAAI,CAACA,SAAS;IAC9B,IAAIA,SAAS,EAAE;MACX,KAAK,IAAIiF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjF,SAAS,CAAC+F,MAAM,EAAEd,CAAC,EAAE,EAAE;QACvC+F,EAAE,CAACqB,SAAS,CAACE,cAAc,CAACvM,SAAS,CAACiF,CAAC,CAAC,CAAC;MAC7C;IACJ;IACA,IAAI,IAAI,CAACoG,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAACF,gBAAgB,CAACH,EAAE,CAAC;IACvC;IACA,IAAI,IAAI,CAACzJ,YAAY,EAAE;MACnB,IAAI,CAACA,YAAY,CAAC4J,gBAAgB,CAACH,EAAE,CAAC;IAC1C;IACA,IAAI,IAAI,CAAC1C,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAAC6C,gBAAgB,CAACH,EAAE,CAAC;IACxC;EACJ,CAAC;EACDnL,OAAO,CAACO,SAAS,CAACoM,OAAO,GAAG,UAAU/M,GAAG,EAAEgN,IAAI,EAAEC,sBAAsB,EAAE;IACrE,IAAIhG,MAAM,GAAGjH,GAAG,GAAG,IAAI,CAACA,GAAG,CAAC,GAAG,IAAI;IACnC,IAAImL,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACvC,IAAI,CAACpE,MAAM,EAAE;QACTlI,QAAQ,CAAC,YAAY,GACfiB,GAAG,GACH,8BAA8B,GAC9B,IAAI,CAACM,EAAE,CAAC;QACd;MACJ;IACJ;IACA,IAAIsG,QAAQ,GAAG,IAAIxI,QAAQ,CAAC6I,MAAM,EAAE+F,IAAI,EAAEC,sBAAsB,CAAC;IACjEjN,GAAG,KAAK4G,QAAQ,CAACI,UAAU,GAAGhH,GAAG,CAAC;IAClC,IAAI,CAAC6M,WAAW,CAACjG,QAAQ,EAAE5G,GAAG,CAAC;IAC/B,OAAO4G,QAAQ;EACnB,CAAC;EACDxG,OAAO,CAACO,SAAS,CAACkM,WAAW,GAAG,UAAUjG,QAAQ,EAAE5G,GAAG,EAAE;IACrD,IAAIuL,EAAE,GAAG,IAAI,CAACtG,IAAI;IAClB,IAAIiI,EAAE,GAAG,IAAI;IACbtG,QAAQ,CAACuG,MAAM,CAAC,YAAY;MACxBD,EAAE,CAACE,qBAAqB,CAACpN,GAAG,CAAC;IACjC,CAAC,CAAC,CAACqN,IAAI,CAAC,YAAY;MAChB,IAAI9M,SAAS,GAAG2M,EAAE,CAAC3M,SAAS;MAC5B,IAAIwJ,GAAG,GAAGjL,OAAO,CAACyB,SAAS,EAAEqG,QAAQ,CAAC;MACtC,IAAImD,GAAG,IAAI,CAAC,EAAE;QACVxJ,SAAS,CAACyJ,MAAM,CAACD,GAAG,EAAE,CAAC,CAAC;MAC5B;IACJ,CAAC,CAAC;IACF,IAAI,CAACxJ,SAAS,CAACuI,IAAI,CAAClC,QAAQ,CAAC;IAC7B,IAAI2E,EAAE,EAAE;MACJA,EAAE,CAACqB,SAAS,CAACC,WAAW,CAACjG,QAAQ,CAAC;IACtC;IACA2E,EAAE,IAAIA,EAAE,CAAC+B,MAAM,CAAC,CAAC;EACrB,CAAC;EACDlN,OAAO,CAACO,SAAS,CAACyM,qBAAqB,GAAG,UAAUpN,GAAG,EAAE;IACrD,IAAI,CAACqB,UAAU,CAAC,CAAC;EACrB,CAAC;EACDjB,OAAO,CAACO,SAAS,CAAC4M,aAAa,GAAG,UAAUC,KAAK,EAAEC,aAAa,EAAE;IAC9D,IAAIlN,SAAS,GAAG,IAAI,CAACA,SAAS;IAC9B,IAAI2I,GAAG,GAAG3I,SAAS,CAAC+F,MAAM;IAC1B,IAAIoH,aAAa,GAAG,EAAE;IACtB,KAAK,IAAIlI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0D,GAAG,EAAE1D,CAAC,EAAE,EAAE;MAC1B,IAAIoB,QAAQ,GAAGrG,SAAS,CAACiF,CAAC,CAAC;MAC3B,IAAI,CAACgI,KAAK,IAAIA,KAAK,KAAK5G,QAAQ,CAAC4G,KAAK,EAAE;QACpC5G,QAAQ,CAAC+G,IAAI,CAACF,aAAa,CAAC;MAChC,CAAC,MACI;QACDC,aAAa,CAAC5E,IAAI,CAAClC,QAAQ,CAAC;MAChC;IACJ;IACA,IAAI,CAACrG,SAAS,GAAGmN,aAAa;IAC9B,OAAO,IAAI;EACf,CAAC;EACDtN,OAAO,CAACO,SAAS,CAACiN,SAAS,GAAG,UAAU3G,MAAM,EAAEiF,GAAG,EAAE2B,cAAc,EAAE;IACjED,SAAS,CAAC,IAAI,EAAE3G,MAAM,EAAEiF,GAAG,EAAE2B,cAAc,CAAC;EAChD,CAAC;EACDzN,OAAO,CAACO,SAAS,CAACmN,WAAW,GAAG,UAAU7G,MAAM,EAAEiF,GAAG,EAAE2B,cAAc,EAAE;IACnED,SAAS,CAAC,IAAI,EAAE3G,MAAM,EAAEiF,GAAG,EAAE2B,cAAc,EAAE,IAAI,CAAC;EACtD,CAAC;EACDzN,OAAO,CAACO,SAAS,CAACoK,gBAAgB,GAAG,UAAUnD,SAAS,EAAEX,MAAM,EAAEiF,GAAG,EAAE2B,cAAc,EAAE;IACnF,IAAItN,SAAS,GAAGqN,SAAS,CAAC,IAAI,EAAE3G,MAAM,EAAEiF,GAAG,EAAE2B,cAAc,CAAC;IAC5D,KAAK,IAAIrI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjF,SAAS,CAAC+F,MAAM,EAAEd,CAAC,EAAE,EAAE;MACvCjF,SAAS,CAACiF,CAAC,CAAC,CAACsB,qBAAqB,GAAGc,SAAS;IAClD;EACJ,CAAC;EACDxH,OAAO,CAACO,SAAS,CAACiC,eAAe,GAAG,YAAY;IAC5C,OAAO,IAAI;EACf,CAAC;EACDxC,OAAO,CAACO,SAAS,CAACoN,YAAY,GAAG,YAAY;IACzC,OAAO,IAAI;EACf,CAAC;EACD3N,OAAO,CAAC4N,gBAAgB,GAAI,YAAY;IACpC,IAAIC,OAAO,GAAG7N,OAAO,CAACO,SAAS;IAC/BsN,OAAO,CAACC,IAAI,GAAG,SAAS;IACxBD,OAAO,CAAC1G,IAAI,GAAG,EAAE;IACjB0G,OAAO,CAAChO,MAAM,GACVgO,OAAO,CAACtE,MAAM,GACVsE,OAAO,CAACE,OAAO,GACXF,OAAO,CAAChN,SAAS,GACbgN,OAAO,CAACG,QAAQ,GACZH,OAAO,CAACI,UAAU,GACdJ,OAAO,CAACxF,SAAS,GAAG,KAAK;IACjDwF,OAAO,CAACvM,OAAO,GAAGhC,UAAU;IAC5B,IAAI4O,IAAI,GAAG,CAAC,CAAC;IACb,SAASC,kBAAkBA,CAACvO,GAAG,EAAEwO,IAAI,EAAEC,IAAI,EAAE;MACzC,IAAI,CAACH,IAAI,CAACtO,GAAG,GAAGwO,IAAI,GAAGC,IAAI,CAAC,EAAE;QAC1BC,OAAO,CAACC,IAAI,CAAC,eAAe,GAAG3O,GAAG,GAAG,8BAA8B,GAAGwO,IAAI,GAAG,MAAM,GAAGC,IAAI,GAAG,WAAW,CAAC;QACzGH,IAAI,CAACtO,GAAG,GAAGwO,IAAI,GAAGC,IAAI,CAAC,GAAG,IAAI;MAClC;IACJ;IACA,SAASG,oBAAoBA,CAAC5O,GAAG,EAAE6O,UAAU,EAAEL,IAAI,EAAEC,IAAI,EAAE;MACvDK,MAAM,CAACC,cAAc,CAACd,OAAO,EAAEjO,GAAG,EAAE;QAChCgP,GAAG,EAAE,SAAAA,CAAA,EAAY;UACb,IAAI7D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;YACvCkD,kBAAkB,CAACvO,GAAG,EAAEwO,IAAI,EAAEC,IAAI,CAAC;UACvC;UACA,IAAI,CAAC,IAAI,CAACI,UAAU,CAAC,EAAE;YACnB,IAAII,GAAG,GAAG,IAAI,CAACJ,UAAU,CAAC,GAAG,EAAE;YAC/BK,YAAY,CAAC,IAAI,EAAED,GAAG,CAAC;UAC3B;UACA,OAAO,IAAI,CAACJ,UAAU,CAAC;QAC3B,CAAC;QACDM,GAAG,EAAE,SAAAA,CAAUF,GAAG,EAAE;UAChB,IAAI9D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;YACvCkD,kBAAkB,CAACvO,GAAG,EAAEwO,IAAI,EAAEC,IAAI,CAAC;UACvC;UACA,IAAI,CAACD,IAAI,CAAC,GAAGS,GAAG,CAAC,CAAC,CAAC;UACnB,IAAI,CAACR,IAAI,CAAC,GAAGQ,GAAG,CAAC,CAAC,CAAC;UACnB,IAAI,CAACJ,UAAU,CAAC,GAAGI,GAAG;UACtBC,YAAY,CAAC,IAAI,EAAED,GAAG,CAAC;QAC3B;MACJ,CAAC,CAAC;MACF,SAASC,YAAYA,CAACE,IAAI,EAAEH,GAAG,EAAE;QAC7BH,MAAM,CAACC,cAAc,CAACE,GAAG,EAAE,CAAC,EAAE;UAC1BD,GAAG,EAAE,SAAAA,CAAA,EAAY;YACb,OAAOI,IAAI,CAACZ,IAAI,CAAC;UACrB,CAAC;UACDW,GAAG,EAAE,SAAAA,CAAUE,GAAG,EAAE;YAChBD,IAAI,CAACZ,IAAI,CAAC,GAAGa,GAAG;UACpB;QACJ,CAAC,CAAC;QACFP,MAAM,CAACC,cAAc,CAACE,GAAG,EAAE,CAAC,EAAE;UAC1BD,GAAG,EAAE,SAAAA,CAAA,EAAY;YACb,OAAOI,IAAI,CAACX,IAAI,CAAC;UACrB,CAAC;UACDU,GAAG,EAAE,SAAAA,CAAUE,GAAG,EAAE;YAChBD,IAAI,CAACX,IAAI,CAAC,GAAGY,GAAG;UACpB;QACJ,CAAC,CAAC;MACN;IACJ;IACA,IAAIP,MAAM,CAACC,cAAc,EAAE;MACvBH,oBAAoB,CAAC,UAAU,EAAE,YAAY,EAAE,GAAG,EAAE,GAAG,CAAC;MACxDA,oBAAoB,CAAC,OAAO,EAAE,cAAc,EAAE,QAAQ,EAAE,QAAQ,CAAC;MACjEA,oBAAoB,CAAC,QAAQ,EAAE,eAAe,EAAE,SAAS,EAAE,SAAS,CAAC;IACzE;EACJ,CAAC,CAAE,CAAC;EACJ,OAAOxO,OAAO;AAClB,CAAC,CAAC,CAAE;AACJpB,KAAK,CAACoB,OAAO,EAAE7B,QAAQ,CAAC;AACxBS,KAAK,CAACoB,OAAO,EAAElC,aAAa,CAAC;AAC7B,SAAS0P,SAASA,CAAC0B,UAAU,EAAErI,MAAM,EAAEiF,GAAG,EAAE2B,cAAc,EAAE0B,OAAO,EAAE;EACjErD,GAAG,GAAGA,GAAG,IAAI,CAAC,CAAC;EACf,IAAI3L,SAAS,GAAG,EAAE;EAClBiP,gBAAgB,CAACF,UAAU,EAAE,EAAE,EAAEA,UAAU,EAAErI,MAAM,EAAEiF,GAAG,EAAE2B,cAAc,EAAEtN,SAAS,EAAEgP,OAAO,CAAC;EAC7F,IAAIE,WAAW,GAAGlP,SAAS,CAAC+F,MAAM;EAClC,IAAIoJ,YAAY,GAAG,KAAK;EACxB,IAAIC,OAAO,GAAGzD,GAAG,CAACmB,IAAI;EACtB,IAAIuC,UAAU,GAAG1D,GAAG,CAAC2D,OAAO;EAC5B,IAAIC,MAAM,GAAG,SAAAA,CAAA,EAAY;IACrBJ,YAAY,GAAG,IAAI;IACnBD,WAAW,EAAE;IACb,IAAIA,WAAW,IAAI,CAAC,EAAE;MAClBC,YAAY,GACLC,OAAO,IAAIA,OAAO,CAAC,CAAC,GACpBC,UAAU,IAAIA,UAAU,CAAC,CAAE;IACtC;EACJ,CAAC;EACD,IAAIG,SAAS,GAAG,SAAAA,CAAA,EAAY;IACxBN,WAAW,EAAE;IACb,IAAIA,WAAW,IAAI,CAAC,EAAE;MAClBC,YAAY,GACLC,OAAO,IAAIA,OAAO,CAAC,CAAC,GACpBC,UAAU,IAAIA,UAAU,CAAC,CAAE;IACtC;EACJ,CAAC;EACD,IAAI,CAACH,WAAW,EAAE;IACdE,OAAO,IAAIA,OAAO,CAAC,CAAC;EACxB;EACA,IAAIpP,SAAS,CAAC+F,MAAM,GAAG,CAAC,IAAI4F,GAAG,CAACiB,MAAM,EAAE;IACpC5M,SAAS,CAAC,CAAC,CAAC,CAAC4M,MAAM,CAAC,UAAUlG,MAAM,EAAE+I,OAAO,EAAE;MAC3C9D,GAAG,CAACiB,MAAM,CAAC6C,OAAO,CAAC;IACvB,CAAC,CAAC;EACN;EACA,KAAK,IAAIxK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjF,SAAS,CAAC+F,MAAM,EAAEd,CAAC,EAAE,EAAE;IACvC,IAAIoB,QAAQ,GAAGrG,SAAS,CAACiF,CAAC,CAAC;IAC3B,IAAIsK,MAAM,EAAE;MACRlJ,QAAQ,CAACyG,IAAI,CAACyC,MAAM,CAAC;IACzB;IACA,IAAIC,SAAS,EAAE;MACXnJ,QAAQ,CAACiJ,OAAO,CAACE,SAAS,CAAC;IAC/B;IACA,IAAI7D,GAAG,CAAC+D,KAAK,EAAE;MACXrJ,QAAQ,CAAC8B,QAAQ,CAACwD,GAAG,CAACxD,QAAQ,CAAC;IACnC;IACA9B,QAAQ,CAACsJ,KAAK,CAAChE,GAAG,CAACiE,MAAM,CAAC;EAC9B;EACA,OAAO5P,SAAS;AACpB;AACA,SAAS6P,cAAcA,CAACC,MAAM,EAAEpJ,MAAM,EAAEiC,GAAG,EAAE;EACzC,KAAK,IAAI1D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0D,GAAG,EAAE1D,CAAC,EAAE,EAAE;IAC1B6K,MAAM,CAAC7K,CAAC,CAAC,GAAGyB,MAAM,CAACzB,CAAC,CAAC;EACzB;AACJ;AACA,SAAS8K,SAASA,CAACzK,KAAK,EAAE;EACtB,OAAO5G,WAAW,CAAC4G,KAAK,CAAC,CAAC,CAAC,CAAC;AAChC;AACA,SAAS0K,SAASA,CAACtJ,MAAM,EAAEoJ,MAAM,EAAErQ,GAAG,EAAE;EACpC,IAAIf,WAAW,CAACoR,MAAM,CAACrQ,GAAG,CAAC,CAAC,EAAE;IAC1B,IAAI,CAACf,WAAW,CAACgI,MAAM,CAACjH,GAAG,CAAC,CAAC,EAAE;MAC3BiH,MAAM,CAACjH,GAAG,CAAC,GAAG,EAAE;IACpB;IACA,IAAId,YAAY,CAACmR,MAAM,CAACrQ,GAAG,CAAC,CAAC,EAAE;MAC3B,IAAIkJ,GAAG,GAAGmH,MAAM,CAACrQ,GAAG,CAAC,CAACsG,MAAM;MAC5B,IAAIW,MAAM,CAACjH,GAAG,CAAC,CAACsG,MAAM,KAAK4C,GAAG,EAAE;QAC5BjC,MAAM,CAACjH,GAAG,CAAC,GAAG,IAAKqQ,MAAM,CAACrQ,GAAG,CAAC,CAACwQ,WAAW,CAAEtH,GAAG,CAAC;QAChDkH,cAAc,CAACnJ,MAAM,CAACjH,GAAG,CAAC,EAAEqQ,MAAM,CAACrQ,GAAG,CAAC,EAAEkJ,GAAG,CAAC;MACjD;IACJ,CAAC,MACI;MACD,IAAIuH,SAAS,GAAGJ,MAAM,CAACrQ,GAAG,CAAC;MAC3B,IAAI0Q,SAAS,GAAGzJ,MAAM,CAACjH,GAAG,CAAC;MAC3B,IAAI2Q,IAAI,GAAGF,SAAS,CAACnK,MAAM;MAC3B,IAAIgK,SAAS,CAACG,SAAS,CAAC,EAAE;QACtB,IAAIG,IAAI,GAAGH,SAAS,CAAC,CAAC,CAAC,CAACnK,MAAM;QAC9B,KAAK,IAAId,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmL,IAAI,EAAEnL,CAAC,EAAE,EAAE;UAC3B,IAAI,CAACkL,SAAS,CAAClL,CAAC,CAAC,EAAE;YACfkL,SAAS,CAAClL,CAAC,CAAC,GAAGqL,KAAK,CAAClQ,SAAS,CAAC8I,KAAK,CAACqH,IAAI,CAACL,SAAS,CAACjL,CAAC,CAAC,CAAC;UAC3D,CAAC,MACI;YACD4K,cAAc,CAACM,SAAS,CAAClL,CAAC,CAAC,EAAEiL,SAAS,CAACjL,CAAC,CAAC,EAAEoL,IAAI,CAAC;UACpD;QACJ;MACJ,CAAC,MACI;QACDR,cAAc,CAACM,SAAS,EAAED,SAAS,EAAEE,IAAI,CAAC;MAC9C;MACAD,SAAS,CAACpK,MAAM,GAAGmK,SAAS,CAACnK,MAAM;IACvC;EACJ,CAAC,MACI;IACDW,MAAM,CAACjH,GAAG,CAAC,GAAGqQ,MAAM,CAACrQ,GAAG,CAAC;EAC7B;AACJ;AACA,SAAS+Q,WAAWA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAC7B,OAAOD,IAAI,KAAKC,IAAI,IACbhS,WAAW,CAAC+R,IAAI,CAAC,IAAI/R,WAAW,CAACgS,IAAI,CAAC,IAAIC,aAAa,CAACF,IAAI,EAAEC,IAAI,CAAC;AAC9E;AACA,SAASC,aAAaA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAC/B,IAAIlI,GAAG,GAAGiI,IAAI,CAAC7K,MAAM;EACrB,IAAI4C,GAAG,KAAKkI,IAAI,CAAC9K,MAAM,EAAE;IACrB,OAAO,KAAK;EAChB;EACA,KAAK,IAAId,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0D,GAAG,EAAE1D,CAAC,EAAE,EAAE;IAC1B,IAAI2L,IAAI,CAAC3L,CAAC,CAAC,KAAK4L,IAAI,CAAC5L,CAAC,CAAC,EAAE;MACrB,OAAO,KAAK;IAChB;EACJ;EACA,OAAO,IAAI;AACf;AACA,SAASgK,gBAAgBA,CAACF,UAAU,EAAE+B,MAAM,EAAEC,UAAU,EAAErK,MAAM,EAAEiF,GAAG,EAAE2B,cAAc,EAAEtN,SAAS,EAAEgP,OAAO,EAAE;EACvG,IAAIgC,UAAU,GAAG3S,IAAI,CAACqI,MAAM,CAAC;EAC7B,IAAIyB,QAAQ,GAAGwD,GAAG,CAACxD,QAAQ;EAC3B,IAAI8I,KAAK,GAAGtF,GAAG,CAACsF,KAAK;EACrB,IAAIC,QAAQ,GAAGvF,GAAG,CAACuF,QAAQ;EAC3B,IAAIC,UAAU,GAAGxF,GAAG,CAACwF,UAAU;EAC/B,IAAIC,UAAU,GAAG,CAAChT,QAAQ,CAACkP,cAAc,CAAC;EAC1C,IAAI+D,eAAe,GAAGtC,UAAU,CAAC/O,SAAS;EAC1C,IAAIsR,aAAa,GAAG,EAAE;EACtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,UAAU,CAACjL,MAAM,EAAEwL,CAAC,EAAE,EAAE;IACxC,IAAIC,QAAQ,GAAGR,UAAU,CAACO,CAAC,CAAC;IAC5B,IAAIE,SAAS,GAAG/K,MAAM,CAAC8K,QAAQ,CAAC;IAChC,IAAIC,SAAS,IAAI,IAAI,IAAIV,UAAU,CAACS,QAAQ,CAAC,IAAI,IAAI,KAC7CJ,UAAU,IAAI9D,cAAc,CAACkE,QAAQ,CAAC,CAAC,EAAE;MAC7C,IAAIpT,QAAQ,CAACqT,SAAS,CAAC,IAChB,CAAC/S,WAAW,CAAC+S,SAAS,CAAC,IACvB,CAAC7S,gBAAgB,CAAC6S,SAAS,CAAC,EAAE;QACjC,IAAIX,MAAM,EAAE;UACR,IAAI,CAAC9B,OAAO,EAAE;YACV+B,UAAU,CAACS,QAAQ,CAAC,GAAGC,SAAS;YAChC1C,UAAU,CAAClC,qBAAqB,CAACiE,MAAM,CAAC;UAC5C;UACA;QACJ;QACA7B,gBAAgB,CAACF,UAAU,EAAEyC,QAAQ,EAAET,UAAU,CAACS,QAAQ,CAAC,EAAEC,SAAS,EAAE9F,GAAG,EAAE2B,cAAc,IAAIA,cAAc,CAACkE,QAAQ,CAAC,EAAExR,SAAS,EAAEgP,OAAO,CAAC;MAChJ,CAAC,MACI;QACDsC,aAAa,CAAC/I,IAAI,CAACiJ,QAAQ,CAAC;MAChC;IACJ,CAAC,MACI,IAAI,CAACxC,OAAO,EAAE;MACf+B,UAAU,CAACS,QAAQ,CAAC,GAAGC,SAAS;MAChC1C,UAAU,CAAClC,qBAAqB,CAACiE,MAAM,CAAC;MACxCQ,aAAa,CAAC/I,IAAI,CAACiJ,QAAQ,CAAC;IAChC;EACJ;EACA,IAAIE,MAAM,GAAGJ,aAAa,CAACvL,MAAM;EACjC,IAAI,CAACmL,QAAQ,IAAIQ,MAAM,EAAE;IACrB,KAAK,IAAIzM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoM,eAAe,CAACtL,MAAM,EAAEd,CAAC,EAAE,EAAE;MAC7C,IAAIoB,QAAQ,GAAGgL,eAAe,CAACpM,CAAC,CAAC;MACjC,IAAIoB,QAAQ,CAACI,UAAU,KAAKqK,MAAM,EAAE;QAChC,IAAIa,UAAU,GAAGtL,QAAQ,CAACuL,UAAU,CAACN,aAAa,CAAC;QACnD,IAAIK,UAAU,EAAE;UACZ,IAAInI,GAAG,GAAGjL,OAAO,CAAC8S,eAAe,EAAEhL,QAAQ,CAAC;UAC5CgL,eAAe,CAAC5H,MAAM,CAACD,GAAG,EAAE,CAAC,CAAC;QAClC;MACJ;IACJ;EACJ;EACA,IAAI,CAACmC,GAAG,CAAC+D,KAAK,EAAE;IACZ4B,aAAa,GAAGzS,MAAM,CAACyS,aAAa,EAAE,UAAU7R,GAAG,EAAE;MAAE,OAAO,CAAC+Q,WAAW,CAAC9J,MAAM,CAACjH,GAAG,CAAC,EAAEsR,UAAU,CAACtR,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC;IAC5GiS,MAAM,GAAGJ,aAAa,CAACvL,MAAM;EACjC;EACA,IAAI2L,MAAM,GAAG,CAAC,IACN/F,GAAG,CAAC+D,KAAK,IAAI,CAAC1P,SAAS,CAAC+F,MAAO,EAAE;IACrC,IAAI8L,cAAc,GAAG,KAAK,CAAC;IAC3B,IAAIC,cAAc,GAAG,KAAK,CAAC;IAC3B,IAAIC,WAAW,GAAG,KAAK,CAAC;IACxB,IAAI/C,OAAO,EAAE;MACT8C,cAAc,GAAG,CAAC,CAAC;MACnB,IAAIX,UAAU,EAAE;QACZU,cAAc,GAAG,CAAC,CAAC;MACvB;MACA,KAAK,IAAI5M,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyM,MAAM,EAAEzM,CAAC,EAAE,EAAE;QAC7B,IAAIuM,QAAQ,GAAGF,aAAa,CAACrM,CAAC,CAAC;QAC/B6M,cAAc,CAACN,QAAQ,CAAC,GAAGT,UAAU,CAACS,QAAQ,CAAC;QAC/C,IAAIL,UAAU,EAAE;UACZU,cAAc,CAACL,QAAQ,CAAC,GAAG9K,MAAM,CAAC8K,QAAQ,CAAC;QAC/C,CAAC,MACI;UACDT,UAAU,CAACS,QAAQ,CAAC,GAAG9K,MAAM,CAAC8K,QAAQ,CAAC;QAC3C;MACJ;IACJ,CAAC,MACI,IAAIL,UAAU,EAAE;MACjBY,WAAW,GAAG,CAAC,CAAC;MAChB,KAAK,IAAI9M,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyM,MAAM,EAAEzM,CAAC,EAAE,EAAE;QAC7B,IAAIuM,QAAQ,GAAGF,aAAa,CAACrM,CAAC,CAAC;QAC/B8M,WAAW,CAACP,QAAQ,CAAC,GAAG1T,UAAU,CAACiT,UAAU,CAACS,QAAQ,CAAC,CAAC;QACxDxB,SAAS,CAACe,UAAU,EAAErK,MAAM,EAAE8K,QAAQ,CAAC;MAC3C;IACJ;IACA,IAAInL,QAAQ,GAAG,IAAIxI,QAAQ,CAACkT,UAAU,EAAE,KAAK,EAAE,KAAK,EAAEG,QAAQ,GAAGrS,MAAM,CAACwS,eAAe,EAAE,UAAUhL,QAAQ,EAAE;MAAE,OAAOA,QAAQ,CAACI,UAAU,KAAKqK,MAAM;IAAE,CAAC,CAAC,GAAG,IAAI,CAAC;IAChKzK,QAAQ,CAACI,UAAU,GAAGqK,MAAM;IAC5B,IAAInF,GAAG,CAACsB,KAAK,EAAE;MACX5G,QAAQ,CAAC4G,KAAK,GAAGtB,GAAG,CAACsB,KAAK;IAC9B;IACA,IAAIkE,UAAU,IAAIU,cAAc,EAAE;MAC9BxL,QAAQ,CAAC2L,YAAY,CAAC,CAAC,EAAEH,cAAc,EAAEP,aAAa,CAAC;IAC3D;IACA,IAAIS,WAAW,EAAE;MACb1L,QAAQ,CAAC2L,YAAY,CAAC,CAAC,EAAED,WAAW,EAAET,aAAa,CAAC;IACxD;IACAjL,QAAQ,CAAC2L,YAAY,CAAC7J,QAAQ,IAAI,IAAI,GAAG,GAAG,GAAGA,QAAQ,EAAE6G,OAAO,GAAG8C,cAAc,GAAGpL,MAAM,EAAE4K,aAAa,CAAC,CAACL,KAAK,CAACA,KAAK,IAAI,CAAC,CAAC;IAC5HlC,UAAU,CAACzC,WAAW,CAACjG,QAAQ,EAAEyK,MAAM,CAAC;IACxC9Q,SAAS,CAACuI,IAAI,CAAClC,QAAQ,CAAC;EAC5B;AACJ;AACA,eAAexG,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}