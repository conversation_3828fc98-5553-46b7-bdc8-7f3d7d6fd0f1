import { jsPDF } from 'jspdf';
import { saveAs } from 'file-saver';

/**
 * Export as PDF file
 * @param {string} content - Text content to export
 * @param {string} filename - Filename (optional)
 */
export const exportToPDF = (content, filename = 'optimized-resume') => {
  try {
    // Create new PDF document
    const doc = new jsPDF();
    
    // Add Chinese font support if needed
    // For basic text, we can use the default font
    
    // Set font (support Chinese characters)
    doc.setFont("helvetica", "normal");
    doc.setFontSize(12);
    
    // Page settings
    const pageWidth = doc.internal.pageSize.getWidth();
    const pageHeight = doc.internal.pageSize.getHeight();
    const margin = 20;
    const maxWidth = pageWidth - 2 * margin;
    
    let yPosition = 30;
    const lineHeight = 6;
    
    // Split text into lines
    const lines = content.split('\n');
    
    lines.forEach(line => {
      if (line.trim() === '') {
        // Empty line
        yPosition += lineHeight;
        return;
      }
      
      // Check if new page is needed
      if (yPosition > pageHeight - 30) {
        doc.addPage();
        yPosition = 30;
      }
      
      // Detect titles (all caps or starting with =)
      if (line.toUpperCase() === line && line.length < 50) {
        doc.setFontSize(16);
        doc.setFont("helvetica", "bold");
        yPosition += 5;
      } 
      // Detect subtitles (containing **)
      else if (line.includes('**')) {
        doc.setFontSize(14);
        doc.setFont("helvetica", "bold");
        yPosition += 3;
      } 
      // Regular text
      else {
        doc.setFontSize(12);
        doc.setFont("helvetica", "normal");
      }
      
      // Handle long text wrapping
      const splitText = doc.splitTextToSize(line, maxWidth);
      
      splitText.forEach(textLine => {
        if (yPosition > pageHeight - 30) {
          doc.addPage();
          yPosition = 30;
        }
        
        doc.text(textLine, margin, yPosition);
        yPosition += lineHeight;
      });
      
      yPosition += 2; // Line spacing
    });
    
    // Save PDF
    doc.save(`${filename}.pdf`);
    
  } catch (error) {
    console.error('PDF export failed:', error);
    throw new Error('PDF export failed, please try again');
  }
};

/**
 * Export as DOCX file (simplified version)
 * @param {string} content - Text content to export
 * @param {string} filename - Filename (optional)
 */
export const exportToDocx = (content, filename = 'optimized-resume') => {
  try {
    // Create DOCX blob (simplified)
    const processedContent = content
      .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold markdown
      .replace(/#{1,6}\s/g, '') // Remove headers
      .replace(/\*/g, '•'); // Convert bullets
    
    const blob = new Blob([processedContent], {
      type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    });
    
    // Create download link
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${filename}.docx`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    
  } catch (error) {
    console.error('DOCX export failed:', error);
    throw new Error('DOCX export failed, please try again');
  }
};

/**
 * Export as plain text file
 * @param {string} content - Text content to export
 * @param {string} filename - Filename (optional)
 */
export const exportToText = (content, filename = 'optimized-resume') => {
  try {
    // Clean content (remove markdown format)
    const plainText = content
      .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold
      .replace(/#{1,6}\s/g, '') // Remove headers
      .replace(/\*/g, '•'); // Convert bullets
    
    const blob = new Blob([plainText], { type: 'text/plain' });
    
    // Create download link
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${filename}.txt`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    
  } catch (error) {
    console.error('Text export failed:', error);
    throw new Error('Text export failed, please try again');
  }
};

// Alias for backward compatibility
export const exportToWord = exportToDocx;

/**
 * Generate filename with current timestamp
 * @param {string} baseName - Base filename
 * @param {string} extension - File extension
 * @returns {string} - Filename with timestamp
 */
export const generateTimestampFilename = (baseName, extension) => {
  const now = new Date();
  const timestamp = now.toISOString().replace(/[:.]/g, '-').slice(0, -5);
  return `${baseName}_${timestamp}.${extension}`;
}; 