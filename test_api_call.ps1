# PowerShell脚本测试API调用

# 测试数据
$testData = @{
    resume_text = "张三，软件工程师，3年Python开发经验，熟悉Web开发，有Django项目经验。教育背景：计算机科学学士学位。技能：Python, JavaScript, HTML, CSS, MySQL。"
    job_description = "我们正在寻找一名高级Python开发工程师，要求：5年以上Python开发经验，熟悉Django或Flask框架，具备React前端开发能力，熟悉PostgreSQL数据库，优秀的沟通能力，计算机科学相关学位。"
} | ConvertTo-Json

Write-Host "🔄 测试简历优化API..." -ForegroundColor Yellow
Write-Host "📝 请求数据: $testData" -ForegroundColor Gray

try {
    $response = Invoke-WebRequest -Uri "http://localhost:5000/api/optimize-resume" -Method POST -Body $testData -ContentType "application/json" -TimeoutSec 120
    
    Write-Host "✅ API调用成功!" -ForegroundColor Green
    Write-Host "📊 状态码: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "📄 响应内容:" -ForegroundColor Cyan
    
    # 解析JSON响应
    $jsonResponse = $response.Content | ConvertFrom-Json
    
    # 显示关键信息
    Write-Host "成功状态: $($jsonResponse.success)" -ForegroundColor $(if($jsonResponse.success) {"Green"} else {"Red"})
    
    if ($jsonResponse.optimized_resume) {
        Write-Host "✅ 优化简历长度: $($jsonResponse.optimized_resume.Length) 字符" -ForegroundColor Green
        Write-Host "📄 优化简历前200字符: $($jsonResponse.optimized_resume.Substring(0, [Math]::Min(200, $jsonResponse.optimized_resume.Length)))" -ForegroundColor White
    } else {
        Write-Host "❌ 没有优化简历内容" -ForegroundColor Red
    }
    
    if ($jsonResponse.optimized_resume_json) {
        Write-Host "✅ 优化JSON数组长度: $($jsonResponse.optimized_resume_json.Count)" -ForegroundColor Green
    } else {
        Write-Host "❌ 没有优化JSON数据" -ForegroundColor Red
    }
    
    if ($jsonResponse.error) {
        Write-Host "❌ 错误信息: $($jsonResponse.error)" -ForegroundColor Red
    }
    
    # 保存完整响应到文件
    $jsonResponse | ConvertTo-Json -Depth 10 | Out-File -FilePath "api_test_response.json" -Encoding UTF8
    Write-Host "📁 完整响应已保存到 api_test_response.json" -ForegroundColor Blue
    
} catch {
    Write-Host "❌ API调用失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "详细错误: $($_.Exception)" -ForegroundColor Gray
}
