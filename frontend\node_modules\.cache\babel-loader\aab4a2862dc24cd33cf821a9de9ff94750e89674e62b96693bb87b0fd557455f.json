{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\n// TODO depends on DataZoom and Brush\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport BrushController from '../../helper/BrushController.js';\nimport BrushTargetManager from '../../helper/BrushTargetManager.js';\nimport * as history from '../../dataZoom/history.js';\nimport sliderMove from '../../helper/sliderMove.js';\nimport { ToolboxFeature } from '../featureManager.js';\nimport { makeInternalComponentId, parseFinder } from '../../../util/model.js';\nimport { registerInternalOptionCreator } from '../../../model/internalComponentCreator.js';\nvar each = zrUtil.each;\nvar DATA_ZOOM_ID_BASE = makeInternalComponentId('toolbox-dataZoom_');\nvar ICON_TYPES = ['zoom', 'back'];\nvar DataZoomFeature = /** @class */function (_super) {\n  __extends(DataZoomFeature, _super);\n  function DataZoomFeature() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  DataZoomFeature.prototype.render = function (featureModel, ecModel, api, payload) {\n    if (!this._brushController) {\n      this._brushController = new BrushController(api.getZr());\n      this._brushController.on('brush', zrUtil.bind(this._onBrush, this)).mount();\n    }\n    updateZoomBtnStatus(featureModel, ecModel, this, payload, api);\n    updateBackBtnStatus(featureModel, ecModel);\n  };\n  DataZoomFeature.prototype.onclick = function (ecModel, api, type) {\n    handlers[type].call(this);\n  };\n  DataZoomFeature.prototype.remove = function (ecModel, api) {\n    this._brushController && this._brushController.unmount();\n  };\n  DataZoomFeature.prototype.dispose = function (ecModel, api) {\n    this._brushController && this._brushController.dispose();\n  };\n  DataZoomFeature.prototype._onBrush = function (eventParam) {\n    var areas = eventParam.areas;\n    if (!eventParam.isEnd || !areas.length) {\n      return;\n    }\n    var snapshot = {};\n    var ecModel = this.ecModel;\n    this._brushController.updateCovers([]); // remove cover\n    var brushTargetManager = new BrushTargetManager(makeAxisFinder(this.model), ecModel, {\n      include: ['grid']\n    });\n    brushTargetManager.matchOutputRanges(areas, ecModel, function (area, coordRange, coordSys) {\n      if (coordSys.type !== 'cartesian2d') {\n        return;\n      }\n      var brushType = area.brushType;\n      if (brushType === 'rect') {\n        setBatch('x', coordSys, coordRange[0]);\n        setBatch('y', coordSys, coordRange[1]);\n      } else {\n        setBatch({\n          lineX: 'x',\n          lineY: 'y'\n        }[brushType], coordSys, coordRange);\n      }\n    });\n    history.push(ecModel, snapshot);\n    this._dispatchZoomAction(snapshot);\n    function setBatch(dimName, coordSys, minMax) {\n      var axis = coordSys.getAxis(dimName);\n      var axisModel = axis.model;\n      var dataZoomModel = findDataZoom(dimName, axisModel, ecModel);\n      // Restrict range.\n      var minMaxSpan = dataZoomModel.findRepresentativeAxisProxy(axisModel).getMinMaxSpan();\n      if (minMaxSpan.minValueSpan != null || minMaxSpan.maxValueSpan != null) {\n        minMax = sliderMove(0, minMax.slice(), axis.scale.getExtent(), 0, minMaxSpan.minValueSpan, minMaxSpan.maxValueSpan);\n      }\n      dataZoomModel && (snapshot[dataZoomModel.id] = {\n        dataZoomId: dataZoomModel.id,\n        startValue: minMax[0],\n        endValue: minMax[1]\n      });\n    }\n    function findDataZoom(dimName, axisModel, ecModel) {\n      var found;\n      ecModel.eachComponent({\n        mainType: 'dataZoom',\n        subType: 'select'\n      }, function (dzModel) {\n        var has = dzModel.getAxisModel(dimName, axisModel.componentIndex);\n        has && (found = dzModel);\n      });\n      return found;\n    }\n  };\n  ;\n  DataZoomFeature.prototype._dispatchZoomAction = function (snapshot) {\n    var batch = [];\n    // Convert from hash map to array.\n    each(snapshot, function (batchItem, dataZoomId) {\n      batch.push(zrUtil.clone(batchItem));\n    });\n    batch.length && this.api.dispatchAction({\n      type: 'dataZoom',\n      from: this.uid,\n      batch: batch\n    });\n  };\n  DataZoomFeature.getDefaultOption = function (ecModel) {\n    var defaultOption = {\n      show: true,\n      filterMode: 'filter',\n      // Icon group\n      icon: {\n        zoom: 'M0,13.5h26.9 M13.5,26.9V0 M32.1,13.5H58V58H13.5 V32.1',\n        back: 'M22,1.4L9.9,13.5l12.3,12.3 M10.3,13.5H54.9v44.6 H10.3v-26'\n      },\n      // `zoom`, `back`\n      title: ecModel.getLocaleModel().get(['toolbox', 'dataZoom', 'title']),\n      brushStyle: {\n        borderWidth: 0,\n        color: 'rgba(210,219,238,0.2)'\n      }\n    };\n    return defaultOption;\n  };\n  return DataZoomFeature;\n}(ToolboxFeature);\nvar handlers = {\n  zoom: function () {\n    var nextActive = !this._isZoomActive;\n    this.api.dispatchAction({\n      type: 'takeGlobalCursor',\n      key: 'dataZoomSelect',\n      dataZoomSelectActive: nextActive\n    });\n  },\n  back: function () {\n    this._dispatchZoomAction(history.pop(this.ecModel));\n  }\n};\nfunction makeAxisFinder(dzFeatureModel) {\n  var setting = {\n    xAxisIndex: dzFeatureModel.get('xAxisIndex', true),\n    yAxisIndex: dzFeatureModel.get('yAxisIndex', true),\n    xAxisId: dzFeatureModel.get('xAxisId', true),\n    yAxisId: dzFeatureModel.get('yAxisId', true)\n  };\n  // If both `xAxisIndex` `xAxisId` not set, it means 'all'.\n  // If both `yAxisIndex` `yAxisId` not set, it means 'all'.\n  // Some old cases set like this below to close yAxis control but leave xAxis control:\n  // `{ feature: { dataZoom: { yAxisIndex: false } }`.\n  if (setting.xAxisIndex == null && setting.xAxisId == null) {\n    setting.xAxisIndex = 'all';\n  }\n  if (setting.yAxisIndex == null && setting.yAxisId == null) {\n    setting.yAxisIndex = 'all';\n  }\n  return setting;\n}\nfunction updateBackBtnStatus(featureModel, ecModel) {\n  featureModel.setIconStatus('back', history.count(ecModel) > 1 ? 'emphasis' : 'normal');\n}\nfunction updateZoomBtnStatus(featureModel, ecModel, view, payload, api) {\n  var zoomActive = view._isZoomActive;\n  if (payload && payload.type === 'takeGlobalCursor') {\n    zoomActive = payload.key === 'dataZoomSelect' ? payload.dataZoomSelectActive : false;\n  }\n  view._isZoomActive = zoomActive;\n  featureModel.setIconStatus('zoom', zoomActive ? 'emphasis' : 'normal');\n  var brushTargetManager = new BrushTargetManager(makeAxisFinder(featureModel), ecModel, {\n    include: ['grid']\n  });\n  var panels = brushTargetManager.makePanelOpts(api, function (targetInfo) {\n    return targetInfo.xAxisDeclared && !targetInfo.yAxisDeclared ? 'lineX' : !targetInfo.xAxisDeclared && targetInfo.yAxisDeclared ? 'lineY' : 'rect';\n  });\n  view._brushController.setPanels(panels).enableBrush(zoomActive && panels.length ? {\n    brushType: 'auto',\n    brushStyle: featureModel.getModel('brushStyle').getItemStyle()\n  } : false);\n}\nregisterInternalOptionCreator('dataZoom', function (ecModel) {\n  var toolboxModel = ecModel.getComponent('toolbox', 0);\n  var featureDataZoomPath = ['feature', 'dataZoom'];\n  if (!toolboxModel || toolboxModel.get(featureDataZoomPath) == null) {\n    return;\n  }\n  var dzFeatureModel = toolboxModel.getModel(featureDataZoomPath);\n  var dzOptions = [];\n  var finder = makeAxisFinder(dzFeatureModel);\n  var finderResult = parseFinder(ecModel, finder);\n  each(finderResult.xAxisModels, function (axisModel) {\n    return buildInternalOptions(axisModel, 'xAxis', 'xAxisIndex');\n  });\n  each(finderResult.yAxisModels, function (axisModel) {\n    return buildInternalOptions(axisModel, 'yAxis', 'yAxisIndex');\n  });\n  function buildInternalOptions(axisModel, axisMainType, axisIndexPropName) {\n    var axisIndex = axisModel.componentIndex;\n    var newOpt = {\n      type: 'select',\n      $fromToolbox: true,\n      // Default to be filter\n      filterMode: dzFeatureModel.get('filterMode', true) || 'filter',\n      // Id for merge mapping.\n      id: DATA_ZOOM_ID_BASE + axisMainType + axisIndex\n    };\n    newOpt[axisIndexPropName] = axisIndex;\n    dzOptions.push(newOpt);\n  }\n  return dzOptions;\n});\nexport default DataZoomFeature;", "map": {"version": 3, "names": ["__extends", "zrUtil", "BrushController", "BrushTargetManager", "history", "slider<PERSON><PERSON>", "ToolboxFeature", "makeInternalComponentId", "parseFinder", "registerInternalOptionCreator", "each", "DATA_ZOOM_ID_BASE", "ICON_TYPES", "DataZoomFeature", "_super", "apply", "arguments", "prototype", "render", "featureModel", "ecModel", "api", "payload", "_brushController", "getZr", "on", "bind", "_onBrush", "mount", "updateZoomBtnStatus", "updateBackBtnStatus", "onclick", "type", "handlers", "call", "remove", "unmount", "dispose", "eventParam", "areas", "isEnd", "length", "snapshot", "updateCovers", "brushTargetManager", "makeAxisFinder", "model", "include", "matchOutputRanges", "area", "coordRange", "coordSys", "brushType", "setBatch", "lineX", "lineY", "push", "_dispatchZoomAction", "dimName", "minMax", "axis", "getAxis", "axisModel", "dataZoomModel", "findDataZoom", "minMaxSpan", "findRepresentativeAxisProxy", "getMinMaxSpan", "minValueSpan", "maxValueSpan", "slice", "scale", "getExtent", "id", "dataZoomId", "startValue", "endValue", "found", "eachComponent", "mainType", "subType", "dzModel", "has", "getAxisModel", "componentIndex", "batch", "batchItem", "clone", "dispatchAction", "from", "uid", "getDefaultOption", "defaultOption", "show", "filterMode", "icon", "zoom", "back", "title", "getLocaleModel", "get", "brushStyle", "borderWidth", "color", "nextActive", "_isZoomActive", "key", "dataZoomSelectActive", "pop", "dzFeatureModel", "setting", "xAxisIndex", "yAxisIndex", "xAxisId", "yAxisId", "setIconStatus", "count", "view", "zoomActive", "panels", "makePanelOpts", "targetInfo", "xAxisDeclared", "yAxisDeclared", "set<PERSON><PERSON><PERSON>", "enableBrush", "getModel", "getItemStyle", "toolboxModel", "getComponent", "featureDataZoomPath", "dzOptions", "finder", "finderResult", "xAxisModels", "buildInternalOptions", "yAxisModels", "axisMainType", "axisIndexPropName", "axisIndex", "newOpt", "$fromToolbox"], "sources": ["E:/AI/SmartCV/node_modules/echarts/lib/component/toolbox/feature/DataZoom.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\n// TODO depends on DataZoom and Brush\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport BrushController from '../../helper/BrushController.js';\nimport BrushTargetManager from '../../helper/BrushTargetManager.js';\nimport * as history from '../../dataZoom/history.js';\nimport sliderMove from '../../helper/sliderMove.js';\nimport { ToolboxFeature } from '../featureManager.js';\nimport { makeInternalComponentId, parseFinder } from '../../../util/model.js';\nimport { registerInternalOptionCreator } from '../../../model/internalComponentCreator.js';\nvar each = zrUtil.each;\nvar DATA_ZOOM_ID_BASE = makeInternalComponentId('toolbox-dataZoom_');\nvar ICON_TYPES = ['zoom', 'back'];\nvar DataZoomFeature = /** @class */function (_super) {\n  __extends(DataZoomFeature, _super);\n  function DataZoomFeature() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  DataZoomFeature.prototype.render = function (featureModel, ecModel, api, payload) {\n    if (!this._brushController) {\n      this._brushController = new BrushController(api.getZr());\n      this._brushController.on('brush', zrUtil.bind(this._onBrush, this)).mount();\n    }\n    updateZoomBtnStatus(featureModel, ecModel, this, payload, api);\n    updateBackBtnStatus(featureModel, ecModel);\n  };\n  DataZoomFeature.prototype.onclick = function (ecModel, api, type) {\n    handlers[type].call(this);\n  };\n  DataZoomFeature.prototype.remove = function (ecModel, api) {\n    this._brushController && this._brushController.unmount();\n  };\n  DataZoomFeature.prototype.dispose = function (ecModel, api) {\n    this._brushController && this._brushController.dispose();\n  };\n  DataZoomFeature.prototype._onBrush = function (eventParam) {\n    var areas = eventParam.areas;\n    if (!eventParam.isEnd || !areas.length) {\n      return;\n    }\n    var snapshot = {};\n    var ecModel = this.ecModel;\n    this._brushController.updateCovers([]); // remove cover\n    var brushTargetManager = new BrushTargetManager(makeAxisFinder(this.model), ecModel, {\n      include: ['grid']\n    });\n    brushTargetManager.matchOutputRanges(areas, ecModel, function (area, coordRange, coordSys) {\n      if (coordSys.type !== 'cartesian2d') {\n        return;\n      }\n      var brushType = area.brushType;\n      if (brushType === 'rect') {\n        setBatch('x', coordSys, coordRange[0]);\n        setBatch('y', coordSys, coordRange[1]);\n      } else {\n        setBatch({\n          lineX: 'x',\n          lineY: 'y'\n        }[brushType], coordSys, coordRange);\n      }\n    });\n    history.push(ecModel, snapshot);\n    this._dispatchZoomAction(snapshot);\n    function setBatch(dimName, coordSys, minMax) {\n      var axis = coordSys.getAxis(dimName);\n      var axisModel = axis.model;\n      var dataZoomModel = findDataZoom(dimName, axisModel, ecModel);\n      // Restrict range.\n      var minMaxSpan = dataZoomModel.findRepresentativeAxisProxy(axisModel).getMinMaxSpan();\n      if (minMaxSpan.minValueSpan != null || minMaxSpan.maxValueSpan != null) {\n        minMax = sliderMove(0, minMax.slice(), axis.scale.getExtent(), 0, minMaxSpan.minValueSpan, minMaxSpan.maxValueSpan);\n      }\n      dataZoomModel && (snapshot[dataZoomModel.id] = {\n        dataZoomId: dataZoomModel.id,\n        startValue: minMax[0],\n        endValue: minMax[1]\n      });\n    }\n    function findDataZoom(dimName, axisModel, ecModel) {\n      var found;\n      ecModel.eachComponent({\n        mainType: 'dataZoom',\n        subType: 'select'\n      }, function (dzModel) {\n        var has = dzModel.getAxisModel(dimName, axisModel.componentIndex);\n        has && (found = dzModel);\n      });\n      return found;\n    }\n  };\n  ;\n  DataZoomFeature.prototype._dispatchZoomAction = function (snapshot) {\n    var batch = [];\n    // Convert from hash map to array.\n    each(snapshot, function (batchItem, dataZoomId) {\n      batch.push(zrUtil.clone(batchItem));\n    });\n    batch.length && this.api.dispatchAction({\n      type: 'dataZoom',\n      from: this.uid,\n      batch: batch\n    });\n  };\n  DataZoomFeature.getDefaultOption = function (ecModel) {\n    var defaultOption = {\n      show: true,\n      filterMode: 'filter',\n      // Icon group\n      icon: {\n        zoom: 'M0,13.5h26.9 M13.5,26.9V0 M32.1,13.5H58V58H13.5 V32.1',\n        back: 'M22,1.4L9.9,13.5l12.3,12.3 M10.3,13.5H54.9v44.6 H10.3v-26'\n      },\n      // `zoom`, `back`\n      title: ecModel.getLocaleModel().get(['toolbox', 'dataZoom', 'title']),\n      brushStyle: {\n        borderWidth: 0,\n        color: 'rgba(210,219,238,0.2)'\n      }\n    };\n    return defaultOption;\n  };\n  return DataZoomFeature;\n}(ToolboxFeature);\nvar handlers = {\n  zoom: function () {\n    var nextActive = !this._isZoomActive;\n    this.api.dispatchAction({\n      type: 'takeGlobalCursor',\n      key: 'dataZoomSelect',\n      dataZoomSelectActive: nextActive\n    });\n  },\n  back: function () {\n    this._dispatchZoomAction(history.pop(this.ecModel));\n  }\n};\nfunction makeAxisFinder(dzFeatureModel) {\n  var setting = {\n    xAxisIndex: dzFeatureModel.get('xAxisIndex', true),\n    yAxisIndex: dzFeatureModel.get('yAxisIndex', true),\n    xAxisId: dzFeatureModel.get('xAxisId', true),\n    yAxisId: dzFeatureModel.get('yAxisId', true)\n  };\n  // If both `xAxisIndex` `xAxisId` not set, it means 'all'.\n  // If both `yAxisIndex` `yAxisId` not set, it means 'all'.\n  // Some old cases set like this below to close yAxis control but leave xAxis control:\n  // `{ feature: { dataZoom: { yAxisIndex: false } }`.\n  if (setting.xAxisIndex == null && setting.xAxisId == null) {\n    setting.xAxisIndex = 'all';\n  }\n  if (setting.yAxisIndex == null && setting.yAxisId == null) {\n    setting.yAxisIndex = 'all';\n  }\n  return setting;\n}\nfunction updateBackBtnStatus(featureModel, ecModel) {\n  featureModel.setIconStatus('back', history.count(ecModel) > 1 ? 'emphasis' : 'normal');\n}\nfunction updateZoomBtnStatus(featureModel, ecModel, view, payload, api) {\n  var zoomActive = view._isZoomActive;\n  if (payload && payload.type === 'takeGlobalCursor') {\n    zoomActive = payload.key === 'dataZoomSelect' ? payload.dataZoomSelectActive : false;\n  }\n  view._isZoomActive = zoomActive;\n  featureModel.setIconStatus('zoom', zoomActive ? 'emphasis' : 'normal');\n  var brushTargetManager = new BrushTargetManager(makeAxisFinder(featureModel), ecModel, {\n    include: ['grid']\n  });\n  var panels = brushTargetManager.makePanelOpts(api, function (targetInfo) {\n    return targetInfo.xAxisDeclared && !targetInfo.yAxisDeclared ? 'lineX' : !targetInfo.xAxisDeclared && targetInfo.yAxisDeclared ? 'lineY' : 'rect';\n  });\n  view._brushController.setPanels(panels).enableBrush(zoomActive && panels.length ? {\n    brushType: 'auto',\n    brushStyle: featureModel.getModel('brushStyle').getItemStyle()\n  } : false);\n}\nregisterInternalOptionCreator('dataZoom', function (ecModel) {\n  var toolboxModel = ecModel.getComponent('toolbox', 0);\n  var featureDataZoomPath = ['feature', 'dataZoom'];\n  if (!toolboxModel || toolboxModel.get(featureDataZoomPath) == null) {\n    return;\n  }\n  var dzFeatureModel = toolboxModel.getModel(featureDataZoomPath);\n  var dzOptions = [];\n  var finder = makeAxisFinder(dzFeatureModel);\n  var finderResult = parseFinder(ecModel, finder);\n  each(finderResult.xAxisModels, function (axisModel) {\n    return buildInternalOptions(axisModel, 'xAxis', 'xAxisIndex');\n  });\n  each(finderResult.yAxisModels, function (axisModel) {\n    return buildInternalOptions(axisModel, 'yAxis', 'yAxisIndex');\n  });\n  function buildInternalOptions(axisModel, axisMainType, axisIndexPropName) {\n    var axisIndex = axisModel.componentIndex;\n    var newOpt = {\n      type: 'select',\n      $fromToolbox: true,\n      // Default to be filter\n      filterMode: dzFeatureModel.get('filterMode', true) || 'filter',\n      // Id for merge mapping.\n      id: DATA_ZOOM_ID_BASE + axisMainType + axisIndex\n    };\n    newOpt[axisIndexPropName] = axisIndex;\n    dzOptions.push(newOpt);\n  }\n  return dzOptions;\n});\nexport default DataZoomFeature;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC;AACA,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,OAAOC,eAAe,MAAM,iCAAiC;AAC7D,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAO,KAAKC,OAAO,MAAM,2BAA2B;AACpD,OAAOC,UAAU,MAAM,4BAA4B;AACnD,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,uBAAuB,EAAEC,WAAW,QAAQ,wBAAwB;AAC7E,SAASC,6BAA6B,QAAQ,4CAA4C;AAC1F,IAAIC,IAAI,GAAGT,MAAM,CAACS,IAAI;AACtB,IAAIC,iBAAiB,GAAGJ,uBAAuB,CAAC,mBAAmB,CAAC;AACpE,IAAIK,UAAU,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC;AACjC,IAAIC,eAAe,GAAG,aAAa,UAAUC,MAAM,EAAE;EACnDd,SAAS,CAACa,eAAe,EAAEC,MAAM,CAAC;EAClC,SAASD,eAAeA,CAAA,EAAG;IACzB,OAAOC,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;EACjE;EACAH,eAAe,CAACI,SAAS,CAACC,MAAM,GAAG,UAAUC,YAAY,EAAEC,OAAO,EAAEC,GAAG,EAAEC,OAAO,EAAE;IAChF,IAAI,CAAC,IAAI,CAACC,gBAAgB,EAAE;MAC1B,IAAI,CAACA,gBAAgB,GAAG,IAAIrB,eAAe,CAACmB,GAAG,CAACG,KAAK,CAAC,CAAC,CAAC;MACxD,IAAI,CAACD,gBAAgB,CAACE,EAAE,CAAC,OAAO,EAAExB,MAAM,CAACyB,IAAI,CAAC,IAAI,CAACC,QAAQ,EAAE,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;IAC7E;IACAC,mBAAmB,CAACV,YAAY,EAAEC,OAAO,EAAE,IAAI,EAAEE,OAAO,EAAED,GAAG,CAAC;IAC9DS,mBAAmB,CAACX,YAAY,EAAEC,OAAO,CAAC;EAC5C,CAAC;EACDP,eAAe,CAACI,SAAS,CAACc,OAAO,GAAG,UAAUX,OAAO,EAAEC,GAAG,EAAEW,IAAI,EAAE;IAChEC,QAAQ,CAACD,IAAI,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC;EAC3B,CAAC;EACDrB,eAAe,CAACI,SAAS,CAACkB,MAAM,GAAG,UAAUf,OAAO,EAAEC,GAAG,EAAE;IACzD,IAAI,CAACE,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAACa,OAAO,CAAC,CAAC;EAC1D,CAAC;EACDvB,eAAe,CAACI,SAAS,CAACoB,OAAO,GAAG,UAAUjB,OAAO,EAAEC,GAAG,EAAE;IAC1D,IAAI,CAACE,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAACc,OAAO,CAAC,CAAC;EAC1D,CAAC;EACDxB,eAAe,CAACI,SAAS,CAACU,QAAQ,GAAG,UAAUW,UAAU,EAAE;IACzD,IAAIC,KAAK,GAAGD,UAAU,CAACC,KAAK;IAC5B,IAAI,CAACD,UAAU,CAACE,KAAK,IAAI,CAACD,KAAK,CAACE,MAAM,EAAE;MACtC;IACF;IACA,IAAIC,QAAQ,GAAG,CAAC,CAAC;IACjB,IAAItB,OAAO,GAAG,IAAI,CAACA,OAAO;IAC1B,IAAI,CAACG,gBAAgB,CAACoB,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC;IACxC,IAAIC,kBAAkB,GAAG,IAAIzC,kBAAkB,CAAC0C,cAAc,CAAC,IAAI,CAACC,KAAK,CAAC,EAAE1B,OAAO,EAAE;MACnF2B,OAAO,EAAE,CAAC,MAAM;IAClB,CAAC,CAAC;IACFH,kBAAkB,CAACI,iBAAiB,CAACT,KAAK,EAAEnB,OAAO,EAAE,UAAU6B,IAAI,EAAEC,UAAU,EAAEC,QAAQ,EAAE;MACzF,IAAIA,QAAQ,CAACnB,IAAI,KAAK,aAAa,EAAE;QACnC;MACF;MACA,IAAIoB,SAAS,GAAGH,IAAI,CAACG,SAAS;MAC9B,IAAIA,SAAS,KAAK,MAAM,EAAE;QACxBC,QAAQ,CAAC,GAAG,EAAEF,QAAQ,EAAED,UAAU,CAAC,CAAC,CAAC,CAAC;QACtCG,QAAQ,CAAC,GAAG,EAAEF,QAAQ,EAAED,UAAU,CAAC,CAAC,CAAC,CAAC;MACxC,CAAC,MAAM;QACLG,QAAQ,CAAC;UACPC,KAAK,EAAE,GAAG;UACVC,KAAK,EAAE;QACT,CAAC,CAACH,SAAS,CAAC,EAAED,QAAQ,EAAED,UAAU,CAAC;MACrC;IACF,CAAC,CAAC;IACF9C,OAAO,CAACoD,IAAI,CAACpC,OAAO,EAAEsB,QAAQ,CAAC;IAC/B,IAAI,CAACe,mBAAmB,CAACf,QAAQ,CAAC;IAClC,SAASW,QAAQA,CAACK,OAAO,EAAEP,QAAQ,EAAEQ,MAAM,EAAE;MAC3C,IAAIC,IAAI,GAAGT,QAAQ,CAACU,OAAO,CAACH,OAAO,CAAC;MACpC,IAAII,SAAS,GAAGF,IAAI,CAACd,KAAK;MAC1B,IAAIiB,aAAa,GAAGC,YAAY,CAACN,OAAO,EAAEI,SAAS,EAAE1C,OAAO,CAAC;MAC7D;MACA,IAAI6C,UAAU,GAAGF,aAAa,CAACG,2BAA2B,CAACJ,SAAS,CAAC,CAACK,aAAa,CAAC,CAAC;MACrF,IAAIF,UAAU,CAACG,YAAY,IAAI,IAAI,IAAIH,UAAU,CAACI,YAAY,IAAI,IAAI,EAAE;QACtEV,MAAM,GAAGtD,UAAU,CAAC,CAAC,EAAEsD,MAAM,CAACW,KAAK,CAAC,CAAC,EAAEV,IAAI,CAACW,KAAK,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAEP,UAAU,CAACG,YAAY,EAAEH,UAAU,CAACI,YAAY,CAAC;MACrH;MACAN,aAAa,KAAKrB,QAAQ,CAACqB,aAAa,CAACU,EAAE,CAAC,GAAG;QAC7CC,UAAU,EAAEX,aAAa,CAACU,EAAE;QAC5BE,UAAU,EAAEhB,MAAM,CAAC,CAAC,CAAC;QACrBiB,QAAQ,EAAEjB,MAAM,CAAC,CAAC;MACpB,CAAC,CAAC;IACJ;IACA,SAASK,YAAYA,CAACN,OAAO,EAAEI,SAAS,EAAE1C,OAAO,EAAE;MACjD,IAAIyD,KAAK;MACTzD,OAAO,CAAC0D,aAAa,CAAC;QACpBC,QAAQ,EAAE,UAAU;QACpBC,OAAO,EAAE;MACX,CAAC,EAAE,UAAUC,OAAO,EAAE;QACpB,IAAIC,GAAG,GAAGD,OAAO,CAACE,YAAY,CAACzB,OAAO,EAAEI,SAAS,CAACsB,cAAc,CAAC;QACjEF,GAAG,KAAKL,KAAK,GAAGI,OAAO,CAAC;MAC1B,CAAC,CAAC;MACF,OAAOJ,KAAK;IACd;EACF,CAAC;EACD;EACAhE,eAAe,CAACI,SAAS,CAACwC,mBAAmB,GAAG,UAAUf,QAAQ,EAAE;IAClE,IAAI2C,KAAK,GAAG,EAAE;IACd;IACA3E,IAAI,CAACgC,QAAQ,EAAE,UAAU4C,SAAS,EAAEZ,UAAU,EAAE;MAC9CW,KAAK,CAAC7B,IAAI,CAACvD,MAAM,CAACsF,KAAK,CAACD,SAAS,CAAC,CAAC;IACrC,CAAC,CAAC;IACFD,KAAK,CAAC5C,MAAM,IAAI,IAAI,CAACpB,GAAG,CAACmE,cAAc,CAAC;MACtCxD,IAAI,EAAE,UAAU;MAChByD,IAAI,EAAE,IAAI,CAACC,GAAG;MACdL,KAAK,EAAEA;IACT,CAAC,CAAC;EACJ,CAAC;EACDxE,eAAe,CAAC8E,gBAAgB,GAAG,UAAUvE,OAAO,EAAE;IACpD,IAAIwE,aAAa,GAAG;MAClBC,IAAI,EAAE,IAAI;MACVC,UAAU,EAAE,QAAQ;MACpB;MACAC,IAAI,EAAE;QACJC,IAAI,EAAE,uDAAuD;QAC7DC,IAAI,EAAE;MACR,CAAC;MACD;MACAC,KAAK,EAAE9E,OAAO,CAAC+E,cAAc,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;MACrEC,UAAU,EAAE;QACVC,WAAW,EAAE,CAAC;QACdC,KAAK,EAAE;MACT;IACF,CAAC;IACD,OAAOX,aAAa;EACtB,CAAC;EACD,OAAO/E,eAAe;AACxB,CAAC,CAACP,cAAc,CAAC;AACjB,IAAI2B,QAAQ,GAAG;EACb+D,IAAI,EAAE,SAAAA,CAAA,EAAY;IAChB,IAAIQ,UAAU,GAAG,CAAC,IAAI,CAACC,aAAa;IACpC,IAAI,CAACpF,GAAG,CAACmE,cAAc,CAAC;MACtBxD,IAAI,EAAE,kBAAkB;MACxB0E,GAAG,EAAE,gBAAgB;MACrBC,oBAAoB,EAAEH;IACxB,CAAC,CAAC;EACJ,CAAC;EACDP,IAAI,EAAE,SAAAA,CAAA,EAAY;IAChB,IAAI,CAACxC,mBAAmB,CAACrD,OAAO,CAACwG,GAAG,CAAC,IAAI,CAACxF,OAAO,CAAC,CAAC;EACrD;AACF,CAAC;AACD,SAASyB,cAAcA,CAACgE,cAAc,EAAE;EACtC,IAAIC,OAAO,GAAG;IACZC,UAAU,EAAEF,cAAc,CAACT,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC;IAClDY,UAAU,EAAEH,cAAc,CAACT,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC;IAClDa,OAAO,EAAEJ,cAAc,CAACT,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC;IAC5Cc,OAAO,EAAEL,cAAc,CAACT,GAAG,CAAC,SAAS,EAAE,IAAI;EAC7C,CAAC;EACD;EACA;EACA;EACA;EACA,IAAIU,OAAO,CAACC,UAAU,IAAI,IAAI,IAAID,OAAO,CAACG,OAAO,IAAI,IAAI,EAAE;IACzDH,OAAO,CAACC,UAAU,GAAG,KAAK;EAC5B;EACA,IAAID,OAAO,CAACE,UAAU,IAAI,IAAI,IAAIF,OAAO,CAACI,OAAO,IAAI,IAAI,EAAE;IACzDJ,OAAO,CAACE,UAAU,GAAG,KAAK;EAC5B;EACA,OAAOF,OAAO;AAChB;AACA,SAAShF,mBAAmBA,CAACX,YAAY,EAAEC,OAAO,EAAE;EAClDD,YAAY,CAACgG,aAAa,CAAC,MAAM,EAAE/G,OAAO,CAACgH,KAAK,CAAChG,OAAO,CAAC,GAAG,CAAC,GAAG,UAAU,GAAG,QAAQ,CAAC;AACxF;AACA,SAASS,mBAAmBA,CAACV,YAAY,EAAEC,OAAO,EAAEiG,IAAI,EAAE/F,OAAO,EAAED,GAAG,EAAE;EACtE,IAAIiG,UAAU,GAAGD,IAAI,CAACZ,aAAa;EACnC,IAAInF,OAAO,IAAIA,OAAO,CAACU,IAAI,KAAK,kBAAkB,EAAE;IAClDsF,UAAU,GAAGhG,OAAO,CAACoF,GAAG,KAAK,gBAAgB,GAAGpF,OAAO,CAACqF,oBAAoB,GAAG,KAAK;EACtF;EACAU,IAAI,CAACZ,aAAa,GAAGa,UAAU;EAC/BnG,YAAY,CAACgG,aAAa,CAAC,MAAM,EAAEG,UAAU,GAAG,UAAU,GAAG,QAAQ,CAAC;EACtE,IAAI1E,kBAAkB,GAAG,IAAIzC,kBAAkB,CAAC0C,cAAc,CAAC1B,YAAY,CAAC,EAAEC,OAAO,EAAE;IACrF2B,OAAO,EAAE,CAAC,MAAM;EAClB,CAAC,CAAC;EACF,IAAIwE,MAAM,GAAG3E,kBAAkB,CAAC4E,aAAa,CAACnG,GAAG,EAAE,UAAUoG,UAAU,EAAE;IACvE,OAAOA,UAAU,CAACC,aAAa,IAAI,CAACD,UAAU,CAACE,aAAa,GAAG,OAAO,GAAG,CAACF,UAAU,CAACC,aAAa,IAAID,UAAU,CAACE,aAAa,GAAG,OAAO,GAAG,MAAM;EACnJ,CAAC,CAAC;EACFN,IAAI,CAAC9F,gBAAgB,CAACqG,SAAS,CAACL,MAAM,CAAC,CAACM,WAAW,CAACP,UAAU,IAAIC,MAAM,CAAC9E,MAAM,GAAG;IAChFW,SAAS,EAAE,MAAM;IACjBiD,UAAU,EAAElF,YAAY,CAAC2G,QAAQ,CAAC,YAAY,CAAC,CAACC,YAAY,CAAC;EAC/D,CAAC,GAAG,KAAK,CAAC;AACZ;AACAtH,6BAA6B,CAAC,UAAU,EAAE,UAAUW,OAAO,EAAE;EAC3D,IAAI4G,YAAY,GAAG5G,OAAO,CAAC6G,YAAY,CAAC,SAAS,EAAE,CAAC,CAAC;EACrD,IAAIC,mBAAmB,GAAG,CAAC,SAAS,EAAE,UAAU,CAAC;EACjD,IAAI,CAACF,YAAY,IAAIA,YAAY,CAAC5B,GAAG,CAAC8B,mBAAmB,CAAC,IAAI,IAAI,EAAE;IAClE;EACF;EACA,IAAIrB,cAAc,GAAGmB,YAAY,CAACF,QAAQ,CAACI,mBAAmB,CAAC;EAC/D,IAAIC,SAAS,GAAG,EAAE;EAClB,IAAIC,MAAM,GAAGvF,cAAc,CAACgE,cAAc,CAAC;EAC3C,IAAIwB,YAAY,GAAG7H,WAAW,CAACY,OAAO,EAAEgH,MAAM,CAAC;EAC/C1H,IAAI,CAAC2H,YAAY,CAACC,WAAW,EAAE,UAAUxE,SAAS,EAAE;IAClD,OAAOyE,oBAAoB,CAACzE,SAAS,EAAE,OAAO,EAAE,YAAY,CAAC;EAC/D,CAAC,CAAC;EACFpD,IAAI,CAAC2H,YAAY,CAACG,WAAW,EAAE,UAAU1E,SAAS,EAAE;IAClD,OAAOyE,oBAAoB,CAACzE,SAAS,EAAE,OAAO,EAAE,YAAY,CAAC;EAC/D,CAAC,CAAC;EACF,SAASyE,oBAAoBA,CAACzE,SAAS,EAAE2E,YAAY,EAAEC,iBAAiB,EAAE;IACxE,IAAIC,SAAS,GAAG7E,SAAS,CAACsB,cAAc;IACxC,IAAIwD,MAAM,GAAG;MACX5G,IAAI,EAAE,QAAQ;MACd6G,YAAY,EAAE,IAAI;MAClB;MACA/C,UAAU,EAAEe,cAAc,CAACT,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,IAAI,QAAQ;MAC9D;MACA3B,EAAE,EAAE9D,iBAAiB,GAAG8H,YAAY,GAAGE;IACzC,CAAC;IACDC,MAAM,CAACF,iBAAiB,CAAC,GAAGC,SAAS;IACrCR,SAAS,CAAC3E,IAAI,CAACoF,MAAM,CAAC;EACxB;EACA,OAAOT,SAAS;AAClB,CAAC,CAAC;AACF,eAAetH,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}