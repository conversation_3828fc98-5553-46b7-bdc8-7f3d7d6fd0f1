# Simple API test script

$testData = @{
    resume_text = "<PERSON>, Software Engineer, 3 years Python experience, familiar with web development, Django project experience. Education: Bachelor in Computer Science. Skills: Python, JavaScript, HTML, CSS, MySQL."
    job_description = "We are looking for a Senior Python Developer. Requirements: 5+ years Python experience, familiar with Django or Flask, React frontend skills, PostgreSQL database, excellent communication, Computer Science degree."
}

$jsonData = $testData | ConvertTo-Json

Write-Host "Testing resume optimization API..." -ForegroundColor Yellow

try {
    $response = Invoke-WebRequest -Uri "http://localhost:5000/api/optimize-resume" -Method POST -Body $jsonData -ContentType "application/json" -TimeoutSec 120
    
    Write-Host "API call successful!" -ForegroundColor Green
    Write-Host "Status Code: $($response.StatusCode)" -ForegroundColor Green
    
    $jsonResponse = $response.Content | ConvertFrom-Json
    
    Write-Host "Success: $($jsonResponse.success)" -ForegroundColor $(if($jsonResponse.success) {"Green"} else {"Red"})
    
    if ($jsonResponse.optimized_resume) {
        Write-Host "Optimized resume length: $($jsonResponse.optimized_resume.Length) characters" -ForegroundColor Green
        Write-Host "First 200 chars: $($jsonResponse.optimized_resume.Substring(0, [Math]::Min(200, $jsonResponse.optimized_resume.Length)))" -ForegroundColor White
    } else {
        Write-Host "No optimized resume content" -ForegroundColor Red
    }
    
    if ($jsonResponse.optimized_resume_json) {
        Write-Host "Optimized JSON array length: $($jsonResponse.optimized_resume_json.Count)" -ForegroundColor Green
    } else {
        Write-Host "No optimized JSON data" -ForegroundColor Red
    }
    
    if ($jsonResponse.error) {
        Write-Host "Error: $($jsonResponse.error)" -ForegroundColor Red
    }
    
    # Save full response
    $jsonResponse | ConvertTo-Json -Depth 10 | Out-File -FilePath "api_response.json" -Encoding UTF8
    Write-Host "Full response saved to api_response.json" -ForegroundColor Blue
    
} catch {
    Write-Host "API call failed: $($_.Exception.Message)" -ForegroundColor Red
}
