{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n * Licensed to the Apache Software Foundation (ASF) under one\r\n * or more contributor license agreements.  See the NOTICE file\r\n * distributed with this work for additional information\r\n * regarding copyright ownership.  The ASF licenses this file\r\n * to you under the Apache License, Version 2.0 (the\r\n * \"License\"); you may not use this file except in compliance\r\n * with the License.  You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing,\r\n * software distributed under the License is distributed on an\r\n * \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n * KIND, either express or implied.  See the License for the\r\n * specific language governing permissions and limitations\r\n * under the License.\r\n */\n/**\r\n * Language: English.\r\n */\nexport default {\n  time: {\n    month: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'],\n    monthAbbr: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],\n    dayOfWeek: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],\n    dayOfWeekAbbr: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']\n  },\n  legend: {\n    selector: {\n      all: 'All',\n      inverse: 'Inv'\n    }\n  },\n  toolbox: {\n    brush: {\n      title: {\n        rect: 'Box Select',\n        polygon: 'Lasso Select',\n        lineX: 'Horizontally Select',\n        lineY: 'Vertically Select',\n        keep: 'Keep Selections',\n        clear: 'Clear Selections'\n      }\n    },\n    dataView: {\n      title: 'Data View',\n      lang: ['Data View', 'Close', 'Refresh']\n    },\n    dataZoom: {\n      title: {\n        zoom: 'Zoom',\n        back: 'Zoom Reset'\n      }\n    },\n    magicType: {\n      title: {\n        line: 'Switch to Line Chart',\n        bar: 'Switch to Bar Chart',\n        stack: 'Stack',\n        tiled: 'Tile'\n      }\n    },\n    restore: {\n      title: 'Restore'\n    },\n    saveAsImage: {\n      title: 'Save as Image',\n      lang: ['Right Click to Save Image']\n    }\n  },\n  series: {\n    typeNames: {\n      pie: 'Pie chart',\n      bar: 'Bar chart',\n      line: 'Line chart',\n      scatter: 'Scatter plot',\n      effectScatter: 'Ripple scatter plot',\n      radar: 'Radar chart',\n      tree: 'Tree',\n      treemap: 'Treemap',\n      boxplot: 'Boxplot',\n      candlestick: 'Candlestick',\n      k: 'K line chart',\n      heatmap: 'Heat map',\n      map: 'Map',\n      parallel: 'Parallel coordinate map',\n      lines: 'Line graph',\n      graph: 'Relationship graph',\n      sankey: 'Sankey diagram',\n      funnel: 'Funnel chart',\n      gauge: 'Gauge',\n      pictorialBar: 'Pictorial bar',\n      themeRiver: 'Theme River Map',\n      sunburst: 'Sunburst',\n      custom: 'Custom chart',\n      chart: 'Chart'\n    }\n  },\n  aria: {\n    general: {\n      withTitle: 'This is a chart about \"{title}\"',\n      withoutTitle: 'This is a chart'\n    },\n    series: {\n      single: {\n        prefix: '',\n        withName: ' with type {seriesType} named {seriesName}.',\n        withoutName: ' with type {seriesType}.'\n      },\n      multiple: {\n        prefix: '. It consists of {seriesCount} series count.',\n        withName: ' The {seriesId} series is a {seriesType} representing {seriesName}.',\n        withoutName: ' The {seriesId} series is a {seriesType}.',\n        separator: {\n          middle: '',\n          end: ''\n        }\n      }\n    },\n    data: {\n      allData: 'The data is as follows: ',\n      partialData: 'The first {displayCnt} items are: ',\n      withName: 'the data for {name} is {value}',\n      withoutName: '{value}',\n      separator: {\n        middle: ', ',\n        end: '. '\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["time", "month", "monthAbbr", "dayOfWeek", "dayOfWeekAbbr", "legend", "selector", "all", "inverse", "toolbox", "brush", "title", "rect", "polygon", "lineX", "lineY", "keep", "clear", "dataView", "lang", "dataZoom", "zoom", "back", "magicType", "line", "bar", "stack", "tiled", "restore", "saveAsImage", "series", "typeNames", "pie", "scatter", "effectScatter", "radar", "tree", "treemap", "boxplot", "candlestick", "k", "heatmap", "map", "parallel", "lines", "graph", "sankey", "funnel", "gauge", "pictorialBar", "themeRiver", "sunburst", "custom", "chart", "aria", "general", "with<PERSON><PERSON><PERSON>", "without<PERSON>itle", "single", "prefix", "with<PERSON><PERSON>", "without<PERSON><PERSON>", "multiple", "separator", "middle", "end", "data", "allData", "partialData"], "sources": ["E:/AI/SmartCV/node_modules/echarts/lib/i18n/langEN.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n * Licensed to the Apache Software Foundation (ASF) under one\r\n * or more contributor license agreements.  See the NOTICE file\r\n * distributed with this work for additional information\r\n * regarding copyright ownership.  The ASF licenses this file\r\n * to you under the Apache License, Version 2.0 (the\r\n * \"License\"); you may not use this file except in compliance\r\n * with the License.  You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing,\r\n * software distributed under the License is distributed on an\r\n * \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n * KIND, either express or implied.  See the License for the\r\n * specific language governing permissions and limitations\r\n * under the License.\r\n */\n/**\r\n * Language: English.\r\n */\nexport default {\n  time: {\n    month: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'],\n    monthAbbr: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],\n    dayOfWeek: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],\n    dayOfWeekAbbr: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']\n  },\n  legend: {\n    selector: {\n      all: 'All',\n      inverse: 'Inv'\n    }\n  },\n  toolbox: {\n    brush: {\n      title: {\n        rect: 'Box Select',\n        polygon: 'Lasso Select',\n        lineX: 'Horizontally Select',\n        lineY: 'Vertically Select',\n        keep: 'Keep Selections',\n        clear: 'Clear Selections'\n      }\n    },\n    dataView: {\n      title: 'Data View',\n      lang: ['Data View', 'Close', 'Refresh']\n    },\n    dataZoom: {\n      title: {\n        zoom: 'Zoom',\n        back: 'Zoom Reset'\n      }\n    },\n    magicType: {\n      title: {\n        line: 'Switch to Line Chart',\n        bar: 'Switch to Bar Chart',\n        stack: 'Stack',\n        tiled: 'Tile'\n      }\n    },\n    restore: {\n      title: 'Restore'\n    },\n    saveAsImage: {\n      title: 'Save as Image',\n      lang: ['Right Click to Save Image']\n    }\n  },\n  series: {\n    typeNames: {\n      pie: 'Pie chart',\n      bar: 'Bar chart',\n      line: 'Line chart',\n      scatter: 'Scatter plot',\n      effectScatter: 'Ripple scatter plot',\n      radar: 'Radar chart',\n      tree: 'Tree',\n      treemap: 'Treemap',\n      boxplot: 'Boxplot',\n      candlestick: 'Candlestick',\n      k: 'K line chart',\n      heatmap: 'Heat map',\n      map: 'Map',\n      parallel: 'Parallel coordinate map',\n      lines: 'Line graph',\n      graph: 'Relationship graph',\n      sankey: 'Sankey diagram',\n      funnel: 'Funnel chart',\n      gauge: 'Gauge',\n      pictorialBar: 'Pictorial bar',\n      themeRiver: 'Theme River Map',\n      sunburst: 'Sunburst',\n      custom: 'Custom chart',\n      chart: 'Chart'\n    }\n  },\n  aria: {\n    general: {\n      withTitle: 'This is a chart about \"{title}\"',\n      withoutTitle: 'This is a chart'\n    },\n    series: {\n      single: {\n        prefix: '',\n        withName: ' with type {seriesType} named {seriesName}.',\n        withoutName: ' with type {seriesType}.'\n      },\n      multiple: {\n        prefix: '. It consists of {seriesCount} series count.',\n        withName: ' The {seriesId} series is a {seriesType} representing {seriesName}.',\n        withoutName: ' The {seriesId} series is a {seriesType}.',\n        separator: {\n          middle: '',\n          end: ''\n        }\n      }\n    },\n    data: {\n      allData: 'The data is as follows: ',\n      partialData: 'The first {displayCnt} items are: ',\n      withName: 'the data for {name} is {value}',\n      withoutName: '{value}',\n      separator: {\n        middle: ', ',\n        end: '. '\n      }\n    }\n  }\n};"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;EACbA,IAAI,EAAE;IACJC,KAAK,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CAAC;IACjIC,SAAS,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAC/FC,SAAS,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,CAAC;IACzFC,aAAa,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK;EACjE,CAAC;EACDC,MAAM,EAAE;IACNC,QAAQ,EAAE;MACRC,GAAG,EAAE,KAAK;MACVC,OAAO,EAAE;IACX;EACF,CAAC;EACDC,OAAO,EAAE;IACPC,KAAK,EAAE;MACLC,KAAK,EAAE;QACLC,IAAI,EAAE,YAAY;QAClBC,OAAO,EAAE,cAAc;QACvBC,KAAK,EAAE,qBAAqB;QAC5BC,KAAK,EAAE,mBAAmB;QAC1BC,IAAI,EAAE,iBAAiB;QACvBC,KAAK,EAAE;MACT;IACF,CAAC;IACDC,QAAQ,EAAE;MACRP,KAAK,EAAE,WAAW;MAClBQ,IAAI,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,SAAS;IACxC,CAAC;IACDC,QAAQ,EAAE;MACRT,KAAK,EAAE;QACLU,IAAI,EAAE,MAAM;QACZC,IAAI,EAAE;MACR;IACF,CAAC;IACDC,SAAS,EAAE;MACTZ,KAAK,EAAE;QACLa,IAAI,EAAE,sBAAsB;QAC5BC,GAAG,EAAE,qBAAqB;QAC1BC,KAAK,EAAE,OAAO;QACdC,KAAK,EAAE;MACT;IACF,CAAC;IACDC,OAAO,EAAE;MACPjB,KAAK,EAAE;IACT,CAAC;IACDkB,WAAW,EAAE;MACXlB,KAAK,EAAE,eAAe;MACtBQ,IAAI,EAAE,CAAC,2BAA2B;IACpC;EACF,CAAC;EACDW,MAAM,EAAE;IACNC,SAAS,EAAE;MACTC,GAAG,EAAE,WAAW;MAChBP,GAAG,EAAE,WAAW;MAChBD,IAAI,EAAE,YAAY;MAClBS,OAAO,EAAE,cAAc;MACvBC,aAAa,EAAE,qBAAqB;MACpCC,KAAK,EAAE,aAAa;MACpBC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,SAAS;MAClBC,OAAO,EAAE,SAAS;MAClBC,WAAW,EAAE,aAAa;MAC1BC,CAAC,EAAE,cAAc;MACjBC,OAAO,EAAE,UAAU;MACnBC,GAAG,EAAE,KAAK;MACVC,QAAQ,EAAE,yBAAyB;MACnCC,KAAK,EAAE,YAAY;MACnBC,KAAK,EAAE,oBAAoB;MAC3BC,MAAM,EAAE,gBAAgB;MACxBC,MAAM,EAAE,cAAc;MACtBC,KAAK,EAAE,OAAO;MACdC,YAAY,EAAE,eAAe;MAC7BC,UAAU,EAAE,iBAAiB;MAC7BC,QAAQ,EAAE,UAAU;MACpBC,MAAM,EAAE,cAAc;MACtBC,KAAK,EAAE;IACT;EACF,CAAC;EACDC,IAAI,EAAE;IACJC,OAAO,EAAE;MACPC,SAAS,EAAE,iCAAiC;MAC5CC,YAAY,EAAE;IAChB,CAAC;IACD3B,MAAM,EAAE;MACN4B,MAAM,EAAE;QACNC,MAAM,EAAE,EAAE;QACVC,QAAQ,EAAE,6CAA6C;QACvDC,WAAW,EAAE;MACf,CAAC;MACDC,QAAQ,EAAE;QACRH,MAAM,EAAE,8CAA8C;QACtDC,QAAQ,EAAE,qEAAqE;QAC/EC,WAAW,EAAE,2CAA2C;QACxDE,SAAS,EAAE;UACTC,MAAM,EAAE,EAAE;UACVC,GAAG,EAAE;QACP;MACF;IACF,CAAC;IACDC,IAAI,EAAE;MACJC,OAAO,EAAE,0BAA0B;MACnCC,WAAW,EAAE,oCAAoC;MACjDR,QAAQ,EAAE,gCAAgC;MAC1CC,WAAW,EAAE,SAAS;MACtBE,SAAS,EAAE;QACTC,MAAM,EAAE,IAAI;QACZC,GAAG,EAAE;MACP;IACF;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}