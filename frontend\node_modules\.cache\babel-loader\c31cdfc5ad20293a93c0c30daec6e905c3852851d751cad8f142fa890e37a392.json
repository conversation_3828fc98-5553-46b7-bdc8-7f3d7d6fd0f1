{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { defaults } from 'zrender/lib/core/util.js';\nexport function installTimelineAction(registers) {\n  registers.registerAction({\n    type: 'timelineChange',\n    event: 'timelineChanged',\n    update: 'prepareAndUpdate'\n  }, function (payload, ecModel, api) {\n    var timelineModel = ecModel.getComponent('timeline');\n    if (timelineModel && payload.currentIndex != null) {\n      timelineModel.setCurrentIndex(payload.currentIndex);\n      if (!timelineModel.get('loop', true) && timelineModel.isIndexMax() && timelineModel.getPlayState()) {\n        timelineModel.setPlayState(false);\n        // The timeline has played to the end, trigger event\n        api.dispatchAction({\n          type: 'timelinePlayChange',\n          playState: false,\n          from: payload.from\n        });\n      }\n    }\n    // Set normalized currentIndex to payload.\n    ecModel.resetOption('timeline', {\n      replaceMerge: timelineModel.get('replaceMerge', true)\n    });\n    return defaults({\n      currentIndex: timelineModel.option.currentIndex\n    }, payload);\n  });\n  registers.registerAction({\n    type: 'timelinePlayChange',\n    event: 'timelinePlayChanged',\n    update: 'update'\n  }, function (payload, ecModel) {\n    var timelineModel = ecModel.getComponent('timeline');\n    if (timelineModel && payload.playState != null) {\n      timelineModel.setPlayState(payload.playState);\n    }\n  });\n}", "map": {"version": 3, "names": ["defaults", "installTimelineAction", "registers", "registerAction", "type", "event", "update", "payload", "ecModel", "api", "timelineModel", "getComponent", "currentIndex", "setCurrentIndex", "get", "isIndexMax", "getPlayState", "setPlayState", "dispatchAction", "playState", "from", "resetOption", "replaceMerge", "option"], "sources": ["E:/AI/SmartCV/node_modules/echarts/lib/component/timeline/timelineAction.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { defaults } from 'zrender/lib/core/util.js';\nexport function installTimelineAction(registers) {\n  registers.registerAction({\n    type: 'timelineChange',\n    event: 'timelineChanged',\n    update: 'prepareAndUpdate'\n  }, function (payload, ecModel, api) {\n    var timelineModel = ecModel.getComponent('timeline');\n    if (timelineModel && payload.currentIndex != null) {\n      timelineModel.setCurrentIndex(payload.currentIndex);\n      if (!timelineModel.get('loop', true) && timelineModel.isIndexMax() && timelineModel.getPlayState()) {\n        timelineModel.setPlayState(false);\n        // The timeline has played to the end, trigger event\n        api.dispatchAction({\n          type: 'timelinePlayChange',\n          playState: false,\n          from: payload.from\n        });\n      }\n    }\n    // Set normalized currentIndex to payload.\n    ecModel.resetOption('timeline', {\n      replaceMerge: timelineModel.get('replaceMerge', true)\n    });\n    return defaults({\n      currentIndex: timelineModel.option.currentIndex\n    }, payload);\n  });\n  registers.registerAction({\n    type: 'timelinePlayChange',\n    event: 'timelinePlayChanged',\n    update: 'update'\n  }, function (payload, ecModel) {\n    var timelineModel = ecModel.getComponent('timeline');\n    if (timelineModel && payload.playState != null) {\n      timelineModel.setPlayState(payload.playState);\n    }\n  });\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,QAAQ,QAAQ,0BAA0B;AACnD,OAAO,SAASC,qBAAqBA,CAACC,SAAS,EAAE;EAC/CA,SAAS,CAACC,cAAc,CAAC;IACvBC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,iBAAiB;IACxBC,MAAM,EAAE;EACV,CAAC,EAAE,UAAUC,OAAO,EAAEC,OAAO,EAAEC,GAAG,EAAE;IAClC,IAAIC,aAAa,GAAGF,OAAO,CAACG,YAAY,CAAC,UAAU,CAAC;IACpD,IAAID,aAAa,IAAIH,OAAO,CAACK,YAAY,IAAI,IAAI,EAAE;MACjDF,aAAa,CAACG,eAAe,CAACN,OAAO,CAACK,YAAY,CAAC;MACnD,IAAI,CAACF,aAAa,CAACI,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,IAAIJ,aAAa,CAACK,UAAU,CAAC,CAAC,IAAIL,aAAa,CAACM,YAAY,CAAC,CAAC,EAAE;QAClGN,aAAa,CAACO,YAAY,CAAC,KAAK,CAAC;QACjC;QACAR,GAAG,CAACS,cAAc,CAAC;UACjBd,IAAI,EAAE,oBAAoB;UAC1Be,SAAS,EAAE,KAAK;UAChBC,IAAI,EAAEb,OAAO,CAACa;QAChB,CAAC,CAAC;MACJ;IACF;IACA;IACAZ,OAAO,CAACa,WAAW,CAAC,UAAU,EAAE;MAC9BC,YAAY,EAAEZ,aAAa,CAACI,GAAG,CAAC,cAAc,EAAE,IAAI;IACtD,CAAC,CAAC;IACF,OAAOd,QAAQ,CAAC;MACdY,YAAY,EAAEF,aAAa,CAACa,MAAM,CAACX;IACrC,CAAC,EAAEL,OAAO,CAAC;EACb,CAAC,CAAC;EACFL,SAAS,CAACC,cAAc,CAAC;IACvBC,IAAI,EAAE,oBAAoB;IAC1BC,KAAK,EAAE,qBAAqB;IAC5BC,MAAM,EAAE;EACV,CAAC,EAAE,UAAUC,OAAO,EAAEC,OAAO,EAAE;IAC7B,IAAIE,aAAa,GAAGF,OAAO,CAACG,YAAY,CAAC,UAAU,CAAC;IACpD,IAAID,aAAa,IAAIH,OAAO,CAACY,SAAS,IAAI,IAAI,EAAE;MAC9CT,aAAa,CAACO,YAAY,CAACV,OAAO,CAACY,SAAS,CAAC;IAC/C;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}