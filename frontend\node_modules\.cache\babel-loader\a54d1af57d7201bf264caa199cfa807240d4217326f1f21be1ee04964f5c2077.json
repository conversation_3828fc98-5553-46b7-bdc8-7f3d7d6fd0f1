{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/**\r\n * Calculate slider move result.\r\n * Usage:\r\n * (1) If both handle0 and handle1 are needed to be moved, set minSpan the same as\r\n * maxSpan and the same as `Math.abs(handleEnd[1] - handleEnds[0])`.\r\n * (2) If handle0 is forbidden to cross handle1, set minSpan as `0`.\r\n *\r\n * @param delta Move length.\r\n * @param handleEnds handleEnds[0] can be bigger then handleEnds[1].\r\n *              handleEnds will be modified in this method.\r\n * @param extent handleEnds is restricted by extent.\r\n *              extent[0] should less or equals than extent[1].\r\n * @param handleIndex Can be 'all', means that both move the two handleEnds.\r\n * @param minSpan The range of dataZoom can not be smaller than that.\r\n *              If not set, handle0 and cross handle1. If set as a non-negative\r\n *              number (including `0`), handles will push each other when reaching\r\n *              the minSpan.\r\n * @param maxSpan The range of dataZoom can not be larger than that.\r\n * @return The input handleEnds.\r\n */\nexport default function sliderMove(delta, handleEnds, extent, handleIndex, minSpan, maxSpan) {\n  delta = delta || 0;\n  var extentSpan = extent[1] - extent[0];\n  // Notice maxSpan and minSpan can be null/undefined.\n  if (minSpan != null) {\n    minSpan = restrict(minSpan, [0, extentSpan]);\n  }\n  if (maxSpan != null) {\n    maxSpan = Math.max(maxSpan, minSpan != null ? minSpan : 0);\n  }\n  if (handleIndex === 'all') {\n    var handleSpan = Math.abs(handleEnds[1] - handleEnds[0]);\n    handleSpan = restrict(handleSpan, [0, extentSpan]);\n    minSpan = maxSpan = restrict(handleSpan, [minSpan, maxSpan]);\n    handleIndex = 0;\n  }\n  handleEnds[0] = restrict(handleEnds[0], extent);\n  handleEnds[1] = restrict(handleEnds[1], extent);\n  var originalDistSign = getSpanSign(handleEnds, handleIndex);\n  handleEnds[handleIndex] += delta;\n  // Restrict in extent.\n  var extentMinSpan = minSpan || 0;\n  var realExtent = extent.slice();\n  originalDistSign.sign < 0 ? realExtent[0] += extentMinSpan : realExtent[1] -= extentMinSpan;\n  handleEnds[handleIndex] = restrict(handleEnds[handleIndex], realExtent);\n  // Expand span.\n  var currDistSign;\n  currDistSign = getSpanSign(handleEnds, handleIndex);\n  if (minSpan != null && (currDistSign.sign !== originalDistSign.sign || currDistSign.span < minSpan)) {\n    // If minSpan exists, 'cross' is forbidden.\n    handleEnds[1 - handleIndex] = handleEnds[handleIndex] + originalDistSign.sign * minSpan;\n  }\n  // Shrink span.\n  currDistSign = getSpanSign(handleEnds, handleIndex);\n  if (maxSpan != null && currDistSign.span > maxSpan) {\n    handleEnds[1 - handleIndex] = handleEnds[handleIndex] + currDistSign.sign * maxSpan;\n  }\n  return handleEnds;\n}\nfunction getSpanSign(handleEnds, handleIndex) {\n  var dist = handleEnds[handleIndex] - handleEnds[1 - handleIndex];\n  // If `handleEnds[0] === handleEnds[1]`, always believe that handleEnd[0]\n  // is at left of handleEnds[1] for non-cross case.\n  return {\n    span: Math.abs(dist),\n    sign: dist > 0 ? -1 : dist < 0 ? 1 : handleIndex ? -1 : 1\n  };\n}\nfunction restrict(value, extend) {\n  return Math.min(extend[1] != null ? extend[1] : Infinity, Math.max(extend[0] != null ? extend[0] : -Infinity, value));\n}", "map": {"version": 3, "names": ["slider<PERSON><PERSON>", "delta", "handleEnds", "extent", "handleIndex", "minSpan", "maxSpan", "extentSpan", "restrict", "Math", "max", "handleSpan", "abs", "originalDistSign", "getSpanSign", "extentMinSpan", "realExtent", "slice", "sign", "currDistSign", "span", "dist", "value", "extend", "min", "Infinity"], "sources": ["E:/AI/SmartCV/node_modules/echarts/lib/component/helper/sliderMove.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/**\r\n * Calculate slider move result.\r\n * Usage:\r\n * (1) If both handle0 and handle1 are needed to be moved, set minSpan the same as\r\n * maxSpan and the same as `Math.abs(handleEnd[1] - handleEnds[0])`.\r\n * (2) If handle0 is forbidden to cross handle1, set minSpan as `0`.\r\n *\r\n * @param delta Move length.\r\n * @param handleEnds handleEnds[0] can be bigger then handleEnds[1].\r\n *              handleEnds will be modified in this method.\r\n * @param extent handleEnds is restricted by extent.\r\n *              extent[0] should less or equals than extent[1].\r\n * @param handleIndex Can be 'all', means that both move the two handleEnds.\r\n * @param minSpan The range of dataZoom can not be smaller than that.\r\n *              If not set, handle0 and cross handle1. If set as a non-negative\r\n *              number (including `0`), handles will push each other when reaching\r\n *              the minSpan.\r\n * @param maxSpan The range of dataZoom can not be larger than that.\r\n * @return The input handleEnds.\r\n */\nexport default function sliderMove(delta, handleEnds, extent, handleIndex, minSpan, maxSpan) {\n  delta = delta || 0;\n  var extentSpan = extent[1] - extent[0];\n  // Notice maxSpan and minSpan can be null/undefined.\n  if (minSpan != null) {\n    minSpan = restrict(minSpan, [0, extentSpan]);\n  }\n  if (maxSpan != null) {\n    maxSpan = Math.max(maxSpan, minSpan != null ? minSpan : 0);\n  }\n  if (handleIndex === 'all') {\n    var handleSpan = Math.abs(handleEnds[1] - handleEnds[0]);\n    handleSpan = restrict(handleSpan, [0, extentSpan]);\n    minSpan = maxSpan = restrict(handleSpan, [minSpan, maxSpan]);\n    handleIndex = 0;\n  }\n  handleEnds[0] = restrict(handleEnds[0], extent);\n  handleEnds[1] = restrict(handleEnds[1], extent);\n  var originalDistSign = getSpanSign(handleEnds, handleIndex);\n  handleEnds[handleIndex] += delta;\n  // Restrict in extent.\n  var extentMinSpan = minSpan || 0;\n  var realExtent = extent.slice();\n  originalDistSign.sign < 0 ? realExtent[0] += extentMinSpan : realExtent[1] -= extentMinSpan;\n  handleEnds[handleIndex] = restrict(handleEnds[handleIndex], realExtent);\n  // Expand span.\n  var currDistSign;\n  currDistSign = getSpanSign(handleEnds, handleIndex);\n  if (minSpan != null && (currDistSign.sign !== originalDistSign.sign || currDistSign.span < minSpan)) {\n    // If minSpan exists, 'cross' is forbidden.\n    handleEnds[1 - handleIndex] = handleEnds[handleIndex] + originalDistSign.sign * minSpan;\n  }\n  // Shrink span.\n  currDistSign = getSpanSign(handleEnds, handleIndex);\n  if (maxSpan != null && currDistSign.span > maxSpan) {\n    handleEnds[1 - handleIndex] = handleEnds[handleIndex] + currDistSign.sign * maxSpan;\n  }\n  return handleEnds;\n}\nfunction getSpanSign(handleEnds, handleIndex) {\n  var dist = handleEnds[handleIndex] - handleEnds[1 - handleIndex];\n  // If `handleEnds[0] === handleEnds[1]`, always believe that handleEnd[0]\n  // is at left of handleEnds[1] for non-cross case.\n  return {\n    span: Math.abs(dist),\n    sign: dist > 0 ? -1 : dist < 0 ? 1 : handleIndex ? -1 : 1\n  };\n}\nfunction restrict(value, extend) {\n  return Math.min(extend[1] != null ? extend[1] : Infinity, Math.max(extend[0] != null ? extend[0] : -Infinity, value));\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASA,UAAUA,CAACC,KAAK,EAAEC,UAAU,EAAEC,MAAM,EAAEC,WAAW,EAAEC,OAAO,EAAEC,OAAO,EAAE;EAC3FL,KAAK,GAAGA,KAAK,IAAI,CAAC;EAClB,IAAIM,UAAU,GAAGJ,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC;EACtC;EACA,IAAIE,OAAO,IAAI,IAAI,EAAE;IACnBA,OAAO,GAAGG,QAAQ,CAACH,OAAO,EAAE,CAAC,CAAC,EAAEE,UAAU,CAAC,CAAC;EAC9C;EACA,IAAID,OAAO,IAAI,IAAI,EAAE;IACnBA,OAAO,GAAGG,IAAI,CAACC,GAAG,CAACJ,OAAO,EAAED,OAAO,IAAI,IAAI,GAAGA,OAAO,GAAG,CAAC,CAAC;EAC5D;EACA,IAAID,WAAW,KAAK,KAAK,EAAE;IACzB,IAAIO,UAAU,GAAGF,IAAI,CAACG,GAAG,CAACV,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,CAAC;IACxDS,UAAU,GAAGH,QAAQ,CAACG,UAAU,EAAE,CAAC,CAAC,EAAEJ,UAAU,CAAC,CAAC;IAClDF,OAAO,GAAGC,OAAO,GAAGE,QAAQ,CAACG,UAAU,EAAE,CAACN,OAAO,EAAEC,OAAO,CAAC,CAAC;IAC5DF,WAAW,GAAG,CAAC;EACjB;EACAF,UAAU,CAAC,CAAC,CAAC,GAAGM,QAAQ,CAACN,UAAU,CAAC,CAAC,CAAC,EAAEC,MAAM,CAAC;EAC/CD,UAAU,CAAC,CAAC,CAAC,GAAGM,QAAQ,CAACN,UAAU,CAAC,CAAC,CAAC,EAAEC,MAAM,CAAC;EAC/C,IAAIU,gBAAgB,GAAGC,WAAW,CAACZ,UAAU,EAAEE,WAAW,CAAC;EAC3DF,UAAU,CAACE,WAAW,CAAC,IAAIH,KAAK;EAChC;EACA,IAAIc,aAAa,GAAGV,OAAO,IAAI,CAAC;EAChC,IAAIW,UAAU,GAAGb,MAAM,CAACc,KAAK,CAAC,CAAC;EAC/BJ,gBAAgB,CAACK,IAAI,GAAG,CAAC,GAAGF,UAAU,CAAC,CAAC,CAAC,IAAID,aAAa,GAAGC,UAAU,CAAC,CAAC,CAAC,IAAID,aAAa;EAC3Fb,UAAU,CAACE,WAAW,CAAC,GAAGI,QAAQ,CAACN,UAAU,CAACE,WAAW,CAAC,EAAEY,UAAU,CAAC;EACvE;EACA,IAAIG,YAAY;EAChBA,YAAY,GAAGL,WAAW,CAACZ,UAAU,EAAEE,WAAW,CAAC;EACnD,IAAIC,OAAO,IAAI,IAAI,KAAKc,YAAY,CAACD,IAAI,KAAKL,gBAAgB,CAACK,IAAI,IAAIC,YAAY,CAACC,IAAI,GAAGf,OAAO,CAAC,EAAE;IACnG;IACAH,UAAU,CAAC,CAAC,GAAGE,WAAW,CAAC,GAAGF,UAAU,CAACE,WAAW,CAAC,GAAGS,gBAAgB,CAACK,IAAI,GAAGb,OAAO;EACzF;EACA;EACAc,YAAY,GAAGL,WAAW,CAACZ,UAAU,EAAEE,WAAW,CAAC;EACnD,IAAIE,OAAO,IAAI,IAAI,IAAIa,YAAY,CAACC,IAAI,GAAGd,OAAO,EAAE;IAClDJ,UAAU,CAAC,CAAC,GAAGE,WAAW,CAAC,GAAGF,UAAU,CAACE,WAAW,CAAC,GAAGe,YAAY,CAACD,IAAI,GAAGZ,OAAO;EACrF;EACA,OAAOJ,UAAU;AACnB;AACA,SAASY,WAAWA,CAACZ,UAAU,EAAEE,WAAW,EAAE;EAC5C,IAAIiB,IAAI,GAAGnB,UAAU,CAACE,WAAW,CAAC,GAAGF,UAAU,CAAC,CAAC,GAAGE,WAAW,CAAC;EAChE;EACA;EACA,OAAO;IACLgB,IAAI,EAAEX,IAAI,CAACG,GAAG,CAACS,IAAI,CAAC;IACpBH,IAAI,EAAEG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,GAAGjB,WAAW,GAAG,CAAC,CAAC,GAAG;EAC1D,CAAC;AACH;AACA,SAASI,QAAQA,CAACc,KAAK,EAAEC,MAAM,EAAE;EAC/B,OAAOd,IAAI,CAACe,GAAG,CAACD,MAAM,CAAC,CAAC,CAAC,IAAI,IAAI,GAAGA,MAAM,CAAC,CAAC,CAAC,GAAGE,QAAQ,EAAEhB,IAAI,CAACC,GAAG,CAACa,MAAM,CAAC,CAAC,CAAC,IAAI,IAAI,GAAGA,MAAM,CAAC,CAAC,CAAC,GAAG,CAACE,QAAQ,EAAEH,KAAK,CAAC,CAAC;AACvH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}