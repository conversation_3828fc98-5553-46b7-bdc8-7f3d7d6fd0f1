import React, { useState, useEffect } from 'react';
import PDFViewer from './PDFViewer';
import { uploadTempFile } from '../services/api';

const DocumentPreview = ({ file, content, title, className = "", onClose }) => {
  const [previewType, setPreviewType] = useState('text');
  const [isLoading, setIsLoading] = useState(false);
  const [fileUrl, setFileUrl] = useState(null);
  const [previewError, setPreviewError] = useState(null);
  const [previewMode, setPreviewMode] = useState('text');
  const [publicFileUrl, setPublicFileUrl] = useState(null);

  useEffect(() => {
    if (file) {
      generatePreview();
    }
  }, [file]);

  const generatePreview = async () => {
    if (!file) return;

    setIsLoading(true);
    setPreviewError(null);
    
    try {
      const fileExtension = file.name.split('.').pop().toLowerCase();
      
      if (fileExtension === 'pdf') {
        setPreviewType('pdf');
      } else if (fileExtension === 'docx') {
        // 上传文件到后端获取公开URL
        try {
          const uploadResult = await uploadTempFile(file);
          setPublicFileUrl(uploadResult.file_url);
          setPreviewType('docx-real-preview');
        } catch (error) {
          console.error('Upload temp file failed:', error);
          setPreviewError('文件上传失败，使用文本预览');
          // 回退到本地URL用于下载
          const url = URL.createObjectURL(file);
          setFileUrl(url);
          setPreviewType('docx-real-preview');
        }
      } else if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(fileExtension)) {
        setPreviewType('image');
      } else {
        setPreviewType('text');
      }
    } catch (error) {
      console.error('Preview generation failed:', error);
      setPreviewError('预览生成失败');
      setPreviewType('text');
    } finally {
      setIsLoading(false);
    }
  };

  // 清理URL对象
  useEffect(() => {
    return () => {
      if (fileUrl) {
        URL.revokeObjectURL(fileUrl);
      }
    };
  }, [fileUrl]);

  // 格式化DOCX内容，保留表格结构
  const formatDocxContent = (content) => {
    if (!content) return 'Document content extracted from Word file';
    
    let formattedContent = content;
    
    // 转义HTML特殊字符
    formattedContent = formattedContent
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;');
    
    // 处理表格标记
    formattedContent = formattedContent
      .replace(/=== 表格 (\d+) ===/g, 
        '<div style="background-color: #f3f4f6; padding: 8px 12px; margin: 16px 0 8px 0; border-left: 4px solid #3b82f6; font-weight: bold; color: #1f2937;">表格 $1</div>')
      .replace(/=== 表格结束 ===/g, 
        '<div style="margin: 8px 0 16px 0; border-bottom: 1px solid #e5e7eb;"></div>');
    
    // 处理表格行（包含 | 分隔符的行）
    formattedContent = formattedContent.replace(/^(.+\|.+)$/gm, (match) => {
      const cells = match.split('|').map(cell => cell.trim());
      const cellHtml = cells.map(cell => 
        `<td style="border: 1px solid #d1d5db; padding: 8px 12px; background-color: #f9fafb; vertical-align: top; font-size: 13px; line-height: 1.4;">${cell}</td>`
      ).join('');
      return `<div style="margin: 4px 0;"><table style="width: 100%; border-collapse: collapse; margin: 2px 0;"><tr>${cellHtml}</tr></table></div>`;
    });
    
    // 处理普通段落
    formattedContent = formattedContent.replace(/^([^<\n].*)$/gm, 
      '<p style="margin: 8px 0; line-height: 1.6; color: #374151;">$1</p>');
    
    // 处理换行
    formattedContent = formattedContent.replace(/\n/g, '');
    
    return formattedContent;
  };

  const renderPreview = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center h-96 bg-gray-50 rounded-lg">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto mb-2"></div>
            <span className="text-gray-600">Loading document preview...</span>
          </div>
        </div>
      );
    }

    switch (previewType) {
      case 'pdf':
        return <PDFViewer file={file} />;

      case 'docx-real-preview':
        return (
          <div className="paper-preview rounded-lg min-h-[600px] max-h-[800px] overflow-hidden">
            <div className="h-full flex flex-col">
              {/* 预览选项标签 */}
              <div className="flex space-x-2 mb-4 border-b border-gray-200 pb-2">
                <button
                  onClick={() => setPreviewMode('office')}
                  className={`px-3 py-1 text-sm rounded ${
                    previewMode === 'office' 
                      ? 'bg-blue-500 text-white' 
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  Office预览
                </button>
                <button
                  onClick={() => setPreviewMode('google')}
                  className={`px-3 py-1 text-sm rounded ${
                    previewMode === 'google' 
                      ? 'bg-blue-500 text-white' 
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  Google预览
                </button>
                <button
                  onClick={() => setPreviewMode('text')}
                  className={`px-3 py-1 text-sm rounded ${
                    previewMode === 'text' 
                      ? 'bg-blue-500 text-white' 
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  文本预览
                </button>
                
                {/* 下载按钮 */}
                <div className="flex-1"></div>
                <a
                  href={fileUrl || URL.createObjectURL(file)}
                  download={file.name}
                  className="px-3 py-1 text-sm bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
                >
                  📥 下载原文档
                </a>
              </div>

              {/* 预览内容 */}
              <div className="flex-1">
                {previewError && (
                  <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div className="flex items-center">
                      <svg className="w-5 h-5 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                      <span className="text-yellow-800 text-sm">{previewError}</span>
                    </div>
                  </div>
                )}
                {renderDocxPreview()}
              </div>
            </div>
          </div>
        );

      case 'image':
        return (
          <div className="paper-preview rounded-lg p-8 min-h-[600px] max-h-[800px] overflow-y-auto flex items-center justify-center">
            <img 
              src={URL.createObjectURL(file)}
              alt="Resume preview" 
              className="max-w-full max-h-full object-contain rounded-lg shadow-md"
              style={{ maxHeight: '600px' }}
            />
          </div>
        );

      case 'docx':
        return (
          <div className="paper-preview rounded-lg p-8 min-h-[600px] max-h-[800px] overflow-y-auto">
            <div 
              className="text-sm leading-relaxed text-gray-800"
              style={{ 
                fontFamily: 'Georgia, serif',
                lineHeight: '1.6'
              }}
              dangerouslySetInnerHTML={{ 
                __html: formatDocxContent(content) 
              }}
            />
          </div>
        );

      case 'text':
      default:
        return (
          <div className="paper-preview rounded-lg p-8 min-h-[600px] max-h-[800px] overflow-y-auto">
            <pre 
              className="text-sm leading-relaxed text-gray-800 whitespace-pre-wrap"
              style={{ 
                fontFamily: 'Georgia, serif',
                lineHeight: '1.6'
              }}
            >
              {content || 'Document content not available'}
            </pre>
          </div>
        );
    }
  };

  const renderDocxPreview = () => {
    const previewUrl = publicFileUrl || fileUrl;
    
    if (!previewUrl) {
      return (
        <div className="flex items-center justify-center h-96">
          <div className="text-center text-gray-500">
            <div className="text-lg mb-2">📄</div>
            <div>文档URL生成中...</div>
          </div>
        </div>
      );
    }

    switch (previewMode) {
      case 'office':
        // 使用Microsoft Office Online预览
        if (publicFileUrl) {
          return (
            <div className="w-full h-full">
              <iframe
                src={`https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(publicFileUrl)}`}
                width="100%"
                height="100%"
                style={{ minHeight: '500px', border: 'none' }}
                title="Office Online Preview"
                onError={(e) => {
                  console.error('Office Online preview failed:', e);
                  setPreviewError('Office预览加载失败，请尝试其他预览方式');
                }}
              />
            </div>
          );
        } else {
          return (
            <div className="flex items-center justify-center h-96">
              <div className="text-center text-gray-500 max-w-md">
                <div className="text-lg mb-2">⚠️</div>
                <div className="mb-2 font-medium">Office在线预览不可用</div>
                <div className="text-sm mb-4">Office Online只能预览公开可访问的文档URL。本地文件无法直接预览。</div>
                <button
                  onClick={() => setPreviewMode('text')}
                  className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                >
                  切换到文本预览
                </button>
              </div>
            </div>
          );
        }
        
      case 'google':
        // 使用Google Docs Viewer预览
        if (publicFileUrl) {
          return (
            <div className="w-full h-full">
              <iframe
                src={`https://docs.google.com/gview?url=${encodeURIComponent(publicFileUrl)}&embedded=true`}
                width="100%"
                height="100%"
                style={{ minHeight: '500px', border: 'none' }}
                title="Google Docs Preview"
                onError={(e) => {
                  console.error('Google Docs preview failed:', e);
                  setPreviewError('Google预览加载失败，请尝试其他预览方式');
                }}
              />
            </div>
          );
        } else {
          return (
            <div className="flex items-center justify-center h-96">
              <div className="text-center text-gray-500 max-w-md">
                <div className="text-lg mb-2">⚠️</div>
                <div className="mb-2 font-medium">Google在线预览不可用</div>
                <div className="text-sm mb-4">Google Docs Viewer只能预览公开可访问的文档URL。本地文件无法直接预览。</div>
                <button
                  onClick={() => setPreviewMode('text')}
                  className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                >
                  切换到文本预览
                </button>
              </div>
            </div>
          );
        }
        
      case 'text':
        return (
          <div className="paper-preview rounded-lg p-8 min-h-[600px] max-h-[800px] overflow-y-auto">
            <div 
              className="text-sm leading-relaxed text-gray-800"
              style={{ 
                fontFamily: 'Georgia, serif',
                lineHeight: '1.6'
              }}
              dangerouslySetInnerHTML={{ 
                __html: formatDocxContent(content) 
              }}
            />
          </div>
        );
        
      default:
        return (
          <div className="flex items-center justify-center h-96">
            <div className="text-center text-gray-500">
              <div className="text-lg mb-2">📄</div>
              <div>预览模式选择错误</div>
            </div>
          </div>
        );
    }
  };

  return (
    <div className={`${className}`}>
      <div className="mb-4">
        <div className="flex items-center justify-between mb-3">
          <h4 className="font-medium text-gray-700 flex items-center">
            {title}
          </h4>
          {onClose && (
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-red-500 transition-colors"
              title="Close preview"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}
        </div>
      </div>
      
      {renderPreview()}
      
      {previewType === 'text' && file && (
        <div className="mt-2 text-center">
          <button
            onClick={generatePreview}
            className="text-orange-600 hover:text-orange-700 text-sm font-medium"
          >
            Try loading document preview again
          </button>
        </div>
      )}
    </div>
  );
};

export default DocumentPreview; 