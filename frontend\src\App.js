import React, { useState, useRef, useEffect } from 'react';
import './index.css';
import FileUpload from './components/FileUpload';
import JobDescriptionInput from './components/JobDescriptionInput';
import ResultDisplay from './components/ResultDisplay';
import LoadingSpinner from './components/LoadingSpinner';
import { optimizeResume, optimizeResumeDocxWithFormat, downloadOptimizedDocx } from './services/api';
import { extractTextFromImage, isImageFile, validateImageForOCR } from './services/ocrService';

function App() {
  const [resumeFile, setResumeFile] = useState(null);
  const [extractedText, setExtractedText] = useState('');
  const [ocrProgress, setOcrProgress] = useState(0);
  const [isExtractingText, setIsExtractingText] = useState(false);
  const [jobDescription, setJobDescription] = useState('');
  const [jobDescriptionFile, setJobDescriptionFile] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState(null);
  const [error, setError] = useState(null);
  
  const resultRef = useRef(null);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyPress = (e) => {
      // Ctrl/Cmd + Enter to optimize
      if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
        e.preventDefault();
        if (!isLoading && resumeFile) {
          handleOptimize();
        }
      }
      // Ctrl/Cmd + R to reset
      if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
        e.preventDefault();
        handleReset();
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [isLoading, resumeFile]);

  // Handle file upload and OCR extraction
  const handleFileUpload = async (file) => {
    console.log('handleFileUpload called with file:', file);
    
    setResumeFile(file);
    setError(null);
    setExtractedText('');
    
    if (file && isImageFile(file)) {
      console.log('File is an image, validating...');
      const validation = validateImageForOCR(file);
      console.log('Validation result:', validation);
      
      if (!validation.isValid) {
        setError(validation.message);
        return;
      }
      
      setIsExtractingText(true);
      setOcrProgress(0);
      
      try {
        const text = await extractTextFromImage(file, setOcrProgress);
        setExtractedText(text);
        console.log('Extracted text:', text);
      } catch (err) {
        console.error('OCR Error:', err);
        setError(err.message || 'Failed to extract text from image');
      } finally {
        setIsExtractingText(false);
        setOcrProgress(0);
      }
    } else {
      console.log('File is not an image or file is null');
    }
  };

  const handleOptimize = async () => {
    if (!resumeFile) {
      setError('Please upload your resume file first. We support PDF, Word documents, and image formats.');
      return;
    }
    
    if (!jobDescription.trim() && !jobDescriptionFile) {
      setError('Please provide a job description either by typing it in or uploading a file.');
      return;
    }

    // 检查图片文件是否需要等待OCR提取
    if (isImageFile(resumeFile) && !extractedText && !isExtractingText) {
      setError('Please wait for text extraction to complete before optimizing.');
      return;
    }

    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      let response;
      
      if (isImageFile(resumeFile) && extractedText) {
        // 图片文件：使用OCR提取的文本
        const textBlob = new Blob([extractedText], { type: 'text/plain' });
        const textFile = new File([textBlob], 'extracted_resume.txt', { type: 'text/plain' });
        response = await optimizeResume(textFile, jobDescription, jobDescriptionFile);
        
        // 存储原始图片文件供显示使用
        response.originalFile = resumeFile;
        response.extractedText = extractedText;
      } else {
        // PDF和Word文件：使用常规优化
        response = await optimizeResume(resumeFile, jobDescription, jobDescriptionFile);
      }
      
      setResult(response);
      
      // 滚动到结果区域
      setTimeout(() => {
        resultRef.current?.scrollIntoView({ behavior: 'smooth' });
      }, 100);
      
    } catch (err) {
      console.error('Optimization error:', err);
      let errorMessage = 'An error occurred during optimization. ';
      
      if (err.message) {
        errorMessage += err.message;
      } else {
        errorMessage += 'Please check your internet connection and try again.';
      }
      
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleReset = () => {
    setResumeFile(null);
    setExtractedText('');
    setOcrProgress(0);
    setIsExtractingText(false);
    setJobDescription('');
    setJobDescriptionFile(null);
    setResult(null);
    setError(null);
  };

  // 检查是否为DOCX文件
  const isDocxFile = (file) => {
    if (!file) return false;
    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    return fileExtension === 'docx';
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Enhanced Header */}
      <header className="bg-white/80 backdrop-blur-sm shadow-sm border-b border-gray-100 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
          <div className="flex items-center justify-start">
            <div className="flex items-center space-x-3">
              <div className="w-16 h-16 flex items-center justify-center">
                <img 
                  src={`/ICON.png?t=${Date.now()}`}
                  alt="SmartCV Logo" 
                  className="w-full h-full object-contain"
                />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-orange-600">
                  SmartCV
                </h1>
                <p className="text-xs text-gray-600 font-medium">AI-Powered Resume Optimization Platform</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="">
          {/* Hero Section */}
          <div className="text-center space-y-4 mb-8">
            <div className="space-y-3">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-800 leading-tight">
                Tailor CV to Match Your Dream Job
              </h2>
              <p className="text-base text-gray-600 max-w-2xl mx-auto leading-relaxed">
                Instantly reshape your resume to fit target job, bypass ATS filters and stand out to HR.
              </p>
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <div className="bg-red-50 border-l-4 border-red-400 rounded-lg p-6 shadow-sm mb-8">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <svg className="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-red-800 font-medium">{error}</p>
                </div>
              </div>
            </div>
          )}

          {/* Upload Section */}
          <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-10 mb-8">
            {/* Step Progress Indicator */}
            <div className="flex items-center justify-center mb-10">
              <div className="flex items-center space-x-8">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-yellow-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                    1
                  </div>
                  <span className="text-lg font-semibold text-gray-900">Upload CV</span>
                </div>
                <div className="w-12 h-0.5 bg-orange-300"></div>
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                    2
                  </div>
                  <span className="text-lg font-semibold text-gray-900">Input Job Description</span>
                </div>
                <div className="w-12 h-0.5 bg-orange-300"></div>
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-red-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                    3
                  </div>
                  <span className="text-lg font-semibold text-gray-900">Optimize</span>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-12 gap-8 items-start">
              {/* Step 1: Resume Upload - 40% width */}
              <div className="lg:col-span-5 space-y-6">
                <FileUpload
                  file={resumeFile}
                  onFileSelect={handleFileUpload}
                  accept=".pdf,.docx,.doc,.jpg,.jpeg,.png,.gif,.bmp,.webp"
                  placeholder="Drop your resume file here (PDF, Word, or Image formats)"
                />
                
                {/* OCR Progress */}
                {isExtractingText && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-center space-x-3 mb-3">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
                      <span className="text-blue-700 font-medium">Extracting text from image...</span>
                    </div>
                    <div className="w-full bg-blue-200 rounded-full h-2">
                      <div 
                        className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${ocrProgress}%` }}
                      ></div>
                    </div>
                    <div className="text-sm text-blue-600 mt-2">{ocrProgress}% complete</div>
                  </div>
                )}
                
                {/* Extracted Text Preview - Only for images */}
                {extractedText && !isExtractingText && isImageFile(resumeFile) && (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-2">
                      <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span className="text-green-700 font-medium">Text extracted from image successfully!</span>
                    </div>
                    <div className="text-sm text-gray-600 max-h-32 overflow-y-auto bg-white p-2 rounded border">
                      {extractedText.substring(0, 200)}
                      {extractedText.length > 200 && '...'}
                    </div>
                    <div className="text-xs text-green-600 mt-1">
                      {extractedText.length} characters extracted
                    </div>
                  </div>
                )}
                
                {/* File Type Info - Only for PDF/Word */}
                {resumeFile && !isImageFile(resumeFile) && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-2">
                      <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span className="text-blue-700 font-medium">Document uploaded successfully!</span>
                    </div>
                    <div className="text-sm text-blue-600">
                      {resumeFile.type === 'application/pdf' ? 'PDF document ready for optimization' :
                       resumeFile.type.includes('word') ? 'Word document ready for optimization' :
                       'Document ready for optimization'}
                    </div>
                  </div>
                )}
              </div>

              {/* Step 2: Job Description Input - 40% width */}
              <div className="lg:col-span-5 space-y-6 mt-[16px]">
                <JobDescriptionInput
                  textValue={jobDescription}
                  onTextChange={setJobDescription}
                  file={jobDescriptionFile}
                  onFileChange={setJobDescriptionFile}
                />
              </div>

              {/* Step 3: Action Buttons - 20% width */}
              <div className="lg:col-span-2 flex flex-col justify-start space-y-4">
                <div className="space-y-3 mt-4">
                  <div className="relative group">
                    <button
                      onClick={handleOptimize}
                      disabled={isLoading || !resumeFile}
                      className={`w-full px-4 py-3 text-white font-semibold rounded-lg shadow-md transition-all duration-200 text-sm ${
                        !resumeFile 
                          ? 'bg-gray-400 cursor-not-allowed' 
                          : isLoading 
                            ? 'bg-orange-500 opacity-75 cursor-wait' 
                            : 'bg-orange-500 hover:bg-orange-600 hover:shadow-lg transform hover:-translate-y-0.5'
                      }`}
                    >
                      <span className="flex items-center justify-center space-x-2">
                        {isLoading ? (
                          <>
                            <svg className="animate-spin h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            <span>Optimizing...</span>
                          </>
                        ) : (
                          <>
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                            <span>Optimize</span>
                          </>
                        )}
                      </span>
                    </button>
                    
                    {/* Tooltip for disabled state */}
                    {!resumeFile && !isLoading && (
                      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                        <div className="bg-gray-800 text-white text-xs rounded py-1 px-2 whitespace-nowrap">
                          Upload resume first
                          <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-800"></div>
                        </div>
                      </div>
                    )}
                  </div>
                  
                  <button
                    onClick={handleReset}
                    className="w-full px-4 py-3 bg-gray-100 text-gray-700 font-medium rounded-lg hover:bg-gray-200 transform hover:-translate-y-0.5 transition-all duration-200 border border-gray-200 text-sm"
                  >
                    <span className="flex items-center justify-center space-x-2">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                      </svg>
                      <span>Reset</span>
                    </span>
                  </button>
                </div>
              </div>
            </div>
            
            {/* Tip at bottom right corner of the card */}
            <div className="mt-6 text-right">
              <span className="text-sm text-orange-600 font-medium">
                💡 More detailed descriptions lead to better optimization
              </span>
            </div>
          </div>

          {/* Loading Spinner */}
          {isLoading && (
            <div className="flex justify-center mb-8">
              <LoadingSpinner />
            </div>
          )}

          {/* Results */}
          {result && (
            <div ref={resultRef} className="mb-8">
              <ResultDisplay result={result} originalFile={resumeFile} jobDescription={jobDescription} />
            </div>
          )}

          {/* Features Section - Only show when no results */}
          {!result && !isLoading && (
            <div style={{ marginTop: '150px' }}>
              <div className="text-center mb-12">
                <h3 className="text-2xl font-bold text-gray-900 mb-4">Why SmartCV?</h3>
                <p className="text-gray-600 max-w-2xl mx-auto">
                  Your resume. Smarter, sharper, and ready to win that dream job.
                </p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="text-center group">
                  <div className="w-16 h-16 bg-yellow-500 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-2">AI-Powered Analysis</h4>
                  <p className="text-gray-600 text-sm">Advanced algorithms analyze job requirements and optimize your resume accordingly</p>
                </div>
                
                <div className="text-center group">
                  <div className="w-16 h-16 bg-orange-500 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-2">Instant Results</h4>
                  <p className="text-gray-600 text-sm">Get professionally optimized resumes in seconds, not hours</p>
                </div>
                
                <div className="text-center group">
                  <div className="w-16 h-16 bg-red-500 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-2">Multiple Formats</h4>
                  <p className="text-gray-600 text-sm">Export your optimized resume in PDF, DOCX, or plain text format</p>
                </div>
              </div>
            </div>
          )}
        </div>
      </main>

      {/* Enhanced Footer */}
      <footer className="bg-gray-50 border-t border-gray-200 mt-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center space-y-4">
            <div className="flex items-center justify-center space-x-2">
              <div className="w-8 h-8 flex items-center justify-center">
                <img 
                  src={`/ICON.png?t=${Date.now()}`}
                  alt="SmartCV Logo" 
                  className="w-full h-full object-contain"
                />
              </div>
              <span className="text-xl font-bold text-gray-900">SmartCV</span>
            </div>
            <p className="text-gray-600">© 2024 SmartCV - AI-Powered Resume Optimization Platform</p>
            <div className="flex items-center justify-center space-x-2 text-sm text-gray-500">
              <span>Powered by</span>
              <a 
                href="https://vlisoft.com" 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-orange-600 hover:text-orange-700 font-medium transition-colors duration-200"
              >
                Vlisoft.com
              </a>
              <span>•</span>
              <span>Empowering careers with AI</span>
            </div>
            
            {/* Privacy and Terms Links */}
            <div className="mt-4 text-xs text-gray-400 text-center">
              <span className="inline-flex items-center space-x-4">
                <a
                  href="/privacy-notice"
                  className="px-3 py-2 bg-gray-700 text-white rounded hover:bg-gray-600 transition-colors"
                  onClick={(e) => {
                    e.preventDefault();
                    window.open('/privacy-notice.html', '_blank');
                  }}
                >
                  Privacy Notice
                </a>
                <a
                  href="/terms-privacy"
                  className="px-3 py-2 bg-gray-700 text-white rounded hover:bg-gray-600 transition-colors"
                  onClick={(e) => {
                    e.preventDefault();
                    window.open('/terms-privacy.html', '_blank');
                  }}
                >
                  Terms & Privacy
                </a>
              </span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}

export default App; 