{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n// Only create one roam controller for each coordinate system.\n// one roam controller might be refered by two inside data zoom\n// components (for example, one for x and one for y). When user\n// pan or zoom, only dispatch one action for those data zoom\n// components.\nimport RoamController from '../../component/helper/RoamController.js';\nimport * as throttleUtil from '../../util/throttle.js';\nimport { makeInner } from '../../util/model.js';\nimport { each, curry, createHashMap } from 'zrender/lib/core/util.js';\nimport { collectReferCoordSysModelInfo } from './helper.js';\nvar inner = makeInner();\nexport function setViewInfoToCoordSysRecord(api, dataZoomModel, getRange) {\n  inner(api).coordSysRecordMap.each(function (coordSysRecord) {\n    var dzInfo = coordSysRecord.dataZoomInfoMap.get(dataZoomModel.uid);\n    if (dzInfo) {\n      dzInfo.getRange = getRange;\n    }\n  });\n}\nexport function disposeCoordSysRecordIfNeeded(api, dataZoomModel) {\n  var coordSysRecordMap = inner(api).coordSysRecordMap;\n  var coordSysKeyArr = coordSysRecordMap.keys();\n  for (var i = 0; i < coordSysKeyArr.length; i++) {\n    var coordSysKey = coordSysKeyArr[i];\n    var coordSysRecord = coordSysRecordMap.get(coordSysKey);\n    var dataZoomInfoMap = coordSysRecord.dataZoomInfoMap;\n    if (dataZoomInfoMap) {\n      var dzUid = dataZoomModel.uid;\n      var dzInfo = dataZoomInfoMap.get(dzUid);\n      if (dzInfo) {\n        dataZoomInfoMap.removeKey(dzUid);\n        if (!dataZoomInfoMap.keys().length) {\n          disposeCoordSysRecord(coordSysRecordMap, coordSysRecord);\n        }\n      }\n    }\n  }\n}\nfunction disposeCoordSysRecord(coordSysRecordMap, coordSysRecord) {\n  if (coordSysRecord) {\n    coordSysRecordMap.removeKey(coordSysRecord.model.uid);\n    var controller = coordSysRecord.controller;\n    controller && controller.dispose();\n  }\n}\nfunction createCoordSysRecord(api, coordSysModel) {\n  // These init props will never change after record created.\n  var coordSysRecord = {\n    model: coordSysModel,\n    containsPoint: curry(containsPoint, coordSysModel),\n    dispatchAction: curry(dispatchAction, api),\n    dataZoomInfoMap: null,\n    controller: null\n  };\n  // Must not do anything depends on coordSysRecord outside the event handler here,\n  // because coordSysRecord not completed yet.\n  var controller = coordSysRecord.controller = new RoamController(api.getZr());\n  each(['pan', 'zoom', 'scrollMove'], function (eventName) {\n    controller.on(eventName, function (event) {\n      var batch = [];\n      coordSysRecord.dataZoomInfoMap.each(function (dzInfo) {\n        // Check whether the behaviors (zoomOnMouseWheel, moveOnMouseMove,\n        // moveOnMouseWheel, ...) enabled.\n        if (!event.isAvailableBehavior(dzInfo.model.option)) {\n          return;\n        }\n        var method = (dzInfo.getRange || {})[eventName];\n        var range = method && method(dzInfo.dzReferCoordSysInfo, coordSysRecord.model.mainType, coordSysRecord.controller, event);\n        !dzInfo.model.get('disabled', true) && range && batch.push({\n          dataZoomId: dzInfo.model.id,\n          start: range[0],\n          end: range[1]\n        });\n      });\n      batch.length && coordSysRecord.dispatchAction(batch);\n    });\n  });\n  return coordSysRecord;\n}\n/**\r\n * This action will be throttled.\r\n */\nfunction dispatchAction(api, batch) {\n  if (!api.isDisposed()) {\n    api.dispatchAction({\n      type: 'dataZoom',\n      animation: {\n        easing: 'cubicOut',\n        duration: 100\n      },\n      batch: batch\n    });\n  }\n}\nfunction containsPoint(coordSysModel, e, x, y) {\n  return coordSysModel.coordinateSystem.containPoint([x, y]);\n}\n/**\r\n * Merge roamController settings when multiple dataZooms share one roamController.\r\n */\nfunction mergeControllerParams(dataZoomInfoMap) {\n  var controlType;\n  // DO NOT use reserved word (true, false, undefined) as key literally. Even if encapsulated\n  // as string, it is probably revert to reserved word by compress tool. See #7411.\n  var prefix = 'type_';\n  var typePriority = {\n    'type_true': 2,\n    'type_move': 1,\n    'type_false': 0,\n    'type_undefined': -1\n  };\n  var preventDefaultMouseMove = true;\n  dataZoomInfoMap.each(function (dataZoomInfo) {\n    var dataZoomModel = dataZoomInfo.model;\n    var oneType = dataZoomModel.get('disabled', true) ? false : dataZoomModel.get('zoomLock', true) ? 'move' : true;\n    if (typePriority[prefix + oneType] > typePriority[prefix + controlType]) {\n      controlType = oneType;\n    }\n    // Prevent default move event by default. If one false, do not prevent. Otherwise\n    // users may be confused why it does not work when multiple insideZooms exist.\n    preventDefaultMouseMove = preventDefaultMouseMove && dataZoomModel.get('preventDefaultMouseMove', true);\n  });\n  return {\n    controlType: controlType,\n    opt: {\n      // RoamController will enable all of these functionalities,\n      // and the final behavior is determined by its event listener\n      // provided by each inside zoom.\n      zoomOnMouseWheel: true,\n      moveOnMouseMove: true,\n      moveOnMouseWheel: true,\n      preventDefaultMouseMove: !!preventDefaultMouseMove\n    }\n  };\n}\nexport function installDataZoomRoamProcessor(registers) {\n  registers.registerProcessor(registers.PRIORITY.PROCESSOR.FILTER, function (ecModel, api) {\n    var apiInner = inner(api);\n    var coordSysRecordMap = apiInner.coordSysRecordMap || (apiInner.coordSysRecordMap = createHashMap());\n    coordSysRecordMap.each(function (coordSysRecord) {\n      // `coordSysRecordMap` always exists (because it holds the `roam controller`, which should\n      // better not re-create each time), but clear `dataZoomInfoMap` each round of the workflow.\n      coordSysRecord.dataZoomInfoMap = null;\n    });\n    ecModel.eachComponent({\n      mainType: 'dataZoom',\n      subType: 'inside'\n    }, function (dataZoomModel) {\n      var dzReferCoordSysWrap = collectReferCoordSysModelInfo(dataZoomModel);\n      each(dzReferCoordSysWrap.infoList, function (dzCoordSysInfo) {\n        var coordSysUid = dzCoordSysInfo.model.uid;\n        var coordSysRecord = coordSysRecordMap.get(coordSysUid) || coordSysRecordMap.set(coordSysUid, createCoordSysRecord(api, dzCoordSysInfo.model));\n        var dataZoomInfoMap = coordSysRecord.dataZoomInfoMap || (coordSysRecord.dataZoomInfoMap = createHashMap());\n        // Notice these props might be changed each time for a single dataZoomModel.\n        dataZoomInfoMap.set(dataZoomModel.uid, {\n          dzReferCoordSysInfo: dzCoordSysInfo,\n          model: dataZoomModel,\n          getRange: null\n        });\n      });\n    });\n    // (1) Merge dataZoom settings for each coord sys and set to the roam controller.\n    // (2) Clear coord sys if not refered by any dataZoom.\n    coordSysRecordMap.each(function (coordSysRecord) {\n      var controller = coordSysRecord.controller;\n      var firstDzInfo;\n      var dataZoomInfoMap = coordSysRecord.dataZoomInfoMap;\n      if (dataZoomInfoMap) {\n        var firstDzKey = dataZoomInfoMap.keys()[0];\n        if (firstDzKey != null) {\n          firstDzInfo = dataZoomInfoMap.get(firstDzKey);\n        }\n      }\n      if (!firstDzInfo) {\n        disposeCoordSysRecord(coordSysRecordMap, coordSysRecord);\n        return;\n      }\n      var controllerParams = mergeControllerParams(dataZoomInfoMap);\n      controller.enable(controllerParams.controlType, controllerParams.opt);\n      controller.setPointerChecker(coordSysRecord.containsPoint);\n      throttleUtil.createOrUpdate(coordSysRecord, 'dispatchAction', firstDzInfo.model.get('throttle', true), 'fixRate');\n    });\n  });\n}", "map": {"version": 3, "names": ["RoamController", "throttleUtil", "makeInner", "each", "curry", "createHashMap", "collectReferCoordSysModelInfo", "inner", "setViewInfoToCoordSysRecord", "api", "dataZoomModel", "getRange", "coordSysRecordMap", "coordSysRecord", "dzInfo", "dataZoomInfoMap", "get", "uid", "disposeCoordSysRecordIfNeeded", "coordSysKeyArr", "keys", "i", "length", "coordSysKey", "dzUid", "<PERSON><PERSON><PERSON>", "disposeCoordSysRecord", "model", "controller", "dispose", "createCoordSysRecord", "coordSysModel", "containsPoint", "dispatchAction", "getZr", "eventName", "on", "event", "batch", "isAvailableBehavior", "option", "method", "range", "dzReferCoordSysInfo", "mainType", "push", "dataZoomId", "id", "start", "end", "isDisposed", "type", "animation", "easing", "duration", "e", "x", "y", "coordinateSystem", "containPoint", "mergeControllerParams", "controlType", "prefix", "typePriority", "preventDefaultMouseMove", "dataZoomInfo", "oneType", "opt", "zoomOnMouseWheel", "moveOnMouseMove", "moveOnMouseWheel", "installDataZoomRoamProcessor", "registers", "registerProcessor", "PRIORITY", "PROCESSOR", "FILTER", "ecModel", "apiInner", "eachComponent", "subType", "dzReferCoordSysWrap", "infoList", "dzCoordSysInfo", "coordSysUid", "set", "firstDzInfo", "firstDzKey", "controllerParams", "enable", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createOrUpdate"], "sources": ["E:/AI/SmartCV/node_modules/echarts/lib/component/dataZoom/roams.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n// Only create one roam controller for each coordinate system.\n// one roam controller might be refered by two inside data zoom\n// components (for example, one for x and one for y). When user\n// pan or zoom, only dispatch one action for those data zoom\n// components.\nimport RoamController from '../../component/helper/RoamController.js';\nimport * as throttleUtil from '../../util/throttle.js';\nimport { makeInner } from '../../util/model.js';\nimport { each, curry, createHashMap } from 'zrender/lib/core/util.js';\nimport { collectReferCoordSysModelInfo } from './helper.js';\nvar inner = makeInner();\nexport function setViewInfoToCoordSysRecord(api, dataZoomModel, getRange) {\n  inner(api).coordSysRecordMap.each(function (coordSysRecord) {\n    var dzInfo = coordSysRecord.dataZoomInfoMap.get(dataZoomModel.uid);\n    if (dzInfo) {\n      dzInfo.getRange = getRange;\n    }\n  });\n}\nexport function disposeCoordSysRecordIfNeeded(api, dataZoomModel) {\n  var coordSysRecordMap = inner(api).coordSysRecordMap;\n  var coordSysKeyArr = coordSysRecordMap.keys();\n  for (var i = 0; i < coordSysKeyArr.length; i++) {\n    var coordSysKey = coordSysKeyArr[i];\n    var coordSysRecord = coordSysRecordMap.get(coordSysKey);\n    var dataZoomInfoMap = coordSysRecord.dataZoomInfoMap;\n    if (dataZoomInfoMap) {\n      var dzUid = dataZoomModel.uid;\n      var dzInfo = dataZoomInfoMap.get(dzUid);\n      if (dzInfo) {\n        dataZoomInfoMap.removeKey(dzUid);\n        if (!dataZoomInfoMap.keys().length) {\n          disposeCoordSysRecord(coordSysRecordMap, coordSysRecord);\n        }\n      }\n    }\n  }\n}\nfunction disposeCoordSysRecord(coordSysRecordMap, coordSysRecord) {\n  if (coordSysRecord) {\n    coordSysRecordMap.removeKey(coordSysRecord.model.uid);\n    var controller = coordSysRecord.controller;\n    controller && controller.dispose();\n  }\n}\nfunction createCoordSysRecord(api, coordSysModel) {\n  // These init props will never change after record created.\n  var coordSysRecord = {\n    model: coordSysModel,\n    containsPoint: curry(containsPoint, coordSysModel),\n    dispatchAction: curry(dispatchAction, api),\n    dataZoomInfoMap: null,\n    controller: null\n  };\n  // Must not do anything depends on coordSysRecord outside the event handler here,\n  // because coordSysRecord not completed yet.\n  var controller = coordSysRecord.controller = new RoamController(api.getZr());\n  each(['pan', 'zoom', 'scrollMove'], function (eventName) {\n    controller.on(eventName, function (event) {\n      var batch = [];\n      coordSysRecord.dataZoomInfoMap.each(function (dzInfo) {\n        // Check whether the behaviors (zoomOnMouseWheel, moveOnMouseMove,\n        // moveOnMouseWheel, ...) enabled.\n        if (!event.isAvailableBehavior(dzInfo.model.option)) {\n          return;\n        }\n        var method = (dzInfo.getRange || {})[eventName];\n        var range = method && method(dzInfo.dzReferCoordSysInfo, coordSysRecord.model.mainType, coordSysRecord.controller, event);\n        !dzInfo.model.get('disabled', true) && range && batch.push({\n          dataZoomId: dzInfo.model.id,\n          start: range[0],\n          end: range[1]\n        });\n      });\n      batch.length && coordSysRecord.dispatchAction(batch);\n    });\n  });\n  return coordSysRecord;\n}\n/**\r\n * This action will be throttled.\r\n */\nfunction dispatchAction(api, batch) {\n  if (!api.isDisposed()) {\n    api.dispatchAction({\n      type: 'dataZoom',\n      animation: {\n        easing: 'cubicOut',\n        duration: 100\n      },\n      batch: batch\n    });\n  }\n}\nfunction containsPoint(coordSysModel, e, x, y) {\n  return coordSysModel.coordinateSystem.containPoint([x, y]);\n}\n/**\r\n * Merge roamController settings when multiple dataZooms share one roamController.\r\n */\nfunction mergeControllerParams(dataZoomInfoMap) {\n  var controlType;\n  // DO NOT use reserved word (true, false, undefined) as key literally. Even if encapsulated\n  // as string, it is probably revert to reserved word by compress tool. See #7411.\n  var prefix = 'type_';\n  var typePriority = {\n    'type_true': 2,\n    'type_move': 1,\n    'type_false': 0,\n    'type_undefined': -1\n  };\n  var preventDefaultMouseMove = true;\n  dataZoomInfoMap.each(function (dataZoomInfo) {\n    var dataZoomModel = dataZoomInfo.model;\n    var oneType = dataZoomModel.get('disabled', true) ? false : dataZoomModel.get('zoomLock', true) ? 'move' : true;\n    if (typePriority[prefix + oneType] > typePriority[prefix + controlType]) {\n      controlType = oneType;\n    }\n    // Prevent default move event by default. If one false, do not prevent. Otherwise\n    // users may be confused why it does not work when multiple insideZooms exist.\n    preventDefaultMouseMove = preventDefaultMouseMove && dataZoomModel.get('preventDefaultMouseMove', true);\n  });\n  return {\n    controlType: controlType,\n    opt: {\n      // RoamController will enable all of these functionalities,\n      // and the final behavior is determined by its event listener\n      // provided by each inside zoom.\n      zoomOnMouseWheel: true,\n      moveOnMouseMove: true,\n      moveOnMouseWheel: true,\n      preventDefaultMouseMove: !!preventDefaultMouseMove\n    }\n  };\n}\nexport function installDataZoomRoamProcessor(registers) {\n  registers.registerProcessor(registers.PRIORITY.PROCESSOR.FILTER, function (ecModel, api) {\n    var apiInner = inner(api);\n    var coordSysRecordMap = apiInner.coordSysRecordMap || (apiInner.coordSysRecordMap = createHashMap());\n    coordSysRecordMap.each(function (coordSysRecord) {\n      // `coordSysRecordMap` always exists (because it holds the `roam controller`, which should\n      // better not re-create each time), but clear `dataZoomInfoMap` each round of the workflow.\n      coordSysRecord.dataZoomInfoMap = null;\n    });\n    ecModel.eachComponent({\n      mainType: 'dataZoom',\n      subType: 'inside'\n    }, function (dataZoomModel) {\n      var dzReferCoordSysWrap = collectReferCoordSysModelInfo(dataZoomModel);\n      each(dzReferCoordSysWrap.infoList, function (dzCoordSysInfo) {\n        var coordSysUid = dzCoordSysInfo.model.uid;\n        var coordSysRecord = coordSysRecordMap.get(coordSysUid) || coordSysRecordMap.set(coordSysUid, createCoordSysRecord(api, dzCoordSysInfo.model));\n        var dataZoomInfoMap = coordSysRecord.dataZoomInfoMap || (coordSysRecord.dataZoomInfoMap = createHashMap());\n        // Notice these props might be changed each time for a single dataZoomModel.\n        dataZoomInfoMap.set(dataZoomModel.uid, {\n          dzReferCoordSysInfo: dzCoordSysInfo,\n          model: dataZoomModel,\n          getRange: null\n        });\n      });\n    });\n    // (1) Merge dataZoom settings for each coord sys and set to the roam controller.\n    // (2) Clear coord sys if not refered by any dataZoom.\n    coordSysRecordMap.each(function (coordSysRecord) {\n      var controller = coordSysRecord.controller;\n      var firstDzInfo;\n      var dataZoomInfoMap = coordSysRecord.dataZoomInfoMap;\n      if (dataZoomInfoMap) {\n        var firstDzKey = dataZoomInfoMap.keys()[0];\n        if (firstDzKey != null) {\n          firstDzInfo = dataZoomInfoMap.get(firstDzKey);\n        }\n      }\n      if (!firstDzInfo) {\n        disposeCoordSysRecord(coordSysRecordMap, coordSysRecord);\n        return;\n      }\n      var controllerParams = mergeControllerParams(dataZoomInfoMap);\n      controller.enable(controllerParams.controlType, controllerParams.opt);\n      controller.setPointerChecker(coordSysRecord.containsPoint);\n      throttleUtil.createOrUpdate(coordSysRecord, 'dispatchAction', firstDzInfo.model.get('throttle', true), 'fixRate');\n    });\n  });\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,YAAY,MAAM,wBAAwB;AACtD,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,SAASC,IAAI,EAAEC,KAAK,EAAEC,aAAa,QAAQ,0BAA0B;AACrE,SAASC,6BAA6B,QAAQ,aAAa;AAC3D,IAAIC,KAAK,GAAGL,SAAS,CAAC,CAAC;AACvB,OAAO,SAASM,2BAA2BA,CAACC,GAAG,EAAEC,aAAa,EAAEC,QAAQ,EAAE;EACxEJ,KAAK,CAACE,GAAG,CAAC,CAACG,iBAAiB,CAACT,IAAI,CAAC,UAAUU,cAAc,EAAE;IAC1D,IAAIC,MAAM,GAAGD,cAAc,CAACE,eAAe,CAACC,GAAG,CAACN,aAAa,CAACO,GAAG,CAAC;IAClE,IAAIH,MAAM,EAAE;MACVA,MAAM,CAACH,QAAQ,GAAGA,QAAQ;IAC5B;EACF,CAAC,CAAC;AACJ;AACA,OAAO,SAASO,6BAA6BA,CAACT,GAAG,EAAEC,aAAa,EAAE;EAChE,IAAIE,iBAAiB,GAAGL,KAAK,CAACE,GAAG,CAAC,CAACG,iBAAiB;EACpD,IAAIO,cAAc,GAAGP,iBAAiB,CAACQ,IAAI,CAAC,CAAC;EAC7C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,cAAc,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IAC9C,IAAIE,WAAW,GAAGJ,cAAc,CAACE,CAAC,CAAC;IACnC,IAAIR,cAAc,GAAGD,iBAAiB,CAACI,GAAG,CAACO,WAAW,CAAC;IACvD,IAAIR,eAAe,GAAGF,cAAc,CAACE,eAAe;IACpD,IAAIA,eAAe,EAAE;MACnB,IAAIS,KAAK,GAAGd,aAAa,CAACO,GAAG;MAC7B,IAAIH,MAAM,GAAGC,eAAe,CAACC,GAAG,CAACQ,KAAK,CAAC;MACvC,IAAIV,MAAM,EAAE;QACVC,eAAe,CAACU,SAAS,CAACD,KAAK,CAAC;QAChC,IAAI,CAACT,eAAe,CAACK,IAAI,CAAC,CAAC,CAACE,MAAM,EAAE;UAClCI,qBAAqB,CAACd,iBAAiB,EAAEC,cAAc,CAAC;QAC1D;MACF;IACF;EACF;AACF;AACA,SAASa,qBAAqBA,CAACd,iBAAiB,EAAEC,cAAc,EAAE;EAChE,IAAIA,cAAc,EAAE;IAClBD,iBAAiB,CAACa,SAAS,CAACZ,cAAc,CAACc,KAAK,CAACV,GAAG,CAAC;IACrD,IAAIW,UAAU,GAAGf,cAAc,CAACe,UAAU;IAC1CA,UAAU,IAAIA,UAAU,CAACC,OAAO,CAAC,CAAC;EACpC;AACF;AACA,SAASC,oBAAoBA,CAACrB,GAAG,EAAEsB,aAAa,EAAE;EAChD;EACA,IAAIlB,cAAc,GAAG;IACnBc,KAAK,EAAEI,aAAa;IACpBC,aAAa,EAAE5B,KAAK,CAAC4B,aAAa,EAAED,aAAa,CAAC;IAClDE,cAAc,EAAE7B,KAAK,CAAC6B,cAAc,EAAExB,GAAG,CAAC;IAC1CM,eAAe,EAAE,IAAI;IACrBa,UAAU,EAAE;EACd,CAAC;EACD;EACA;EACA,IAAIA,UAAU,GAAGf,cAAc,CAACe,UAAU,GAAG,IAAI5B,cAAc,CAACS,GAAG,CAACyB,KAAK,CAAC,CAAC,CAAC;EAC5E/B,IAAI,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,YAAY,CAAC,EAAE,UAAUgC,SAAS,EAAE;IACvDP,UAAU,CAACQ,EAAE,CAACD,SAAS,EAAE,UAAUE,KAAK,EAAE;MACxC,IAAIC,KAAK,GAAG,EAAE;MACdzB,cAAc,CAACE,eAAe,CAACZ,IAAI,CAAC,UAAUW,MAAM,EAAE;QACpD;QACA;QACA,IAAI,CAACuB,KAAK,CAACE,mBAAmB,CAACzB,MAAM,CAACa,KAAK,CAACa,MAAM,CAAC,EAAE;UACnD;QACF;QACA,IAAIC,MAAM,GAAG,CAAC3B,MAAM,CAACH,QAAQ,IAAI,CAAC,CAAC,EAAEwB,SAAS,CAAC;QAC/C,IAAIO,KAAK,GAAGD,MAAM,IAAIA,MAAM,CAAC3B,MAAM,CAAC6B,mBAAmB,EAAE9B,cAAc,CAACc,KAAK,CAACiB,QAAQ,EAAE/B,cAAc,CAACe,UAAU,EAAES,KAAK,CAAC;QACzH,CAACvB,MAAM,CAACa,KAAK,CAACX,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI0B,KAAK,IAAIJ,KAAK,CAACO,IAAI,CAAC;UACzDC,UAAU,EAAEhC,MAAM,CAACa,KAAK,CAACoB,EAAE;UAC3BC,KAAK,EAAEN,KAAK,CAAC,CAAC,CAAC;UACfO,GAAG,EAAEP,KAAK,CAAC,CAAC;QACd,CAAC,CAAC;MACJ,CAAC,CAAC;MACFJ,KAAK,CAAChB,MAAM,IAAIT,cAAc,CAACoB,cAAc,CAACK,KAAK,CAAC;IACtD,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAOzB,cAAc;AACvB;AACA;AACA;AACA;AACA,SAASoB,cAAcA,CAACxB,GAAG,EAAE6B,KAAK,EAAE;EAClC,IAAI,CAAC7B,GAAG,CAACyC,UAAU,CAAC,CAAC,EAAE;IACrBzC,GAAG,CAACwB,cAAc,CAAC;MACjBkB,IAAI,EAAE,UAAU;MAChBC,SAAS,EAAE;QACTC,MAAM,EAAE,UAAU;QAClBC,QAAQ,EAAE;MACZ,CAAC;MACDhB,KAAK,EAAEA;IACT,CAAC,CAAC;EACJ;AACF;AACA,SAASN,aAAaA,CAACD,aAAa,EAAEwB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAC7C,OAAO1B,aAAa,CAAC2B,gBAAgB,CAACC,YAAY,CAAC,CAACH,CAAC,EAAEC,CAAC,CAAC,CAAC;AAC5D;AACA;AACA;AACA;AACA,SAASG,qBAAqBA,CAAC7C,eAAe,EAAE;EAC9C,IAAI8C,WAAW;EACf;EACA;EACA,IAAIC,MAAM,GAAG,OAAO;EACpB,IAAIC,YAAY,GAAG;IACjB,WAAW,EAAE,CAAC;IACd,WAAW,EAAE,CAAC;IACd,YAAY,EAAE,CAAC;IACf,gBAAgB,EAAE,CAAC;EACrB,CAAC;EACD,IAAIC,uBAAuB,GAAG,IAAI;EAClCjD,eAAe,CAACZ,IAAI,CAAC,UAAU8D,YAAY,EAAE;IAC3C,IAAIvD,aAAa,GAAGuD,YAAY,CAACtC,KAAK;IACtC,IAAIuC,OAAO,GAAGxD,aAAa,CAACM,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,GAAG,KAAK,GAAGN,aAAa,CAACM,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,GAAG,MAAM,GAAG,IAAI;IAC/G,IAAI+C,YAAY,CAACD,MAAM,GAAGI,OAAO,CAAC,GAAGH,YAAY,CAACD,MAAM,GAAGD,WAAW,CAAC,EAAE;MACvEA,WAAW,GAAGK,OAAO;IACvB;IACA;IACA;IACAF,uBAAuB,GAAGA,uBAAuB,IAAItD,aAAa,CAACM,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAAC;EACzG,CAAC,CAAC;EACF,OAAO;IACL6C,WAAW,EAAEA,WAAW;IACxBM,GAAG,EAAE;MACH;MACA;MACA;MACAC,gBAAgB,EAAE,IAAI;MACtBC,eAAe,EAAE,IAAI;MACrBC,gBAAgB,EAAE,IAAI;MACtBN,uBAAuB,EAAE,CAAC,CAACA;IAC7B;EACF,CAAC;AACH;AACA,OAAO,SAASO,4BAA4BA,CAACC,SAAS,EAAE;EACtDA,SAAS,CAACC,iBAAiB,CAACD,SAAS,CAACE,QAAQ,CAACC,SAAS,CAACC,MAAM,EAAE,UAAUC,OAAO,EAAEpE,GAAG,EAAE;IACvF,IAAIqE,QAAQ,GAAGvE,KAAK,CAACE,GAAG,CAAC;IACzB,IAAIG,iBAAiB,GAAGkE,QAAQ,CAAClE,iBAAiB,KAAKkE,QAAQ,CAAClE,iBAAiB,GAAGP,aAAa,CAAC,CAAC,CAAC;IACpGO,iBAAiB,CAACT,IAAI,CAAC,UAAUU,cAAc,EAAE;MAC/C;MACA;MACAA,cAAc,CAACE,eAAe,GAAG,IAAI;IACvC,CAAC,CAAC;IACF8D,OAAO,CAACE,aAAa,CAAC;MACpBnC,QAAQ,EAAE,UAAU;MACpBoC,OAAO,EAAE;IACX,CAAC,EAAE,UAAUtE,aAAa,EAAE;MAC1B,IAAIuE,mBAAmB,GAAG3E,6BAA6B,CAACI,aAAa,CAAC;MACtEP,IAAI,CAAC8E,mBAAmB,CAACC,QAAQ,EAAE,UAAUC,cAAc,EAAE;QAC3D,IAAIC,WAAW,GAAGD,cAAc,CAACxD,KAAK,CAACV,GAAG;QAC1C,IAAIJ,cAAc,GAAGD,iBAAiB,CAACI,GAAG,CAACoE,WAAW,CAAC,IAAIxE,iBAAiB,CAACyE,GAAG,CAACD,WAAW,EAAEtD,oBAAoB,CAACrB,GAAG,EAAE0E,cAAc,CAACxD,KAAK,CAAC,CAAC;QAC9I,IAAIZ,eAAe,GAAGF,cAAc,CAACE,eAAe,KAAKF,cAAc,CAACE,eAAe,GAAGV,aAAa,CAAC,CAAC,CAAC;QAC1G;QACAU,eAAe,CAACsE,GAAG,CAAC3E,aAAa,CAACO,GAAG,EAAE;UACrC0B,mBAAmB,EAAEwC,cAAc;UACnCxD,KAAK,EAAEjB,aAAa;UACpBC,QAAQ,EAAE;QACZ,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IACF;IACA;IACAC,iBAAiB,CAACT,IAAI,CAAC,UAAUU,cAAc,EAAE;MAC/C,IAAIe,UAAU,GAAGf,cAAc,CAACe,UAAU;MAC1C,IAAI0D,WAAW;MACf,IAAIvE,eAAe,GAAGF,cAAc,CAACE,eAAe;MACpD,IAAIA,eAAe,EAAE;QACnB,IAAIwE,UAAU,GAAGxE,eAAe,CAACK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C,IAAImE,UAAU,IAAI,IAAI,EAAE;UACtBD,WAAW,GAAGvE,eAAe,CAACC,GAAG,CAACuE,UAAU,CAAC;QAC/C;MACF;MACA,IAAI,CAACD,WAAW,EAAE;QAChB5D,qBAAqB,CAACd,iBAAiB,EAAEC,cAAc,CAAC;QACxD;MACF;MACA,IAAI2E,gBAAgB,GAAG5B,qBAAqB,CAAC7C,eAAe,CAAC;MAC7Da,UAAU,CAAC6D,MAAM,CAACD,gBAAgB,CAAC3B,WAAW,EAAE2B,gBAAgB,CAACrB,GAAG,CAAC;MACrEvC,UAAU,CAAC8D,iBAAiB,CAAC7E,cAAc,CAACmB,aAAa,CAAC;MAC1D/B,YAAY,CAAC0F,cAAc,CAAC9E,cAAc,EAAE,gBAAgB,EAAEyE,WAAW,CAAC3D,KAAK,CAACX,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE,SAAS,CAAC;IACnH,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}