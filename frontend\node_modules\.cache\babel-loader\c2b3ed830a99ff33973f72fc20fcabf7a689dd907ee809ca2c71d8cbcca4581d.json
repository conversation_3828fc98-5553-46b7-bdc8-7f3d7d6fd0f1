{"ast": null, "code": "export default function windingLine(x0, y0, x1, y1, x, y) {\n  if (y > y0 && y > y1 || y < y0 && y < y1) {\n    return 0;\n  }\n  if (y1 === y0) {\n    return 0;\n  }\n  var t = (y - y0) / (y1 - y0);\n  var dir = y1 < y0 ? 1 : -1;\n  if (t === 1 || t === 0) {\n    dir = y1 < y0 ? 0.5 : -0.5;\n  }\n  var x_ = t * (x1 - x0) + x0;\n  return x_ === x ? Infinity : x_ > x ? dir : 0;\n}", "map": {"version": 3, "names": ["windingLine", "x0", "y0", "x1", "y1", "x", "y", "t", "dir", "x_", "Infinity"], "sources": ["E:/AI/SmartCV/node_modules/zrender/lib/contain/windingLine.js"], "sourcesContent": ["export default function windingLine(x0, y0, x1, y1, x, y) {\n    if ((y > y0 && y > y1) || (y < y0 && y < y1)) {\n        return 0;\n    }\n    if (y1 === y0) {\n        return 0;\n    }\n    var t = (y - y0) / (y1 - y0);\n    var dir = y1 < y0 ? 1 : -1;\n    if (t === 1 || t === 0) {\n        dir = y1 < y0 ? 0.5 : -0.5;\n    }\n    var x_ = t * (x1 - x0) + x0;\n    return x_ === x ? Infinity : x_ > x ? dir : 0;\n}\n"], "mappings": "AAAA,eAAe,SAASA,WAAWA,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,CAAC,EAAEC,CAAC,EAAE;EACtD,IAAKA,CAAC,GAAGJ,EAAE,IAAII,CAAC,GAAGF,EAAE,IAAME,CAAC,GAAGJ,EAAE,IAAII,CAAC,GAAGF,EAAG,EAAE;IAC1C,OAAO,CAAC;EACZ;EACA,IAAIA,EAAE,KAAKF,EAAE,EAAE;IACX,OAAO,CAAC;EACZ;EACA,IAAIK,CAAC,GAAG,CAACD,CAAC,GAAGJ,EAAE,KAAKE,EAAE,GAAGF,EAAE,CAAC;EAC5B,IAAIM,GAAG,GAAGJ,EAAE,GAAGF,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;EAC1B,IAAIK,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAK,CAAC,EAAE;IACpBC,GAAG,GAAGJ,EAAE,GAAGF,EAAE,GAAG,GAAG,GAAG,CAAC,GAAG;EAC9B;EACA,IAAIO,EAAE,GAAGF,CAAC,IAAIJ,EAAE,GAAGF,EAAE,CAAC,GAAGA,EAAE;EAC3B,OAAOQ,EAAE,KAAKJ,CAAC,GAAGK,QAAQ,GAAGD,EAAE,GAAGJ,CAAC,GAAGG,GAAG,GAAG,CAAC;AACjD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}