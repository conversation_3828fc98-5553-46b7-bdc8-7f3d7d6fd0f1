#!/usr/bin/env python3
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

from ai_optimizer import calculate_job_match_score, extract_keywords_advanced

# 测试数据
sample_resume = """
John Doe
Software Developer

EXPERIENCE
Senior Python Developer | ABC Company | 2020-2023
- Developed web applications using Python and Flask
- Worked with databases and SQL
- Led a team of 3 developers
- Improved system performance by 30%

Junior Software Engineer | XYZ Corp | 2018-2020  
- Built REST APIs using Django
- Collaborated with cross-functional teams
- Implemented unit testing

EDUCATION
Bachelor of Science in Computer Science
University of Technology | 2018

SKILLS
Programming Languages: Python, JavaScript, Java
Frameworks: Django, Flask, React
Databases: MySQL, PostgreSQL
Tools: Git, Docker
"""

sample_job_description = """
We are looking for a Senior Python Developer to join our team.

Requirements:
- 3+ years of experience with Python development
- Experience with Django or Flask frameworks
- Knowledge of databases (MySQL, PostgreSQL)
- Experience with Git version control
- Strong problem-solving skills
- Team collaboration experience
- Bachelor's degree in Computer Science or related field

Nice to have:
- Experience with <PERSON><PERSON>
- Knowledge of React
- Experience with REST API development
- Unit testing experience
"""

def test_match_score():
    print("=== Testing Job Match Score Calculation ===\n")
    
    # 1. 先分析JD
    print("1. Analyzing Job Description...")
    jd_analysis = extract_keywords_advanced(sample_job_description)
    print(f"   Hard Skills: {jd_analysis.get('hard_skills', [])}")
    print(f"   Soft Skills: {jd_analysis.get('soft_skills', [])}")
    print(f"   Industry Terms: {jd_analysis.get('industry_terms', [])}")
    print()
    
    # 2. 计算匹配度
    print("2. Calculating Match Score...")
    match_result = calculate_job_match_score(sample_resume, sample_job_description, jd_analysis)
    
    if 'error' in match_result:
        print(f"   ERROR: {match_result['error']}")
        return
    
    print(f"   Overall Match Score: {match_result['overall_match_score']}%")
    print()
    
    # 3. 显示详细分解
    print("3. Detailed Breakdown:")
    breakdown = match_result.get('breakdown', {})
    
    for category, data in breakdown.items():
        print(f"   {category.replace('_', ' ').title()}:")
        print(f"     Score: {data['score']}% (Weight: {data['weight']})")
        
        if 'matched' in data and 'total' in data:
            print(f"     Matched: {data['matched']}")
            print(f"     Total Required: {data['total']}")
            missing = [item for item in data['total'] if item not in data['matched']]
            if missing:
                print(f"     Missing: {missing}")
        print()

if __name__ == "__main__":
    test_match_score() 