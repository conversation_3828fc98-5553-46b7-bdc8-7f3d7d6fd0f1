<!DOCTYPE html>
<html>
<head>
    <title>Image Upload Test</title>
</head>
<body>
    <h1>Image Upload Test</h1>
    <input type="file" accept="image/*" onchange="handleFileChange(this)">
    
    <div id="result"></div>
    
    <script>
        function handleFileChange(input) {
            const file = input.files[0];
            const result = document.getElementById('result');
            
            if (file) {
                result.innerHTML = `
                    <h3>File Information:</h3>
                    <p><strong>Name:</strong> ${file.name}</p>
                    <p><strong>Type:</strong> ${file.type}</p>
                    <p><strong>Size:</strong> ${file.size} bytes</p>
                    <p><strong>Is Image:</strong> ${file.type.startsWith('image/')}</p>
                `;
                
                // Display image preview
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        result.innerHTML += `
                            <h3>Preview:</h3>
                            <img src="${e.target.result}" style="max-width: 300px; max-height: 300px;" />
                        `;
                    };
                    reader.readAsDataURL(file);
                }
            }
        }
    </script>
</body>
</html> 