{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport ComponentModel from '../../model/Component.js';\nvar GridModel = /** @class */function (_super) {\n  __extends(GridModel, _super);\n  function GridModel() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  GridModel.type = 'grid';\n  GridModel.dependencies = ['xAxis', 'yAxis'];\n  GridModel.layoutMode = 'box';\n  GridModel.defaultOption = {\n    show: false,\n    // zlevel: 0,\n    z: 0,\n    left: '10%',\n    top: 60,\n    right: '10%',\n    bottom: 70,\n    // If grid size contain label\n    containLabel: false,\n    // width: {totalWidth} - left - right,\n    // height: {totalHeight} - top - bottom,\n    backgroundColor: 'rgba(0,0,0,0)',\n    borderWidth: 1,\n    borderColor: '#ccc'\n  };\n  return GridModel;\n}(ComponentModel);\nexport default GridModel;", "map": {"version": 3, "names": ["__extends", "ComponentModel", "GridModel", "_super", "apply", "arguments", "type", "dependencies", "layoutMode", "defaultOption", "show", "z", "left", "top", "right", "bottom", "containLabel", "backgroundColor", "borderWidth", "borderColor"], "sources": ["E:/AI/SmartCV/node_modules/echarts/lib/coord/cartesian/GridModel.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport ComponentModel from '../../model/Component.js';\nvar GridModel = /** @class */function (_super) {\n  __extends(GridModel, _super);\n  function GridModel() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  GridModel.type = 'grid';\n  GridModel.dependencies = ['xAxis', 'yAxis'];\n  GridModel.layoutMode = 'box';\n  GridModel.defaultOption = {\n    show: false,\n    // zlevel: 0,\n    z: 0,\n    left: '10%',\n    top: 60,\n    right: '10%',\n    bottom: 70,\n    // If grid size contain label\n    containLabel: false,\n    // width: {totalWidth} - left - right,\n    // height: {totalHeight} - top - bottom,\n    backgroundColor: 'rgba(0,0,0,0)',\n    borderWidth: 1,\n    borderColor: '#ccc'\n  };\n  return GridModel;\n}(ComponentModel);\nexport default GridModel;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,cAAc,MAAM,0BAA0B;AACrD,IAAIC,SAAS,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC7CH,SAAS,CAACE,SAAS,EAAEC,MAAM,CAAC;EAC5B,SAASD,SAASA,CAAA,EAAG;IACnB,OAAOC,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;EACjE;EACAH,SAAS,CAACI,IAAI,GAAG,MAAM;EACvBJ,SAAS,CAACK,YAAY,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC;EAC3CL,SAAS,CAACM,UAAU,GAAG,KAAK;EAC5BN,SAAS,CAACO,aAAa,GAAG;IACxBC,IAAI,EAAE,KAAK;IACX;IACAC,CAAC,EAAE,CAAC;IACJC,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,EAAE;IACPC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,EAAE;IACV;IACAC,YAAY,EAAE,KAAK;IACnB;IACA;IACAC,eAAe,EAAE,eAAe;IAChCC,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE;EACf,CAAC;EACD,OAAOjB,SAAS;AAClB,CAAC,CAACD,cAAc,CAAC;AACjB,eAAeC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}