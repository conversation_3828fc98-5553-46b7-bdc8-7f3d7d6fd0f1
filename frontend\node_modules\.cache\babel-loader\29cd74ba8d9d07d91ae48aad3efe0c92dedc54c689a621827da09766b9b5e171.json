{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n// @ts-nocheck\nimport * as zrUtil from 'zrender/lib/core/util.js';\nvar KEY_DELIMITER = '-->';\n/**\r\n * params handler\r\n * @param {module:echarts/model/SeriesModel} seriesModel\r\n * @returns {*}\r\n */\nvar getAutoCurvenessParams = function (seriesModel) {\n  return seriesModel.get('autoCurveness') || null;\n};\n/**\r\n * Generate a list of edge curvatures, 20 is the default\r\n * @param {module:echarts/model/SeriesModel} seriesModel\r\n * @param {number} appendLength\r\n * @return  20 => [0, -0.2, 0.2, -0.4, 0.4, -0.6, 0.6, -0.8, 0.8, -1, 1, -1.2, 1.2, -1.4, 1.4, -1.6, 1.6, -1.8, 1.8, -2]\r\n */\nvar createCurveness = function (seriesModel, appendLength) {\n  var autoCurvenessParmas = getAutoCurvenessParams(seriesModel);\n  var length = 20;\n  var curvenessList = [];\n  // handler the function set\n  if (zrUtil.isNumber(autoCurvenessParmas)) {\n    length = autoCurvenessParmas;\n  } else if (zrUtil.isArray(autoCurvenessParmas)) {\n    seriesModel.__curvenessList = autoCurvenessParmas;\n    return;\n  }\n  // append length\n  if (appendLength > length) {\n    length = appendLength;\n  }\n  // make sure the length is even\n  var len = length % 2 ? length + 2 : length + 3;\n  curvenessList = [];\n  for (var i = 0; i < len; i++) {\n    curvenessList.push((i % 2 ? i + 1 : i) / 10 * (i % 2 ? -1 : 1));\n  }\n  seriesModel.__curvenessList = curvenessList;\n};\n/**\r\n * Create different cache key data in the positive and negative directions, in order to set the curvature later\r\n * @param {number|string|module:echarts/data/Graph.Node} n1\r\n * @param {number|string|module:echarts/data/Graph.Node} n2\r\n * @param {module:echarts/model/SeriesModel} seriesModel\r\n * @returns {string} key\r\n */\nvar getKeyOfEdges = function (n1, n2, seriesModel) {\n  var source = [n1.id, n1.dataIndex].join('.');\n  var target = [n2.id, n2.dataIndex].join('.');\n  return [seriesModel.uid, source, target].join(KEY_DELIMITER);\n};\n/**\r\n * get opposite key\r\n * @param {string} key\r\n * @returns {string}\r\n */\nvar getOppositeKey = function (key) {\n  var keys = key.split(KEY_DELIMITER);\n  return [keys[0], keys[2], keys[1]].join(KEY_DELIMITER);\n};\n/**\r\n * get edgeMap with key\r\n * @param edge\r\n * @param {module:echarts/model/SeriesModel} seriesModel\r\n */\nvar getEdgeFromMap = function (edge, seriesModel) {\n  var key = getKeyOfEdges(edge.node1, edge.node2, seriesModel);\n  return seriesModel.__edgeMap[key];\n};\n/**\r\n * calculate all cases total length\r\n * @param edge\r\n * @param seriesModel\r\n * @returns {number}\r\n */\nvar getTotalLengthBetweenNodes = function (edge, seriesModel) {\n  var len = getEdgeMapLengthWithKey(getKeyOfEdges(edge.node1, edge.node2, seriesModel), seriesModel);\n  var lenV = getEdgeMapLengthWithKey(getKeyOfEdges(edge.node2, edge.node1, seriesModel), seriesModel);\n  return len + lenV;\n};\n/**\r\n *\r\n * @param key\r\n */\nvar getEdgeMapLengthWithKey = function (key, seriesModel) {\n  var edgeMap = seriesModel.__edgeMap;\n  return edgeMap[key] ? edgeMap[key].length : 0;\n};\n/**\r\n * Count the number of edges between the same two points, used to obtain the curvature table and the parity of the edge\r\n * @see /graph/GraphSeries.js@getInitialData\r\n * @param {module:echarts/model/SeriesModel} seriesModel\r\n */\nexport function initCurvenessList(seriesModel) {\n  if (!getAutoCurvenessParams(seriesModel)) {\n    return;\n  }\n  seriesModel.__curvenessList = [];\n  seriesModel.__edgeMap = {};\n  // calc the array of curveness List\n  createCurveness(seriesModel);\n}\n/**\r\n * set edgeMap with key\r\n * @param {number|string|module:echarts/data/Graph.Node} n1\r\n * @param {number|string|module:echarts/data/Graph.Node} n2\r\n * @param {module:echarts/model/SeriesModel} seriesModel\r\n * @param {number} index\r\n */\nexport function createEdgeMapForCurveness(n1, n2, seriesModel, index) {\n  if (!getAutoCurvenessParams(seriesModel)) {\n    return;\n  }\n  var key = getKeyOfEdges(n1, n2, seriesModel);\n  var edgeMap = seriesModel.__edgeMap;\n  var oppositeEdges = edgeMap[getOppositeKey(key)];\n  // set direction\n  if (edgeMap[key] && !oppositeEdges) {\n    edgeMap[key].isForward = true;\n  } else if (oppositeEdges && edgeMap[key]) {\n    oppositeEdges.isForward = true;\n    edgeMap[key].isForward = false;\n  }\n  edgeMap[key] = edgeMap[key] || [];\n  edgeMap[key].push(index);\n}\n/**\r\n * get curvature for edge\r\n * @param edge\r\n * @param {module:echarts/model/SeriesModel} seriesModel\r\n * @param index\r\n */\nexport function getCurvenessForEdge(edge, seriesModel, index, needReverse) {\n  var autoCurvenessParams = getAutoCurvenessParams(seriesModel);\n  var isArrayParam = zrUtil.isArray(autoCurvenessParams);\n  if (!autoCurvenessParams) {\n    return null;\n  }\n  var edgeArray = getEdgeFromMap(edge, seriesModel);\n  if (!edgeArray) {\n    return null;\n  }\n  var edgeIndex = -1;\n  for (var i = 0; i < edgeArray.length; i++) {\n    if (edgeArray[i] === index) {\n      edgeIndex = i;\n      break;\n    }\n  }\n  // if totalLen is Longer createCurveness\n  var totalLen = getTotalLengthBetweenNodes(edge, seriesModel);\n  createCurveness(seriesModel, totalLen);\n  edge.lineStyle = edge.lineStyle || {};\n  // if is opposite edge, must set curvenss to opposite number\n  var curKey = getKeyOfEdges(edge.node1, edge.node2, seriesModel);\n  var curvenessList = seriesModel.__curvenessList;\n  // if pass array no need parity\n  var parityCorrection = isArrayParam ? 0 : totalLen % 2 ? 0 : 1;\n  if (!edgeArray.isForward) {\n    // the opposite edge show outside\n    var oppositeKey = getOppositeKey(curKey);\n    var len = getEdgeMapLengthWithKey(oppositeKey, seriesModel);\n    var resValue = curvenessList[edgeIndex + len + parityCorrection];\n    // isNeedReverse, simple, force type need reverse the curveness in the junction of the forword and the opposite\n    if (needReverse) {\n      // set as array may make the parity handle with the len of opposite\n      if (isArrayParam) {\n        if (autoCurvenessParams && autoCurvenessParams[0] === 0) {\n          return (len + parityCorrection) % 2 ? resValue : -resValue;\n        } else {\n          return ((len % 2 ? 0 : 1) + parityCorrection) % 2 ? resValue : -resValue;\n        }\n      } else {\n        return (len + parityCorrection) % 2 ? resValue : -resValue;\n      }\n    } else {\n      return curvenessList[edgeIndex + len + parityCorrection];\n    }\n  } else {\n    return curvenessList[parityCorrection + edgeIndex];\n  }\n}", "map": {"version": 3, "names": ["zrUtil", "KEY_DELIMITER", "getAutoCurvenessParams", "seriesModel", "get", "createCurveness", "appendLength", "autoCurvenessParmas", "length", "curvenessList", "isNumber", "isArray", "__curvenessList", "len", "i", "push", "getKeyOfEdges", "n1", "n2", "source", "id", "dataIndex", "join", "target", "uid", "getOppositeKey", "key", "keys", "split", "getEdgeFromMap", "edge", "node1", "node2", "__edgeMap", "getTotalLengthBetweenNodes", "getEdgeMapLengthWithKey", "lenV", "edgeMap", "initCurvenessList", "createEdgeMapForCurveness", "index", "opposite<PERSON><PERSON>", "isForward", "getCurvenessForEdge", "needReverse", "autoCurvenessParams", "isArrayParam", "edgeArray", "edgeIndex", "totalLen", "lineStyle", "cur<PERSON><PERSON>", "parityCorrection", "<PERSON><PERSON><PERSON>", "resValue"], "sources": ["E:/AI/SmartCV/node_modules/echarts/lib/chart/helper/multipleGraphEdgeHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n// @ts-nocheck\nimport * as zrUtil from 'zrender/lib/core/util.js';\nvar KEY_DELIMITER = '-->';\n/**\r\n * params handler\r\n * @param {module:echarts/model/SeriesModel} seriesModel\r\n * @returns {*}\r\n */\nvar getAutoCurvenessParams = function (seriesModel) {\n  return seriesModel.get('autoCurveness') || null;\n};\n/**\r\n * Generate a list of edge curvatures, 20 is the default\r\n * @param {module:echarts/model/SeriesModel} seriesModel\r\n * @param {number} appendLength\r\n * @return  20 => [0, -0.2, 0.2, -0.4, 0.4, -0.6, 0.6, -0.8, 0.8, -1, 1, -1.2, 1.2, -1.4, 1.4, -1.6, 1.6, -1.8, 1.8, -2]\r\n */\nvar createCurveness = function (seriesModel, appendLength) {\n  var autoCurvenessParmas = getAutoCurvenessParams(seriesModel);\n  var length = 20;\n  var curvenessList = [];\n  // handler the function set\n  if (zrUtil.isNumber(autoCurvenessParmas)) {\n    length = autoCurvenessParmas;\n  } else if (zrUtil.isArray(autoCurvenessParmas)) {\n    seriesModel.__curvenessList = autoCurvenessParmas;\n    return;\n  }\n  // append length\n  if (appendLength > length) {\n    length = appendLength;\n  }\n  // make sure the length is even\n  var len = length % 2 ? length + 2 : length + 3;\n  curvenessList = [];\n  for (var i = 0; i < len; i++) {\n    curvenessList.push((i % 2 ? i + 1 : i) / 10 * (i % 2 ? -1 : 1));\n  }\n  seriesModel.__curvenessList = curvenessList;\n};\n/**\r\n * Create different cache key data in the positive and negative directions, in order to set the curvature later\r\n * @param {number|string|module:echarts/data/Graph.Node} n1\r\n * @param {number|string|module:echarts/data/Graph.Node} n2\r\n * @param {module:echarts/model/SeriesModel} seriesModel\r\n * @returns {string} key\r\n */\nvar getKeyOfEdges = function (n1, n2, seriesModel) {\n  var source = [n1.id, n1.dataIndex].join('.');\n  var target = [n2.id, n2.dataIndex].join('.');\n  return [seriesModel.uid, source, target].join(KEY_DELIMITER);\n};\n/**\r\n * get opposite key\r\n * @param {string} key\r\n * @returns {string}\r\n */\nvar getOppositeKey = function (key) {\n  var keys = key.split(KEY_DELIMITER);\n  return [keys[0], keys[2], keys[1]].join(KEY_DELIMITER);\n};\n/**\r\n * get edgeMap with key\r\n * @param edge\r\n * @param {module:echarts/model/SeriesModel} seriesModel\r\n */\nvar getEdgeFromMap = function (edge, seriesModel) {\n  var key = getKeyOfEdges(edge.node1, edge.node2, seriesModel);\n  return seriesModel.__edgeMap[key];\n};\n/**\r\n * calculate all cases total length\r\n * @param edge\r\n * @param seriesModel\r\n * @returns {number}\r\n */\nvar getTotalLengthBetweenNodes = function (edge, seriesModel) {\n  var len = getEdgeMapLengthWithKey(getKeyOfEdges(edge.node1, edge.node2, seriesModel), seriesModel);\n  var lenV = getEdgeMapLengthWithKey(getKeyOfEdges(edge.node2, edge.node1, seriesModel), seriesModel);\n  return len + lenV;\n};\n/**\r\n *\r\n * @param key\r\n */\nvar getEdgeMapLengthWithKey = function (key, seriesModel) {\n  var edgeMap = seriesModel.__edgeMap;\n  return edgeMap[key] ? edgeMap[key].length : 0;\n};\n/**\r\n * Count the number of edges between the same two points, used to obtain the curvature table and the parity of the edge\r\n * @see /graph/GraphSeries.js@getInitialData\r\n * @param {module:echarts/model/SeriesModel} seriesModel\r\n */\nexport function initCurvenessList(seriesModel) {\n  if (!getAutoCurvenessParams(seriesModel)) {\n    return;\n  }\n  seriesModel.__curvenessList = [];\n  seriesModel.__edgeMap = {};\n  // calc the array of curveness List\n  createCurveness(seriesModel);\n}\n/**\r\n * set edgeMap with key\r\n * @param {number|string|module:echarts/data/Graph.Node} n1\r\n * @param {number|string|module:echarts/data/Graph.Node} n2\r\n * @param {module:echarts/model/SeriesModel} seriesModel\r\n * @param {number} index\r\n */\nexport function createEdgeMapForCurveness(n1, n2, seriesModel, index) {\n  if (!getAutoCurvenessParams(seriesModel)) {\n    return;\n  }\n  var key = getKeyOfEdges(n1, n2, seriesModel);\n  var edgeMap = seriesModel.__edgeMap;\n  var oppositeEdges = edgeMap[getOppositeKey(key)];\n  // set direction\n  if (edgeMap[key] && !oppositeEdges) {\n    edgeMap[key].isForward = true;\n  } else if (oppositeEdges && edgeMap[key]) {\n    oppositeEdges.isForward = true;\n    edgeMap[key].isForward = false;\n  }\n  edgeMap[key] = edgeMap[key] || [];\n  edgeMap[key].push(index);\n}\n/**\r\n * get curvature for edge\r\n * @param edge\r\n * @param {module:echarts/model/SeriesModel} seriesModel\r\n * @param index\r\n */\nexport function getCurvenessForEdge(edge, seriesModel, index, needReverse) {\n  var autoCurvenessParams = getAutoCurvenessParams(seriesModel);\n  var isArrayParam = zrUtil.isArray(autoCurvenessParams);\n  if (!autoCurvenessParams) {\n    return null;\n  }\n  var edgeArray = getEdgeFromMap(edge, seriesModel);\n  if (!edgeArray) {\n    return null;\n  }\n  var edgeIndex = -1;\n  for (var i = 0; i < edgeArray.length; i++) {\n    if (edgeArray[i] === index) {\n      edgeIndex = i;\n      break;\n    }\n  }\n  // if totalLen is Longer createCurveness\n  var totalLen = getTotalLengthBetweenNodes(edge, seriesModel);\n  createCurveness(seriesModel, totalLen);\n  edge.lineStyle = edge.lineStyle || {};\n  // if is opposite edge, must set curvenss to opposite number\n  var curKey = getKeyOfEdges(edge.node1, edge.node2, seriesModel);\n  var curvenessList = seriesModel.__curvenessList;\n  // if pass array no need parity\n  var parityCorrection = isArrayParam ? 0 : totalLen % 2 ? 0 : 1;\n  if (!edgeArray.isForward) {\n    // the opposite edge show outside\n    var oppositeKey = getOppositeKey(curKey);\n    var len = getEdgeMapLengthWithKey(oppositeKey, seriesModel);\n    var resValue = curvenessList[edgeIndex + len + parityCorrection];\n    // isNeedReverse, simple, force type need reverse the curveness in the junction of the forword and the opposite\n    if (needReverse) {\n      // set as array may make the parity handle with the len of opposite\n      if (isArrayParam) {\n        if (autoCurvenessParams && autoCurvenessParams[0] === 0) {\n          return (len + parityCorrection) % 2 ? resValue : -resValue;\n        } else {\n          return ((len % 2 ? 0 : 1) + parityCorrection) % 2 ? resValue : -resValue;\n        }\n      } else {\n        return (len + parityCorrection) % 2 ? resValue : -resValue;\n      }\n    } else {\n      return curvenessList[edgeIndex + len + parityCorrection];\n    }\n  } else {\n    return curvenessList[parityCorrection + edgeIndex];\n  }\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,IAAIC,aAAa,GAAG,KAAK;AACzB;AACA;AACA;AACA;AACA;AACA,IAAIC,sBAAsB,GAAG,SAAAA,CAAUC,WAAW,EAAE;EAClD,OAAOA,WAAW,CAACC,GAAG,CAAC,eAAe,CAAC,IAAI,IAAI;AACjD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,eAAe,GAAG,SAAAA,CAAUF,WAAW,EAAEG,YAAY,EAAE;EACzD,IAAIC,mBAAmB,GAAGL,sBAAsB,CAACC,WAAW,CAAC;EAC7D,IAAIK,MAAM,GAAG,EAAE;EACf,IAAIC,aAAa,GAAG,EAAE;EACtB;EACA,IAAIT,MAAM,CAACU,QAAQ,CAACH,mBAAmB,CAAC,EAAE;IACxCC,MAAM,GAAGD,mBAAmB;EAC9B,CAAC,MAAM,IAAIP,MAAM,CAACW,OAAO,CAACJ,mBAAmB,CAAC,EAAE;IAC9CJ,WAAW,CAACS,eAAe,GAAGL,mBAAmB;IACjD;EACF;EACA;EACA,IAAID,YAAY,GAAGE,MAAM,EAAE;IACzBA,MAAM,GAAGF,YAAY;EACvB;EACA;EACA,IAAIO,GAAG,GAAGL,MAAM,GAAG,CAAC,GAAGA,MAAM,GAAG,CAAC,GAAGA,MAAM,GAAG,CAAC;EAC9CC,aAAa,GAAG,EAAE;EAClB,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,GAAG,EAAEC,CAAC,EAAE,EAAE;IAC5BL,aAAa,CAACM,IAAI,CAAC,CAACD,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGA,CAAC,IAAI,EAAE,IAAIA,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;EACjE;EACAX,WAAW,CAACS,eAAe,GAAGH,aAAa;AAC7C,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIO,aAAa,GAAG,SAAAA,CAAUC,EAAE,EAAEC,EAAE,EAAEf,WAAW,EAAE;EACjD,IAAIgB,MAAM,GAAG,CAACF,EAAE,CAACG,EAAE,EAAEH,EAAE,CAACI,SAAS,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;EAC5C,IAAIC,MAAM,GAAG,CAACL,EAAE,CAACE,EAAE,EAAEF,EAAE,CAACG,SAAS,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;EAC5C,OAAO,CAACnB,WAAW,CAACqB,GAAG,EAAEL,MAAM,EAAEI,MAAM,CAAC,CAACD,IAAI,CAACrB,aAAa,CAAC;AAC9D,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,IAAIwB,cAAc,GAAG,SAAAA,CAAUC,GAAG,EAAE;EAClC,IAAIC,IAAI,GAAGD,GAAG,CAACE,KAAK,CAAC3B,aAAa,CAAC;EACnC,OAAO,CAAC0B,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC,CAACL,IAAI,CAACrB,aAAa,CAAC;AACxD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,IAAI4B,cAAc,GAAG,SAAAA,CAAUC,IAAI,EAAE3B,WAAW,EAAE;EAChD,IAAIuB,GAAG,GAAGV,aAAa,CAACc,IAAI,CAACC,KAAK,EAAED,IAAI,CAACE,KAAK,EAAE7B,WAAW,CAAC;EAC5D,OAAOA,WAAW,CAAC8B,SAAS,CAACP,GAAG,CAAC;AACnC,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,IAAIQ,0BAA0B,GAAG,SAAAA,CAAUJ,IAAI,EAAE3B,WAAW,EAAE;EAC5D,IAAIU,GAAG,GAAGsB,uBAAuB,CAACnB,aAAa,CAACc,IAAI,CAACC,KAAK,EAAED,IAAI,CAACE,KAAK,EAAE7B,WAAW,CAAC,EAAEA,WAAW,CAAC;EAClG,IAAIiC,IAAI,GAAGD,uBAAuB,CAACnB,aAAa,CAACc,IAAI,CAACE,KAAK,EAAEF,IAAI,CAACC,KAAK,EAAE5B,WAAW,CAAC,EAAEA,WAAW,CAAC;EACnG,OAAOU,GAAG,GAAGuB,IAAI;AACnB,CAAC;AACD;AACA;AACA;AACA;AACA,IAAID,uBAAuB,GAAG,SAAAA,CAAUT,GAAG,EAAEvB,WAAW,EAAE;EACxD,IAAIkC,OAAO,GAAGlC,WAAW,CAAC8B,SAAS;EACnC,OAAOI,OAAO,CAACX,GAAG,CAAC,GAAGW,OAAO,CAACX,GAAG,CAAC,CAAClB,MAAM,GAAG,CAAC;AAC/C,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS8B,iBAAiBA,CAACnC,WAAW,EAAE;EAC7C,IAAI,CAACD,sBAAsB,CAACC,WAAW,CAAC,EAAE;IACxC;EACF;EACAA,WAAW,CAACS,eAAe,GAAG,EAAE;EAChCT,WAAW,CAAC8B,SAAS,GAAG,CAAC,CAAC;EAC1B;EACA5B,eAAe,CAACF,WAAW,CAAC;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASoC,yBAAyBA,CAACtB,EAAE,EAAEC,EAAE,EAAEf,WAAW,EAAEqC,KAAK,EAAE;EACpE,IAAI,CAACtC,sBAAsB,CAACC,WAAW,CAAC,EAAE;IACxC;EACF;EACA,IAAIuB,GAAG,GAAGV,aAAa,CAACC,EAAE,EAAEC,EAAE,EAAEf,WAAW,CAAC;EAC5C,IAAIkC,OAAO,GAAGlC,WAAW,CAAC8B,SAAS;EACnC,IAAIQ,aAAa,GAAGJ,OAAO,CAACZ,cAAc,CAACC,GAAG,CAAC,CAAC;EAChD;EACA,IAAIW,OAAO,CAACX,GAAG,CAAC,IAAI,CAACe,aAAa,EAAE;IAClCJ,OAAO,CAACX,GAAG,CAAC,CAACgB,SAAS,GAAG,IAAI;EAC/B,CAAC,MAAM,IAAID,aAAa,IAAIJ,OAAO,CAACX,GAAG,CAAC,EAAE;IACxCe,aAAa,CAACC,SAAS,GAAG,IAAI;IAC9BL,OAAO,CAACX,GAAG,CAAC,CAACgB,SAAS,GAAG,KAAK;EAChC;EACAL,OAAO,CAACX,GAAG,CAAC,GAAGW,OAAO,CAACX,GAAG,CAAC,IAAI,EAAE;EACjCW,OAAO,CAACX,GAAG,CAAC,CAACX,IAAI,CAACyB,KAAK,CAAC;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,mBAAmBA,CAACb,IAAI,EAAE3B,WAAW,EAAEqC,KAAK,EAAEI,WAAW,EAAE;EACzE,IAAIC,mBAAmB,GAAG3C,sBAAsB,CAACC,WAAW,CAAC;EAC7D,IAAI2C,YAAY,GAAG9C,MAAM,CAACW,OAAO,CAACkC,mBAAmB,CAAC;EACtD,IAAI,CAACA,mBAAmB,EAAE;IACxB,OAAO,IAAI;EACb;EACA,IAAIE,SAAS,GAAGlB,cAAc,CAACC,IAAI,EAAE3B,WAAW,CAAC;EACjD,IAAI,CAAC4C,SAAS,EAAE;IACd,OAAO,IAAI;EACb;EACA,IAAIC,SAAS,GAAG,CAAC,CAAC;EAClB,KAAK,IAAIlC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiC,SAAS,CAACvC,MAAM,EAAEM,CAAC,EAAE,EAAE;IACzC,IAAIiC,SAAS,CAACjC,CAAC,CAAC,KAAK0B,KAAK,EAAE;MAC1BQ,SAAS,GAAGlC,CAAC;MACb;IACF;EACF;EACA;EACA,IAAImC,QAAQ,GAAGf,0BAA0B,CAACJ,IAAI,EAAE3B,WAAW,CAAC;EAC5DE,eAAe,CAACF,WAAW,EAAE8C,QAAQ,CAAC;EACtCnB,IAAI,CAACoB,SAAS,GAAGpB,IAAI,CAACoB,SAAS,IAAI,CAAC,CAAC;EACrC;EACA,IAAIC,MAAM,GAAGnC,aAAa,CAACc,IAAI,CAACC,KAAK,EAAED,IAAI,CAACE,KAAK,EAAE7B,WAAW,CAAC;EAC/D,IAAIM,aAAa,GAAGN,WAAW,CAACS,eAAe;EAC/C;EACA,IAAIwC,gBAAgB,GAAGN,YAAY,GAAG,CAAC,GAAGG,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;EAC9D,IAAI,CAACF,SAAS,CAACL,SAAS,EAAE;IACxB;IACA,IAAIW,WAAW,GAAG5B,cAAc,CAAC0B,MAAM,CAAC;IACxC,IAAItC,GAAG,GAAGsB,uBAAuB,CAACkB,WAAW,EAAElD,WAAW,CAAC;IAC3D,IAAImD,QAAQ,GAAG7C,aAAa,CAACuC,SAAS,GAAGnC,GAAG,GAAGuC,gBAAgB,CAAC;IAChE;IACA,IAAIR,WAAW,EAAE;MACf;MACA,IAAIE,YAAY,EAAE;QAChB,IAAID,mBAAmB,IAAIA,mBAAmB,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;UACvD,OAAO,CAAChC,GAAG,GAAGuC,gBAAgB,IAAI,CAAC,GAAGE,QAAQ,GAAG,CAACA,QAAQ;QAC5D,CAAC,MAAM;UACL,OAAO,CAAC,CAACzC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,IAAIuC,gBAAgB,IAAI,CAAC,GAAGE,QAAQ,GAAG,CAACA,QAAQ;QAC1E;MACF,CAAC,MAAM;QACL,OAAO,CAACzC,GAAG,GAAGuC,gBAAgB,IAAI,CAAC,GAAGE,QAAQ,GAAG,CAACA,QAAQ;MAC5D;IACF,CAAC,MAAM;MACL,OAAO7C,aAAa,CAACuC,SAAS,GAAGnC,GAAG,GAAGuC,gBAAgB,CAAC;IAC1D;EACF,CAAC,MAAM;IACL,OAAO3C,aAAa,CAAC2C,gBAAgB,GAAGJ,SAAS,CAAC;EACpD;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}